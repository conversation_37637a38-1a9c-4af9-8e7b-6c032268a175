package com.github.wxpay.sdk;

import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * 用于生成测试SIGN
 * <AUTHOR>
 *
 */
@Slf4j
public class Sign {
	public static String test() {
		String appid="wxd7ad1410f6eb7757";
		String auth_code="130659679008752267";
		String body="幸福店-大公仔";
		String mch_id="1486784502";
		String nonce_str=WXPayUtil.generateNonceStr();
		String out_trade_no="52501201407033233368019";
		String spbill_create_ip="***************";
		String sub_mch_id="1487145542";
		String total_fee="1";
		String key="sdyingxianglisdyingxiangli123456";
		String all="appid="+appid+"&auth_code="+auth_code+"&body="+body+"&mch_id="+mch_id+"&nonce_str="+nonce_str+"&out_trade_no="+out_trade_no;
		all+="&spbill_create_ip="+spbill_create_ip+"&total_fee="+total_fee+"&key="+key;
		System.out.println(all);

		Map<String,String> map=new HashMap<String, String>();
		map.put("appid", appid);
		map.put("mch_id", mch_id);
		map.put("nonce_str", nonce_str);
		map.put("sub_mch_id", sub_mch_id);
		map.put("body", body);//传入参数
		map.put("out_trade_no", out_trade_no);//传入参数
		map.put("total_fee", total_fee);
		map.put("spbill_create_ip", spbill_create_ip);//传入参数IP地址
		map.put("auth_code", auth_code);//传入参数，支付码
		String xml="";
		try {
			xml=WXPayUtil.generateSignedXml(map, key);
			System.out.println(xml);
		} catch (Exception e) {
			System.out.println("格式转换异常！");
			log.error("生成xml失败",e);
		}
		return xml;
	}


}

