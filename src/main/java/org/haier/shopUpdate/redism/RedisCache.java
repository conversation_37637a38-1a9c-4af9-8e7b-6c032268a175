package org.haier.shopUpdate.redism;

import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.cache.Cache;
import org.haier.shopUpdate.wechat.AuthUtil;
import org.springframework.data.redis.connection.jedis.JedisConnection;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.serializer.JdkSerializationRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializer;

import redis.clients.jedis.exceptions.JedisConnectionException;

/**
 *
 * @描述: 使用第三方内存数据库Redis作为二级缓存
 * @版权: Copyright (c) 2016
 * @作者: xiad
 * @版本: 1.0
 * @创建日期: 2016年3月2日
 * @创建时间: 下午8:02:57
 */
@Slf4j
public class RedisCache implements Cache {
//	private static final Logger logger = LoggerFactory.getLogger(RedisCache.class);

	private static JedisConnectionFactory jedisConnectionFactory;

	private final String id;

	/**
	 * The {@code ReadWriteLock}.
	 */
	private final ReadWriteLock readWriteLock = new ReentrantReadWriteLock();

	public RedisCache () {
		this.id = "1";
	}

	public RedisCache(final String id) {
		if (id == null) {
			throw new IllegalArgumentException("Cache instances require an ID");
		}
		this.id = id;
	}

	public void clear() {
		JedisConnection connection = null;
		try {
			connection = jedisConnectionFactory.getConnection();
//			connection.flushDb();
//			connection.flushAll();
		} catch (JedisConnectionException e) {
			log.error("清空缓存异常：",e);
		} finally {
			if (connection != null) {
				connection.close();
			}
		}
	}

	public String getId() {
		return this.id;
	}

	public void keyList() {
		JedisConnection connection = null;
		try {
			connection = jedisConnectionFactory.getConnection();
			String k = "*";
		} catch (JedisConnectionException e) {
			log.error("获取key列表异常：",e);
		} finally {
			if (connection != null) {
				connection.close();
			}
		}
	}

	public Object getObject(Object key) {
		Object result = null;
		JedisConnection connection = null;
		try {
			connection = jedisConnectionFactory.getConnection();
			RedisSerializer<Object> serializer = new JdkSerializationRedisSerializer();
			result = serializer.deserialize(connection.get(serializer.serialize(AuthUtil.REDISFORTEST + key)));
			//System.out.println("存储KEY==="+key.toString()+"》》》存储结果=========="+result);
		} catch (JedisConnectionException e) {
			log.error("获取缓存异常：",e);
		} finally {
			if (connection != null) {
				connection.close();
			}
		}
		return result;
	}

	public ReadWriteLock getReadWriteLock() {
		return this.readWriteLock;
	}

	public int getSize() {
		int result = 0;
		JedisConnection connection = null;
		try {
			connection = jedisConnectionFactory.getConnection();
			result = Integer.valueOf(connection.dbSize().toString());
		} catch (JedisConnectionException e) {
			log.error("获取缓存大小异常：",e);
		} finally {
			if (connection != null) {
				connection.close();
			}
		}
		return result;
	}

	public void putObject(Object key, Object value) {
		JedisConnection connection = null;
		try {
			connection = jedisConnectionFactory.getConnection();
			RedisSerializer<Object> serializer = new JdkSerializationRedisSerializer();
			connection.setEx(serializer.serialize(AuthUtil.REDISFORTEST + key), 300, serializer.serialize(value));
		} catch (JedisConnectionException e) {
			log.error("存储缓存异常：",e);
		} finally {
			if (connection != null) {
				connection.close();
			}
		}
	}

	public void putObjectNoExpire(Object key, Object value) {
		JedisConnection connection = null;
		try {
			connection = jedisConnectionFactory.getConnection();
			RedisSerializer<Object> serializer = new JdkSerializationRedisSerializer();
			connection.set(serializer.serialize(AuthUtil.REDISFORTEST + key), serializer.serialize(value));
		} catch (JedisConnectionException e) {
			log.error("存储缓存异常：",e);
		} finally {
			if (connection != null) {
				connection.close();
			}
		}
	}

	@SuppressWarnings({ "hiding", "unchecked" })
	public <T>T getList(String key){
		T result = null;
		JedisConnection connection = null;
		try {
			connection = jedisConnectionFactory.getConnection();
			RedisSerializer<Object> serializer = new JdkSerializationRedisSerializer();
			Object b = serializer.deserialize(connection.get(serializer.serialize(key)));
			if(null != b) {
				result = (T) serializer.deserialize(connection.get(serializer.serialize(AuthUtil.REDISFORTEST + key)));
			}
//			System.out.println("存储KEY==="+key.toString()+"》》》存储结果=========="+result);
		} catch (JedisConnectionException e) {
			log.error("获取缓存异常：",e);
		} finally {
			if (connection != null) {
				connection.close();
			}
		}
		return result;
	}

	/**
	 * 设置自定义的存储过期时间
	 *
	 * @param key
	 * @param value
	 * @param time
	 */
	public void putObject(Object key,Object value,Integer time){
		JedisConnection connection = null;
		try {
			connection = jedisConnectionFactory.getConnection();
			RedisSerializer<Object> serializer = new JdkSerializationRedisSerializer();
			connection.setEx(serializer.serialize(AuthUtil.REDISFORTEST + key), time, serializer.serialize(value));
//			System.out.println(key+value+time);
		} catch (JedisConnectionException e) {
			System.out.println("存储异常！！");
			log.error("存储缓存异常：",e);
		} finally {
			if (connection != null) {
				connection.close();
			}
		}
	}
	public Object removeObject(Object key) {
		JedisConnection connection = null;
		Object result = null;
		try {
			connection = jedisConnectionFactory.getConnection();
			RedisSerializer<Object> serializer = new JdkSerializationRedisSerializer();
			result = connection.expire(serializer.serialize(AuthUtil.REDISFORTEST + key), 0);
		} catch (JedisConnectionException e) {
			log.error("删除缓存异常：",e);
		} finally {
			if (connection != null) {
				connection.close();
			}
		}
		return result;
	}

	public static void setJedisConnectionFactory(JedisConnectionFactory jedisConnectionFactory) {
		RedisCache.jedisConnectionFactory = jedisConnectionFactory;
	}

}
