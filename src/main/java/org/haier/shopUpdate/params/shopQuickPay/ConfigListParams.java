package org.haier.shopUpdate.params.shopQuickPay;

import org.haier.shopUpdate.params.PageParams;

import java.math.BigDecimal;

public class ConfigListParams extends PageParams {
    //店铺编号
    private Long shopUnique;

    private Integer validStatus;

    private BigDecimal amountMoney;

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public Integer getValidStatus() {
        return validStatus;
    }

    public void setValidStatus(Integer validStatus) {
        this.validStatus = validStatus;
    }

    public BigDecimal getAmountMoney() {
        return amountMoney;
    }

    public void setAmountMoney(BigDecimal amountMoney) {
        this.amountMoney = amountMoney;
    }
}
