package org.haier.shopUpdate.params.shopStockDetail;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName shopStockDetailAddParams
 * <AUTHOR>
 * @Date 2024/5/6 9:37
 */

public class ShopStockDetailAddParams implements Serializable {
    private String ShopUnique;
    private String listUnique;
    private String sourceUnique;
    private String supplierUnique;
    private Integer stockKind;
    private Integer auditStatus;
    private Long auditId;
    private Date auditTime;
    private String auditContent;
    private String stockRemarks;
    private String stockPicture;
    private Integer stockType;
    private Integer stockResource;
    private Integer stockOrigin;
    private Long staffId;
    private Date stockTime;
    private BigDecimal totalCount;
    private BigDecimal totalAmount;
    private Long updateId;
    private Date updateTime;

    public String getShopUnique() {
        return ShopUnique;
    }

    public void setShopUnique(String shopUnique) {
        ShopUnique = shopUnique;
    }

    public String getListUnique() {
        return listUnique;
    }

    public void setListUnique(String listUnique) {
        this.listUnique = listUnique;
    }

    public String getSourceUnique() {
        return sourceUnique;
    }

    public void setSourceUnique(String sourceUnique) {
        this.sourceUnique = sourceUnique;
    }

    public String getSupplierUnique() {
        return supplierUnique;
    }

    public void setSupplierUnique(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }

    public Integer getStockKind() {
        return stockKind;
    }

    public void setStockKind(Integer stockKind) {
        this.stockKind = stockKind;
    }

    public Integer getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    public Long getAuditId() {
        return auditId;
    }

    public void setAuditId(Long auditId) {
        this.auditId = auditId;
    }

    public Date getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }

    public String getAuditContent() {
        return auditContent;
    }

    public void setAuditContent(String auditContent) {
        this.auditContent = auditContent;
    }

    public String getStockRemarks() {
        return stockRemarks;
    }

    public void setStockRemarks(String stockRemarks) {
        this.stockRemarks = stockRemarks;
    }

    public String getStockPicture() {
        return stockPicture;
    }

    public void setStockPicture(String stockPicture) {
        this.stockPicture = stockPicture;
    }

    public Integer getStockType() {
        return stockType;
    }

    public void setStockType(Integer stockType) {
        this.stockType = stockType;
    }

    public Integer getStockResource() {
        return stockResource;
    }

    public void setStockResource(Integer stockResource) {
        this.stockResource = stockResource;
    }

    public Integer getStockOrigin() {
        return stockOrigin;
    }

    public void setStockOrigin(Integer stockOrigin) {
        this.stockOrigin = stockOrigin;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public Date getStockTime() {
        return stockTime;
    }

    public void setStockTime(Date stockTime) {
        this.stockTime = stockTime;
    }

    public BigDecimal getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(BigDecimal totalCount) {
        this.totalCount = totalCount;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Long getUpdateId() {
        return updateId;
    }

    public void setUpdateId(Long updateId) {
        this.updateId = updateId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
