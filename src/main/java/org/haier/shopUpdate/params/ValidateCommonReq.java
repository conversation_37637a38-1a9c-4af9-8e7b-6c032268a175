package org.haier.shopUpdate.params;

import org.haier.shopUpdate.entity.goods.goodsRecord.GoodsOperParam;

import javax.validation.constraints.NotNull;

public class ValidateCommonReq extends GoodsOperParam {

    @NotNull(message = "请输入店铺编号")
    private Long shopUnique;
    /**
     * 员工id
     */
    private Integer staffId;

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public Integer getStaffId() {
        return staffId;
    }

    public void setStaffId(Integer staffId) {
        this.staffId = staffId;
    }
}
