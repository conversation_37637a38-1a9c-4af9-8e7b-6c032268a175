package org.haier.shopUpdate.params.goodsOnlineSetting;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @ClassName minSaleCountAddParams
 * <AUTHOR>
 * @Date 2024/5/15 15:56
 */

public class MinSaleCountAddParams implements Serializable {
    private Integer goodsId;
    private BigDecimal minSaleCount;

    public Integer getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Integer goodsId) {
        this.goodsId = goodsId;
    }

    public BigDecimal getMinSaleCount() {
        return minSaleCount;
    }

    public void setMinSaleCount(BigDecimal minSaleCount) {
        this.minSaleCount = minSaleCount;
    }
}
