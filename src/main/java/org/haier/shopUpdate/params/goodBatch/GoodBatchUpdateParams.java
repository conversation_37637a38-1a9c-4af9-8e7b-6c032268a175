package org.haier.shopUpdate.params.goodBatch;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @ClassName GoodBatchUpdateParams
 * <AUTHOR>
 * @Date 2024/5/17 17:34
 */

public class GoodBatchUpdateParams implements Serializable {
    private Long goodsBatchId;
    private BigDecimal goodsCount;
    private Long updateId;

    public Long getGoodsBatchId() {
        return goodsBatchId;
    }

    public void setGoodsBatchId(Long goodsBatchId) {
        this.goodsBatchId = goodsBatchId;
    }

    public BigDecimal getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(BigDecimal goodsCount) {
        this.goodsCount = goodsCount;
    }

    public Long getUpdateId() {
        return updateId;
    }

    public void setUpdateId(Long updateId) {
        this.updateId = updateId;
    }
}
