package org.haier.shopUpdate.params.goodBatch;

import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @ClassName GoodBatchListQueryParams
 * <AUTHOR>
 * @Date 2024/4/28 13:48
 */

public class GoodBatchQueryParams implements Serializable {
    /**
     * 商品条码
     */
    @NotBlank(message = "商品条码不能为空")
    private String goodsBarcode;
    /**
     * 店铺编码
     */
    @NotBlank(message = "店铺编码不能为空")
    private String shopUnique;

    private String batchUnique;

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public String getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(String shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getBatchUnique() {
        return batchUnique;
    }

    public void setBatchUnique(String batchUnique) {
        this.batchUnique = batchUnique;
    }
}
