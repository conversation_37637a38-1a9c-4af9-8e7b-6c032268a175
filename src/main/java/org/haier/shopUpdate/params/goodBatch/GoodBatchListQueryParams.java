package org.haier.shopUpdate.params.goodBatch;

import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @ClassName GoodBatchListQueryParams
 * <AUTHOR>
 * @Date 2024/4/28 13:48
 */

public class GoodBatchListQueryParams {
    /**
     * 商品条码
     */
    @NotBlank(message = "商品条码不能为空")
    private String goodsBarcode;
    /**
     * 店铺编码
     */
    @NotBlank(message = "店铺编码不能为空")
    private String shopUnique;
    private String batchUnique;
    /**
     * 出入库记录编号(查询出入库历史时传值)
     */
    private Integer stockId;
    /**
     * 出入库类型：1-入库；2-出库(查询出入库历史时传值)
     */
    private Integer stockType;

    @NotNull(message = "请输入页码数")
    @Min(value = 1,message = "页码数最小为1")
    private Integer pageIndex; //页码数

    @NotNull(message = "请输入每页条数")
    @Min(value = 1,message = "每页条数最小为1")
    private Integer pageSize; //每页条数

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public String getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(String shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getBatchUnique() {
        return batchUnique;
    }

    public void setBatchUnique(String batchUnique) {
        this.batchUnique = batchUnique;
    }

    public Integer getStockId() {
        return stockId;
    }

    public void setStockId(Integer stockId) {
        this.stockId = stockId;
    }

    public Integer getStockType() {
        return stockType;
    }

    public void setStockType(Integer stockType) {
        this.stockType = stockType;
    }

    public Integer getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Integer pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
