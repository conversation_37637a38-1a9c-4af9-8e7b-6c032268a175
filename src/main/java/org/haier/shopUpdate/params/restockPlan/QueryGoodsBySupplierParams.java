package org.haier.shopUpdate.params.restockPlan;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

public class QueryGoodsBySupplierParams {
    /**
     * 店铺编号
     */
    @NotNull(message = "请输入店铺编号")
    @Min(message="请输入店铺编号",value=1)
    private Long shopUnique;
    /**
     * 补货计划ID
     */
    @NotNull(message = "请输入计划ID")
    @Min(message="请输入计划ID",value=1)
    private Long restockPlanId;
    /**
     * 补货计划供货商ID
     */
    @NotNull(message = "请输入供货商ID")
    @Min(message="请输入供货商ID",value=1)
    private Long shopRestockplanPresentId;

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public Long getRestockPlanId() {
        return restockPlanId;
    }

    public void setRestockPlanId(Long restockPlanId) {
        this.restockPlanId = restockPlanId;
    }

    public Long getShopRestockplanPresentId() {
        return shopRestockplanPresentId;
    }

    public void setShopRestockplanPresentId(Long shopRestockplanPresentId) {
        this.shopRestockplanPresentId = shopRestockplanPresentId;
    }
}
