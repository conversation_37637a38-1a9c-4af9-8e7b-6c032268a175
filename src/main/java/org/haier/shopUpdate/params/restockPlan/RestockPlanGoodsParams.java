package org.haier.shopUpdate.params.restockPlan;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

public class RestockPlanGoodsParams {
    /**
     * 店铺编号
     */
    @NotNull(message = "请输入店铺编号")
    @Min(message="请输入店铺编号",value=1)
    private Long shopUnique;
    /**
     * 补货计划编号
     */
    @NotNull(message = "请输入补货计划ID")
    @Min(message="请输入补货计划ID",value=1)
    private Long restockPlanId;
    /**
     * 商品条码
     */
    private String goodsBarcode;
    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 页码数
     */
    @NotNull(message = "请输入页码数")
    @Min(value = 1,message = "页码数最小为1")
    private Integer pageIndex;
    /**
     * 每页条数
     */
    @NotNull(message = "请输入每页条数")
    @Min(value = 1,message = "每页条数最小为1")
    private Integer pageSize;

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public Long getRestockPlanId() {
        return restockPlanId;
    }

    public void setRestockPlanId(Long restockPlanId) {
        this.restockPlanId = restockPlanId;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public Integer getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Integer pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
