package org.haier.shopUpdate.params.restockPlan;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

public class AddRestockPlanParams {
    /**
     * 店铺编号
     */
    @NotNull(message = "请输入店铺编号")
    @Min(message="请输入店铺编号",value=1)
    private Long shopUnique;
    /**
     * 操作人姓名
     */
    @NotNull(message = "请输入创建人")
    private String loginUser;
    /**
     * 补货计划名称
     */
    private String restockPlanName;

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getLoginUser() {
        return loginUser;
    }

    public void setLoginUser(String loginUser) {
        this.loginUser = loginUser;
    }

    public String getRestockPlanName() {
        return restockPlanName;
    }

    public void setRestockPlanName(String restockPlanName) {
        this.restockPlanName = restockPlanName;
    }
}
