package org.haier.shopUpdate.params.restockPlan;

import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

public class UpdateStatusParams {
    /**
     * 补货计划ID
     */
    @NotNull(message = "请输入补货计划ID")
    @Min(message="请输入补货计划ID",value=1)
    private Long restockPlanId;
    /**
     * 店铺编号
     */
    @NotNull(message = "请输入店铺编号")
    @Min(message="请输入店铺编号",value=1)
    private Long shopUnique;
    /**
     * 计划状态:1：待生成；2：已生成；3.已取消
     */
    @NotNull(message = "请输入计划状态")
    @Range(min = 1,max = 3)
    private Integer planStatus;

    public Long getRestockPlanId() {
        return restockPlanId;
    }

    public void setRestockPlanId(Long restockPlanId) {
        this.restockPlanId = restockPlanId;
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public Integer getPlanStatus() {
        return planStatus;
    }

    public void setPlanStatus(Integer planStatus) {
        this.planStatus = planStatus;
    }
}
