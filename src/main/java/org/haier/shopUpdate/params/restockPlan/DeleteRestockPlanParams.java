package org.haier.shopUpdate.params.restockPlan;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

public class DeleteRestockPlanParams {
    /**
     * 店铺编号
     */
    @NotNull(message = "请输入店铺编号")
    @Min(message="请输入店铺编号",value=1)
    private Long shopUnique;
    /**
     * 补货计划ID
     */
    @NotNull(message = "请输入补货计划ID")
    @Min(message="请输入补货计划ID",value=1)
    private Long shopRestockplanId;

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public Long getShopRestockplanId() {
        return shopRestockplanId;
    }

    public void setShopRestockplanId(Long shopRestockplanId) {
        this.shopRestockplanId = shopRestockplanId;
    }
}
