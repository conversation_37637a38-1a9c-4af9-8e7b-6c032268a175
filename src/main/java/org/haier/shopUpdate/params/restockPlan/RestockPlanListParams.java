package org.haier.shopUpdate.params.restockPlan;

import org.haier.shopUpdate.params.ValidateCommonReq;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

public class RestockPlanListParams {
    /**
     * 补货计划创建开始时间:yyyy-MM-dd
     */
    private String startDate;
    /**
     * 补货计划创建结束时间:yyyy-MM-dd
     */
    private String endDate;
    /**
     * 补货计划状态:1待生成2已生成3已取消，默认全部
     */
    private Integer planStatus;
    /**
     * 店铺编号
     */
    @NotNull(message = "请输入店铺编号")
    private Long shopUnique;
    /**
     * 页码数
     */
    @NotNull(message = "请输入页码数")
    @Min(value = 1,message = "页码数最小为1")
    private Integer pageIndex;
    /**
     * 每页条数
     */
    @NotNull(message = "请输入每页条数")
    @Min(value = 1,message = "每页条数最小为1")
    private Integer pageSize;
    /**
     * 补货计划名称
     */
    private String planName;

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }


    public Integer getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Integer pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPlanStatus() {
        return planStatus;
    }

    public void setPlanStatus(Integer planStatus) {
        this.planStatus = planStatus;
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getPlanName() {
        return planName;
    }

    public void setPlanName(String planName) {
        this.planName = planName;
    }
}
