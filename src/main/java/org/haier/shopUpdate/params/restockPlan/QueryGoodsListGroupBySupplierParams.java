package org.haier.shopUpdate.params.restockPlan;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

public class QueryGoodsListGroupBySupplierParams {
    /**
     * 店铺编号
     */
    @NotNull(message = "请输入店铺编号")
    private Long shopUnique;
    /**
     * 补货计划编号
     */
    @NotNull(message = "请输入补货计划ID")
    @Min(message="请输入补货计划ID",value=1)
    private Long restockPlanId;

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public Long getRestockPlanId() {
        return restockPlanId;
    }

    public void setRestockPlanId(Long restockPlanId) {
        this.restockPlanId = restockPlanId;
    }
}
