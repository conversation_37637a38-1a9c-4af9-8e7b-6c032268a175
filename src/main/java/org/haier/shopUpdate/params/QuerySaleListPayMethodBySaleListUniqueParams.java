package org.haier.shopUpdate.params;

import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * @author: 王恩龙
 * @create: 2024-05-21 11:51
 * @Description:
 */
public class QuerySaleListPayMethodBySaleListUniqueParams implements Serializable {
    private static final long serialVersionUID = 1L;

    //订单编号
    @NotBlank(message = "订单编号不能为空")
    private String saleListUnique;

    //是否退款订单：1、收款订单；2、退款订单。由订单列表返回
    @NotBlank(message = "订单类型不能为空")
    private Integer orderType;

    public String getSaleListUnique() {
        return saleListUnique;
    }

    public void setSaleListUnique(String saleListUnique) {
        this.saleListUnique = saleListUnique;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }
}
