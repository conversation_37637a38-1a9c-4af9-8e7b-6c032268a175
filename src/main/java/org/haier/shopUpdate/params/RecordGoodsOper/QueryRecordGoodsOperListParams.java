package org.haier.shopUpdate.params.RecordGoodsOper;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

public class QueryRecordGoodsOperListParams {
    @NotNull(message = "请输入店铺编号")
    @Min(message="请输入店铺编号",value=1)
    private String shopUnique;//店铺编号
    @NotNull(message = "请输入商品条码")
    @Min(message="请输入商品条码",value=1)
    private String goodsBarcode;//商品条码
    private String startDate;
    private String endDate;
    private String userName;
    private Integer pageIndex; //页码数
    private Integer pageSize; //每页条数

    public String getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(String shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Integer getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Integer pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
