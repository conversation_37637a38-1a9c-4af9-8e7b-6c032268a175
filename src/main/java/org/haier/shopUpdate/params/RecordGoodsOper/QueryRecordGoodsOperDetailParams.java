package org.haier.shopUpdate.params.RecordGoodsOper;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

public class QueryRecordGoodsOperDetailParams {
    @NotNull(message = "请输入店铺编号")
    @Min(message="请输入店铺编号",value=1)
    private String shopUnique;//店铺编号
    @NotNull(message = "请输入商品条码")
    @Min(message="请输入商品条码",value=1)
    private String goodsBarcode;//商品条码
    @NotNull(message = "请输入操作记录ID")
    @Min(message="请输入操作记录ID",value=1)
    private String recordGoodsOperId;

    public String getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(String shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public String getRecordGoodsOperId() {
        return recordGoodsOperId;
    }

    public void setRecordGoodsOperId(String recordGoodsOperId) {
        this.recordGoodsOperId = recordGoodsOperId;
    }
}
