package org.haier.shopUpdate.params.payCenter;

import com.alibaba.fastjson.JSONObject;

import java.io.Serializable;

/**
 * @ClassName RefundParams
 * <AUTHOR>
 * @Date 2025/3/31 17:05
 */
public class RefundParams implements Serializable {
    //用户ID
    private String mchId;
    //用户ID地址
    private String clientIp;
    //商户订单号，唯一
    private String mchTradeNo;
    /**
     * 商户退款单号
     */
    private String mchRefundNo;
    //退款金额，单位分
    private Integer refundFee;
    //货币类型
    private String feeType;
    //自定义参数，原样返回
    private JSONObject customJson;
    //回调地址
    private String callBackUrl;
    private String remark;

    public String getMchId() {
        return mchId;
    }

    public void setMchId(String mchId) {
        this.mchId = mchId;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public String getMchTradeNo() {
        return mchTradeNo;
    }

    public void setMchTradeNo(String mchTradeNo) {
        this.mchTradeNo = mchTradeNo;
    }

    public String getMchRefundNo() {
        return mchRefundNo;
    }

    public void setMchRefundNo(String mchRefundNo) {
        this.mchRefundNo = mchRefundNo;
    }

    public Integer getRefundFee() {
        return refundFee;
    }

    public void setRefundFee(Integer refundFee) {
        this.refundFee = refundFee;
    }

    public String getFeeType() {
        return feeType;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    public JSONObject getCustomJson() {
        return customJson;
    }

    public void setCustomJson(JSONObject customJson) {
        this.customJson = customJson;
    }

    public String getCallBackUrl() {
        return callBackUrl;
    }

    public void setCallBackUrl(String callBackUrl) {
        this.callBackUrl = callBackUrl;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
