package org.haier.shopUpdate.params;

import java.io.Serializable;

/**
 * @author: 王恩龙
 * @create: 2024-05-20 14:47
 * @Description:
 */
public class QuerySaleListByPayMethodParams extends QueryStatisticsByShopParams implements Serializable {
    //分页
    private Integer pageNum;
    private Integer pageSize;
    private Integer payMethod;
    //订单类型：1、收款单；2、退款单
    private Integer orderType;
    //订单归属店铺类型：1-11：普通订单；12、餐饮订单
    private Integer shopType;

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(Integer payMethod) {
        this.payMethod = payMethod;
    }

    public Integer getStartNum() {
        return (pageNum - 1) * pageSize;
    }

    public Integer getOrderType() {
        return orderType;
    }
    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }
    public Integer getShopType() {
        return shopType;
    }
    public void setShopType(Integer shopType) {
        this.shopType = shopType;
    }

    @Override
    public String toString() {
        return "QuerySaleListByPayMethodParams{" +
                "\"pageNum\":" + pageNum +
                ", \"pageSize\":" + pageSize +
                ", \"payMethod\":" + payMethod +
                ", \"orderType\":" + orderType +
                ", \"shopType\":" + shopType +
                ",\"shopUnique\":\"" + getShopUnique() + "\"" +
                ",\"startTime\":\"" + getStartTime() + "\"" +
                ",\"endTime\":\"" + getEndTime() + "\"" +
                ",\"staffId\":\"" + getStaffId() + "\"" +
                ",\"saleType\":\"" + getSaleType() + "\"" +
                "}";
    }
}
