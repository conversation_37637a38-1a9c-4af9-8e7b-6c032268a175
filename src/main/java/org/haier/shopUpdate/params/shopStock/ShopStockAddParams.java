package org.haier.shopUpdate.params.shopStock;

import org.apache.ibatis.annotations.Param;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName ShopStockAddParams
 * <AUTHOR>
 * @Date 2024/5/6 9:30
 */

public class ShopStockAddParams implements Serializable {
    private String goodsBarcode;
    private BigDecimal goodsCount;
    private BigDecimal stockCount;
    private Integer stockType;
    private Date stockTime;
    private String shopUnique;
    private Integer stockResource;
    private String listUnique;
    private BigDecimal stockPrice;
    private Integer stockOrigin;
    private Long staffId;
    private Long failId;
    private Date goodsProd;
    private Date goodsExp;
    private Integer goodsLife;
    private Integer checkTimeFlg;

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public BigDecimal getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(BigDecimal goodsCount) {
        this.goodsCount = goodsCount;
    }

    public BigDecimal getStockCount() {
        return stockCount;
    }

    public void setStockCount(BigDecimal stockCount) {
        this.stockCount = stockCount;
    }

    public Integer getStockType() {
        return stockType;
    }

    public void setStockType(Integer stockType) {
        this.stockType = stockType;
    }

    public Date getStockTime() {
        return stockTime;
    }

    public void setStockTime(Date stockTime) {
        this.stockTime = stockTime;
    }

    public String getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(String shopUnique) {
        this.shopUnique = shopUnique;
    }

    public Integer getStockResource() {
        return stockResource;
    }

    public void setStockResource(Integer stockResource) {
        this.stockResource = stockResource;
    }

    public String getListUnique() {
        return listUnique;
    }

    public void setListUnique(String listUnique) {
        this.listUnique = listUnique;
    }

    public BigDecimal getStockPrice() {
        return stockPrice;
    }

    public void setStockPrice(BigDecimal stockPrice) {
        this.stockPrice = stockPrice;
    }

    public Integer getStockOrigin() {
        return stockOrigin;
    }

    public void setStockOrigin(Integer stockOrigin) {
        this.stockOrigin = stockOrigin;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public Long getFailId() {
        return failId;
    }

    public void setFailId(Long failId) {
        this.failId = failId;
    }

    public Date getGoodsProd() {
        return goodsProd;
    }

    public void setGoodsProd(Date goodsProd) {
        this.goodsProd = goodsProd;
    }

    public Date getGoodsExp() {
        return goodsExp;
    }

    public void setGoodsExp(Date goodsExp) {
        this.goodsExp = goodsExp;
    }

    public Integer getGoodsLife() {
        return goodsLife;
    }

    public void setGoodsLife(Integer goodsLife) {
        this.goodsLife = goodsLife;
    }

    public Integer getCheckTimeFlg() {
        return checkTimeFlg;
    }

    public void setCheckTimeFlg(Integer checkTimeFlg) {
        this.checkTimeFlg = checkTimeFlg;
    }
}
