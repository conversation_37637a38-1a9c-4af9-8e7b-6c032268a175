package org.haier.shopUpdate.params.shopSupBill;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

public class ModifyPaymentOrderParams extends AddPaymentOrderParams{
    /**
     * 购销单付款凭证编号
     */
    @NotNull(message = "请输入购销单付款凭证编号")
    @Min(message="请输入购销单付款凭证编号",value=1)
    private Long paymentId;
    /**
     * 删除标识
     */
    private Integer delFlag;
    /**
     * 更新标识
     */
    @NotNull(message = "请输入更新标识")
    @Min(message="请输入更新标识",value=1)
    private Integer modifyType;//1修改2删除
    public Long getPaymentId() {
        return paymentId;
    }

    public void setPaymentId(Long paymentId) {
        this.paymentId = paymentId;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Integer getModifyType() {
        return modifyType;
    }

    public void setModifyType(Integer modifyType) {
        this.modifyType = modifyType;
    }
}
