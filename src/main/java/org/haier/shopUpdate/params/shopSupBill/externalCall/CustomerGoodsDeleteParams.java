package org.haier.shopUpdate.params.shopSupBill.externalCall;

import java.io.Serializable;

/**
 * 店铺商品绑定供货商
 *
 * @ClassName CustomerGoodsAddParams
 * <AUTHOR>
 * @Date 2023/9/11 14:02
 **/
public class CustomerGoodsDeleteParams implements Serializable {
    private static final long serialVersionUID = 7704180427172516602L;

    /**
     * 供应商编码
     */
    private String supplierUnique;

    /**
     * 客户编码
     */
    private String customerUnique;

    /**
     * 商品条码
     */
    private String goodsBarcode;

    public String getSupplierUnique() {
        return supplierUnique;
    }

    public void setSupplierUnique(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }

    public String getCustomerUnique() {
        return customerUnique;
    }

    public void setCustomerUnique(String customerUnique) {
        this.customerUnique = customerUnique;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }
}
