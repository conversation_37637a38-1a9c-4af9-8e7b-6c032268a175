package org.haier.shopUpdate.params.shopSupBill.externalCall;

import java.io.Serializable;

/**
 * 供货商客户绑定状态参数
 *
 * @ClassName SupplierCustomerBindParams
 * <AUTHOR>
 * @Date 2023/9/11 17:13
 **/

public class SupplierCustomerAuditStatusParams implements Serializable {
    /**
     * 供货商编码
     */
    private String supplierUnique;

    /**
     * 商户编码
     */
    private String customerUnique;

    /**
     * 审核状态
     */
    private Integer auditStatus;

    public String getSupplierUnique() {
        return supplierUnique;
    }

    public void setSupplierUnique(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }

    public String getCustomerUnique() {
        return customerUnique;
    }

    public void setCustomerUnique(String customerUnique) {
        this.customerUnique = customerUnique;
    }

    public Integer getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }
}
