package org.haier.shopUpdate.params.shopSupBill.supExternalCall;

import java.math.BigDecimal;
import java.util.List;

/**
 * 客户更新购销单数量
 *
 * @ClassName BillSyncParams
 * <AUTHOR>
 * @Date 2023/9/13 11:04
 **/
public class BillSyncParams {

    /**
     * 供货商编码
     */
    private String supplierUnique;
    /**
     * 客户编码
     */
    private String customerUnique;
    /**
     * 购销单单号
     */
    private String billNo;

    public String getSupplierUnique() {
        return supplierUnique;
    }

    public void setSupplierUnique(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }

    public String getCustomerUnique() {
        return customerUnique;
    }

    public void setCustomerUnique(String customerUnique) {
        this.customerUnique = customerUnique;
    }

    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    public List<BillDetailSyncParams> getDetailList() {
        return detailList;
    }

    public void setDetailList(List<BillDetailSyncParams> detailList) {
        this.detailList = detailList;
    }

    /**
     * 商品数量列表
     */
    private List<BillDetailSyncParams> detailList;


}
