package org.haier.shopUpdate.params.shopSupBill.externalCall;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

public class UpdateSupBillStatusParams {
    /**
     * 供货商编码
     */
    @NotNull(message = "供货商编码不能为空")
    private String supplierUnique;
    /**
     *店铺编号
     */
    @NotNull(message = "客户编码不能为空")
    private String customerUnique;
    /**
     *购销单编码
     */
    @NotNull(message = "购销单编码不能为空")
    private String billNo;
    /**
     *购销单状态
     */
    @NotNull(message = "购销单状态不能为空")
    @Min(message="请输入购销单状态",value=1)
    private Integer status;


    public String getSupplierUnique() {
        return supplierUnique;
    }

    public void setSupplierUnique(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }

    public String getCustomerUnique() {
        return customerUnique;
    }

    public void setCustomerUnique(String customerUnique) {
        this.customerUnique = customerUnique;
    }

    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
