package org.haier.shopUpdate.params.shopSupBill;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

public class CancelCheckGoodsParams extends QueryBillGoodsListParams{
    /**
     *购销单明细ID
     */
    @NotNull(message = "请输入购销单明细ID")
    @Min(message="请输入购销单明细ID",value=1)
    private Long detailId;
    /**
     *供应商编号
     */
    @NotNull(message = "请输入供应商编号")
    private String supplierUnique;
    /**
     *员工编号
     */
    @NotNull(message = "请输入员工编号")
    private Long createId;
    /**
     *员工姓名
     */
    @NotNull(message = "请输入员工姓名")
    private String createBy;

    public Long getDetailId() {
        return detailId;
    }

    public void setDetailId(Long detailId) {
        this.detailId = detailId;
    }

    public String getSupplierUnique() {
        return supplierUnique;
    }

    public void setSupplierUnique(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }

    public Long getCreateId() {
        return createId;
    }

    public void setCreateId(Long createId) {
        this.createId = createId;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
}
