package org.haier.shopUpdate.params.shopSupBill;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

public class UpdateBillStatusParams {
    /**
     * 购销单编号
     */
    @NotNull(message = "请输入购销单编号")
    @Min(message="请输入购销单编号",value=1)
    private Long id;
    /**
     *状态
     */
    @NotNull(message = "请输入状态")
    @Min(message="请输入状态",value=1)
    private Integer status;
    /**
     *店铺编号
     */
    @NotNull(message = "请输入店铺编号")
    @Min(message="请输入店铺编号",value=1)
    private Long shopUnique;

    /**
     *员工编号
     */
    @NotNull(message = "请输入员工编号")
    private Long createId;
    /**
     *员工姓名
     */
    @NotNull(message = "请输入员工姓名")
    private String createBy;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public Long getCreateId() {
        return createId;
    }

    public void setCreateId(Long createId) {
        this.createId = createId;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
}
