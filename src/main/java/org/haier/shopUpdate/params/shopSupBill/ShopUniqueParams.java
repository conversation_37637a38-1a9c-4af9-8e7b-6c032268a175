package org.haier.shopUpdate.params.shopSupBill;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

public class ShopUniqueParams {
    /**
     * 店铺编号
     */
    @NotNull(message = "请输入店铺编号")
    @Min(message="请输入店铺编号",value=1)
    private Long shopUnique;
    public Long getShopUnique() {
        return shopUnique;
    }
    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

}
