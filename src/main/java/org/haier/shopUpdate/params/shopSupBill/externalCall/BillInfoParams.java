package org.haier.shopUpdate.params.shopSupBill.externalCall;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

public class BillInfoParams {
    /**
     * 供应商编号
     */
    @NotNull(message = "请输入供应商编号")
    private String supplierUnique;
    /**
     * 店铺编号
     */
    @NotNull(message = "请输入店铺编号")
    private String customerUnique;
    /**
     * 购销单编号
     */
    @NotNull(message = "请输入购销单编号")
    private String billNo;
    /**
     * 商品种类数量
     */
    @NotNull(message = "请输入商品种类数量")
    private Integer categoryCount;
    /**
     * 商品总数量
     */
    @NotNull(message = "请输入商品总数量")
    private BigDecimal totalCount;
    /**
     * 总金额
     */
    @NotNull(message = "请输入总金额")
    private BigDecimal totalMoney;
    /**
     * 购销单状态
     */
    @NotNull(message = "请输入购销单状态")
    private Integer status;
    /**
     * 单据凭证列表
     */
    private List<String> imageUrlList;

    /**
     * 备注
     */
    private String remark;
    /**
     * 购销单明细
     */
    @NotNull(message = "请输入商品明细")
    private List<BillDetailInfoParams> detail;

    public String getSupplierUnique() {
        return supplierUnique;
    }

    public void setSupplierUnique(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }

    public String getCustomerUnique() {
        return customerUnique;
    }

    public void setCustomerUnique(String customerUnique) {
        this.customerUnique = customerUnique;
    }

    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    public Integer getCategoryCount() {
        return categoryCount;
    }

    public void setCategoryCount(Integer categoryCount) {
        this.categoryCount = categoryCount;
    }

    public BigDecimal getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(BigDecimal totalCount) {
        this.totalCount = totalCount;
    }

    public BigDecimal getTotalMoney() {
        return totalMoney;
    }

    public void setTotalMoney(BigDecimal totalMoney) {
        this.totalMoney = totalMoney;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public List<String> getImageUrlList() {
        return imageUrlList;
    }

    public void setImageUrlList(List<String> imageUrlList) {
        this.imageUrlList = imageUrlList;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public List<BillDetailInfoParams> getDetail() {
        return detail;
    }

    public void setDetail(List<BillDetailInfoParams> detail) {
        this.detail = detail;
    }

}
