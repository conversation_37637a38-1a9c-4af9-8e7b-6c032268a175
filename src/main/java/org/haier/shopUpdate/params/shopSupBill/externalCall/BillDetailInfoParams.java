package org.haier.shopUpdate.params.shopSupBill.externalCall;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

public class BillDetailInfoParams{
    /**
     * 采购单编号
     */
    private String orderNo;
    /**
     * 商品名称
     */
    @NotNull(message = "请输入商品名称")
    private String goodsName;
    /**
     *商品条码
     */
    @NotNull(message = "请输入商品条码")
    private String goodsBarcode;
    /**
     *商品单位
     */
    private String goodsUnit;
    /**
     *商品单价
     */
    @NotNull(message = "请输入商品单价")
    private String goodsPurchasePrice;
    /**
     *商品单位
     */
    @NotNull(message = "请输入商品计价单位")
    private String goodsPurchaseUnit;

    @NotNull(message = "请输入配送数量")
    private BigDecimal goodsPurchaseCount;
    /**
     *商品配送数量
     */
    private BigDecimal goodsCount;
    /**
     *商品图片路径
     */
    private String goodsImageUrl;
    /**
     *建议售价
     */
    @NotNull(message = "请输入建议售价")
    private BigDecimal goodsSalePrice;
    /**
     *生产日期
     */
    @NotNull(message = "请输入生产日期")
    private String goodsProduceDate;
    /**
     *商品金额小计
     */
    private BigDecimal subtotalMoney;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public String getGoodsPurchaseUnit() {
        return goodsPurchaseUnit;
    }

    public void setGoodsPurchaseUnit(String goodsPurchaseUnit) {
        this.goodsPurchaseUnit = goodsPurchaseUnit;
    }

    public BigDecimal getGoodsPurchaseCount() {
        return goodsPurchaseCount;
    }

    public void setGoodsPurchaseCount(BigDecimal goodsPurchaseCount) {
        this.goodsPurchaseCount = goodsPurchaseCount;
    }

    public String getGoodsUnit() {
        return goodsUnit;
    }

    public void setGoodsUnit(String goodsUnit) {
        this.goodsUnit = goodsUnit;
    }

    public String getGoodsPurchasePrice() {
        return goodsPurchasePrice;
    }

    public void setGoodsPurchasePrice(String goodsPurchasePrice) {
        this.goodsPurchasePrice = goodsPurchasePrice;
    }

    public BigDecimal getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(BigDecimal goodsCount) {
        this.goodsCount = goodsCount;
    }

    public String getGoodsImageUrl() {
        return goodsImageUrl;
    }

    public void setGoodsImageUrl(String goodsImageUrl) {
        this.goodsImageUrl = goodsImageUrl;
    }

    public BigDecimal getGoodsSalePrice() {
        return goodsSalePrice;
    }

    public void setGoodsSalePrice(BigDecimal goodsSalePrice) {
        this.goodsSalePrice = goodsSalePrice;
    }

    public String getGoodsProduceDate() {
        return goodsProduceDate;
    }

    public void setGoodsProduceDate(String goodsProduceDate) {
        this.goodsProduceDate = goodsProduceDate;
    }

    public BigDecimal getSubtotalMoney() {
        return subtotalMoney;
    }

    public void setSubtotalMoney(BigDecimal subtotalMoney) {
        this.subtotalMoney = subtotalMoney;
    }
}