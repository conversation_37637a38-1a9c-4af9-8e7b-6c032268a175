package org.haier.shopUpdate.params.shopSupBill.supExternalCall;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 补货单明细参数
 *
 * @ClassName OrderDetailAddParams
 * <AUTHOR>
 * @Date 2023/9/11 11:40
 **/
public class OrderDetailAddParams {

    /**
     * 订单编码
     */
    @NotNull(message = "订单编码不能为空")
    private String orderNo;

    /**
     * 商品条码
     */
    @NotNull(message = "商品条码不能为空")
    private String goodsBarcode;

    /**
     * 商品数量
     */
    @NotNull(message = "商品数量不能为空")
    @DecimalMin(value = "0", message = "商品数量不能小于等于0")
    private BigDecimal goodsCount;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public BigDecimal getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(BigDecimal goodsCount) {
        this.goodsCount = goodsCount;
    }
}
