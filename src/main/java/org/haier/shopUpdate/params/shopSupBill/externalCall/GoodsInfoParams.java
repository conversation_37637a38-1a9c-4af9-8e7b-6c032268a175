package org.haier.shopUpdate.params.shopSupBill.externalCall;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

public class GoodsInfoParams {
    /**
     * 供应商编号
     */
    @NotNull(message = "请输入供应商编号")
    private String supplierUnique;
    /**
     * 店铺编号
     */
    @NotNull(message = "请输入店铺编号")
    private String customerUnique;

    /**
     * 商品详情
     */
    private List<GoodsDetailInfoParams> goodsList;

    public String getSupplierUnique() {
        return supplierUnique;
    }

    public void setSupplierUnique(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }

    public String getCustomerUnique() {
        return customerUnique;
    }

    public void setCustomerUnique(String customerUnique) {
        this.customerUnique = customerUnique;
    }

    public List<GoodsDetailInfoParams> getGoodsList() {
        return goodsList;
    }

    public void setGoodsList(List<GoodsDetailInfoParams> goodsList) {
        this.goodsList = goodsList;
    }
}
