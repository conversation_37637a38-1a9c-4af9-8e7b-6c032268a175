package org.haier.shopUpdate.params.shopSupBill.externalCall;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

public class GoodsDetailInfoParams {
    /**
     * 商品条码
     */
    @NotNull(message = "请输入商品条码")
    private String goodsBarcode;
    /**
     * 商品名称
     */
    @NotNull(message = "请输入商品名称")
    private String goodsName;
    /**
     * 商品别名
     */
    private String goodsAlias;
    /**
     * 商品进价
     */
    @NotNull(message = "请输入商品进价")
    private BigDecimal goodsInPrice;
    /**
     * 商品售价
     */
    @NotNull(message = "请输入商品售价")
    private BigDecimal goodsSalePrice;
    /**
     * 保质天数
     */
    @NotNull(message = "请输入保质天数")
    private Integer expirationDate;
    /**
     * 商品生产地
     */
    private String goodsAddress;
    /**
     * 商品图片路径
     */
    private String goodsImageUrl;
    /**
     * 商品规格
     */
    private String goodsStandard;
    /**
     * 商品计价单位
     */
    private String goodsUnit;
    /**
     * 商品生产日期
     */
    @NotNull(message = "请输入生产日期")
    private String goodsProduceDate;
    /**
     * 称重商品类型：0、按件；1、按重量
     */
    @NotNull(message = "请输入称重商品类型")
    private Integer goodsChengType;

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsAlias() {
        return goodsAlias;
    }

    public void setGoodsAlias(String goodsAlias) {
        this.goodsAlias = goodsAlias;
    }

    public BigDecimal getGoodsInPrice() {
        return goodsInPrice;
    }

    public void setGoodsInPrice(BigDecimal goodsInPrice) {
        this.goodsInPrice = goodsInPrice;
    }

    public BigDecimal getGoodsSalePrice() {
        return goodsSalePrice;
    }

    public void setGoodsSalePrice(BigDecimal goodsSalePrice) {
        this.goodsSalePrice = goodsSalePrice;
    }

    public Integer getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(Integer expirationDate) {
        this.expirationDate = expirationDate;
    }

    public String getGoodsAddress() {
        return goodsAddress;
    }

    public void setGoodsAddress(String goodsAddress) {
        this.goodsAddress = goodsAddress;
    }

    public String getGoodsImageUrl() {
        return goodsImageUrl;
    }

    public void setGoodsImageUrl(String goodsImageUrl) {
        this.goodsImageUrl = goodsImageUrl;
    }

    public String getGoodsStandard() {
        return goodsStandard;
    }

    public void setGoodsStandard(String goodsStandard) {
        this.goodsStandard = goodsStandard;
    }

    public String getGoodsUnit() {
        return goodsUnit;
    }

    public void setGoodsUnit(String goodsUnit) {
        this.goodsUnit = goodsUnit;
    }

    public String getGoodsProduceDate() {
        return goodsProduceDate;
    }

    public void setGoodsProduceDate(String goodsProduceDate) {
        this.goodsProduceDate = goodsProduceDate;
    }

    public Integer getGoodsChengType() {
        return goodsChengType;
    }

    public void setGoodsChengType(Integer goodsChengType) {
        this.goodsChengType = goodsChengType;
    }
}