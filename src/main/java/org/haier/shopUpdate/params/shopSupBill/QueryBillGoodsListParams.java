package org.haier.shopUpdate.params.shopSupBill;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

public class QueryBillGoodsListParams extends ShopUniqueParams{
    /**
     * 购销单ID
     */
    @NotNull(message = "请输入购销单ID")
    @Min(message="请输入购销单ID",value=1)
    private Long id;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
}
