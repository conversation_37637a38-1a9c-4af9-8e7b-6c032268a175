package org.haier.shopUpdate.params.shopSupBill.externalCall;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

public class ShopGoodsInfoParams {
    /**
     * 供应商编号
     */
    @NotNull(message = "请输入供应商编号")
    private String supplierUnique;
    /**
     * 店铺列表
     */
    @NotNull(message = "请输入店铺编号")
    private List<String> shopList;

    /**
     * 商品详情
     */
    private List<GoodsDetailInfoParams> goodsList;

    public List<GoodsDetailInfoParams> getGoodsList() {
        return goodsList;
    }

    public List<String> getShopList() {
        return shopList;
    }

    public void setShopList(List<String> shopList) {
        this.shopList = shopList;
    }

    public void setGoodsList(List<GoodsDetailInfoParams> goodsList) {
        this.goodsList = goodsList;
    }

    public String getSupplierUnique() {
        return supplierUnique;
    }

    public void setSupplierUnique(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }

}
