package org.haier.shopUpdate.params;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class CreateOrderParams implements Serializable {
    //订单编号
    private String sale_list_unique;
    //配送方式
    private String delivery_type;
    //商品重量
    private String goods_weight;
    //骑手ID
    private String shop_courier_id;
    //骑手名称
    private String courier_name;
    //骑手手机号
    private String courier_phone;
    //订单核实信息
    private BigDecimal return_price;
    //商品列表
    private String goodsList;
    private String sale_list_cashier;
}
