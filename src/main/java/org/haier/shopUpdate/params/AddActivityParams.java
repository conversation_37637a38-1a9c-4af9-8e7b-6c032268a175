package org.haier.shopUpdate.params;

public class AddActivityParams {
    /**
     * 店铺标识
     */
    private Long shop_unique;
    /**
     * 促销名称
     */
    private String promotion_activity_name;
    /**
     * 开始时间
     */
    private String startDate;
    /**
     * 结束时间
     */
    private String endDate;
    /**
     * 是否参与订单促销
     */
    private Integer order_activity;
    /**
     * 活动范围
     */
    private Integer activity_range;
    /**
     * 优惠详情
     */
    private String detailJson;

    public Long getShop_unique() {
        return shop_unique;
    }

    public void setShop_unique(Long shop_unique) {
        this.shop_unique = shop_unique;
    }

    public String getPromotion_activity_name() {
        return promotion_activity_name;
    }

    public void setPromotion_activity_name(String promotion_activity_name) {
        this.promotion_activity_name = promotion_activity_name;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public Integer getOrder_activity() {
        return order_activity;
    }

    public void setOrder_activity(Integer order_activity) {
        this.order_activity = order_activity;
    }

    public Integer getActivity_range() {
        return activity_range;
    }

    public void setActivity_range(Integer activity_range) {
        this.activity_range = activity_range;
    }

    public String getDetailJson() {
        return detailJson;
    }

    public void setDetailJson(String detailJson) {
        this.detailJson = detailJson;
    }
}
