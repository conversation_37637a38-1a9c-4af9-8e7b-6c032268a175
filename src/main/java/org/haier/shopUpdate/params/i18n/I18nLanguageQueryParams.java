package org.haier.shopUpdate.params.i18n;

import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @ClassName I18nQueryParams
 * <AUTHOR>
 * @Date 2024/12/19 10:01
 */

public class I18nLanguageQueryParams implements Serializable {
    /**
     * 语言：中文（chinese），英文（english），哈萨克语（kazakh），马来语（malay），泰语（hai）
     */
    @NotBlank(message = "语言不能为空")
    private String language;
    /**
     * 端类型：安卓收银机（1），windows收银机（2），安卓APP（3），苹果APP（4），后台（5），小程序（6），H5（7）
     */
    @NotNull(message = "端类型不能为空")
    private Integer type;

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}
