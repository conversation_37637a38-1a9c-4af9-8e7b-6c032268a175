package org.haier.shopUpdate.params.inventoryTask;

import org.haier.shopUpdate.params.ValidateCommonReq;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

public class InventoryGoodsRecordParams extends ValidateCommonReq {

    @NotBlank(message = "请输入商品条码")
    private String goodsBarcode; //商品条码

    @NotNull(message = "请输入员工编号")
    private Integer staffId; //员工编号

    @NotNull(message = "请输入页码数")
    @Min(value = 1,message = "页码数最小为1")
    private Integer pageIndex; //页码数

    @NotNull(message = "请输入每页条数")
    @Min(value = 1,message = "每页条数最小为1")
    private Integer pageSize; //每页条数

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public Integer getStaffId() {
        return staffId;
    }

    public void setStaffId(Integer staffId) {
        this.staffId = staffId;
    }

    public Integer getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Integer pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
