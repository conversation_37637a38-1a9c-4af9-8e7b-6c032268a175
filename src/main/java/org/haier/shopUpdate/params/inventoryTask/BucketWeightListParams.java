package org.haier.shopUpdate.params.inventoryTask;

import org.haier.shopUpdate.params.ValidateCommonReq;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

public class BucketWeightListParams extends ValidateCommonReq {

    @NotNull(message = "请输入页码数")
    @Min(value = 1,message = "页码数最小为1")
    private Integer pageIndex; //页码数

    @NotNull(message = "请输入每页条数")
    @Min(value = 1,message = "每页条数最小为1")
    private Integer pageSize; //每页条数

    public Integer getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Integer pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
