package org.haier.shopUpdate.params.inventoryTask;

import org.haier.shopUpdate.params.ValidateCommonReq;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

public class AddTaskDetailParams extends ValidateCommonReq {

    @NotNull(message = "请输入登录员工编号")
    private Integer staffId; //登录员工编号

    @NotNull(message = "请输入任务编号")
    private Long taskId; //任务编号

    @NotBlank(message = "请输入商品条码")
    private String goodsBarcode; //商品条码

    @NotNull(message = "请输入盘点总数量")
    @DecimalMin(value = "0", message = "最小数量为0")
    private BigDecimal inventoryCountTotal; //盘点总数量 商品+筐重量

    @NotNull(message = "请输入筐重量")
    @DecimalMin(value = "0", message = "最小重量为0")
    private BigDecimal bucketWeight;


    private String goodsPosition;

    private String remarks; //备注

    public Integer getStaffId() {
        return staffId;
    }

    public void setStaffId(Integer staffId) {
        this.staffId = staffId;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public BigDecimal getInventoryCountTotal() {
        return inventoryCountTotal;
    }

    public void setInventoryCountTotal(BigDecimal inventoryCountTotal) {
        this.inventoryCountTotal = inventoryCountTotal;
    }

    public BigDecimal getBucketWeight() {
        return bucketWeight;
    }

    public void setBucketWeight(BigDecimal bucketWeight) {
        this.bucketWeight = bucketWeight;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getGoodsPosition() {
        return goodsPosition;
    }

    public void setGoodsPosition(String goodsPosition) {
        this.goodsPosition = goodsPosition;
    }
}
