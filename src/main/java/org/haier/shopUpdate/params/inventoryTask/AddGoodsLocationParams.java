package org.haier.shopUpdate.params.inventoryTask;

import org.haier.shopUpdate.params.ValidateCommonReq;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;

public class AddGoodsLocationParams extends ValidateCommonReq {

    @NotNull(message = "请输入登录员工编号")
    private Integer staffId; //登录员工编号

    @NotBlank(message = "请输入货位")
    private String locationName; //货位

    public Integer getStaffId() {
        return staffId;
    }

    public void setStaffId(Integer staffId) {
        this.staffId = staffId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }
}
