package org.haier.shopUpdate.params.inventoryTask;

import javax.validation.constraints.NotNull;

public class UpdateGoodsLocationParams extends AddGoodsLocationParams{

    @NotNull(message = "请输入货位编号")
    private Long locationId; //货位编号

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }
}
