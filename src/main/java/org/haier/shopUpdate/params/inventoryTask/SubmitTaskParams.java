package org.haier.shopUpdate.params.inventoryTask;

import org.haier.shopUpdate.params.ValidateCommonReq;

import javax.validation.constraints.NotNull;

public class SubmitTaskParams extends ValidateCommonReq {

    @NotNull(message = "请输入登录员工编号")
    private Integer staffId; //登录员工编号

    @NotNull(message = "请输入任务编号")
    private Long taskId; //任务编号

    public Integer getStaffId() {
        return staffId;
    }

    public void setStaffId(Integer staffId) {
        this.staffId = staffId;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }
}
