package org.haier.shopUpdate.params.inventoryTask;

import javax.validation.constraints.NotNull;

public class UpdateTaskDetailParams extends AddTaskDetailParams{

    @NotNull(message = "请输入任务详情编号")
    private Long taskDetailId; //详情id

    private String goodsPosition;

    public Long getTaskDetailId() {
        return taskDetailId;
    }

    public void setTaskDetailId(Long taskDetailId) {
        this.taskDetailId = taskDetailId;
    }

    @Override
    public String getGoodsPosition() {
        return goodsPosition;
    }

    @Override
    public void setGoodsPosition(String goodsPosition) {
        this.goodsPosition = goodsPosition;
    }
}
