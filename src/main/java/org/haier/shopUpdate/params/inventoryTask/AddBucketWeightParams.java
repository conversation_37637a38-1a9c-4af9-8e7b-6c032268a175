package org.haier.shopUpdate.params.inventoryTask;

import org.haier.shopUpdate.params.ValidateCommonReq;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

public class AddBucketWeightParams extends ValidateCommonReq {

    @NotNull(message = "请输入登录员工编号")
    private Integer staffId; //登录员工编号

    @NotNull(message = "请输入筐重量")
    @DecimalMin(value = "0",message = "请输入正确的筐重量")
    private BigDecimal bucketWeight;

    public Integer getStaffId() {
        return staffId;
    }

    public void setStaffId(Integer staffId) {
        this.staffId = staffId;
    }

    public BigDecimal getBucketWeight() {
        return bucketWeight;
    }

    public void setBucketWeight(BigDecimal bucketWeight) {
        this.bucketWeight = bucketWeight;
    }
}
