package org.haier.shopUpdate.params.inventoryTask;

import org.haier.shopUpdate.params.ValidateCommonReq;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

public class TaskPreviewParams extends ValidateCommonReq {

    @NotNull(message = "请输入登录员工编号")
    private Integer staffId; //登录员工编号

    @NotNull(message = "请输入任务编号")
    private Long taskId; //任务编号

    @NotNull(message = "请输入页码数")
    @Min(value = 1,message = "页码数最小为1")
    private Integer pageIndex; //页码数

    @NotNull(message = "请输入每页条数")
    @Min(value = 1,message = "每页条数最小为1")
    private Integer pageSize; //每页条数

    private String searchKey; //商品名称、条码模糊查询

    private String from; //来源 DeviceTypEnum

    public Integer getStaffId() {
        return staffId;
    }

    public void setStaffId(Integer staffId) {
        this.staffId = staffId;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Integer getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Integer pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getFrom() {
        return from;
    }

    public void setFrom(String from) {
        this.from = from;
    }

    public String getSearchKey() {
        return searchKey;
    }

    public void setSearchKey(String searchKey) {
        this.searchKey = searchKey;
    }
}
