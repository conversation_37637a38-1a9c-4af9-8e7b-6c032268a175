package org.haier.shopUpdate.params.inventoryTask;

import org.haier.shopUpdate.params.ValidateCommonReq;

import javax.validation.constraints.NotNull;

public class DeleteGoodsLocationParams extends ValidateCommonReq {

    @NotNull(message = "请输入货位编号")
    private Long locationId; //货位编号

    @NotNull(message = "请输入登录员工编号")
    private Integer staffId; //登录员工编号

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public Integer getStaffId() {
        return staffId;
    }

    public void setStaffId(Integer staffId) {
        this.staffId = staffId;
    }
}
