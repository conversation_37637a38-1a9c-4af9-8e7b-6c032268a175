package org.haier.shopUpdate.params.goodsSaleBatch;

import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * @ClassName GoodSaleBatchByListUniqueQueryParams
 * <AUTHOR>
 * @Date 2024/4/28 13:48
 */

public class GoodSaleBatchByListUniqueQueryParams implements Serializable {
    /**
     * 商品条码
     */
    @NotBlank(message = "商品条码不能为空")
    private String goodsBarcode;
    /**
     * 店铺编码
     */
    @NotBlank(message = "店铺编码不能为空")
    private String shopUnique;

    private String listUnique;

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public String getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(String shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getListUnique() {
        return listUnique;
    }

    public void setListUnique(String listUnique) {
        this.listUnique = listUnique;
    }
}
