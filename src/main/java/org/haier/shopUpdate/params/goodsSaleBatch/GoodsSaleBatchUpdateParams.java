package org.haier.shopUpdate.params.goodsSaleBatch;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @ClassName GoodsSaleBatchUpdateParams
 * <AUTHOR>
 * @Date 2024/5/17 17:28
 */

public class GoodsSaleBatchUpdateParams implements Serializable {
    private Long goodsSaleBatchId;
    private BigDecimal goodsOutCount;
    private Long updateId;

    public Long getGoodsSaleBatchId() {
        return goodsSaleBatchId;
    }

    public void setGoodsSaleBatchId(Long goodsSaleBatchId) {
        this.goodsSaleBatchId = goodsSaleBatchId;
    }

    public BigDecimal getGoodsOutCount() {
        return goodsOutCount;
    }

    public void setGoodsOutCount(BigDecimal goodsOutCount) {
        this.goodsOutCount = goodsOutCount;
    }

    public Long getUpdateId() {
        return updateId;
    }

    public void setUpdateId(Long updateId) {
        this.updateId = updateId;
    }
}
