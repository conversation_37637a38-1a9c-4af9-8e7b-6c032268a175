package org.haier.shopUpdate.params.shopSupplier;

import javax.validation.constraints.NotNull;

public class SupplierUniqueWithPageParams extends ShopUniqueParams {
    /**
     * 供应商编号
     */
    @NotNull(message = "请输入供应商编号")
    private String supplierUnique;
    /**
     * 页码数
     */
    private Integer pageIndex;
    /**
     * 每页条数
     */
    private Integer pageSize;

    public String getSupplierUnique() {
        return supplierUnique;
    }

    public void setSupplierUnique(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }

    public Integer getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Integer pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
