package org.haier.shopUpdate.params.shopSupplier;

import org.haier.shopUpdate.params.ValidateCommonReq;

import javax.validation.constraints.NotNull;

public class CreateParams extends ShopUniqueParams {
    /**
     * 操作人ID
     */
    @NotNull(message = "请输入操作人ID")
    private Long createId;
    /**
     * 操作人姓名
     */
    @NotNull(message = "请输入操作人姓名")
    private String createBy;

    public Long getCreateId() {
        return createId;
    }

    public void setCreateId(Long createId) {
        this.createId = createId;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
}
