package org.haier.shopUpdate.params.shopSupplier;

public class QuerySupListParams extends ShopUniqueParams {
    /**
     * 供应商名称或手机号
     */
    private String queryMeg;
    /**
     * 所属分类
     */
    private String supKindUnique;
    /**
     * 页码数
     */
    private Integer pageIndex;
    /**
     * 每页条数
     */
    private Integer pageSize;

    public String getQueryMeg() {
        return queryMeg;
    }

    public void setQueryMeg(String queryMeg) {
        this.queryMeg = queryMeg;
    }

    public String getSupKindUnique() {
        return supKindUnique;
    }

    public void setSupKindUnique(String supKindUnique) {
        this.supKindUnique = supKindUnique;
    }

    public Integer getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Integer pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
