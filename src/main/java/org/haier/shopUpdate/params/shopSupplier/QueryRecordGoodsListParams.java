package org.haier.shopUpdate.params.shopSupplier;

import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;

public class QueryRecordGoodsListParams extends SupplierUniqueParams {
    /**
     * 是否建档:0-未建档；1-已建档
     */
    @NotNull(message = "请输入建档状态")
    @Range(min = 0,max = 1)
    private Integer recordStatus;
    /**
     * 页码数
     */
    private Integer pageIndex;
    /**
     * 每页条数
     */
    private Integer pageSize;

    public Integer getRecordStatus() {
        return recordStatus;
    }

    public void setRecordStatus(Integer recordStatus) {
        this.recordStatus = recordStatus;
    }
    public Integer getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Integer pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
