package org.haier.shopUpdate.params.shopSupplier;

import org.haier.shopUpdate.params.ValidateCommonReq;

import javax.validation.constraints.NotNull;

public class SupKindModifyParams extends CreateParams {
    /**
     * 分类编号
     */
    @NotNull(message = "请输入分类编号")
    private String supKindUnique;
    /**
     *分类名称
     */
    private String supKindName;
    /**
     *更新方式：1-修改，2-删除
     */
    @NotNull(message = "请输入更新方式")
    private Integer modifyType;

    public String getSupKindUnique() {
        return supKindUnique;
    }

    public void setSupKindUnique(String supKindUnique) {
        this.supKindUnique = supKindUnique;
    }

    public String getSupKindName() {
        return supKindName;
    }

    public void setSupKindName(String supKindName) {
        this.supKindName = supKindName;
    }

    public Integer getModifyType() {
        return modifyType;
    }

    public void setModifyType(Integer modifyType) {
        this.modifyType = modifyType;
    }
}
