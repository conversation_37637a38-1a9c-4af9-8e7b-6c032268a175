package org.haier.shopUpdate.params.shopSupplier;

import javax.validation.constraints.NotNull;

public class QueryRepayHisInfoParams extends SupplierUniqueParams{
    /**
     * 还款记录ID
     */
    @NotNull(message = "请输入还款记录ID")
    private Long paymentId;

    public Long getPaymentId() {
        return paymentId;
    }

    public void setPaymentId(Long paymentId) {
        this.paymentId = paymentId;
    }
}
