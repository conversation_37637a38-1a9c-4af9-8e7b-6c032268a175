package org.haier.shopUpdate.params.speech;

import java.io.Serializable;

/**
 * 新增语音识别记录参数
 */
public class AddNewSpeechEntityParams implements Serializable {
    //设备类型
    private String appType;
    //申请编号，又后台生成，并返回给前端，用于后续查询识别结果
    private String applyNo;
    //店铺编号
    private Long shopUnique;
    //识别的语音内存
    private String speechTextPhone;
    //当前登录员工编号，用于判断是否有权限
    private Long staffId;

    public String getAppType() {
        return appType;
    }

    public void setAppType(String appType) {
        this.appType = appType;
    }

    public String getApplyNo() {
        return applyNo;
    }

    public void setApplyNo(String applyNo) {
        this.applyNo = applyNo;
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getSpeechTextPhone() {
        return speechTextPhone;
    }

    public void setSpeechTextPhone(String speechTextPhone) {
        this.speechTextPhone = speechTextPhone;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    @Override
    public String toString() {
        return "AddNewSpeechEntityParams{" +
                "appType='" + appType + '\'' +
                ", applyNo='" + applyNo + '\'' +
                ", shopUnique=" + shopUnique +
                ", speechTextPhone='" + speechTextPhone + '\'' +
                ", staffId=" + staffId +
                '}';
    }
}
