package org.haier.shopUpdate.params.speech;

import java.io.Serializable;

public class SysQueryParams implements Serializable {
    //请求编号
    private String apply_no;
    //语音文字
    private String recog_text;
    //文本类型：1、文本；0、语音文件
    private String recognition_type;
    //语音文本的base64编码
    private String fileBase64Text;
    //文件类型
    private String fileType;

    public String getApply_no() {
        return apply_no;
    }

    public void setApply_no(String apply_no) {
        this.apply_no = apply_no;
    }

    public String getRecog_text() {
        return recog_text;
    }

    public void setRecog_text(String recog_text) {
        this.recog_text = recog_text;
    }

    public String getRecognition_type() {
        return recognition_type;
    }

    public void setRecognition_type(String recognition_type) {
        this.recognition_type = recognition_type;
    }

    public String getFileBase64Text() {
        return fileBase64Text;
    }

    public void setFileBase64Text(String fileBase64Text) {
        this.fileBase64Text = fileBase64Text;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }
}
