package org.haier.shopUpdate.params.speech;

import org.haier.shopUpdate.params.PageParams;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 查询常用的命令
 */
public class QueryCommonSpeechListParams extends PageParams implements Serializable {
    /**
     * 应用类型
     */
    @NotNull(message = "应用类型不能为空")
    private String appType;
    //需要查询的店铺编号
    @NotNull(message = "店铺编号不能为空")
    private Long shopUnique;
    //需要查询的店员编号
    private Long staffId;

    List<String> list;
    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public List<String> getList() {
        return list;
    }

    public void setList(List<String> list) {
        this.list = list;
    }

    public String getAppType() {
        return appType;
    }

    public void setAppType(String appType) {
        this.appType = appType;
    }
}
