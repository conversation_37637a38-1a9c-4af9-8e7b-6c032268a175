package org.haier.shopUpdate.params.goods;

import java.io.Serializable;
import java.util.List;

/**
 * 查询商品历史最新进价
 */
public class QueryGoodsLastInPriceParams implements Serializable {
    //店铺编号
    private String shopUnique;
    //商品列表
    private List<String> list;

    public String getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(String shopUnique) {
        this.shopUnique = shopUnique;
    }

    public List<String> getList() {
        return list;
    }

    public void setList(List<String> list) {
        this.list = list;
    }
}
