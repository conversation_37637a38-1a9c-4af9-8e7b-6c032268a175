package org.haier.shopUpdate.params.goods.allocation;

public class QueryAllocationParams {

    /**
     * @see org.haier.shopUpdate.enums.AllocationShopStatus
     */
    private Integer allocationStatus;

    private String searchKey;
    private String startTime;

    private String endTime;

    /**
     * 查询店铺 ID
     */
    private Long pullStoreOfId;

    private Integer pageIndex;

    private Integer pageSize;

    public String getSearchKey() {
        return searchKey;
    }

    public void setSearchKey(String searchKey) {
        this.searchKey = searchKey;
    }

    public Integer getAllocationStatus() {
        return allocationStatus;
    }

    public void setAllocationStatus(Integer allocationStatus) {
        this.allocationStatus = allocationStatus;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Long getPullStoreOfId() {
        return pullStoreOfId;
    }

    public void setPullStoreOfId(Long pullStoreOfId) {
        this.pullStoreOfId = pullStoreOfId;
    }

    public Integer getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Integer pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
