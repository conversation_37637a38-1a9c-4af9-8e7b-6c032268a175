package org.haier.shopUpdate.params.goods.allocation;

import java.util.List;

public class AddAllocateShopsParams {
    /**
     * 调入 店铺 ID
     */
    private Long inboundStoreOfId;
    /**
     * 调出 店铺 ID
     */
    private Long pullStoreOfId;
    /**
     * 操作人
     */
    private Integer userId;
    private String userName;

    private List<AddAllocateShopsDetail> detailList;

    public Long getInboundStoreOfId() {
        return inboundStoreOfId;
    }

    public void setInboundStoreOfId(Long inboundStoreOfId) {
        this.inboundStoreOfId = inboundStoreOfId;
    }

    public Long getPullStoreOfId() {
        return pullStoreOfId;
    }

    public void setPullStoreOfId(Long pullStoreOfId) {
        this.pullStoreOfId = pullStoreOfId;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public List<AddAllocateShopsDetail> getDetailList() {
        return detailList;
    }

    public void setDetailList(List<AddAllocateShopsDetail> detailList) {
        this.detailList = detailList;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }
}
