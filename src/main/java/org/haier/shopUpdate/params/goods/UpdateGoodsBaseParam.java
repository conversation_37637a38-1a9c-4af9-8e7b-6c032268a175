package org.haier.shopUpdate.params.goods;

import org.haier.shopUpdate.params.ValidateCommonReq;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

public class UpdateGoodsBaseParam extends ValidateCommonReq {
    /**
     * 称重商品类型：0、按件；1、按重量
     */
    @NotNull(message = "请输入商品计件类型")
    private Integer goodsChengType;

    /**
     * 商品库存量
     */
    private BigDecimal goodsCount;

    /**
     * 商品分类-二级分类编号
     */
    @NotBlank(message = "请输入商品分类")
    private String goodsKindUnique;

    @Valid
    private List<UpdateGoodsParam> goodsMessage;
    /**
     * 默认供应商编号
     */
    private String supplierUnique;
    /**
     * 包装外键
     */
    @NotNull(message = "请输入包装外键")
    private Long foreignKey;
    /**
     * 品牌
     */
    private String goodsBrand;

    /**
     * 产商品特殊说明
     */
    private String goodsRemarks;
    /**
     * 产地
     */
    private String goodsAddress;
    /**
     * 保质期
     */
    private String goodsLife;

    /**
     * 0:没有库存预警 1:有库存预警
     */
    private Integer stockWarningStatus;
    /**
     * 提醒库存量，低于该库存时，提醒缺货
     */
    private BigDecimal outStockWaringCount;
    /**
     * 滞销库存量，高于该值是，商品滞销
     */
    private BigDecimal unsalableCount;

    public BigDecimal getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(BigDecimal goodsCount) {
        this.goodsCount = goodsCount;
    }

    public Integer getGoodsChengType() {
        return goodsChengType;
    }

    public void setGoodsChengType(Integer goodsChengType) {
        this.goodsChengType = goodsChengType;
    }

    public String getGoodsKindUnique() {
        return goodsKindUnique;
    }

    public void setGoodsKindUnique(String goodsKindUnique) {
        this.goodsKindUnique = goodsKindUnique;
    }

    public List<UpdateGoodsParam> getGoodsMessage() {
        return goodsMessage;
    }

    public void setGoodsMessage(List<UpdateGoodsParam> goodsMessage) {
        this.goodsMessage = goodsMessage;
    }

    public String getSupplierUnique() {
        return supplierUnique;
    }

    public void setSupplierUnique(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }


    public String getGoodsBrand() {
        return goodsBrand;
    }

    public void setGoodsBrand(String goodsBrand) {
        this.goodsBrand = goodsBrand;
    }

    public Long getForeignKey() {
        return foreignKey;
    }

    public void setForeignKey(Long foreignKey) {
        this.foreignKey = foreignKey;
    }

    public String getGoodsRemarks() {
        return goodsRemarks;
    }

    public void setGoodsRemarks(String goodsRemarks) {
        this.goodsRemarks = goodsRemarks;
    }

    public String getGoodsAddress() {
        return goodsAddress;
    }

    public void setGoodsAddress(String goodsAddress) {
        this.goodsAddress = goodsAddress;
    }

    public String getGoodsLife() {
        return goodsLife;
    }

    public void setGoodsLife(String goodsLife) {
        this.goodsLife = goodsLife;
    }

    public Integer getStockWarningStatus() {
        return stockWarningStatus;
    }

    public void setStockWarningStatus(Integer stockWarningStatus) {
        this.stockWarningStatus = stockWarningStatus;
    }

    public BigDecimal getOutStockWaringCount() {
        return outStockWaringCount;
    }

    public void setOutStockWaringCount(BigDecimal outStockWaringCount) {
        this.outStockWaringCount = outStockWaringCount;
    }

    public BigDecimal getUnsalableCount() {
        return unsalableCount;
    }

    public void setUnsalableCount(BigDecimal unsalableCount) {
        this.unsalableCount = unsalableCount;
    }
}
