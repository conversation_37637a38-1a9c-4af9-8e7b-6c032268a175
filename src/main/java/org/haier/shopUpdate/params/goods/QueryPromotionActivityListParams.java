package org.haier.shopUpdate.params.goods;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 查询店铺的促销活动列表
 */
public class QueryPromotionActivityListParams implements Serializable {
    //店铺
    @NotNull(message = "店铺不能为空")
    private Long shopUnique;
    //活动有效期开始时间
    @NotNull(message = "开始时间不能为空")
    private String startTime;
    //活动有效期结束时间
    @NotNull(message = "结束时间不能为空")
    private String endTime;
    //活动类型:1、商品折扣；2、商品满赠；3、订单促销；4、单品促销
    private Integer type;
    //页码
    private Integer page;
    //每页条数
    private Integer pageSize;

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public void setPage(Integer page) {
        this.page = page;
    }
    public Integer getStartNum() {
        if(page==null){
            return 0;
        }
        return (page-1)*pageSize;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
