package org.haier.shopUpdate.params.goods.allocation;

import org.hibernate.validator.constraints.NotEmpty;

public class FinishAllocateParams {
    @NotEmpty(message = "请输入调货单id")
    private Integer id;
    @NotEmpty(message = "用户id为空")
    private Integer recipientsUserId;
    @NotEmpty(message = "用户姓名为空")
    private String recipientsUserIdName;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getRecipientsUserId() {
        return recipientsUserId;
    }

    public void setRecipientsUserId(Integer recipientsUserId) {
        this.recipientsUserId = recipientsUserId;
    }

    public String getRecipientsUserIdName() {
        return recipientsUserIdName;
    }

    public void setRecipientsUserIdName(String recipientsUserIdName) {
        this.recipientsUserIdName = recipientsUserIdName;
    }
}
