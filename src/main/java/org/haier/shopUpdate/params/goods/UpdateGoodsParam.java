package org.haier.shopUpdate.params.goods;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class UpdateGoodsParam {
//    @NotNull(message = "商品id不能为空")
    private Integer goodsId;
    /**
     * 条形码
     */
    @NotBlank(message = "请输入商品条码")
    private String goodsBarcode;
    /**
     * 商品名称
     */
    @NotBlank(message = "请输入商品名称")
    private String goodsName;
    /**
     * 售价
     */
    @NotNull(message = "请输入商品售价")
    @DecimalMin(value = "0",message = "请输入正确商品售价")
    private BigDecimal goodsSalePrice;
    /**
     * 包含子商品数量
     */
    @NotNull(message = "请输入包含子商品数量")
    private Integer goodsContain;
    /**
     * 单位
     */
//    @NotBlank(message = "请输入单位")
    private String goodsUnit;
    /**
     * 线上上架状态：1、已上架；2、已下架
     */
    private Integer shelfState;
    /**
     * pc收银上架状态：1、已上架；2、已下架
     */
    private Integer pcShelfState;
    /**
     * 别名
     */
    private String goodsAlias;
    /**
     * 进价
     */
    private BigDecimal goodsInPrice;
    /**
     * 会员价
     */
    private BigDecimal goodsCusPrice;

    /**
     * 网售价
     */
    private BigDecimal goodsWebSalePrice;
    /**
     * 划线价
     */
    private BigDecimal underlinedPrice;
    /**
     * 是否设为批发价 0 否 1，是
     */
    private Integer wholesalePriceFlg;

    /**
     * 货位
     */
    private String goodsPosition;
    /**
     * 图片
     */
    private String goodsPicturePath;

    /**
     * 规格
     */
    private String goodsStandard;

    private BigDecimal minSaleCount;
    /**
     * 批发价列表
     */
    private List<WholesaleInfo> wholesaleList;

    @Data
    public static class WholesaleInfo implements Serializable {
        /**
         * 批发价
         */
        private BigDecimal wholesalePrice;
        /**
         * 起批数量
         */
        private BigDecimal wholesaleCount;
    }
    public Integer getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Integer goodsId) {
        this.goodsId = goodsId;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public Integer getGoodsContain() {
        return goodsContain;
    }

    public void setGoodsContain(Integer goodsContain) {
        this.goodsContain = goodsContain;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public BigDecimal getGoodsSalePrice() {
        return goodsSalePrice;
    }

    public void setGoodsSalePrice(BigDecimal goodsSalePrice) {
        this.goodsSalePrice = goodsSalePrice;
    }



    public String getGoodsUnit() {
        return goodsUnit;
    }

    public void setGoodsUnit(String goodsUnit) {
        this.goodsUnit = goodsUnit;
    }

    public String getGoodsAlias() {
        return goodsAlias;
    }

    public void setGoodsAlias(String goodsAlias) {
        this.goodsAlias = goodsAlias;
    }

    public BigDecimal getGoodsInPrice() {
        return goodsInPrice;
    }

    public void setGoodsInPrice(BigDecimal goodsInPrice) {
        this.goodsInPrice = goodsInPrice;
    }

    public BigDecimal getGoodsCusPrice() {
        return goodsCusPrice;
    }

    public void setGoodsCusPrice(BigDecimal goodsCusPrice) {
        this.goodsCusPrice = goodsCusPrice;
    }

    public BigDecimal getGoodsWebSalePrice() {
        return goodsWebSalePrice;
    }

    public void setGoodsWebSalePrice(BigDecimal goodsWebSalePrice) {
        this.goodsWebSalePrice = goodsWebSalePrice;
    }

    public String getGoodsPicturePath() {
        return goodsPicturePath;
    }

    public void setGoodsPicturePath(String goodsPicturePath) {
        this.goodsPicturePath = goodsPicturePath;
    }

    public String getGoodsStandard() {
        return goodsStandard;
    }

    public void setGoodsStandard(String goodsStandard) {
        this.goodsStandard = goodsStandard;
    }

    public Integer getShelfState() {
        return shelfState;
    }

    public void setShelfState(Integer shelfState) {
        this.shelfState = shelfState;
    }

    public Integer getPcShelfState() {
        return pcShelfState;
    }

    public void setPcShelfState(Integer pcShelfState) {
        this.pcShelfState = pcShelfState;
    }

    public BigDecimal getMinSaleCount() {
        return minSaleCount;
    }

    public void setMinSaleCount(BigDecimal minSaleCount) {
        this.minSaleCount = minSaleCount;
    }

    public Integer getWholesalePriceFlg() {
        return wholesalePriceFlg;
    }

    public void setWholesalePriceFlg(Integer wholesalePriceFlg) {
        this.wholesalePriceFlg = wholesalePriceFlg;
    }

    public String getGoodsPosition() {
        return goodsPosition;
    }

    public void setGoodsPosition(String goodsPosition) {
        this.goodsPosition = goodsPosition;
    }

    public BigDecimal getUnderlinedPrice() {
        return underlinedPrice;
    }

    public void setUnderlinedPrice(BigDecimal underlinedPrice) {
        this.underlinedPrice = underlinedPrice;
    }

    public List<WholesaleInfo> getWholesaleList() {
        return wholesaleList;
    }

    public void setWholesaleList(List<WholesaleInfo> wholesaleList) {
        this.wholesaleList = wholesaleList;
    }
}
