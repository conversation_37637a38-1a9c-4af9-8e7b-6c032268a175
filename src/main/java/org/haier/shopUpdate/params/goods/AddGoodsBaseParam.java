package org.haier.shopUpdate.params.goods;

import org.haier.shopUpdate.params.ValidateCommonReq;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class AddGoodsBaseParam  extends ValidateCommonReq {
    /**
     * 称重商品类型：0、按件；1、按重量
     */
    @NotNull(message = "请输入商品计件类型")
    private Integer goodsChengType;

    /**
     * 商品分类-二级分类编号
     */
    @NotBlank(message = "请输入商品分类")
    private String goodsKindUnique;

    @Valid
    private List<AddGoodsParam> goodsMessage;
    /**
     * 默认供应商编号
     */
    private String supplierUnique;

    /**
     * 最新单位库存数量 （老的是Integer）
     */
    @NotNull(message = "请输入商品库存")
    @DecimalMin(value = "0",message = "请输入正确商品库存")
    private BigDecimal goodsCount;
    /**
     * 包装外键
     */
    @NotNull(message = "请输入包装外键")
    private Long foreignKey;
    /**
     * 品牌
     */
    private String goodsBrand;

    /**
     * 产商品特殊说明
     */
    private String goodsRemarks;
    /**
     * 产地
     */
    private String goodsAddress;
    /**
     * 保质期
     */
    private Integer goodsLife;
    /**
     * 生产日期
     */
    private String goodsProd;

    /**
     * 0:没有库存预警 1:有库存预警
     */
    private Integer stockWarningStatus;
    /**
     * 提醒库存量，低于该库存时，提醒缺货
     */
    private BigDecimal outStockWaringCount;
    /**
     * 滞销库存量，高于该值是，商品滞销
     */
    private BigDecimal unsalableCount;

    public Integer getGoodsChengType() {
        return goodsChengType;
    }

    public void setGoodsChengType(Integer goodsChengType) {
        this.goodsChengType = goodsChengType;
    }

    public String getGoodsKindUnique() {
        return goodsKindUnique;
    }

    public void setGoodsKindUnique(String goodsKindUnique) {
        this.goodsKindUnique = goodsKindUnique;
    }

    public List<AddGoodsParam> getGoodsMessage() {
        return goodsMessage;
    }

    public void setGoodsMessage(List<AddGoodsParam> goodsMessage) {
        this.goodsMessage = goodsMessage;
    }

    public String getSupplierUnique() {
        return supplierUnique;
    }

    public void setSupplierUnique(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }

    public BigDecimal getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(BigDecimal goodsCount) {
        this.goodsCount = goodsCount;
    }

    public String getGoodsBrand() {
        return goodsBrand;
    }

    public void setGoodsBrand(String goodsBrand) {
        this.goodsBrand = goodsBrand;
    }

    public Long getForeignKey() {
        return foreignKey;
    }

    public void setForeignKey(Long foreignKey) {
        this.foreignKey = foreignKey;
    }

    public String getGoodsRemarks() {
        return goodsRemarks;
    }

    public void setGoodsRemarks(String goodsRemarks) {
        this.goodsRemarks = goodsRemarks;
    }

    public String getGoodsAddress() {
        return goodsAddress;
    }

    public void setGoodsAddress(String goodsAddress) {
        this.goodsAddress = goodsAddress;
    }

    public Integer getGoodsLife() {
        return goodsLife;
    }

    public void setGoodsLife(Integer goodsLife) {
        this.goodsLife = goodsLife;
    }

    public Integer getStockWarningStatus() {
        return stockWarningStatus;
    }

    public void setStockWarningStatus(Integer stockWarningStatus) {
        this.stockWarningStatus = stockWarningStatus;
    }

    public BigDecimal getOutStockWaringCount() {
        return outStockWaringCount;
    }

    public void setOutStockWaringCount(BigDecimal outStockWaringCount) {
        this.outStockWaringCount = outStockWaringCount;
    }

    public BigDecimal getUnsalableCount() {
        return unsalableCount;
    }

    public void setUnsalableCount(BigDecimal unsalableCount) {
        this.unsalableCount = unsalableCount;
    }

    public String getGoodsProd() {
        return goodsProd;
    }

    public void setGoodsProd(String goodsProd) {
        this.goodsProd = goodsProd;
    }
}
