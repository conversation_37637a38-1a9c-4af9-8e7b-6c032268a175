package org.haier.shopUpdate.params.goods.allocation;

import java.math.BigDecimal;

public class AddAllocateShopsDetail {
    /**
     * 商品名称（供货商）
     */
    private String goodsName;
    /**
     * 商品条形码
     */
    private String goodsBarcode;
    /**
     * 订购数量
     */
    private BigDecimal purchaseListDetailCount;
    /**
     * 采购价格
     */
    private BigDecimal purchaseListDetailPrice;
    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public BigDecimal getPurchaseListDetailCount() {
        return purchaseListDetailCount;
    }

    public void setPurchaseListDetailCount(BigDecimal purchaseListDetailCount) {
        this.purchaseListDetailCount = purchaseListDetailCount;
    }

    public BigDecimal getPurchaseListDetailPrice() {
        return purchaseListDetailPrice;
    }

    public void setPurchaseListDetailPrice(BigDecimal purchaseListDetailPrice) {
        this.purchaseListDetailPrice = purchaseListDetailPrice;
    }
}
