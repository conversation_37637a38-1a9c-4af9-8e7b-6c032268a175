package org.haier.shopUpdate.params.agriculturalSupplier;

import org.haier.shopUpdate.params.ValidateCommonReq;

import javax.validation.constraints.NotNull;

public class DeleteSupplierParams extends ValidateCommonReq {

    @NotNull(message = "请输入删除的编号")
    private Long id; //删除的编号

    @NotNull(message = "请输入操作人")
    private Integer modifyUser; //操作人

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(Integer modifyUser) {
        this.modifyUser = modifyUser;
    }
}
