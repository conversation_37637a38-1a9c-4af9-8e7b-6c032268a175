package org.haier.shopUpdate.params.agriculturalSupplier;

import org.haier.shopUpdate.params.ValidateCommonReq;

import javax.validation.constraints.NotNull;

public class QuerySupplierListParams extends ValidateCommonReq {

    /**
     * 供货商类型：1农批货主、2农批供货商
     */
    @NotNull(message = "请选择供货商类型")
    private Integer type;

    /**
     * 姓名筛选
     */
    private String name;

    /**
     * 手机号筛选
     */
    private String phone;

    /**
     * 页码
     */
    private Integer pageIndex;

    /**
     * 每页数量
     */
    private Integer pageSize;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Integer getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Integer pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
