package org.haier.shopUpdate.params.agriculturalSupplier;

import org.haier.shopUpdate.params.ValidateCommonReq;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;

public class AddSupplierParams extends ValidateCommonReq {

    /**
     * 供货商名称
     */
    @NotBlank(message = "请输入姓名")
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 供货商类型：1农批货主、2农批供货商
     */
    @NotNull(message = "请选择供货商类型")
    private Integer type;

    /**
     * 1启用2禁用
     */
    @NotNull(message = "请选择是否启用")
    private Integer enabled;

    /**
     * 最后一次操作人：shop_staff.staff_id
     */
    @NotNull(message = "请输入操作人")
    private Integer modifyUser;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    public Integer getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(Integer modifyUser) {
        this.modifyUser = modifyUser;
    }

}
