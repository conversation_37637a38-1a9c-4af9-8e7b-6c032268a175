package org.haier.shopUpdate.params.stockRecord;

import java.io.Serializable;

/**
 * @ClassName goodBatchListParams
 * <AUTHOR>
 * @Date 2024/4/28 10:21
 */

public class goodBatchListOutStockParams implements Serializable {
    /**
     * 批次唯一标识
     */
    private String batchUnique;

    /**
     * 出库数量
     */
    private Long goodOutStockCount;

    public String getBatchUnique() {
        return batchUnique;
    }

    public void setBatchUnique(String batchUnique) {
        this.batchUnique = batchUnique;
    }

    public Long getGoodOutStockCount() {
        return goodOutStockCount;
    }

    public void setGoodOutStockCount(Long goodOutStockCount) {
        this.goodOutStockCount = goodOutStockCount;
    }
}
