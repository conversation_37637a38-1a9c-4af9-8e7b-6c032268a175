package org.haier.shopUpdate.params;

import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * @author: 王恩龙
 * @create: 2024-05-18 08:09
 * @Description:
 */
public class QueryStatisticsByShopParams implements Serializable {
    private static final long serialVersionUID = 1L;
    //店铺编号
    @NotBlank(message = "店铺编号不能为空")
    private String shopUnique;
    //开始查询时间
    @NotBlank(message = "开始查询时间不能为空")
    private String startTime;
    //结束查询时间
    @NotBlank(message = "结束查询时间不能为空")
    private String endTime;
    //员工编号
    private String staffId;
    //订单类型：0、线下订单；1、线上订单
    private Integer saleType;

    private Integer payMethod;

    //订单类型：1、销售订单；2、退款订单
    private Integer orderType;

    public String getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(String shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getStaffId() {
        return staffId;
    }

    public void setStaffId(String staffId) {
        this.staffId = staffId;
    }

    public Integer getSaleType() {
        return saleType;
    }

    public void setSaleType(Integer saleType) {
        this.saleType = saleType;
    }

    public Integer getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(Integer payMethod) {
        this.payMethod = payMethod;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    @Override
    public String toString() {
        return "QueryStatisticsByShopParams{" +
                "\"shopUnique\":\"" + shopUnique + "\"" +
                ", \"startTime\":\"" + startTime + "\"" +
                ", \"endTime\":\"" + endTime + "\"" +
                ", \"staffId\":\"" + staffId + "\"" +
                ", \"saleType\":" + saleType + "\"" +
                ", \"payMethod\":" + payMethod + "\"" +
                ", \"orderType\":" + orderType + "\"" +
                "}";
    }
}
