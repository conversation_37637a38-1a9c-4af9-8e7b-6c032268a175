package org.haier.shopUpdate.util;

import java.awt.Graphics2D;
import java.awt.Image;
import java.awt.Toolkit;
import java.awt.image.BufferedImage;
import java.awt.image.ColorModel;
import java.awt.image.DataBuffer;
import java.awt.image.DataBufferInt;
import java.awt.image.DirectColorModel;
import java.awt.image.PixelGrabber;
import java.awt.image.Raster;
import java.awt.image.WritableRaster;
import java.io.*;
import java.math.BigDecimal;
import java.security.MessageDigest;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.log4j.Logger;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import net.coobird.thumbnailator.Thumbnails;

/**
 * <AUTHOR>
 *
 */
@Slf4j
public class ShopsUtil {
	public static Map<String, Object> map = new HashMap<String, Object>();

	public static String removeZeroHead(String str) {
		str = str.substring(1);
		if (str.startsWith("0") && str.length() > 1) {
			str = removeZeroHead(str);
		}
		return str;
	}

	/**
	 * <AUTHOR>
	 * @param inStr
	 * @return
	 */
	public static String string2MD5(String inStr) {
		MessageDigest md5 = null;
		try {
			md5 = MessageDigest.getInstance("MD5");
		} catch (Exception e) {
			log.error("生成MD5失败",e);
			return "";
		}
		char[] charArray = inStr.toCharArray();
		byte[] byteArray = new byte[charArray.length];

		for (int i = 0; i < charArray.length; i++) {
			byteArray[i] = (byte) charArray[i];
		}
		byte[] md5Bytes = md5.digest(byteArray);
		StringBuffer hexValue = new StringBuffer();
		for (int i = 0; i < md5Bytes.length; i++) {
			int val = ((int) md5Bytes[i]) & 0xff;
			if (val < 16) {
				hexValue.append("0");
			}
			hexValue.append(Integer.toHexString(val));
		}
		return hexValue.toString().trim();
	}

	/**
	 * 将输入的时间转换为Timestamp格式
	 *
	 * @param time
	 * @param pattern
	 * @return
	 */
	public static Timestamp getTimestamp(String time, String pattern, int days) {
		if (null == pattern) {
			pattern = "yyyy-MM-dd HH:mm:ss";
		}
		SimpleDateFormat sdf = new SimpleDateFormat(pattern);
		try {
			Date date = sdf.parse(time);
			return new Timestamp(date.getTime() - 3600l * 1000 * 24 * days);
		} catch (ParseException e) {
			log.error("生成Timestamp失败",e);
		}
		return null;
	}

	/**
	 * 创建新的短信验证码
	 *
	 * @return
	 */
	public static Integer getMsgCode() {
		Integer code = 100000;
		Integer random = (int) (Math.random() * 900000);
		return (random + code);
	}

	/**
	 * 获取当前时间若干天前的时间
	 *
	 * @param time
	 * @param pattern
	 * @param days
	 * @return
	 */
	public static Timestamp getNewDays(String time, String pattern, Integer days) {
		if (null == pattern) {
			pattern = "yyyy-MM-dd HH:mm:ss";
		}
		SimpleDateFormat sdf = new SimpleDateFormat(pattern);
		try {
			Date date = sdf.parse(time);
			return new Timestamp(date.getTime() - 3600 * 1000 * 24 * days);
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			log.error("生成新时间失败",e);
		}
		return null;
	}

	/**
	 * 获取当前时间
	 *
	 * @return
	 */
	public static Timestamp getRightNow() {
		Date date = new Date();
		return new Timestamp(date.getTime());
	}

	/**
	 * 创建新的UUID
	 *
	 * @return
	 */
	public static String UUID() {
		UUID uuid = UUID.randomUUID();
		String id = uuid.toString();
		return id;
	}

	/**
	 * 去除字符串中的"-"
	 *
	 * @return
	 */
	public static String newToken() {
		String token = UUID();
		return token.replace("-", "");
	}

	/**
	 * 创建新的编号
	 *
	 * @return
	 */
	public static Long newUnique() {
		Long unique = new Date().getTime();
		return unique;
	}

	/**
	 * 将文件转为byte数组
	 * @param filePath
	 * @return
	 * @throws IOException
	 */
	public static byte[] readFileToBytes(String filePath) throws IOException {
        FileInputStream fis = new FileInputStream(filePath);

        byte[] bytes = new byte[fis.available()];
        fis.read(bytes);
        fis.close();
        return bytes;
    }

	/**
	 * 测试请求是否上传图片，如果上传，返回；否则返回空
	 *
	 * @return
	 */
	public static MultipartFile testMulRequest(HttpServletRequest request, String fileName) {
		MultipartFile file = null;
		if (!(request instanceof MultipartHttpServletRequest)) {
			return null;
		}
		MultipartHttpServletRequest multipartHttpServletRequest = (MultipartHttpServletRequest) request;
		Map<String, MultipartFile> map = multipartHttpServletRequest.getFileMap();
		file = map.get(fileName);
		return file;
	}

	public static List<MultipartFile> getFiles(HttpServletRequest request, String fileName) {
		if (!(request instanceof MultipartHttpServletRequest)) {
			return null;
		}
		MultipartHttpServletRequest multipartHttpServletRequest = (MultipartHttpServletRequest) request;
		return multipartHttpServletRequest.getFiles(fileName);
	}

	/**
	 * 将上传上来的文件保存到本地，并上传到文件服务器
	 * @param file
	 * @param filePath
	 * @param fileName
	 * @return
	 */
	public static boolean saveFile(MultipartFile file,String filePath,String fileName) {
		if (null == file || file.isEmpty()) {
			return false;
		}
		File fileDir = new File(filePath);
		if (!fileDir.exists()) {
			fileDir.mkdirs();
		}
		String fileSavePath = filePath + File.separator + fileName;
		try {
			file.transferTo(new File(fileSavePath));
			return true;
		} catch (Exception e) {
			log.error("保存文件失败",e);
			return false;
		}
	}

	/**
	 * 保存商品图片
	 *
	 * @return
	 */
	public static boolean savePicture(MultipartFile file, String filePath, String goodsName) {
		if (file.isEmpty() || file == null) {
			return false;
		}
		File newFile = null;
		File fileDir = new File(filePath);
		if (fileDir.isDirectory()) {
			File[] subs = fileDir.listFiles();
			for (int i = 0; i < subs.length; i++) {// 如果有同名文件，删除
				String ofname = subs[i].getName();
				if (goodsName.equals(ofname)) {
					subs[i].delete();
				}
			}
		}
		String savePath = filePath + File.separator + goodsName;
		newFile = new File(savePath);// 创建新的图片文件
		try {
			if (!newFile.getParentFile().exists()) {
				newFile.getParentFile().mkdirs();
			}
			newFile.createNewFile();
			file.transferTo(newFile);// 将文件流转换成文件

//			Thumbnails.of(savePath).scale(1f).outputQuality(0.15f).toFile(savePath);
            Thumbnails.of(savePath).size(800,800).toFile(savePath);

		} catch (IOException e) {
			// 文件创建失败或转换失败，返回错误
			log.error("保存图片失败",e);
			return false;
		}
		return true;
	}

//	public static void main(String[] args) {
//		String picPath ="D://test//img//bb.jpg";
//		FileItem fileItem = createFileItem(picPath);
//        MultipartFile mfile = new CommonsMultipartFile(fileItem);
//		savePicture(mfile, "D://test//img//", "new_bb.jpg");
//	}

	public static FileItem createFileItem(String filePath) {
	    FileItemFactory factory = new DiskFileItemFactory(16, null);
	    String textFieldName = "textField";
	    int num = filePath.lastIndexOf(".");
	    String extFile = filePath.substring(num);
	    FileItem item = factory.createItem(textFieldName, "text/plain", true, "MyFileName");
	    File newfile = new File(filePath);
	    int bytesRead = 0;
	    byte[] buffer = new byte[8192];
	    try {
	        FileInputStream fis = new FileInputStream(newfile);
	        OutputStream os = item.getOutputStream();
	        while ((bytesRead = fis.read(buffer, 0, 8192)) != -1) {
	            os.write(buffer, 0, bytesRead);
	        }
	        os.close();
	        fis.close();
	    } catch (IOException e) {
	        log.error("创建文件失败",e);
	    }
	    return item;
	}

	/**
	 * 返回 null表示格式错误
	 *
	 * @param date
	 * @return
	 */
	public static Timestamp stringToTimestamp(String date) {
		System.err.println(date);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		Timestamp time = null;
		try {
			System.out.println(sdf.parse(date));
			time = new Timestamp(sdf.parse(date).getTime());
		} catch (ParseException e) {
			log.error("时间格式转换错误",e);
		}
		return time;
	}

	/***
	 *
	 * @param  图片名称
	 * @return 转化为jpg后的图片
	 */
	public static BufferedImage change2jpg(File f) {
		try {
			Image i = Toolkit.getDefaultToolkit().createImage(f.getAbsolutePath());
			PixelGrabber pg = new PixelGrabber(i, 0, 0, -1, -1, true);
			pg.grabPixels();
			int width = pg.getWidth(), height = pg.getHeight();
			final int[] RGB_MASKS = { 0xFF0000, 0xFF00, 0xFF };
			final ColorModel RGB_OPAQUE = new DirectColorModel(32, RGB_MASKS[0], RGB_MASKS[1], RGB_MASKS[2]);
			DataBuffer buffer = new DataBufferInt((int[]) pg.getPixels(), pg.getWidth() * pg.getHeight());
			WritableRaster raster = Raster.createPackedRaster(buffer, width, height, width, RGB_MASKS, null);
			BufferedImage img = new BufferedImage(RGB_OPAQUE, raster, false, null);
			return img;
		} catch (InterruptedException e) {
			log.error("图片转换错误",e);
			return null;
		}
	}

	public static boolean targetZoomOut(String sourcePath, String resultPath, String dirPath) { // 将目标图片缩小成256*256并保存
		File file1 = new File(sourcePath); // 用file1取得图片名字
		if (!file1.exists()) {
			return false;
		}
		try {
			BufferedImage input = ImageIO.read(file1);
			BufferedImage inputbig = new BufferedImage(400, 400, BufferedImage.TYPE_INT_BGR);
			Graphics2D g = (Graphics2D) inputbig.getGraphics();
			g.drawImage(input, 0, 0, 400, 400, null); // 画图
			g.dispose();
			inputbig.flush();

			File file2 = new File(dirPath); // 此目录保存缩小后的关键图
			if (file2.exists()) {
				System.out.println("多级目录已经存在不需要创建！！");
			} else {
				// 如果要创建的多级目录不存在才需要创建。
				file2.mkdirs();
			}
			ImageIO.write(inputbig, "jpeg", new File(resultPath)); //
			return true;
		} catch (Exception ex) {
			log.error("图片缩小错误",ex);
			return false;
		}
	}

	public static String getPath(HttpServletRequest request) {
		String path = request.getServletContext().getRealPath("/");
		path = path.replaceAll("\\\\", "/");
		path = new File(path).getParentFile().getAbsolutePath() + File.separator;
		return path;
	}


	/**
	 * 提供精确的小数位四舍五入处理。
	 *
	 * @param v 需要四舍五入的数字
	 * @param scale  小数点后保留几位
	 * @return 四舍五入后的结果
	 */
	public static double round(double v, int scale) {
		if (scale < 0) {
			throw new IllegalArgumentException("The scale must be a positive integer or zero");
		}
		BigDecimal b = new BigDecimal(Double.toString(v));
		BigDecimal one = new BigDecimal("1");
		return b.divide(one, scale, BigDecimal.ROUND_HALF_UP).doubleValue();
	}

	/**
	 * 提供（相对）精确的除法运算。当发生除不尽的情况时，由scale参数指 定精度，以后的数字四舍五入。
	 *
	 * @param v1          被除数
	 * @param v2           除数
	 * @param scale    表示表示需要精确到小数点以后几位。
	 * @return 两个参数的商
	 */
	public static double div(double v1, double v2, int scale) {
		if (scale < 0) {
			throw new IllegalArgumentException("The scale must be a positive integer or zero");
		}
		BigDecimal b1 = new BigDecimal(Double.toString(v1));
		BigDecimal b2 = new BigDecimal(Double.toString(v2));
		return b1.divide(b2, scale, BigDecimal.ROUND_HALF_UP).doubleValue();
	}


	/**
	 * double类型的加法
	 * @param a
	 * @param b
	 * @return
	 */
	public static double addDoubleSum(double a, double b) {
		BigDecimal a1 = new BigDecimal(a);
		BigDecimal b1 = new BigDecimal(b);
		return (a1.add(b1).setScale(2, BigDecimal.ROUND_HALF_UP)).doubleValue();// 返回两位小数的结果
	}

	/**
	 * double类型的减法
	 * @param a
	 * @param b
	 * @return
	 */
	public static double subtractionDouble(double a,double b){
		BigDecimal a1 = new BigDecimal(a);
		BigDecimal b1 = new BigDecimal(b);
		return (a1.subtract(b1).setScale(2, BigDecimal.ROUND_HALF_EVEN)).doubleValue();//
	}
	/**
	 * Integer类型的数字计算
	 *
	 * @param a
	 * @param b
	 * @return
	 */
	public static Integer addDoubleSum(Integer a, Integer b) {
		BigDecimal a1 = new BigDecimal(a);
		BigDecimal b1 = new BigDecimal(b);
		return a1.add(b1).intValue();
	}

	/**
	 * double类型的乘法计算
	 *
	 * @param a
	 * @param b
	 * @return
	 */
	public static double multiplicationDouble(double a, double b) {
		BigDecimal a1 = new BigDecimal(a);
		BigDecimal b1 = new BigDecimal(b);
		return (a1.multiply(b1)).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
	}
	/**
	 * 提供（相对）精确的除法运算，当发生除不尽的情况时，精确到 小数点以后10位，以后的数字四舍五入。
	 * @param v1         被除数
	 * @param v2           除数
	 * @return 两个参数的商
	 */
	public static double div(double v1, double v2) {
		return div(v1, v2, 2);
	}


	/**
	 * 日志记录
	 * @param log
	 * @param request
	 */
	public static void recordLog(Logger log,HttpServletRequest request){
		log.info("请求的路径为："+request.getRequestURI());
		String msg="";
		Enumeration<String> enu=request.getParameterNames();
		while (enu.hasMoreElements()) {
			String string = (String) enu.nextElement();
				msg+=string+"="+request.getParameter(string)+"&";
		}
		if(msg.length()>=1){
			if(log.isDebugEnabled()){
				log.debug("请求的参数为"+msg.substring(0, msg.length()-1));
			}
			if(log.isInfoEnabled()){
				log.info("请求的参数为"+msg.substring(0, msg.length()-1));
			}
		}
	}

	 public static boolean targetZoomOut(String sourcePath, String resultPath, String dirPath,int width,int height) { // 将目标图片缩小成256*256并保存
	   		File file1 = new File(sourcePath); // 用file1取得图片名字
	   		if (!file1.exists()) {
	   			return false;
	   		}
	   		try {

	   			BufferedImage input = ImageIO.read(file1);
	   			BufferedImage inputbig = new BufferedImage(width, height, BufferedImage.TYPE_INT_BGR);
	   			Graphics2D g = (Graphics2D) inputbig.getGraphics();
	   			g.drawImage(input, 0, 0, width, height, null); // 画图
	   			g.dispose();
	   			inputbig.flush();

	   			File file2 = new File(dirPath); // 此目录保存缩小后的关键图
	   			if (file2.exists()) {
	   				System.out.println("多级目录已经存在不需要创建！！");
	   			} else {
	   				// 如果要创建的多级目录不存在才需要创建。
	   				file2.mkdirs();
	   			}
	   			ImageIO.write(inputbig, "jpeg", new File(resultPath)); //
	   			return true;
	   		} catch (Exception ex) {
	   			log.error("图片压缩失败，请检查路径是否正确！！",ex);
	   			return false;
	   		}
	 }
	 public static boolean targetZoomOut2(String sourcePath, String resultPath, String dirPath,int width,int height,Float rate) { // 将目标图片缩小成256*256并保存
		 File file1 = new File(sourcePath); // 用file1取得图片名字
		 if (!file1.exists()) {
			 return false;
		 }
		 try {
			 // 如果比例不为空则说明是按比例压缩
			 if (rate != null && rate > 0) {
				 //获得源图片的宽高存入数组中
				 int[] results = getImgWidthHeight(file1);
				 if (results == null || results[0] == 0 || results[1] == 0) {
					 return false;
				 } else {
					 //按比例缩放或扩大图片大小，将浮点型转为整型
					 width = (int) (results[0] * rate);
					 height = (int) (results[1] * rate);
					 System.out.println(width);
					 System.out.println(height);
				 }
			 }

			 BufferedImage input = ImageIO.read(file1);
			 BufferedImage inputbig = new BufferedImage(width, height, BufferedImage.TYPE_INT_BGR);
			 Graphics2D g = (Graphics2D) inputbig.getGraphics();
			 g.drawImage(input, 0, 0, width, height, null); // 画图
			 g.dispose();
			 inputbig.flush();

			 File file2 = new File(dirPath); // 此目录保存缩小后的关键图
			 if (file2.exists()) {
				 System.out.println("多级目录已经存在不需要创建！！");
			 } else {
				 // 如果要创建的多级目录不存在才需要创建。
				 file2.mkdirs();
			 }
			 ImageIO.write(inputbig, "jpeg", new File(resultPath)); //
			 return true;
		 } catch (Exception ex) {
			 log.error("图片压缩失败，请检查路径是否正确！！",ex);
			 return false;
		 }
	 }

	 public static int[] getImgWidthHeight(File file) {
	        InputStream is = null;
	        BufferedImage src = null;
	        int result[] = { 0, 0 };
	        try {
	            // 获得文件输入流
	            is = new FileInputStream(file);
	            // 从流里将图片写入缓冲图片区
	            src = ImageIO.read(is);
	            result[0] =src.getWidth(null); // 得到源图片宽
	            result[1] =src.getHeight(null);// 得到源图片高
	            is.close();  //关闭输入流
	        } catch (Exception ef) {
	            log.error("获取图片宽高失败！！",ef);
	        }

	        return result;
	    }

	 public static void main(String[] args) {
			ShopsUtil.targetZoomOut2("F:\\1.jpg","F:\\2.jpg","F:\\", 500, 500,1.0f);
	}
}
