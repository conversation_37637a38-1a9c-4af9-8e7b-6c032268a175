package org.haier.shopUpdate.util;

public class NewResultObject<T> {
    private Integer status;
    private String msg;
    private T data;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    NewResultObject () {

    }
    NewResultObject (Integer status, String msg) {
        this.status = status;
        this.msg = msg;
    }
    NewResultObject (Integer status, String msg, T data) {
        this.status = status;
        this.msg = msg;
        this.data = data;
    }

    NewResultObject (T data) {
        this.data = data;
    }
    /**
     * 成功
     * @return
     * @param <T>
     */
    public static <T>NewResultObject<T> success() {
        return new NewResultObject<T>(1, "操作成功");
    }
    /**
     * 成功带参数
     * @param data
     * @return
     * @param <T>
     */
    public static <T>NewResultObject<T> success(T data) {
        return new NewResultObject<T>(1, "操作成功",data);
    }
    /**
     * 失败
     * @param msg
     * @return
     * @param <T>
     */
    public static <T>NewResultObject<T> fail(String msg) {
        return new NewResultObject<T>(0, msg);
    }
}
