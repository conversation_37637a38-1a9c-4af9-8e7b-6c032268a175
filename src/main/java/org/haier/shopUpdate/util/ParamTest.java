package org.haier.shopUpdate.util;

import javax.validation.constraints.Pattern;

import org.hibernate.validator.constraints.Range;

/**
 * @
* @author: 作者:王恩龙
* @version: 2022年9月17日 下午3:13:58
*
*/
public class ParamTest {
	
	/*
	 * 我是日期格式 
	 */
	@Pattern(regexp="^((([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)-(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))-02-29))\\s+([0-1]?[0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])$",message="日期吗？总要遵守格式")
	private String datetime;
	
	@Range(min=0,max=100,message="以你的状态你的年龄应该在")
	private Integer age;

	public String getDatetime() {
		return datetime;
	}

	public void setDatetime(String datetime) {
		this.datetime = datetime;
	}

	public Integer getAge() {
		return age;
	}

	public void setAge(Integer age) {
		this.age = age;
	}
}
