package org.haier.shopUpdate.util;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.net.ServerSocket;
import java.net.Socket;


/**
 * 创建新的Socket服务器
 * <AUTHOR>
 */
@Slf4j
public class BuyHooSocketService {
	/**
	 * 创建新的服务器线程
	 */
	@SuppressWarnings("resource")
	public static boolean createBuyHooServiceSocket(){
		try {
			ServerSocket service=new ServerSocket(8888);
			System.out.println("服务器已创建完成，等待用户进行连接！");
			boolean flag=true;
			while(flag){
				Socket client=service.accept();
				new Thread(new BuyhooSocketServiceThread(client)).start();//启动用户端新线程;
			}
		} catch (IOException e) {
			log.error("端口号已被占用",e);
			System.out.println("端口号已被占用");
			return false;
		}
		return true;
	}

	public static void main(String[] args) {
		createBuyHooServiceSocket();
	}
}
