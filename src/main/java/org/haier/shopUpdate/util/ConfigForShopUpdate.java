package org.haier.shopUpdate.util;

import java.io.InputStreamReader;
import java.util.Properties;


/**
 * 记录需要使用固定值的文档信息，
 * 读取的配置文件见 /src/main/resource/configForShopUpdate.properties
 * <AUTHOR>
 *
 */
public class ConfigForShopUpdate {
	public static String DEFAULTPARUNIQUE;//自定义商品分类的默认大类
	public static String DEFAULTPARNAME;//自定义商品分类的默认大类名称
	public static String DEFAULTKINDUNIQUE;//自定义商品分类的默认小类
	public static String DEFAULTKINDNAME;//自定义商品分类的默认小类名称
	static{
		try {
			Properties p=new Properties();
			p.load(new InputStreamReader(ConfigForShopUpdate.class.getClassLoader().getResourceAsStream("configForShopUpdate.properties"), "UTF-8"));
			DEFAULTKINDUNIQUE=p.getProperty("DEFAULTKINDUNIQUE");
			DEFAULTKINDNAME=p.getProperty("DEFAULTKINDNAME");
			DEFAULTPARUNIQUE=p.getProperty("DEFAULTPARUNIQUE");
			DEFAULTPARNAME=p.getProperty("DEFAULTPARNAME");
		} catch (Exception e) {
			
		}
	}
	
}
