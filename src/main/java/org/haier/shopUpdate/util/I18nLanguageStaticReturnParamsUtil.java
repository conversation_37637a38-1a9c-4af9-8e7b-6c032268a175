package org.haier.shopUpdate.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import org.haier.shopUpdate.config.i18n.I18nMsgConfig;
import org.haier.shopUpdate.redism.RedisCache;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * @ClassName I18nLanguageStaticReturnParamsUtil
 * <AUTHOR>
 * @Date 2025/1/21 9:10
 */
@Component
public class I18nLanguageStaticReturnParamsUtil {
    @Resource
    private RedisCache redisCache;

    public String getMessage(String key) {
//获取请求信息
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();

        String language = request.getHeader("language");
        if (ObjectUtil.isNotEmpty(language)) {
            Object messageObject = redisCache.getObject(I18nMsgConfig.REDIS_LOCALE_STATIC_KEY + ":" + I18nMsgConfig.appName);
            Map<String, Map<String, String>> map = (Map<String, Map<String, String>>) messageObject;
            if (ObjectUtil.isNotEmpty(map) && ObjectUtil.isNotEmpty(map.get(language))) {
                String message = map.get(language).get(key);
                if (ObjectUtil.isNotEmpty(message)){
                    return message;
                }
            }
        }
        return key;
    }
}
