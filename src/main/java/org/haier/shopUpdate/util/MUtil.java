package org.haier.shopUpdate.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.UnknownHostException;
import java.security.MessageDigest;
import java.util.*;

@Slf4j
public class MUtil {

	public static List<Map<String, Object>> strToList(String str) {
		JSONArray json = JSONArray.parseArray(str);
		com.alibaba.fastjson.JSONObject object = null;
		Map<String, Object> t = null;
		List<Map<String, Object>> list = new ArrayList<Map<String,Object>>();
		for (int i = 0; i < json.size(); i++) {
			object = com.alibaba.fastjson.JSONObject.parseObject(json.getString(i));
			t = MUtil.toMap(object);
			list.add(t);
		}
		return list;
	}
	public static Map<String, Object> toMap(com.alibaba.fastjson.JSONObject object) throws JSONException {
		Map<String,Object> map = new HashMap<String,Object>();
		Set<String> keysItr = object.keySet();
		for (String str : keysItr) {
			Object value = object.get(str);
			if ((value instanceof JSONArray))
				value = toList((JSONArray)value);
			else if ((value instanceof com.alibaba.fastjson.JSONObject)) {
				value = toMap((com.alibaba.fastjson.JSONObject)value);
			}
			map.put(str, value);
		}
		return map;
	}
	public static List<Object> toList(JSONArray array) throws JSONException {
		List<Object> list = new ArrayList<Object>();
		for (int i = 0; i < array.size(); i++) {
			Object value = array.get(i);
			if ((value instanceof JSONArray))
				value = toList((JSONArray)value);
			else if ((value instanceof com.alibaba.fastjson.JSONObject)) {
				value = toMap((com.alibaba.fastjson.JSONObject)value);
			}
			list.add(value);
		}
		return list;
	}
	 /**
	  * 将map中的对象转换为String
	  */
	 public static String strObject(Object object) {
		    String result = "";
		    if ((object != null) && (!"".equals(object))) {
		      result = object.toString();
		    }
		    return result;
		  }
	 /**
	   * 将JSON字符串转为Map对象
	   * @param json
	   * @return
	   * @throws JSONException
	 * @throws UnsupportedEncodingException
	   */
	 @SuppressWarnings("unchecked")
	public static Map<String, Object> jsonToMap(String json) throws UnsupportedEncodingException{
		 Map<String,Object> retMap = new HashMap<String,Object>();
		 if(json != null) {
//			 json = new String(json.toString().getBytes("ISO-8859-1"),
//						"UTF-8").equals("null") ? "" : new String(json
//						.toString().getBytes("ISO-8859-1"), "UTF-8");
			 JSONObject jO = JSONObject.fromObject(json);
			 retMap =  (Map<String, Object>) JSONObject.toBean(jO, HashMap.class);
			 }
		return retMap;
	  }

	 /**
      * 获取MD5加密
      *
      * @param pwd
      *            需要加密的字符串
      * @return String字符串 加密后的字符串
      */
     public static String getPwd(String pwd) {
         try {
             // 创建加密对象
             MessageDigest digest = MessageDigest.getInstance("md5");

             // 调用加密对象的方法，加密的动作已经完成
             byte[] bs = digest.digest(pwd.getBytes("utf-8"));
             // 接下来，我们要对加密后的结果，进行优化，按照mysql的优化思路走
             // mysql的优化思路：
             // 第一步，将数据全部转换成正数：
             String hexString = "";
             for (byte b : bs) {
                 // 第一步，将数据全部转换成正数：
                 // 解释：为什么采用b&255
                 /*
                  * b:它本来是一个byte类型的数据(1个字节) 255：是一个int类型的数据(4个字节)
                  * byte类型的数据与int类型的数据进行运算，会自动类型提升为int类型 eg: b: 1001 1100(原始数据)
                  * 运算时： b: 0000 0000 0000 0000 0000 0000 1001 1100 255: 0000
                  * 0000 0000 0000 0000 0000 1111 1111 结果：0000 0000 0000 0000
                  * 0000 0000 1001 1100 此时的temp是一个int类型的整数
                  */
                 int temp = b & 255;
                 // 第二步，将所有的数据转换成16进制的形式
                 // 注意：转换的时候注意if正数>=0&&<16，那么如果使用Integer.toHexString()，可能会造成缺少位数
                 // 因此，需要对temp进行判断
                 if (temp < 16 && temp >= 0) {
                     // 手动补上一个“0”
                     hexString = hexString + "0" + Integer.toHexString(temp);
                 } else {
                     hexString = hexString + Integer.toHexString(temp);
                 }
             }
             return hexString;
         } catch (Exception e) {
             // TODO Auto-generated catch block
             log.error("MD5加密失败",e);
         }
		 return "";
     }

     /**
 	 * 时间戳
 	 * @return
 	 */
 	public static String genTimeStamp() {
 	  	long currentTimeMillis = System.currentTimeMillis();//生成时间戳
 	  	long second = currentTimeMillis / 1000L;//（转换成秒）
 		String seconds = String.valueOf(second).substring(0, 10);//（截取前10位）
 	    return seconds;
 	}

 	 /***
	 * 随机字符串
	 * @param length
	 * @return
	 */
    public static String getRandomString(int length) { //length表示生成字符串的长度
        String base = "abcdefghijklmnopqrstuvwxyz0123456789";
        Random random = new Random();
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < length; i++) {
            int number = random.nextInt(base.length());
            sb.append(base.charAt(number));
        }
        return sb.toString();
     }


    /**
	 * 获取当前的IP,这个方法在阿里云服务器上返回的是本地地址
	 * @return
	 * @throws UnknownHostException
	 */
	public static String getIpAddr() {
		HttpServletRequest request = ((ServletRequestAttributes)RequestContextHolder.getRequestAttributes()).getRequest();
		String ip = request.getLocalAddr();
		//String ip = InetAddress.getLocalHost().getHostAddress();(获取内网IP)
		 return ip;
	}

	public static  String getIpAddr2(HttpServletRequest request) {
	    String ip = request.getHeader("x-forwarded-for");
	    if(ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
	        ip = request.getHeader("Proxy-Client-IP");
	    }
	    if(ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
	        ip = request.getHeader("WL-Proxy-Client-IP");
	    }
	    if(ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
	        ip = request.getRemoteAddr();
	    }
	    return ip;
	}

}
