package org.haier.shopUpdate.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;

import java.util.Date;
import java.util.regex.Pattern;

public class TimeUtil {

    // 匹配含有时分秒的时间字符串的正则表达式
    private static final Pattern TIME_WITH_HMS_PATTERN = Pattern.compile("\\d{2}:\\d{2}:\\d{2}");



    public static String fullTimeString(String dateTimeStr) {
        return fullTimeString(dateTimeStr, "");
    }

    private static String fullTimeString(String dateTimeStr,String timeStr) {
       if(hasHourMinuteSecond(dateTimeStr)){
           return dateTimeStr;
       };
        if (StrUtil.isEmpty(timeStr)) {
            timeStr = " 23:59:59";
        }else if (!StrUtil.startWith(timeStr, " ")){
            timeStr =" "+timeStr;
        }
        String combinedStr = dateTimeStr + timeStr;
            // 将组合后的字符串解析为日期时间对象
        Date dateTime = DateUtil.parseDateTime(combinedStr);
        // 将日期时间对象格式化为指定格式的字符串
        return DateUtil.formatDateTime(dateTime);
    }

    public static boolean hasHourMinuteSecond(String timeStr) {
        if (StrUtil.isBlank(timeStr)) {
            return false;
        }
        // 使用正则表达式匹配
        return TIME_WITH_HMS_PATTERN.matcher(timeStr).find();
    }

    public static void main(String[] args) {
        String timeStr1 = "2025-02-27 12:30:00";
        String timeStr2 = "2025-02-27";
        String timeStr3 = "23:59:59 00:00:00";

        boolean result1 = hasHourMinuteSecond(timeStr1);
        boolean result2 = hasHourMinuteSecond(timeStr2);
        boolean result3 = hasHourMinuteSecond(timeStr3);

        System.out.println("时间字符串 " + timeStr1 + " 是否含有时分秒: " + result1);
        System.out.println("时间字符串 " + timeStr2 + " 是否含有时分秒: " + result2);
        System.out.println("时间字符串 " + timeStr3 + " 是否含有时分秒: " + result3);
    }

}
