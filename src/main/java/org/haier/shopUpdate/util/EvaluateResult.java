package org.haier.shopUpdate.util;


/**
 * 类名:com.palmshop.online.entity.PalmResult;
 * 描述:json返回结果的封装类
 * <AUTHOR>
 *
 */
public class EvaluateResult {

	
	private int status;// 状态字：0-失败；1-成功
	private String msg;// 消息说明
	private int totals;// 查询的数据条数
	
	private int perPageNum;//查询的每一页返回的数量
	
	private Object data;// 结果数据
	
	private Object scoreList;//评分列表
	private Double averageScore;//平均分
	
	
	public EvaluateResult() {
		super();
		this.data=0;
		this.totals=0;
		this.status=0;
		this.perPageNum=0;
		this.msg="";
		
	}
	public int getStatus() {
		return status;
	}
	public void setStatus(int status) {
		this.status = status;
	}
	public String getMsg() {
		return msg;
	}
	public void setMsg(String msg) {
		this.msg = msg;
	}
	public int getTotals() {
		return totals;
	}
	public void setTotals(int totals) {
		this.totals = totals;
	}
	public Object getData() {
		return data;
	}
	public void setData(Object data) {
		this.data = data;
	}
	/**
	 * @return the perPageNum
	 */
	public int getPerPageNum() {
		return perPageNum;
	}
	/**
	 * @param perPageNum the perPageNum to set
	 */
	public void setPerPageNum(int perPageNum) {
		this.perPageNum = perPageNum;
	}
	
	public Object getScoreList() {
		return scoreList;
	}
	public void setScoreList(Object scoreList) {
		this.scoreList = scoreList;
	}
	public Double getAverageScore() {
		return averageScore;
	}
	public void setAverageScore(Double averageScore) {
		this.averageScore = averageScore;
	}
	/* (non-Javadoc)
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		return "PalmResult [status=" + status + ", msg=" + msg + ", totals=" + totals + ", perPageNum=" + perPageNum + ", data=" + data + "]";
	}
}
