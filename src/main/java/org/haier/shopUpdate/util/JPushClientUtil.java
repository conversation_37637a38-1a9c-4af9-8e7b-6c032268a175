package org.haier.shopUpdate.util;

import java.util.HashMap;
import java.util.Map;

import cn.jiguang.common.ClientConfig;
import cn.jiguang.common.resp.APIConnectionException;
import cn.jiguang.common.resp.APIRequestException;
import cn.jpush.api.JPushClient;
import cn.jpush.api.push.PushResult;
import cn.jpush.api.push.model.Platform;
import cn.jpush.api.push.model.PushPayload;
import cn.jpush.api.push.model.PushPayload.Builder;
import cn.jpush.api.push.model.audience.Audience;
import cn.jpush.api.push.model.notification.Notification;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class JPushClientUtil {
	public static void main(String[] args) {
		Map<String, String> params = new HashMap<String,String>();
		//notifyApp("1a1018970af5f6467c4","新订单",1,"999999");
		params.put("type", "3");
	}
	public static void  notifyAndoidPos(String registrationId,String alert,Integer registration_phone_type, String outTradeNo){
		JPushClient jpushClient = new JPushClient("c5a0380339c1539d37d7e99b", "4b3943dfc6e97847952d386e", null, ClientConfig.getInstance());
//		JPushClient jpushClient = new JPushClient("f0698de2627e1359f0f9adb4", "456e15c58d91e08b4d1c2b05", null, ClientConfig.getInstance());

		// For push, all you need do is to build PushPayload object.
		PushPayload payload = buildPushObject_android_tag_alertWithTitle(registrationId,alert,"百货商家端",registration_phone_type,outTradeNo);

		try {
			PushResult result = jpushClient.sendPush(payload);
			System.out.println("Got result - " + result);

		} catch (APIConnectionException e) {
			System.out.println("Connection error, should retry later"+ e);
			log.error("Connection error, should retry later"+ e);
		} catch (APIRequestException e) {
			System.out.println("Should review the error, and fix the request"+ e);
			System.out.println("HTTP Status: " + e.getStatus());
			System.out.println("Error Code: " + e.getErrorCode());
			System.out.println("Error Message: " + e.getErrorMessage());
			log.error("异常错误，异常信息：",e);
		}catch (Exception e) {
			log.error("异常错误，异常信息：",e);
		}
	}
    public static PushPayload buildPushObject_android_tag_alertWithTitle2(String registrationId,String alert,String title, Map<String,String> params) {
		Builder  builder= PushPayload.newBuilder();

			builder.setPlatform(Platform.android());
			builder.setNotification(Notification.android(alert, title, params));
			builder.setAudience(Audience.registrationId(registrationId));
			return builder.build();
}

    public static PushPayload buildPushObject_android_tag_alertWithTitle(String registrationId,String alert,String title,Integer registration_phone_type, String outTradeNo) {
    		Builder  builder= PushPayload.newBuilder();
    		if(registration_phone_type==1){
    			builder.setPlatform(Platform.ios());
    			Map<String,String> params=new HashMap<String,String>();
    			params.put("sale_list_unique", outTradeNo);
    			builder.setNotification(Notification.ios(alert,params));
    		}else{
    			builder.setPlatform(Platform.android());
    			Map<String,String> params=new HashMap<String,String>();
    			params.put("sale_list_unique", outTradeNo);
    			builder.setNotification(Notification.android(alert, title, params));
    		}
    		builder.setAudience(Audience.registrationId(registrationId));

    		return builder.build();
    }
}
