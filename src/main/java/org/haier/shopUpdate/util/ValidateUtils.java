package org.haier.shopUpdate.util;

import org.hibernate.validator.HibernateValidator;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.Set;

/**
 * 基于hibernate的校验工具
 */
public class ValidateUtils {

    private static Validator validator = Validation.byProvider(HibernateValidator.class).configure().failFast(true).buildValidatorFactory().getValidator();

    private ValidateUtils(){}

    public static <T> Result validate(T obj) {
        Result result = new Result();
        Set<ConstraintViolation<T>> cvs = validator.validate(obj);
        if (!cvs.isEmpty()) {
            String msg = cvs.iterator().next().getMessage();
            result.setMessage(msg);
            result.setHasError(true);
        }
        return result;
    }

    public static class Result{
        private boolean hasError = false;
        private String message;

        public boolean isHasError() {
            return hasError;
        }

        public void setHasError(boolean hasError) {
            this.hasError = hasError;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }
    }
}
