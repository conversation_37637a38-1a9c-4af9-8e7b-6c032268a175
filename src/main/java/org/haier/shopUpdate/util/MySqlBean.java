package org.haier.shopUpdate.util;

import java.io.IOException;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.ErrorContext;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.core.NestedIOException;
@Slf4j
public class MySqlBean extends org.mybatis.spring.SqlSessionFactoryBean   {

	@Override
	protected SqlSessionFactory buildSqlSessionFactory() throws IOException {
		try {
		    return super.buildSqlSessionFactory();
		} catch (NestedIOException e) {
		    // XML 有错误时打印异常。
			log.error("生成sqlSessionFactory失败",e);
			throw new NestedIOException("Failed to parse mapping resource: '" + "mapper文件" + "'", e);
		} finally {
		    ErrorContext.instance().reset();
		}
	}
}
