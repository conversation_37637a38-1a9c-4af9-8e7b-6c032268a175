package org.haier.shopUpdate.util;

import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Calendar;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;

import lombok.extern.slf4j.Slf4j;
import org.haier.shopUpdate.entity.CusCheckout;

import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

/**
 * 图片二进制转换，和前端约定采用
 *
 * <AUTHOR>
 *
 */
@Slf4j
public class ImageBinary {
	public static BASE64Encoder encoder = new sun.misc.BASE64Encoder();
	public static BASE64Decoder decoder = new sun.misc.BASE64Decoder();

    /**
     * 将图片转换成二进制
     *
     * @return
     */
	public static String getImageBinary(String  filePath,String fileType) {
        File f = new File(filePath);
        BufferedImage bi;
        try {
            bi = ImageIO.read(f);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(bi, fileType, baos);  //经测试转换的图片是格式这里就什么格式，否则会失真
            byte[] bytes = baos.toByteArray();
            return encoder.encodeBuffer(bytes).trim();
        } catch (IOException e) {
            log.error("图片转换二进制失败",e);
        }
        return null;
    }

    /**
     * 将二进制转换为图片
     *
     * @param base64String
     */
	public static boolean base64StringToImage(String base64String,String filePath,String fileName,String fileType) {
		base64String=base64String.replace(" ", "+");

		System.out.println(fileName);
        try {
        	byte[] bytes1 = decoder.decodeBuffer(base64String);
        	ByteArrayInputStream bais = new ByteArrayInputStream(bytes1);
            BufferedImage bi1 = ImageIO.read(bais);
            File w1 = new File(filePath);// 可以是jpg,png,gif格式
            if (!w1.exists()) {
				w1.mkdirs();//当前路径不存在则创建
			}
            System.out.println(filePath+fileName);
            File w2=new File(filePath+fileName);
            if(!w2.exists()){
            	w2.createNewFile();
            }
            ImageIO.write(bi1, fileType, w2);// 不管输出什么格式图片，此处不需改动
        } catch (IOException e) {
            log.error("图片转换二进制失败",e);
            return false;
        }
        return true;
    }

	/**
	 * 创建一条图片保存路径信息
	 */
	public static String mysqlPath(String imgFormat){
		String filePath=ConfigForFace.PORTRAITPATH;
		Calendar now=Calendar.getInstance();
		String fileName=now.getTimeInMillis()+"."+imgFormat;
		return filePath+fileName;
	}
	/**
	 * 将传入的信息转换成文件信息m,并返回用于数据保存的绝对 路径
	 */
	public static String savePicture(CusCheckout checkout,HttpServletRequest request){
		String catPath=request.getServletContext().getRealPath("");//项目所在绝对路径
		catPath=new File(catPath).getParent();
		String path=checkout.getCusPicPath();
		System.out.println("图片的保存路径：：："+path);
//		path=path.replace("\\", "/");
		String fileName=path.substring(path.lastIndexOf(File.separator)+1);
		String filePath=catPath+File.separator+path.substring(0, path.lastIndexOf(File.separator)+1);//图片实际保存的绝对路径
		System.out.println("传入的保存路径为"+filePath);
		boolean flag=ImageBinary.base64StringToImage(checkout.getImageMsg(), filePath,fileName, checkout.getImgFormat());
		if(!flag){
			return null;
		}else{
			return filePath+fileName;
		}
	}

	/**
	 * @Description: 根据图片地址转换为base64编码字符串
	 * @Author:
	 * @CreateTime:
	 * @return
	 */
	public static String getImageStr(InputStream inputStream) {
	    byte[] data = null;
	    try {
	        data = new byte[inputStream.available()];
	        inputStream.read(data);
	        inputStream.close();
	    } catch (IOException e) {
	        log.error("图片转换二进制失败",e);
	    }
	    // 加密
	    BASE64Encoder encoder = new BASE64Encoder();
	    return encoder.encode(data);
	}
}
