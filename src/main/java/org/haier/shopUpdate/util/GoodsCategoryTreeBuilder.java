package org.haier.shopUpdate.util;

import org.haier.shopUpdate.entity.ShopsGroupGoodsKinds;
import org.haier.shopUpdate.entity.ShopsKindGoodsKinds;

import java.util.*;

public class GoodsCategoryTreeBuilder {

//    public static List<ShopsGroupGoodsKinds> buildCategoryTree(List<ShopsGroupGoodsKinds> flatList) {
//
//        if (flatList == null || flatList.isEmpty()) {
//            return Collections.emptyList();
//        }
//
//        // 构建缓存 map，便于后面查找 parent
//        Map<String, ShopsGroupGoodsKinds> nodeMap = new HashMap<>();
//        for (ShopsGroupGoodsKinds node : flatList) {
//            nodeMap.put(node.getGroupUnique(), node);
//            node.setKindDetail(new ArrayList<>()); // 初始化 children
//        }
//
//        // 最终的根节点集合（一级分类）
//        List<ShopsGroupGoodsKinds> rootList = new ArrayList<>();
//
//        // 遍历所有节点，挂载到对应父节点上
//        for (ShopsGroupGoodsKinds node : flatList) {
//            String parentUnique = node.getParentUnique();
//
//            if (parentUnique == null || "0".equals(parentUnique)) {
//                // 一级分类
//                rootList.add(node);
//            } else {
//                // 找到父节点
//                ShopsGroupGoodsKinds parent = nodeMap.get(parentUnique);
//                if (parent != null) {
//                    parent.getKindDetail().add(node);
//                } else {
//                    // 父节点不存在，也可以选择忽略或记录日志
//                    rootList.add(node); // 可选：当作一级节点处理
//                }
//            }
//        }
//
//        return rootList;
//    }

    public static List<ShopsGroupGoodsKinds> buildCategoryTree(List<ShopsGroupGoodsKinds> groupList, List<ShopsKindGoodsKinds> kindList) {

        if (groupList == null || groupList.isEmpty()) {
            return Collections.emptyList();
        }

        // Step 1: 找出所有一级分类（parentUnique == null 或 "0"）
        List<ShopsGroupGoodsKinds> rootGroups = new ArrayList<>();
        for (ShopsGroupGoodsKinds group : groupList) {
            if (group.getParentUnique() == null || "0".equals(group.getParentUnique())) {
                rootGroups.add(group);
            }
        }

        if (kindList == null || kindList.isEmpty()) {
            // 如果没有子分类，直接返回一级分类，kindDetail 为空
            for (ShopsGroupGoodsKinds group : rootGroups) {
                group.setKindDetail(new ArrayList<>());
            }
            return rootGroups;
        }

        // Step 2: 构建 groupUnique -> List<ShopsKindGoodsKinds> 映射
        Map<String, List<ShopsKindGoodsKinds>> childrenMap = new HashMap<>();
        for (ShopsKindGoodsKinds kind : kindList) {
            childrenMap.computeIfAbsent(kind.getGroupUnique(), k -> new ArrayList<>()).add(kind);
        }

        // Step 3: 为每个一级分类填充 kindDetail（第一层子节点 + 构建完整子树）
        for (ShopsGroupGoodsKinds group : rootGroups) {
            String groupUnique = group.getGroupUnique();
            List<ShopsKindGoodsKinds> firstLevelChildren = buildSubTree(groupUnique, childrenMap);
            group.setKindDetail(firstLevelChildren);
        }

        return rootGroups;
    }

    private static List<ShopsKindGoodsKinds> buildSubTree(
            String parentGroupUnique,
            Map<String, List<ShopsKindGoodsKinds>> childrenMap) {

        List<ShopsKindGoodsKinds> children = childrenMap.getOrDefault(parentGroupUnique, Collections.emptyList());

        for (ShopsKindGoodsKinds child : children) {
            List<ShopsKindGoodsKinds> subChildren = buildSubTree(child.getKindUnique(), childrenMap);
            child.setKindDetail(subChildren);
        }

        return children;
    }
}