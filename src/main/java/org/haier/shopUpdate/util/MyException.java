package org.haier.shopUpdate.util;
/**
* @author: 作者:王恩龙
* @version: 2020年11月20日 上午9:19:33
*
*/
public class MyException extends RuntimeException{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	private Integer status;
	private String msg;
	
	public MyException() {
		super();
	}

	public MyException(Integer status, String msg) {
		super();
		this.status = status;
		this.msg = msg;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}
}
