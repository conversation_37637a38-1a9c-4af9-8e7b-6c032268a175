package org.haier.shopUpdate.util;

import java.util.Map;

import com.baidu.yun.core.log.YunLogEvent;
import com.baidu.yun.core.log.YunLogHandler;
import com.baidu.yun.push.auth.PushKeyPair;
import com.baidu.yun.push.client.BaiduPushClient;
import com.baidu.yun.push.constants.BaiduPushConstants;
import com.baidu.yun.push.exception.PushClientException;
import com.baidu.yun.push.exception.PushServerException;
import com.baidu.yun.push.model.PushMsgToSingleDeviceRequest;
import com.baidu.yun.push.model.PushMsgToSingleDeviceResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * 对订单实现不同操作时，向不同用户推送消息
 * <AUTHOR>
 */
@Slf4j
public class BadiDuPushUtil {

	public static boolean pushMessageToBuy(Map<String,Object> map){
		String channelId=map.get("channelId").toString();
		Integer msgExpires = 18000;// 推送消息有效期，单位为S
		Integer appType=Integer.parseInt(map.get("appType").toString());

		Integer deviceType = Integer.parseInt(map.get("cusSource").toString()) + 2;//登录来源（3:ANDRIOD;4:IOS)
		String apiKey = null, secretKey = null;
		Integer deployStatus = Integer.parseInt(PushConfig.BIOSDEVELOPSTATUS);
		String pushMessage=map.get("pushMessage").toString();

		if(1==appType){
			if(deviceType==3){//ANDRIOD设备推送
				apiKey=PushConfig.BANDRIODAPIKEY;
				secretKey=PushConfig.BANDRIODSECRETKEY;
			}else if(deviceType==4){//IOS推送
				apiKey=PushConfig.BIOSBAPIKEY;
				secretKey=PushConfig.BIOSSECRETKEY;
			}else if(deviceType==2){//微信推送
			}
		}else if(3==appType){
			if(deviceType==3){
				apiKey=PushConfig.PANDRIODAPIKEY;
				secretKey=PushConfig.PANDRIODSECRETKEY;
			}else if(deviceType==4){
				apiKey=PushConfig.PIOSAPIKEY;
				secretKey=PushConfig.PIOSSECRETKEY;
			}
		}
		/*
		 * 创建百度推送
		 */
		PushKeyPair pair = new PushKeyPair(apiKey, secretKey);
		BaiduPushClient pushClient = new BaiduPushClient(pair, BaiduPushConstants.CHANNEL_REST_URL);

		pushClient.setChannelLogHandler(new YunLogHandler() {//推送后打印推送结果
			public void onHandle(YunLogEvent event) {
				System.out.println(event.getMessage());
			}
		});
		try {
			PushMsgToSingleDeviceRequest request = new PushMsgToSingleDeviceRequest().addChannelId(channelId)
					.addMessageType(1).addDeviceType(deviceType);
			request.addMsgExpires(msgExpires);
			System.out.println(deviceType);
			if (deviceType == 4) {//IOS推送
				request.addDeployStatus(deployStatus).addMessage(pushMessage);
			} else if (deviceType == 3) {//ANDRIOD推送
	//			pushMessage="{\"title\":\"TEST\",\"description\":\"Hello Baidu push!\"}";
			request.addMessage(pushMessage.toString());
			}
			PushMsgToSingleDeviceResponse response;
			response = pushClient.pushMsgToSingleDevice(request);
			System.out.println("msgId" + response.getMsgId() + "::::" + "SendTime" + response.getSendTime());
		} catch (Exception e) {
			log.error("推送失败",e);
			return false;
		}
		return true;
	}
}
