package org.haier.shopUpdate.util.mqtt;

import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.*;
import org.haier.shopUpdate.redism.RedisCache;

/**
* @author: 作者:王恩龙
* @version: 2022年8月25日 上午10:55:51
*
*/
@Slf4j
public class PushCallback implements MqttCallback{
	private MqttClient client;
	private MqttConnectOptions options;

	private RedisCache redis;

	public PushCallback() {
	}

	public PushCallback(RedisCache redis) {
		this.redis = redis;
	}

	public PushCallback(MqttClient client,MqttConnectOptions options) {
		this.client = client;
		this.options = options;
	}

	public void connectionLost(Throwable cause) {
        // 连接丢失后，一般在这里面进行重连
        System.out.println("连接断开，可以做重连");
        if(null != client && null != options) {
        	 try {
                 System.out.println("开始重连");
                 Thread.sleep(3000);
                 client.connect(options);
             } catch (Exception e) {
                 log.error("异常信息：",e);
             }
        }
    }

    public void deliveryComplete(IMqttDeliveryToken token) {
        System.out.println("deliveryComplete---------" + token.isComplete());
    }

    public void messageArrived(String topic, MqttMessage message) throws Exception {
        // subscribe后得到的消息会执行到这里面
//        System.out.println("接收消息主题 : " + topic);
//        System.out.println("接收消息Qos : " + message.getQos());
//        System.out.println("接收消息内容 : " + new String(message.getPayload()));

        //所有接受的消息统一由shopmanager项目处理，不再其他地方再做处理
    }

}
