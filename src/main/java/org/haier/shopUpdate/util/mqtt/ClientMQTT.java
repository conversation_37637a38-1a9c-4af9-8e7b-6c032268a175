package org.haier.shopUpdate.util.mqtt;

import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.eclipse.paho.client.mqttv3.MqttTopic;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.haier.shopUpdate.project.ProjectConfig;
import org.haier.shopUpdate.redism.RedisCache;
import org.haier.shopUpdate.util.helibao.IPGet;

import java.util.ResourceBundle;
/**
* @author: 作者:王恩龙
* @version: 2022年8月25日 上午10:53:50
*
*/
@Slf4j
public class ClientMQTT {
	public static final String HOST;
    public static final String TOPICMAIN;
    public static final String TOPIC;
    private static final String clientid;
    public static MqttClient client;
    private MqttConnectOptions options;
    public static final String userName;
    public static final String passWord;

    public static RedisCache redis;

    static
    {
    	IPGet.getIp();
    	HOST = ProjectConfig.MQTT_HOST;
        TOPICMAIN = ProjectConfig.MQTT_TOPICMAIN;
    	TOPIC = ProjectConfig.MQTT_TOPIC;
    	clientid = ProjectConfig.MQTT_CLIENTID;
        userName=ProjectConfig.MQTT_USERNAME;
        passWord=ProjectConfig.MQTT_PASSWORD;
    }
    public ClientMQTT() {

    }
    public ClientMQTT(RedisCache redis) {
    	ClientMQTT.redis = redis;
    }

    public void start() {
        try {
            // host为主机名，clientid即连接MQTT的客户端ID，一般以唯一标识符表示，MemoryPersistence设置clientid的保存形式，默认为以内存保存
            client = new MqttClient(HOST, clientid , new MemoryPersistence());
            // MQTT的连接设置
            options = new MqttConnectOptions();
            // 设置是否清空session,这里如果设置为false表示服务器会保留客户端的连接记录，设置为true表示每次连接到服务器都以新的身份连接
            options.setCleanSession(false);
            // 设置连接的用户名
            options.setUserName(userName);
            // 设置连接的密码
            options.setPassword(passWord.toCharArray());
            // 设置超时时间 单位为秒
            options.setConnectionTimeout(10);
            // 设置会话心跳时间 单位为秒 服务器会每隔1.5*20秒的时间向客户端发送个消息判断客户端是否在线，但这个方法并没有重连的机制
            options.setKeepAliveInterval(20);
            //设置断开后重新连接
            options.setAutomaticReconnect(true);
            // 设置回调
            client.setCallback(new PushCallback(redis));
            MqttTopic topic = client.getTopic(TOPICMAIN);
            //setWill方法，如果项目中需要知道客户端是否掉线可以调用该方法。设置最终端口的通知消息
            //遗嘱
            options.setWill(topic, "close".getBytes(), 1, true);
            client.connect(options);
            //订阅消息
            int[] Qos = {2};//0：最多一次 、1：最少一次 、2：只有一次
            String[] topic1 = {TOPICMAIN};
            client.subscribe(topic1, Qos);

        } catch (Exception e) {
            log.error("异常信息：",e);
        }
    }


    public void sendMsg(String msg,String topic) {
    	try {
    		MqttMessage message = new MqttMessage(msg.getBytes());
    		message.setQos(1);
    		client.publish(TOPIC + topic, message);
    	}catch (Exception e) {
    		log.error("异常信息：",e);
		}
    }
}
