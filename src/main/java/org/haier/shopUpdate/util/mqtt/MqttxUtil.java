package org.haier.shopUpdate.util.mqtt;
/**
* @author: 作者:王恩龙
* @version: 2022年8月25日 下午5:47:02
*
*/

import java.util.Map;

import javax.annotation.PostConstruct;

import org.haier.shopUpdate.redism.RedisCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import net.sf.json.JSONObject;

@Component
public class MqttxUtil {
	@Autowired
	private RedisCache rc;
	
	public static ClientMQTT cm = new ClientMQTT();
	
	@PostConstruct
	public void startCM() {
		System.out.println("mqttxUtil" + rc == null);
		if(null != rc) {
			ClientMQTT.redis = rc;
		}
		cm.start();
	}
	
	public static void sendMsg(String msg,String macId) {
		cm.sendMsg(msg,macId);
	}
	
	public static void sendMapMsg(Map<String,Object> map,String macId) {
		JSONObject jo = JSONObject.fromObject(map);
		sendMsg(jo.toString(),macId);
	}
}
