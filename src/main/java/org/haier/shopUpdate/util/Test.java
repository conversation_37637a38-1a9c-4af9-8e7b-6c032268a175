package org.haier.shopUpdate.util;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

import javax.imageio.ImageIO;

import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;


/**
 * 测试用类
 * <AUTHOR>
 *
 */
@Slf4j
public class Test {
	public static void main(String[] args) {
//		String path = "D:\\web\\img\\1542350609530\\690324437097410.png";
//		testPic(path, 400,1);

		for(int i = 0; i < 10; i++) {
			System.out.println(i);
		}

	}

	public static void testPic(String path,Integer size,Integer type) {
		if(null == size ) {
			size =400;
		}
		File f = new File(path);
		if(f.isFile()) {
			if(path.endsWith(".png") || path.endsWith(".jpeg") || path.endsWith(".jpg")) {
				if(type == 1) {
					System.out.println(f.length());
				}else {
					if(f.length()> 300 * 1024) {
						Test.changePic(path, path, size);
					}
				}
			}
		}else if(f.isDirectory()) {
			File[] fl = f.listFiles();
			for(int i = 0 ; i<fl.length;i++) {
				String subPath = fl[i].getAbsolutePath();
				testPic(subPath, size,type);
			}
		}
	}

	/**
	 * 修改
	 * @param path
	 * @param newPath
	 * @param size
	 */
	public static void changePic(String path,String newPath,Integer size) {
		if(null == size ) {
			size =400;
		}
		InputStream is = null;//获取源文件流
		BufferedImage prevImage = null;
		try {
			is = new FileInputStream(new File(path));
			prevImage = ImageIO.read(is);
			double width = prevImage.getWidth();//源文件的宽度
			double height = prevImage.getHeight();//源文件的高度
			if(width <= 400 || height <= 400) {
				return;
			}
			double percent = size / width;
			int newHeight = Integer.parseInt((height * percent + "").substring(0, (height * percent + "").indexOf(".")));
			System.out.println("设置的宽为" + size + " " + "高为" + newHeight);
			Thumbnails.of(path).size(size, newHeight).toFile(newPath);

		}catch (Exception e) {
			log.error("异常信息：",e);
		}finally {
			try {
				is.close();
			} catch (Exception e2) {
				log.error("异常信息：",e2);
			}
		}
	}
	public static Date transferString2Date(String s) {
	    Date date = new Date();
	    try {
	        date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(s);
	    } catch (ParseException e) {
	        //LOGGER.error("时间转换错误, string = {}", s, e);
	    }
	    return date;
	}
}
