package org.haier.shopUpdate.util;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonParseException;

import org.json.JSONObject;

import java.lang.reflect.Type;
import com.baidu.aip.face.AipFace;

/**
 * 百度人工认证功能 https://ai.baidu.com/docs#/Face-Java-SDK/be1a8e76
 * 
 * <AUTHOR>
 *
 */
public class BaiDuFace {
	// 设置APPID/AK/SK
	public static final String APP_ID = "14697049";
	public static final String API_KEY = "1E2rEGWNmWRAv7CnKogQPn7Z";
	public static final String SECRET_KEY = "gMGPmzEWxHly4PX9azeBi1yj205iykvp";
	// 获取AccessToken的API
	public static final String ACCESSTOKENPATH = "https://aip.baidubce.com/oauth/2.0/token";
	public static String ACCESSTOKEN = "";
	public static final String GRANTTYPE = "client_credentials";
	public static AipFace client=new AipFace(BaiDuFace.APP_ID, BaiDuFace.API_KEY, BaiDuFace.SECRET_KEY);
	private static Gson gson = new GsonBuilder().create();

	/**
	 * 创建用户组 type说明： 1、添加用户组； 2、删除用户组（慎用); 3、获取用户组列表信息； 4、添加新用户； 5、查询用户信息；
	 * 6、删除用户； 7、更新用户头像信息； 8、人脸信息删除； 9、获取当前用户人脸信息； 10、身份认证、暂无权限
	 * 11、获取该groupId下所有用户信息 12、检索相同人脸信息 map必包含字段：groupId,用户组编号；
	 * 
	 * @param client
	 * @param map
	 *            groupId
	 * @return
	 * 
	 * 		参数补充说明： group_id:用户组编号； user_id:用户编号 image:图片信息，
	 *         image_type:图片类型；BASE64、URL、FACE_TOKEN face_token：用户头像信息在库里的唯一识别符；
	 *         group_id_list:用户查询时，需要检索的用户组编号，用","隔开
	 *         max_face_num：最多处理的人脸数量，人脸检测时，图片中人脸的数量识别； max_user_num：人脸搜索时，返回数量；
	 *         quality_control：图片质量控制NONE: 不进行控制 LOW:较低的质量要求 NORMAL: 一般的质量要求
	 *         HIGH: 较高的质量要求 默认 NONE liveness_control：活体检测控制：NONE: 不进行控制
	 *         LOW:较低的活体要求(高通过率 低攻击拒绝率) NORMAL: 一般的活体要求(平衡的攻击拒绝率, 通过率) HIGH:
	 *         较高的活体要求(高攻击拒绝率 低通过率) 默认NONE user_info：用户资料；新增用户或更新用户时传入；限制256B
	 *         start:起始序号，查询用户组列表或用户组中用户的列表时用配合length length:返回数量
	 * 
	 */
	public static JSONObject sample( HashMap<String, String> map) {
		String groupId = map.get("groupId") == null ? "1000" : map.get("groupId");
		String userId = map.get("userId");
		String image = map.get("image");
		String imageType = map.get("imageType") == null ? "URL" : map.get("imageType");
		String groupIdList = map.get("groupIdList") == null ? "1000" : map.get("groupIdList");
		String faceToken = map.get("faceToken");
		String type = map.get("type");
		String idCardNumber = map.get("idCardNumber");
		String name = map.get("name");
		
		if (type.equals("1")) {
			return client.groupAdd(groupId, map);
		} else if (type.equals("2")) {
			return client.groupDelete(groupId, map);
		} else if (type.equals("3")) {
			return client.getGroupList(map);
		} else if (type.equals("4")) {
			return client.addUser(image, imageType, groupId, userId, map);
		} else if (type.equals("5")) {
			map.put("quality_control", "NORMAL");
			map.put("liveness_control", "LOW");
			map.put("max_user_num", "1");
			return client.search(image, imageType, groupIdList, map);
		} else if (type.equals("6")) {
			return client.deleteUser(groupId, userId, map);
		} else if (type.equals("7")) {
			return client.updateUser(image, imageType, groupId, userId, map);
		} else if (type.equals("8")) {
			return client.faceDelete(userId, groupId, faceToken, map);
		} else if (type.equals("9")) {
			return client.faceGetlist(userId, groupId, map);
		} else if (type.equals("10")) {
			return client.personVerify(image, imageType, idCardNumber, name, map);
		} else if (type.equals("11")) {
			return client.getGroupUsers(groupIdList, map);
		} else if (type.equals("12")) {
			map.put("max_face_num", "1");// 返回的最多人脸数量
			map.put("face_field", "age,gender");// 添加返回年龄和性别功能
			return client.detect(image, imageType, map);
		}
		return null;
	}
	/**
	 * 获取百度认证的ACCESSTOKEN值
	 */
	public static String getBaiDuAccessToken() {
		System.out.println("获取ACCESSTOKEN");
		String paramString = "grant_type=GRANTTYPE&client_id=" + API_KEY + "&SECRET_KEY=" + SECRET_KEY;
		String res = UtilForRequest.doGet(ACCESSTOKENPATH, paramString);
		return res;
	}

	/**
	 * 以下为工具类
	 * 
	 * @param requestUrl
	 * @param accessToken
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public static String post(String requestUrl, String accessToken, String params) throws Exception {
		String contentType = "application/x-www-form-urlencoded";
		return BaiDuFace.post(requestUrl, accessToken, contentType, params);
	}

	public static String post(String requestUrl, String accessToken, String contentType, String params)
			throws Exception {
		String encoding = "UTF-8";
		if (requestUrl.contains("nlp")) {
			encoding = "GBK";
		}
		return BaiDuFace.post(requestUrl, accessToken, contentType, params, encoding);
	}

	public static String post(String requestUrl, String accessToken, String contentType, String params, String encoding)
			throws Exception {
		String url = requestUrl + "?access_token=" + accessToken;
		return BaiDuFace.postGeneralUrl(url, contentType, params, encoding);
	}

	public static String postGeneralUrl(String generalUrl, String contentType, String params, String encoding)
			throws Exception {
		URL url = new URL(generalUrl);
		// 鎵撳紑鍜孶RL涔嬮棿鐨勮繛鎺�
		HttpURLConnection connection = (HttpURLConnection) url.openConnection();
		connection.setRequestMethod("POST");
		// 璁剧疆閫氱敤鐨勮姹傚睘鎬�
		connection.setRequestProperty("Content-Type", contentType);
		connection.setRequestProperty("Connection", "Keep-Alive");
		connection.setUseCaches(false);
		connection.setDoOutput(true);
		connection.setDoInput(true);

		// 寰楀埌璇锋眰鐨勮緭鍑烘祦瀵硅薄
		DataOutputStream out = new DataOutputStream(connection.getOutputStream());
		out.write(params.getBytes(encoding));
		out.flush();
		out.close();

		// 寤虹珛瀹為檯鐨勮繛鎺�
		connection.connect();
		// 鑾峰彇鎵�鏈夊搷搴斿ご瀛楁
		Map<String, List<String>> headers = connection.getHeaderFields();
		// 閬嶅巻鎵�鏈夌殑鍝嶅簲澶村瓧娈�
		for (String key : headers.keySet()) {
			System.err.println(key + "--->" + headers.get(key));
		}
		// 瀹氫箟 BufferedReader杈撳叆娴佹潵璇诲彇URL鐨勫搷搴�
		BufferedReader in = null;
		in = new BufferedReader(new InputStreamReader(connection.getInputStream(), encoding));
		String result = "";
		String getLine;
		while ((getLine = in.readLine()) != null) {
			result += getLine;
		}
		in.close();
//		System.err.println("result:" + result);
		return result;
	}

	public static String toJson(Object value) {
		return gson.toJson(value);
	}

	public static <T> T fromJson(String json, Class<T> classOfT) throws JsonParseException {
		return gson.fromJson(json, classOfT);
	}

	@SuppressWarnings("unchecked")
	public static <T> T fromJson(String json, Type typeOfT) throws JsonParseException {
		return (T) gson.fromJson(json, typeOfT);
	}

}
