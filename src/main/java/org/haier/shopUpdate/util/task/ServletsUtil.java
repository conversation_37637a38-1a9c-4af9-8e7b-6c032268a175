package org.haier.shopUpdate.util.task;

import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import java.util.WeakHashMap;

import javax.servlet.ServletRequest;

/**
 * 页面请求工具类
 * <AUTHOR>
 *
 */
public class ServletsUtil {
	/**
	 * 封装request请求参数
	 * @param request
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	public static Map<String, Object> getParameters(ServletRequest request) {
		if( null==request ){
			System.out.println("Request must not be null");
		}
		
		Enumeration paramNames = request.getParameterNames();
		Map<String, Object> params = new WeakHashMap<String, Object>();
		Integer page = null ,limit = null ;
		
		while ((paramNames != null) && paramNames.hasMoreElements()) {
			String paramName =  String.valueOf(paramNames.nextElement());
			String[] values = (String[])request.getParameterValues(paramName);
			if ((values == null) || (values.length == 0)) {
			} else if (values.length > 1) {
				params.put(paramName, values);
			} else {
				params.put(paramName, values[0]);
			}
			if("page".equals(paramName)) {
				page = Integer.parseInt(values==null?null:values.length==0?null:values.length==1?values[0]:"1");
			}
			if("limit".equals(paramName)) {
				limit = Integer.parseInt(values==null?null:values.length==0?null:values.length==1?values[0]:"0");
			}
		}
		if(page==null || limit ==null ||limit ==0) {
			
		}else {
			params.put("page", (page-1)*limit);
			params.put("limit", limit);
		}
		return params;
	}
	
	/**
	 * 格式化参数
	 * @param params
	 * @return
	 */
	public static String formatParams(Map<String, Object> params){
		StringBuffer buffer = new StringBuffer("&");
		for ( Map.Entry<String, Object> entry : params.entrySet()) {
			if( !"page".equalsIgnoreCase(entry.getKey()) && !"sortType".equals(entry.getKey()) ){
				buffer.append(entry.getKey() + "=" + entry.getValue()).append("&");
			}
		}
		return buffer.toString().length()>0 ? buffer.toString().substring(0, buffer.toString().lastIndexOf("&")) : "";
	}
	
	/**
	 * 格式化参数
	 * @param params
	 * @return
	 * @time yellow 2015年7月14日 下午1:42:08
	 */
	public static String formatSearchParams(Map<String, Object> params){
		StringBuffer buffer = new StringBuffer("&");
		for ( Map.Entry<String, Object> entry : params.entrySet()) {
			if( !"page".equalsIgnoreCase(entry.getKey()) ){
				buffer.append(entry.getKey() + "=" + entry.getValue()).append("&");
			}
		}
		return buffer.toString().length()>0 ? buffer.toString().substring(0, buffer.toString().lastIndexOf("&")) : "";
	}
	
	/**
	 * main方法测试
	 * @param args
	 */
	public static void main(String[] args) {
		Map<String, Object> params = new HashMap<String, Object>();
		params.put("name", null);
		params.put("password", "123456");
		System.out.println(formatParams(params));
	}
	
}
