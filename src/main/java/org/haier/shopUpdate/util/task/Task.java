package org.haier.shopUpdate.util.task;

import javax.annotation.Resource;

import org.haier.shopUpdate.dao.TestDao;
import org.haier.shopUpdate.util.helibao.IPGet;
import org.haier.shopUpdate.util.wechat.wxpush.WXPush;
import org.haier.shopUpdate.wechat.AuthUtil;

/**
 * 存放定时任务的类，请在注释中说明该定时任务的作用，谢谢
* @author: 作者:王恩龙
* @version: 2020年12月7日 上午8:54:04
*
*/
public class Task {
	
	@Resource
	private TestDao TestDao;
	/**
	 * 定时刷新微信ACCESSTOKEN
	 */
	public void refreshAccessToken() {
		WXPush.getToken();
	}
	
	
	public void addNewTestGoods() {
		TestDao.addNewTestGoods();
	}
	
}
