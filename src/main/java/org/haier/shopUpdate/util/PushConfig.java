package org.haier.shopUpdate.util;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Properties;
@Slf4j
public class PushConfig {
	//安卓购物端百度推送APIKEY
	public static String BANDRIODAPIKEY;
	//安卓购物端百度推送SECRETKEY
	public static String BANDRIODSECRETKEY;
	//IOS购物端百度推送APIKEY
	public static String BIOSBAPIKEY;
	//IOS购物端百度推送SECRETKEY
	public static String BIOSSECRETKEY;
	//IOS购物端百度推送开发状态
	public static String BIOSDEVELOPSTATUS;
	//IOS供货端百度推送APIKEY
	public static String PIOSAPIKEY;
	//IOS供货端百度推送SECRETKEY
	public static String PIOSSECRETKEY;
	//IOS供货端百度推送开发状态
	public static String PIOSDEPLOYSTATUS;
	//ANDRIOD供货端百度推送APIKEY
	public static String PANDRIODAPIKEY;
	//ANDRIOD供货端百度推送SECRETKEY
	public static String PANDRIODSECRETKEY;

	static{
		Properties p=new Properties();
		try {
			p.load(PushConfig.class.getClassLoader().getResourceAsStream("pushConfig.properties"));
			BANDRIODAPIKEY = p.getProperty("BANDRIODAPIKEY");
			BANDRIODSECRETKEY=p.getProperty("BANDRIODSECRETKEY");
			BIOSBAPIKEY=p.getProperty("BIOSBAPIKEY");
			BIOSSECRETKEY=p.getProperty("BIOSSECRETKEY");
			BIOSDEVELOPSTATUS=p.getProperty("BIOSDEVELOPSTATUS");
			PIOSAPIKEY=p.getProperty("PIOSAPIKEY");
			PIOSSECRETKEY=p.getProperty("PIOSSECRETKEY");
			PIOSDEPLOYSTATUS=p.getProperty("PIOSDEPLOYSTATUS");
			PANDRIODAPIKEY=p.getProperty("PANDRIODAPIKEY");
			PANDRIODSECRETKEY=p.getProperty("PANDRIODSECRETKEY");
		} catch (IOException e) {
			log.error("加载配置文件失败",e);
		}
	}
}
