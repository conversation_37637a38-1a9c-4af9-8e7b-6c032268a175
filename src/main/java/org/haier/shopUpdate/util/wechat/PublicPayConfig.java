package org.haier.shopUpdate.util.wechat;

import java.io.InputStream;

/**
* @author: 作者:王恩龙
* @version: 2020年11月18日 上午9:51:22
*
*/
public class PublicPayConfig extends WXPayConfig{

	@Override
	public String getAppID() {
		return "wxd7ad1410f6eb7757";
	}

	@Override
	public String getMchID() {
		// TODO Auto-generated method stub
		return "1520656181";
	}

	@Override
	public String getKey() {
		// TODO Auto-generated method stub
		return "yingxiangli123shangjiabaihuoabcd";
	}

	@Override
	public InputStream getCertStream() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	 public IWXPayDomain getWXPayDomain() {
        // 这个方法需要这样实现, 否则无法正常初始化WXPay
    IWXPayDomain iwxPayDomain = new IWXPayDomain() {

        public void report(String domain, long elapsedTimeMillis, Exception ex) {}

        public DomainInfo getDomain(WXPayConfig config) {
            return new IWXPayDomain.DomainInfo(WXPayConstants.DOMAIN_API, true);
        }
    };
    return iwxPayDomain;

}
	
}
