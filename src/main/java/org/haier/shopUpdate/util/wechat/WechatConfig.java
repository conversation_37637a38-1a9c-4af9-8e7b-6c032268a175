package org.haier.shopUpdate.util.wechat;

import java.io.InputStream;

import org.haier.shopUpdate.util.wechat.IWXPayDomain;
import org.haier.shopUpdate.util.wechat.WXPayConfig;

public class WechatConfig extends WXPayConfig{
	
	
	public String getAppID() {
        return "wx38618456447b4af2";
    }

    /** 微信支付商户号 */
    public String getMchID() {
        return "1520656181";
    }

    public String getKey() {
        return "yingxiangli123shangjiabaihuoabcd";
    }

    public InputStream getCertStream() {
        return null;
    }
    
    public IWXPayDomain getWXPayDomain() {
    	return WXPayDomainSimpleImpl.instance();
    }
}
