package org.haier.shopUpdate.util.wechat.wxpush;
/**
 * 推送消息的内容和字体颜色
* @author: 作者:王恩龙
* @version: 2020年12月5日 下午4:22:18
*
*/
public class DataEntity {
	private String value;
	private String color;
	
	public DataEntity(String value, String color) {
		super();
		this.value = value;
		this.color = color;
	}
	public String getValue() {
		return value;
	}
	public void setValue(String value) {
		this.value = value;
	}
	public String getColor() {
		return color;
	}
	public void setColor(String color) {
		this.color = color;
	}
	
}
