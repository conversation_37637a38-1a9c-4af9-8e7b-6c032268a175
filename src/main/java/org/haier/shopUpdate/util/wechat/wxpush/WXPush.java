package org.haier.shopUpdate.util.wechat.wxpush;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import lombok.extern.slf4j.Slf4j;
import org.haier.shopUpdate.redism.RedisCache;
import org.haier.shopUpdate.util.helibao.IPGet;
import org.haier.shopUpdate.wechat.AuthUtil;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

/**
* @author: 作者:王恩龙
* @version: 2020年12月5日 下午4:21:50
*
*/
@Slf4j
public class WXPush {
	public static final String sendMsgUrl = "https://api.weixin.qq.com/cgi-bin/message/template/send";
	public static final String GETTOKENURL = "https://api.weixin.qq.com/cgi-bin/token";

	static {
		IPGet.getIp();
	}

	//####正式环境数据
	//宁宇创建会员卡成功通知：first:消息头；keyword1:会员卡名称;keyword2:会员卡号；keyword3：领卡时间；remark：备注信息
	public static String ADDNEWCARD = "AdcPkyWaA2R8_XKAcVC_zyosPSu6t0FUB0MIcHAF5dI";
	//充值到帐模板的ID，所需要的参数:first:消息头；keyword1:会员卡号;keyword2:充值金额；keyword3：赠送金额;keyword4：余额;keyword5：充值时间；remark：备注信息
	public static String RECHARGETEMPLATEID = "lURJFEVq9ntk-22xCa5rEOs7F2sFXYPHm8Wf-fWsIrg";
	//消费到帐模板ID,所需要的参数：first：会员名称；keyword1：会员名称；keyword2：会员手机；keyword3：消费金额；keyword4:当前余额；remark：备注信息
	public static String CONSUMETEMPLATEID = "HPrnNOaiFD8mRrU7FAEOVzJNZQVE0QwzoJ_qI6sDIec";
	//退卡通知：first:消息头；keyword1：退款金额；keyword2：商品详情；keyword3：订单编号；remark
	public static String REFUNDCARD = "7K5AvvvnOOKOGnWECzkGzu3G5gWJJXtwa5TauDCa-JE";
	public static String NINGYUAREDISCODE = "ningyuredisCodekey";
	//####正式环境数据结束
	static {
		if(null == IPGet.ExtranetIP || IPGet.ExtranetIP == null) {
			IPGet.getIp();
		}
	}

	 /**
     * 获取token
     * @return token
     */
    public static String getToken() {
    	//限制刷新token的服务器
    	String token = null;
//    	if(IPGet.ExtranetIP.equals(AuthUtil.IP5)) {
//
//    		// 授予形式
//    		String grant_type = "client_credential";
//    		//应用ID
//    		String appid = AuthUtil.APPID;
//    		//密钥
//    		String secret = AuthUtil.APPSECRET;
//    		// 接口地址拼接参数
//    		String getTokenApi =GETTOKENURL + "?grant_type=" + grant_type + "&appid=" + appid
//    				+ "&secret=" + secret;
//    		System.out.println("当前刷新token的参数信息" + getTokenApi);
//    		String tokenJsonStr = doGetPost(getTokenApi, "GET", null);
//    		JSONObject tokenJson = JSONObject.parseObject(tokenJsonStr);
//    		token = tokenJson.get("access_token").toString();
//    		AuthUtil.ACCESSTOKEN = token;
//    		RedisCache rc = new RedisCache(NINGYUAREDISCODE);
//    		rc.putObject("" + NINGYUAREDISCODE, token,720000);
//    		System.out.println("当前缓存的key===" + NINGYUAREDISCODE);
//    		System.out.println("当前NINGYUAREDISCODE ==== " + token);
//    	}else {
//    		//返回缓存的token
//    		RedisCache rc = new RedisCache(NINGYUAREDISCODE);
//    		token = rc.getObject(NINGYUAREDISCODE) == null ? "" : rc.getObject(NINGYUAREDISCODE).toString();
//    	}


        return token;
    }

    /**
     *
     * @param token 登录token，需要保存，并且两小时刷新一次
     * @param toUser 用户的openId,存储在customer_checkout 的cus_weixin字段内
     * @param template_id //模板ID
     */
    public static void SendWeChatMsg(String token,String toUser,String template_id,Map<String,String> map) {
    	System.out.println("当前推送信息=====>>>>");
    	System.out.println(token + "===" + toUser + "===" + template_id + "=====" + map);
    	try {
    		// 接口地址
    		String sendMsgApi = sendMsgUrl + "?access_token="+token;
    		//整体参数map
    		Map<String, Object> paramMap = new HashMap<String, Object>();
    		//消息主题显示相关map
    		Map<String, Object> dataMap = new HashMap<String, Object>();
    		//根据自己的模板定义内容和颜色
    		Set<String> keyset = map.keySet();
    		for(String key : keyset) {
    			dataMap.put(key, new DataEntity(map.get(key), "#173177"));
    		}
    		paramMap.put("touser", toUser);
    		paramMap.put("template_id", template_id);
    		paramMap.put("data", dataMap);
    		System.out.println("消息推送成功============》》》》》"+doGetPost(sendMsgApi,"POST",paramMap));
    	}catch (Exception e) {
    		log.error("异常信息：",e);
    		System.out.println("会员充值通知失败=============>>>>>>>");
		}
    }
	  /**
	   * 调用接口 post
     * @param apiPath
     */
    public static String doGetPost(String apiPath,String type,Map<String,Object> paramMap){
        OutputStreamWriter out = null;
        InputStream is = null;
        String result = null;
        try{
            URL url = new URL(apiPath);// 创建连接
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setDoOutput(true);
            connection.setDoInput(true);
            connection.setUseCaches(false);
            connection.setInstanceFollowRedirects(true);
            connection.setRequestMethod(type) ; // 设置请求方式
            connection.setRequestProperty("Accept", "application/json"); // 设置接收数据的格式
            connection.setRequestProperty("Content-Type", "application/json"); // 设置发送数据的格式
            connection.connect();
            if(type.equals("POST")){
                out = new OutputStreamWriter(connection.getOutputStream(), "UTF-8"); // utf-8编码
                out.append(JSON.toJSONString(paramMap));
                out.flush();
                out.close();
            }
            // 读取响应
            is = connection.getInputStream();
            int length = (int) connection.getContentLength();// 获取长度
            if (length != -1) {
                byte[] data = new byte[length];
                byte[] temp = new byte[512];
                int readLen = 0;
                int destPos = 0;
                while ((readLen = is.read(temp)) > 0) {
                    System.arraycopy(temp, 0, data, destPos, readLen);
                    destPos += readLen;
                }
                result = new String(data, "UTF-8"); // utf-8编码
            }
        } catch (IOException e) {
            log.error("异常信息：",e);
        } finally {
            try {
                is.close();
            } catch (IOException e) {
                log.error("异常信息：",e);
            }
        }
        return  result;
    }
}
