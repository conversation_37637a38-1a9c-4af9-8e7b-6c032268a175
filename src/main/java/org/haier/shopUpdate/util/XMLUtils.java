package org.haier.shopUpdate.util;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.SortedMap;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;

import lombok.extern.slf4j.Slf4j;
import org.jdom.Document;
import org.jdom.Element;
import org.jdom.JDOMException;
import org.jdom.input.SAXBuilder;
@Slf4j
public class XMLUtils {

	 /** <一句话功能简述>
     * <功能详细描述>request转字符串
     * @param request
     * @return
     * @see [类、类#方法、类#成员]
     */
    public static String parseRequst(HttpServletRequest request){
        String body = "";
        try {
            ServletInputStream inputStream = request.getInputStream();
            BufferedReader br = new BufferedReader(new InputStreamReader(inputStream));
            while(true){
                String info = br.readLine();
                if(info == null){
                    break;
                }
                if(body == null || "".equals(body)){
                    body = info;
                }else{
                    body += info;
                }
            }
        } catch (IOException e) {
            log.error("request转字符串失败",e);
        }
        return body;
    }

	/**
     * 解析xml,返回第一级元素键值对。如果第一级元素有子节点，则此节点的值是子节点的xml数据。
     * @param strxml
     * @return
     * @throws JDOMException
     * @throws IOException
     */
    public static Map doXMLParse(String strxml) throws JDOMException,IOException {
       strxml = strxml.replaceFirst("encoding=\".*\"", "encoding=\"UTF-8\"");

       if(null == strxml || "".equals(strxml)) {
          return null;
       }

       Map m = new HashMap();

       InputStream in = new ByteArrayInputStream(strxml.getBytes("UTF-8"));
       SAXBuilder builder = new SAXBuilder();
       Document doc = builder.build(in);
       Element root = doc.getRootElement();
       List list = root.getChildren();
       Iterator it = list.iterator();
       while(it.hasNext()) {
          Element e = (Element) it.next();
          String k = e.getName();
          String v = "";
          List children = e.getChildren();
          if(children.isEmpty()) {
             v = e.getTextNormalize();
          } else {
             v = getChildrenText(children);
          }
          m.put(k, v);
       }

       //关闭流
       in.close();

       return m;
    }

    /**
     * 获取子结点的xml
     * @param children
     * @return String
     */
    public static String getChildrenText(List children) {
       StringBuffer sb = new StringBuffer();
       if(!children.isEmpty()) {
          Iterator it = children.iterator();
          while(it.hasNext()) {
             Element e = (Element) it.next();
             String name = e.getName();
             String value = e.getTextNormalize();
             List list = e.getChildren();
             sb.append("<" + name + ">");
             if(!list.isEmpty()) {
                sb.append(getChildrenText(list));
             }
             sb.append(value);
             sb.append("</" + name + ">");
          }
       }
       return sb.toString();
    }
    /**
     * 将封装好的参数转换成Xml格式类型的字符串
     * @param parameters
     * @return
     */
    public static String createXml(SortedMap<String,Object> parameters){
        StringBuffer sb = new StringBuffer();
        sb.append("<xml>");
        Set es = parameters.entrySet();
        Iterator it = es.iterator();
        while(it.hasNext()) {
            Map.Entry entry = (Map.Entry)it.next();
            String k = (String)entry.getKey();
            String v = entry.getValue().toString();
            if("sign".equalsIgnoreCase(k)){

            }
            else if ("attach".equalsIgnoreCase(k)||"body".equalsIgnoreCase(k)) {
                sb.append("<"+k+">"+"<![CDATA["+v+"]]></"+k+">");
            }
            else {
                sb.append("<"+k+">"+v+"</"+k+">");
            }
        }
        sb.append("<"+"sign"+">"+"<![CDATA["+parameters.get("sign")+"]]></"+"sign"+">");
        sb.append("</xml>");
        return sb.toString();
    }
}
