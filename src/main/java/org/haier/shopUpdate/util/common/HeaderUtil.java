package org.haier.shopUpdate.util.common;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.haier.rabbitmq.service.impl.ReturnListSyncServiceImpl;
import org.haier.shopUpdate.entity.goods.goodsEnum.DeviceSourceEnum;
import org.haier.shopUpdate.entity.goods.goodsEnum.UserTypeEnum;
import org.haier.shopUpdate.entity.goods.goodsRecord.GoodsOperParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import java.net.URLDecoder;
import java.util.Enumeration;
@Slf4j
public class HeaderUtil {
    private static final Logger logger = LoggerFactory.getLogger(HeaderUtil.class);

    /**
     * 获取头文件里的参数信息
     * @param request
     * @return
     */
    public static GoodsOperParam getHeadParam(HttpServletRequest request){

        Enumeration<String> headerParams = request.getHeaderNames();
        System.out.println("当前上传的头文件信息" + headerParams);
        logger.info("headerParam:::[{}]",headerParams);
        while (headerParams.hasMoreElements()){
            String headerParam = headerParams.nextElement();
            String headerValue = request.getHeader(headerParam);

            System.out.println("headerParam:::" + headerParam + "====" + headerValue);
        }
        logger.info("request.getHeaders:::[{}]",request.getHeaderNames());
        logger.info("request.getHeaders-userId:::[{}]", request.getHeader("userId"));
        String userId = request.getHeader("userId");
        String deviceSource = request.getHeader("devicesource");
        String deviceSourceMsg = request.getHeader("devicesourcemsg");
        String userType = request.getHeader("usertype");
        String userName = request.getHeader("username");

        GoodsOperParam goodsOperParam = new GoodsOperParam();
        try{
            if(StrUtil.isNotBlank(deviceSourceMsg)){
                goodsOperParam.setDevicesourcemsg(URLDecoder.decode(deviceSourceMsg, "UTF-8"));
            } else {
                goodsOperParam.setDevicesourcemsg("收银机");
            }

            if(StrUtil.isNotBlank(userName)){
                goodsOperParam.setUserName(URLDecoder.decode(userName, "UTF-8"));
            } else {
                goodsOperParam.setUserName("暂未上传");
            }

            if(StrUtil.isNotBlank(userId) && !userId.equals("")){
                goodsOperParam.setUserId(Integer.parseInt(userId));
            } else {
                goodsOperParam.setUserId(0);
            }
            if(StrUtil.isNotBlank(userType)){
                goodsOperParam.setUserType(Integer.parseInt(userType));
            } else {
                goodsOperParam.setUserType(UserTypeEnum.SHOP_STAFF.getValue());
            }
            if(StrUtil.isNotBlank(deviceSource)){
                goodsOperParam.setDeviceSource(Integer.parseInt(deviceSource));
            } else {
                goodsOperParam.setDeviceSource(DeviceSourceEnum.PC_POS.getValue());
            }
        } catch (Exception e){
            log.error("头文件信息解析异常：",e);
        }
        System.out.println("头文件的商品信息" + goodsOperParam);
        return goodsOperParam;
    }
}
