package org.haier.shopUpdate.util.common;

public class CommonResult {
	//订单返回结果判断:2、失败；1、成功；
		private Integer status;
		//提示信息
		private String msg="成功";
		//返回的结果集合
		private Object data;
		//分页查询的当前页数
		public Integer getStatus() {
			return status;
		}
		public void setStatus(Integer status) {
			this.status = status;
		}
		public String getMsg() {
			return msg;
		}
		public void setMsg(String msg) {
			this.msg = msg;
		}
		public Object getData() {
			return data;
		}
		public void setData(Object data) {
			this.data = data;
		}
		public CommonResult() {
			super();
		}
		public CommonResult(Integer status, String msg) {
			super();
			this.status = status;
			this.msg = msg;
		}
}
