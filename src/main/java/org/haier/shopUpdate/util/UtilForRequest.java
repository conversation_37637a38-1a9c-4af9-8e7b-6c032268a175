package org.haier.shopUpdate.util;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
@Slf4j
public class UtilForRequest {
	/**
	 * GET方式请求
	 * @param url API地址
	 * @param params 参数信息，格式：name=value&name=value
	 * @return
	 */
	public static String doGet(String url,String params) {
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse response = null;
        String result = "";
        try {
            // 通过址默认配置创建一个httpClient实例
            httpClient = HttpClients.createDefault();
            // 创建httpGet远程连接实例
            HttpGet httpGet = new HttpGet(url+"?"+params);
            // 设置请求头信息，鉴权
            httpGet.setHeader("Authorization", "Bearer da3efcbf-0845-4fe3-8aba-ee040be542c0");
            // 设置配置请求参数
            RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(35000)// 连接主机服务超时时间
                    .setConnectionRequestTimeout(35000)// 请求超时时间
                    .setSocketTimeout(60000)// 数据读取超时时间
                    .build();
            // 为httpGet实例设置配置
            httpGet.setConfig(requestConfig);
            // 执行get请求得到返回对象
            response = httpClient.execute(httpGet);
            // 通过返回对象获取返回数据
            HttpEntity entity = response.getEntity();
            // 通过EntityUtils中的toString方法将结果转换为字符串
            result = EntityUtils.toString(entity);
        } catch (IOException e) {
            log.error("异常信息：",e);
        } finally {
            // 关闭资源
            if (null != response) {
                try {
                    response.close();
                } catch (IOException e) {
                    log.error("异常信息：",e);
                }
            }
            if (null != httpClient) {
                try {
                    httpClient.close();
                } catch (IOException e) {
                    log.error("异常信息：",e);
                }
            }
        }
        return result;
	}

	/**
	 * POST方式请求
	 * @param url
	 * @param paramMap
	 * @return
	 */
	 public static String doPost(String url, Map<String, Object> paramMap) {
	        CloseableHttpClient httpClient = null;
	        CloseableHttpResponse httpResponse = null;
	        String result = "";
	        // 创建httpClient实例
	        httpClient = HttpClients.createDefault();
	        // 创建httpPost远程连接实例
	        HttpPost httpPost = new HttpPost(url);
	        // 配置请求参数实例
	        RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(35000)// 设置连接主机服务超时时间
	                .setConnectionRequestTimeout(35000)// 设置连接请求超时时间
	                .setSocketTimeout(60000)// 设置读取数据连接超时时间
	                .build();
	        // 为httpPost实例设置配置
	        httpPost.setConfig(requestConfig);
	        // 设置请求头
	        httpPost.addHeader("Content-Type", "application/x-www-form-urlencoded");
	        // 封装post请求参数
	        if (null != paramMap && paramMap.size() > 0) {
	            List<NameValuePair> nvps = new ArrayList<NameValuePair>();
	            // 通过map集成entrySet方法获取entity
	            Set<Entry<String, Object>> entrySet = paramMap.entrySet();
	            // 循环遍历，获取迭代器
	            Iterator<Entry<String, Object>> iterator = entrySet.iterator();
	            while (iterator.hasNext()) {
	                Entry<String, Object> mapEntry = iterator.next();
	                nvps.add(new BasicNameValuePair(mapEntry.getKey(), mapEntry.getValue()== null ? null : mapEntry.getValue().toString()));
	            }

	            // 为httpPost设置封装好的请求参数
	            try {
	                httpPost.setEntity(new UrlEncodedFormEntity(nvps, "UTF-8"));
	            } catch (UnsupportedEncodingException e) {
	                log.error("异常信息：",e);
	            }
	        }
	        try {
	            // httpClient对象执行post请求,并返回响应参数对象
	            httpResponse = httpClient.execute(httpPost);
	            // 从响应对象中获取响应内容
	            HttpEntity entity = httpResponse.getEntity();
	            result = EntityUtils.toString(entity);
	        } catch (IOException e) {
	            log.error("异常信息：",e);
	        } finally {
	            // 关闭资源
	            if (null != httpResponse) {
	                try {
	                    httpResponse.close();
	                } catch (IOException e) {
	                    log.error("异常信息：",e);
	                }
	            }
	            if (null != httpClient) {
	                try {
	                    httpClient.close();
	                } catch (IOException e) {
	                    log.error("异常信息：",e);
	                }
	            }
	        }
	        return result;
	    }

	 /**
	  * 百度专用
	  * @param ak
	  * @param sk
	  * @return
	  */
	 public static String getAuth(String ak, String sk){

		 // 获取token地址
	        String authHost = "https://aip.baidubce.com/oauth/2.0/token?";
	        String getAccessTokenUrl = authHost
	                // 1. grant_type为固定参数
	                + "grant_type=client_credentials"
	                // 2. 官网获取的 API Key
	                + "&client_id=" + ak
	                // 3. 官网获取的 Secret Key
	                + "&client_secret=" + sk;
	        try {
	            URL realUrl = new URL(getAccessTokenUrl);
	            // 打开和URL之间的连接
	            HttpURLConnection connection = (HttpURLConnection) realUrl.openConnection();
	            connection.setRequestMethod("GET");
	            connection.connect();
	            // 获取所有响应头字段
	            Map<String, List<String>> map = connection.getHeaderFields();
	            // 遍历所有的响应头字段
	            for (String key : map.keySet()) {
	                System.err.println(key + "--->" + map.get(key));
	            }
	            // 定义 BufferedReader输入流来读取URL的响应
	            BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
	            String result = "";
	            String line;
	            while ((line = in.readLine()) != null) {
	                result += line;
	            }
	            /**
	             * 返回结果示例
	             */
	            System.err.println("result:" + result);
	            JSONObject jsonObject = new JSONObject(result);
	            String access_token = jsonObject.getString("access_token");
	            return access_token;
	        } catch (Exception e) {
	            System.err.printf("获取token失败！");
	            e.printStackTrace(System.err);
	        }
	        return null;
	 }
}
