package org.haier.shopUpdate.util.thread;
/**
* @author: 作者:王恩龙
* @version: 2023年8月8日 下午5:30:16
*
*/

import java.util.HashMap;
import java.util.Map;

import org.haier.shopUpdate.util.HttpsUtil;
import org.haier.shopUpdate.util.helibao.HelibaoPayConfig;

public class SendMqttMsg extends Thread{

	private String shopUnique;
	private String goodsBarcode;

	private String barcodes;
	
	public SendMqttMsg() {
	}


	public SendMqttMsg(String shopUnique,String goodsBarcode) {
		this.shopUnique = shopUnique;
		this.goodsBarcode = goodsBarcode;
	}

	public SendMqttMsg(String shopUnique,String goodsBarcode,String barcodes) {
		this.shopUnique = shopUnique;
		this.goodsBarcode = goodsBarcode;
		this.barcodes = barcodes;
	}
	
	@Override
	public void run() {
		super.run();
		Map<String,Object> mqttMap = new HashMap<>();
		mqttMap.put("shopUnique", shopUnique);
		mqttMap.put("goodsBarcode", goodsBarcode);
		mqttMap.put("barcodes", barcodes);
		System.out.println(HelibaoPayConfig.MQTTURL_FOR_UPDATEGOODS);
		String res = HttpsUtil.doPost(HelibaoPayConfig.MQTTURL_FOR_UPDATEGOODS, mqttMap);
		System.out.println(res);
	}
}
