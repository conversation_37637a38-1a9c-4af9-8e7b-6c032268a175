package org.haier.shopUpdate.util.payCenter;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.haier.shopUpdate.config.PayCenterConfig;
import org.haier.shopUpdate.enums.payCenter.RefundStateEnum;
import org.haier.shopUpdate.log.util.IpUtils;
import org.haier.shopUpdate.params.payCenter.RefundResultQueryParams;
import org.haier.shopUpdate.util.ShopsResult;
import org.haier.shopUpdate.util.UtilForJAVA;

import java.lang.reflect.Field;
import java.util.*;

/**
 * @ClassName PayCenterUtil
 * <AUTHOR>
 * @Date 2025/2/15 13:54
 */
@Slf4j
public class PayCenterUtil {
    public static ShopsResult post(String requestUrl, Object requestBody, String mchKey, int count) {
        ShopsResult result = new ShopsResult();
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("yxl-timestamp", System.currentTimeMillis()/1000 + "");
        headerMap.put("yxl-nonce", getRandomString(32));
        SortedMap<String, Object> paramMap = UtilForJAVA.objectToSortMap(requestBody);
        List<String> excludeKeyList = new ArrayList<>();
        excludeKeyList.add("sign");
        excludeKeyList.add("customJson");
        paramMap.put("yxl-timestamp", headerMap.get("yxl-timestamp"));
        paramMap.put("yxl-nonce", headerMap.get("yxl-nonce"));

        String parStr = SHA256withRSAUtils.getParamsSign(paramMap, excludeKeyList);
        String sign = SHA256withRSAUtils.sign256(parStr, mchKey);
        headerMap.put("yxl-sign", sign);

        HttpResponse response = HttpRequest.post(requestUrl).headerMap(headerMap, false).body(JSONUtil.toJsonStr(requestBody)).execute();
        System.out.println("sign:" + JSONUtil.toJsonStr(paramMap));
        System.out.println("请求地址" + requestUrl);
        System.out.println("请求参数" + JSONUtil.toJsonStr(requestBody));
        System.out.println("请求支付结果" + response);
        System.out.println("请求支付Body" + response.body());
        String body = response.body();
        if (body.startsWith("{")) {
            JSONObject jsonObject = JSONUtil.parseObj(body);
            String code = jsonObject.getStr("code");
            if ("200".equals(code)) {
                //请求成功，查看是否退款成功
                JSONObject dataJson = jsonObject.getJSONObject("data");
                String refundState = dataJson.getStr("refundState");
                if (RefundStateEnum.SUCCESS.getCode().equals(refundState)) {
                    //退款成功
                    result.setStatus(1);
                    result.setData(dataJson);
                    result.setMsg("退款成功！");
                } else if (RefundStateEnum.DOING.getCode().equals(refundState)) {
                    //退款请求中
                    result.setStatus(1);
                    result.setData(dataJson);
                    result.setMsg("退款请求中！");
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        log.error("退款失败，线程休眠异常:", e);
                    }
                    if (count < 10) {
                        count++;
                        JSONObject jo = JSONUtil.parseObj(requestBody);
                        RefundResultQueryParams refundResultQueryParams = new RefundResultQueryParams();
                        refundResultQueryParams.setClientIp(IpUtils.getHostIp());
                        refundResultQueryParams.setMchId(jo.getStr("mchId"));
                        refundResultQueryParams.setMchRefundNo(dataJson.getStr("mchRefundNo"));
                        result = post(PayCenterConfig.QUERYREFUNDRESULTURL, refundResultQueryParams, mchKey, count);
                    }
                } else {
                    //未支付或者已关闭
                    result.setStatus(0);
                    result.setMsg("退款失败！");
                }

            } else {
                //请求失败
                result.setStatus(0);
                result.setMsg(jsonObject.getStr("msg"));
            }
        } else {
            //请求失败
            result.setStatus(0);
            result.setMsg("支付中心异常！");
        }
        return result;
    }

    /***
     * 随机字符串
     * @param length
     * @return
     */
    public static String getRandomString(int length) { //length表示生成字符串的长度
        String base = "abcdefghijklmnopqrstuvwxyz0123456789";
        Random random = new Random();
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < length; i++) {
            int number = random.nextInt(base.length());
            sb.append(base.charAt(number));
        }
        return sb.toString();
    }

    /**
     * 将对象转换为Map，排序后返回，去掉sign和空值
     * @param obj
     * @return
     */
    public static SortedMap<String,Object> objectToSortMap(Object obj) {
        Field[] fields = obj.getClass().getDeclaredFields();
        SortedMap<String,Object> map = new TreeMap<String,Object>();
        for (Field f : fields) {
            try {
                f.setAccessible(true);
                if (!f.getName().equals("sign") && f.get(obj) != null) {
                    map.put(f.getName(), f.get(obj));
                }
            } catch ( Exception e) {
                log.error("objectToSortMap error: ",e);
            }
        }
        return map;
    }
}
