package org.haier.shopUpdate.util;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;
import java.util.UUID;

/**
 * UUID工具类 
 */
public class UUIDUtil implements Serializable {
    private static final long serialVersionUID = -741325524978030957L;

    /**
     * 获取36个字符长度的UUID
     * 
     * @return
     */
    public static String getUUID36() {
        return UUID.randomUUID().toString();
    }

    /**
     * 获取32个字符长度的UUID
     * 
     * @return
     */
    public static String getUUID32() {
        String uuid = getUUID36();
        return uuid.replaceAll("-", "");
    }
    
    public static String getVerificationCode(int len){
    	Random r = new Random();
    	String num = "";
    	for (int i = 0; i < len; i++) {
			num += r.nextInt(10);
		}
    	return num;
    }
    
    /**
     * 获取当前时间+5位随机数
     * 
     * @return
     */
    public static String getTimeRandom() {  
    	  
        SimpleDateFormat simpleDateFormat;  
  
        simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");  
  
        Date date = new Date();  
  
        String str = simpleDateFormat.format(date);  
  
        Random random = new Random();  
  
        int rannum = (int) (random.nextDouble() * (99999 - 10000 + 1)) + 10000;// 获取5位随机数  
  
        return str+rannum;
    }  
    
    
    /**
     * 生成货物条形码规则
     * 当前时间+手机号后四位+4位随机数
     * 
     * @return
     */
    public static String getQrCode() {
    	  
        SimpleDateFormat simpleDateFormat;  
        simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");  
        Date date = new Date();  
        String str = simpleDateFormat.format(date);  

        
        
        Random random = new Random();  
  
        int rannum = (int) (random.nextDouble() * (9999 - 1000 + 1)) + 1000;// 获取4位随机数  
  
        return str+rannum;
    }
    
    /**
     * 生成出库单规则
     * 当前时间+手机号后四位+4位随机数
     * 
     * @return
     */
    public static String getOrderOutCode() {
    	  
        SimpleDateFormat simpleDateFormat;  
        simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");  
        Date date = new Date();  
        String str = simpleDateFormat.format(date);  

        
        
        Random random = new Random();  
  
        int rannum = (int) (random.nextDouble() * (9999 - 1000 + 1)) + 1000;// 获取4位随机数  
  
        return str+rannum;
    }
    
    /**
     * 生成单号规则
     * 前缀+当前时间+4位随机数
     * 
     * @return
     */
    public static String getOrderSN(String prefix) {
    	  
        SimpleDateFormat simpleDateFormat;  
        simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");  
        Date date = new Date();  
        String str = simpleDateFormat.format(date);  

        
        
        Random random = new Random();  
  
        int rannum = (int) (random.nextDouble() * (9999 - 1000 + 1)) + 1000;// 获取4位随机数  
  
        return prefix+str+rannum;
    }
    
    public static void main(String[] args) {
 
    	   
    	
//    	for (int i=0; i<10; i++) {
//			System.out.println(getUUID32());
//		}
    	
    	//System.out.println(getVerificationCode(5));
//        System.out.println(getUUID32());
//        System.out.println(getUUID36().length());
//
//        System.out.println(getUUID32());
//        System.out.println(getUUID32().length());
    }

	public static String getOrderInComeCode(String phone) {
		  
        SimpleDateFormat simpleDateFormat;  
        simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");  
        Date date = new Date();  
        String str = simpleDateFormat.format(date);  

        
        
        Random random = new Random();  
  
        int rannum = (int) (random.nextDouble() * (9999 - 1000 + 1)) + 1000;// 获取4位随机数  
  
        return str+rannum;
	}

	//库存交易记录号
	public static String getCargoRecord() {
		// TODO Auto-generated method stub
		 SimpleDateFormat simpleDateFormat;  
	        simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");  
	        Date date = new Date();  
	        String str = simpleDateFormat.format(date);  
 
	        Random random = new Random();  
	  
	        int rannum = (int) (random.nextDouble() * (9999 - 1000 + 1)) + 1000;// 获取4位随机数  
	  
	        return str+rannum;
	}
	
	//创建订单编号
    public static String createOrderNum(){
        return createOrderNum("DD");
    }

    public static String createOrderNum(String suxx){
        SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");//设置日期格式
        String datestr = df.format(new Date());
        String sj_num = String.valueOf((1+Math.random()*9)+(int)(1+Math.random()*9)+(int)(1+Math.random()*9)+(int)(1+Math.random()*9));
        return suxx + datestr + sj_num;
    }


    public static Long createID(){
        Random ran = new Random();
        int m = ran.nextInt(89999) + 10000;
        return  new Date().getTime() + m;
    }
}
