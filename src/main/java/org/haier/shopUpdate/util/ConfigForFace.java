package org.haier.shopUpdate.util;

import java.io.File;

public class ConfigForFace {
	public static Integer c=0;
	//项目路径
	public static String PROJECTURL="http://";
//FACE++ APIKEY
	public static String api_key1="KL9fd1GY8WFiwRJ8Fqbpo4tHINkDrgCT";
	public static String api_key="MGJkktv-KphKb-vtz-68BFCY8ExA0TsP";
	//FACE++ API Secrt
	public static String api_secret1 ="QreMsK2YGn8TKR--mIKy9XvX7S2Vfzjw";
	public static String api_secret="LtzkEx32eWGOupYSCz4XejGXvw8XQ0FB";
	//FACE++ 创建 FACEMAP的URL
	public static String  createUrl="https://api-cn.faceplusplus.com/facepp/v3/faceset/create";
	//FACE++ 将新的用户信息添加到MAP里
	public static String addFaceUrl=" https://api-cn.faceplusplus.com/facepp/v3/faceset/addface";
	//FACE++ 将用户的图片信息移出MAP
	public static String removeFaceUrl="https://api-cn.faceplusplus.com/facepp/v3/faceset/removeface";
	//FACE++ 更新MAP描述信息
	public static String  updateMapUrl="https://api-cn.faceplusplus.com/facepp/v3/faceset/update";
	//FACE++ 获取MAP的所有信息
	public static String detailUrl="https://api-cn.faceplusplus.com/facepp/v3/faceset/getdetail";
	//FACE++ 删除MAP集合
	public static String deleteMapUrl="https://api-cn.faceplusplus.com/facepp/v3/faceset/delete";
	//FACE++ 查询所有的MAP集合
	public static String getMapSetUrl="https://api-cn.faceplusplus.com/facepp/v3/faceset/getfacesets";
	//FACE+= 根据获取的图片信息，将图片的信息保存
	public static String detectUrl="https://api-cn.faceplusplus.com/facepp/v3/detect";
	//在FaceMap集合中找符合图片的用户信息
	public static String searchUrl="https://api-cn.faceplusplus.com/facepp/v3/search";
	//会员头像信息保存路径
	public static String MEMBERHEAD="member"+File.separator+"headImage"+File.separator;
	//会员画像图片信息保存路径
	public static String PORTRAITPATH="portrait"+File.separator+"headImage"+File.separator;
	//会员画像评分信息,当相似度大于此值时，认定两人相同
	public static Double SCORE=90.0;
	//会员人脸绑定次数设置，使用同一人脸支付超过这个次数，则绑定人脸信息
	public static Integer FACECOUNT=3;
}
