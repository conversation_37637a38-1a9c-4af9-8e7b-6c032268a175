package org.haier.shopUpdate.util.helibao;

import org.haier.shopUpdate.project.ProjectConfig;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.SortedMap;

@Component
public class HelibaoPayConfig {
	@Resource
	private ProjectConfig projectConfig;

	//小程序
	//百货商家               
	public final static String APP_ID = "wxd7ad1410f6eb7757";
	public final static String AppSecret = "8d0df70a84fe83c723828d369714a69e";
	public final static String MCH_ID = "1520656181";//商户号（改为自己实际的）https://pay.weixin.qq.com/index.php/core/home/<USER>
	public final static String API_KEY = "yingxiangli123shangjiabaihuoabcd"; //（改为自己实际的）key设置路径：微信商户平台(pay.weixin.qq.com)-
	public final static String UFDODER_URL = "https://api.mch.weixin.qq.com/pay/unifiedorder";
	
	//合利宝供货商城收款MCHID
	public static String PURMCHID = "E1802758671";
	public static String PUBMCHKEY = "kaiOWLtePEPIw8hwjoSWruS0y5SZ0wse";
	
	//合利宝商品账号
	public static String MCHID = "E1802781288";
	//合利宝商户密钥
	public static String MCHKEY = "sSO3EWRyaQtETPpBmcAu2Zttj1ph8HFM";
	//合利宝支付回调路径
	public static String HELIBAONOTIFYURL_RETURN_MONEY = "/shop/shopping/callBackHelibaoPayReturnMoney.do";
	public static String RETURN_MONEY_NOTIFY_URL = "/shop/beans/weixinNotifyReturnMoney.do";
	//赊销还款回调函数
	public static String LOAN_RETURN_WEIXIN_NOTIFY_URL = "/shopUpdate/wechat/wxPayCallBackReturMoney.do";
	public static String LOAN_RETURN_ALIPAY_NOTIFY_URL = "/shopUpdate/aliPay/aliPayCallBackReturnMoney.do";
	public static String LOAN_RETURN_ALIPAY_NOTIFY_URL_FORORDER = "/shopUpdate/aliPay/aliPayCallBackReturnMoneyForOrder.do";
	//发送MQTT消息
	public static String MQTTURL_FOR_UPDATEGOODS = "/shopmanager/util/sendMQTTMsgPub.do";
	public static String RECORDGOODSOPER = "/shopmanager/record/recordGoodsOper.do";
    public static String WECHAT_PAY_NOTIFY_URL = "/shopUpdate/diamonds/weixinPayCallBack.do";

    @PostConstruct
    public void init() {
        HELIBAONOTIFYURL_RETURN_MONEY = projectConfig.getProjectUrl() + HELIBAONOTIFYURL_RETURN_MONEY;
        RETURN_MONEY_NOTIFY_URL = projectConfig.getProjectUrl() + RETURN_MONEY_NOTIFY_URL;
        LOAN_RETURN_WEIXIN_NOTIFY_URL = projectConfig.getProjectUrl() + LOAN_RETURN_WEIXIN_NOTIFY_URL;
        LOAN_RETURN_ALIPAY_NOTIFY_URL = projectConfig.getProjectUrl() + LOAN_RETURN_ALIPAY_NOTIFY_URL;
        LOAN_RETURN_ALIPAY_NOTIFY_URL_FORORDER = projectConfig.getProjectUrl() + LOAN_RETURN_ALIPAY_NOTIFY_URL_FORORDER;
        MQTTURL_FOR_UPDATEGOODS = projectConfig.getProjectUrl() + MQTTURL_FOR_UPDATEGOODS;
        RECORDGOODSOPER = projectConfig.getProjectUrl() + RECORDGOODSOPER;
        WECHAT_PAY_NOTIFY_URL = projectConfig.getProjectUrl() + WECHAT_PAY_NOTIFY_URL;
    }

	//微信支付类型
	public final static String WXPAY = "WXPAY";
	public final static String ALIPAY = "ALIPAY";
	public final static String UNIONPAY = "UNIONPAY";

	  /**
	   * 1获取当前时间 yyyyMMddHHmmss 
     *  
     * @return String 
     */  
    public static String getCurrTime() {  
        Date now = new Date();  
        SimpleDateFormat outFormat = new SimpleDateFormat("yyyyMMddHHmmss");  
        String s = outFormat.format(now);  
        return s;  
    }  
    
    /** 
     * 取出一个指定长度大小的随机正整数. 
     *  
     * @param length 
     *            int 设定所取出随机数的长度。length小于11 
     * @return int 返回生成的随机数。 
     */  
    public static int buildRandom(int length) {  
        int num = 1;  
        double random = Math.random();  
        if (random < 0.1) {  
            random = random + 0.1;  
        }  
        for (int i = 0; i < length; i++) {  
            num = num * 10;  
        }  
        return (int) ((random * num));  
    }  
    
    /** 
     * <AUTHOR> @date 2016-4-22 
     * @Description：sign签名 
     * @param characterEncoding 
     *            编码格式 
     *            请求参数
     * @return 
     */  
    public static String createSign(String characterEncoding, SortedMap<Object, Object> packageParams, String API_KEY) {  
        StringBuffer sb = new StringBuffer();  
        Set es = packageParams.entrySet();  
        Iterator it = es.iterator();  
        while (it.hasNext()) {  
            Map.Entry entry = (Map.Entry) it.next();  
            String k = (String) entry.getKey();  
            String v = (String) entry.getValue();  
            if (null != v && !"".equals(v) && !"sign".equals(k) && !"key".equals(k)) {  
                sb.append(k + "=" + v + "&");  
            }  
        }  
        sb.append("key=" + API_KEY);  
        String sign = MD5Util.MD5Encode(sb.toString(), characterEncoding).toUpperCase();  
        return sign;  
    }  
    
    /** 
     * <AUTHOR> @date 2016-4-22 
     * @Description：将请求参数转换为xml格式的string 
     * @param parameters 
     *            请求参数 
     * @return 
     */  
    public static String getRequestXml(SortedMap<Object, Object> parameters) {  
        StringBuffer sb = new StringBuffer();  
        sb.append("<xml>");  
        Set es = parameters.entrySet();  
        Iterator it = es.iterator();  
        while (it.hasNext()) {  
            Map.Entry entry = (Map.Entry) it.next();  
            String k = (String) entry.getKey();  
            String v = (String) entry.getValue();  
            if ("attach".equalsIgnoreCase(k) || "body".equalsIgnoreCase(k) || "sign".equalsIgnoreCase(k)) {  
                sb.append("<" + k + ">" + "<![CDATA[" + v + "]]></" + k + ">");  
            } else {  
                sb.append("<" + k + ">" + v + "</" + k + ">");  
            }  
        }  
        sb.append("</xml>");  
        return sb.toString();  
    }  
}
