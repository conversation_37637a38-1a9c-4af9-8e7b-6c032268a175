package org.haier.shopUpdate.util;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

/**
 * 本工具类提供与Session相关的静态方法
 * <AUTHOR>
 *
 */
public class SessionUtil {
	public static Map<String,Integer> map=new HashMap<String, Integer>();
	/**
	 * 保存发送的验证信息
	 * @param request
	 * @param response
	 * @param staffAccount
	 * @param code
	 * @return
	 */
	public static boolean createSession(HttpServletRequest request,HttpServletResponse response,String staffAccount,String code,Integer maxAge){
		//获取Session
		HttpSession session=request.getSession();
		//将发送的短信验证码存储到SESSION
		session.setAttribute(staffAccount, code);
		//设置session的存在时间
		session.setMaxInactiveInterval(maxAge);
		return true;
	}

	public static String createANDRIODSession(HttpServletRequest request,HttpServletResponse response,String staffAccount,Integer code,Integer maxAge){
		//获取Session
		HttpSession session=request.getSession();
		//将发送的短信验证码存储到SESSION
		session.setAttribute(staffAccount, code);
		//设置session的存在时间
		session.setMaxInactiveInterval(maxAge);
		map.put(session.getId(), code);
		return session.getId();
	}
	/**
	 * 获取保存的验证码
	 * @param request
	 * @param response
	 * @param staffAccount
	 * @return
	 */
	public static int getSessionMsg(HttpServletRequest request,HttpServletResponse response,String staffAccount,String code){
		//获取SESSION
		HttpSession session=request.getSession();
		System.out.println("第二次的ID"+session.getId());
		Object smsCode=session.getAttribute(staffAccount);
		if(null==smsCode){
			return 2;//验证超时
		}
		if(!smsCode.equals(code)){
			return 3;//验证码错误
		}
		session.invalidate();//验证通过，销毁存储的数据
		return 1;//通过验证
	}
	
	/**
	 * 验证ANDRIOD密码
	 * @param request
	 * @param response
	 * @param staffAccount
	 * @param code
	 * @param sessionId
	 * @return
	 */
	public static int getANDRIODSession(HttpServletRequest request,HttpServletResponse response,String staffAccount,String  code,String sessionId){
		if(map.get(sessionId)==null){
			return 2;
		}
		String oldCode=map.get(sessionId).toString();
		System.out.println("旧的验证码为："+oldCode);
		System.out.println("新的验证码："+code);
		if(oldCode==null){
			return 2;//验证超时
		}
		System.out.println(oldCode.equals(code));
		if(!oldCode.equals(code)){
			return 3;//验证错误
		}
		map.remove(sessionId);
		return 1;
	}
	/**
	 * 登录时创建新的token
	 * @param request
	 * @param response
	 * @param maxAge
	 * @param token
	 * @return
	 */
	public static boolean createNewToken(HttpServletRequest request,HttpServletResponse response,Integer maxAge,String token){
		//获取Session
		HttpSession session=request.getSession();
		//设置token密令
		session.setAttribute("token", token);
		
		System.out.println("登录时的::SESSIONID"+session.getId());
		//设置session的存在时间
		System.out.println("存入的token:::"+session.getAttribute("token"));
		if(null!=maxAge){
			session.setMaxInactiveInterval(maxAge);
		}
		return true;
	}
	
	/**
	 * 获取登录时的token
	 * @param request
	 * @param response
	 * @return
	 */
	public static String getToken(HttpServletRequest request,HttpServletResponse response){
		HttpSession session=request.getSession();
		System.out.println("查询时的SESSIONID:"+session.getId());
		Object token=session.getAttribute("token");
		if(null==token){
			return null;
		}
		System.out.println("获取到的token::::"+token);
		return token.toString();
	}
}
