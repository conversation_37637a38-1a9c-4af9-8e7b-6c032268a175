package org.haier.shopUpdate.util;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.SortedMap;
import java.util.TreeMap;

import org.haier.shopUpdate.util.MUtil;
import org.haier.shopUpdate.util.XMLUtils;
import org.haier.shopUpdate.util.helibao.HelibaoPayConfig;
import org.jdom.JDOMException;

/**
 * 微信支付
 * <AUTHOR>
 *
 */
public class WechatPayUtil {
	
		//（微信开放平台）
		public static final String WEIXINAPPID = "wx38618456447b4af2";
		//微信支付分配的商户号（微信商户平台）
		public static final String WEIXINMCHID = "1520656181";
		//微信商户平台支付密钥key，API密钥，微信商户平台(pay.weixin.qq.com)-->账户中心-->账户设置-->API安全-->密钥设置
		public static final String WEIXIN_KEY = "yingxiangli123shangjiabaihuoabcd";//
		//回调地址
		public static final String notify_url = HelibaoPayConfig.WECHAT_PAY_NOTIFY_URL;

	/**
	 * 微信支付，
	 * @param paramsMap 请求参数Map
	 *        {
	 *        	subject 商品标题,
	 *        	order_number 商户端订单号，唯一性,
	 *        	total_amount 支付金额，单位为元,
	 *        	notify_url 支付宝回调地址，公网可访问地址,
	 *        	param 附加参数，回调中原样返回，任意形式的字符串可为空
	 *        }
	 * @param spbill_create_ip 
	 * @param type 
	 * @return
	 * @throws IOException 网络请求异常
	 * @throws JDOMException xml数据处理发生错误
	 */
	public static Map<String, Object> weixinPay(Map<String,Object> paramsMap) throws IOException, JDOMException{
		String finalmoney= String.format("%.2f", Double.valueOf(MUtil.strObject(paramsMap.get("total_fee"))));//微信请求金额单位为分
		finalmoney = finalmoney.replace(".", "");
		int total_fee= Integer.parseInt(finalmoney);
		SortedMap<String,Object> parametersMap = new TreeMap<String,Object>();
		parametersMap.put("appid", WEIXINAPPID);//应用ID
		parametersMap.put("mch_id", WEIXINMCHID);//商户号
		parametersMap.put("device_info", "WEB");
		parametersMap.put("nonce_str", MUtil.getRandomString(32));//32位随机字符串
		parametersMap.put("body", MUtil.strObject(paramsMap.get("body")));//商品描述
		parametersMap.put("out_trade_no", MUtil.strObject(paramsMap.get("out_trade_no")));//订单号,32个字符内
		parametersMap.put("total_fee", total_fee);//支付金额，有APP端发送
		parametersMap.put("spbill_create_ip", MUtil.strObject(paramsMap.get("spbill_create_ip")));
		parametersMap.put("fee_type", "CNY");//货币类型
		parametersMap.put("notify_url", notify_url);//回调地址
		parametersMap.put("trade_type", "APP");//交易类型 
//		if(null != MUtil.strObject(paramsMap.get("param")) && !"".equals(MUtil.strObject(paramsMap.get("param")))){
//			parametersMap.put("attach", MUtil.strObject(paramsMap.get("param")));//附加参数，在回调中原样返回
//		}
		String sign = createSign("utf-8",parametersMap,WEIXIN_KEY);//生成签名，签名算法
		parametersMap.put("sign", sign);

		String xml = XMLUtils.createXml(parametersMap);//拼接xml请求参数
		System.out.println("微信支付请求参数：：："+xml);
		Map<String, Object> weiXinVo = sendHttpXML(xml,WEIXIN_KEY);//请求微信返回的结果，直接返回手机端
		return weiXinVo;
	}
	
	/**
	 * 微信退款
	 * @param body 商品描述
	 * @param out_trade_no 商户系统内部下单时的订单号，商户号下唯一
	 * @param total_fee 订单总金额
	 * @param refund_fee 退款金额
	 * @return
	 * @throws IOException 网络请求异常
	 * @throws JDOMException xml数据处理发生错误
	 */
	public static boolean weixinRefundPay(String out_trade_no, int total_fee, int refund_fee) throws IOException, JDOMException{
		SortedMap<String,Object> parametersMap = new TreeMap<String,Object>();
//		parametersMap.put("appid", WechatPayUtil.WEIXINAPPID);//应用ID
//		parametersMap.put("mch_id", WechatPayUtil.WEIXINMCHID);//商户号
//		parametersMap.put("nonce_str", MUtil.getRandomString(32));//32位随机字符串
//		parametersMap.put("out_trade_no", out_trade_no);//商户系统内部订单号
//		parametersMap.put("out_refund_no", new SimpleDateFormat("yyyyMMddHHmmss").format(new Date())+(int)(Math.random()*90000+10000));//商户系统内部的退款单号，商户系统内部唯一
//		parametersMap.put("total_fee", total_fee);//订单金额
//		parametersMap.put("refund_fee", refund_fee);//退款金额
//		String sign = createSign("utf-8",parametersMap);//生成签名，签名算法
//		parametersMap.put("sign", sign);
		String xml = XMLUtils.createXml(parametersMap);//拼接xml请求参数
		boolean bool = sendRefundHttpXML(xml);//请求微信返回的结果，直接返回手机端
		return bool;
	}
	
	/**
	   * 微信向网络发送xml数据
	   * @throws IOException 
	   * @throws JDOMException 
	   */
	  public static boolean sendRefundHttpXML(String xml) throws IOException, JDOMException{
		  byte[] xmlbyte = xml.toString().getBytes("UTF-8");  
		  URL url = new URL("https://api.mch.weixin.qq.com/secapi/pay/refund");  
		  HttpURLConnection conn = (HttpURLConnection) url.openConnection();  
		  conn.setConnectTimeout(6* 1000);  
		  conn.setDoOutput(true);//允许输出  
		  conn.setUseCaches(false);//不使用Cache  
		  conn.setRequestMethod("POST");             
		  conn.setRequestProperty("Connection", "Keep-Alive");//维持长连接  
		  conn.setRequestProperty("Charset", "UTF-8");
		  conn.setRequestProperty("Content-Length", String.valueOf(xmlbyte.length));  
		  conn.setRequestProperty("Content-Type", "text/xml; charset=UTF-8");  
		  DataOutputStream outStream = new DataOutputStream(conn.getOutputStream());  
		  outStream.write(xmlbyte);//发送xml数据  
		  outStream.flush();  
		  if (conn.getResponseCode() != 200)   
		      throw new RuntimeException("请求url失败");  
		  BufferedReader input = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"));
		  outStream.close();
		  StringBuffer buffer = new StringBuffer();
		  String str = null;
		  while ((str = input.readLine()) != null) {
			  buffer.append(str);
		  }
		  Map map = XMLUtils.doXMLParse(buffer.toString());
		  String return_code=(String) map.get("return_code");
		  if (input != null) {
				 input.close();
		  }
		  if (outStream != null) {
			  outStream.close();
		  }
		  if (conn != null) {
			  conn.disconnect();
		  }
		  if (return_code.contains("SUCCESS")){
			  return true;
		  }else{
			  return false;
		  }
	  }
	/****************************************************************************************************/
	/**
	  * 微信生成签名，签名算法
	  * @param characterEncoding
	  * @param parameters
	  * @return
	  */
	 public static String createSign(String characterEncoding,SortedMap<String,Object> parameters,String api_key){  
	     StringBuffer sb = new StringBuffer();  
	     Set es = parameters.entrySet();//所有参与传参的参数按照accsii排序（升序）  
	     Iterator it = es.iterator();  
	     while(it.hasNext()) {  
	         Map.Entry entry = (Map.Entry)it.next();  
	         String k = (String)entry.getKey();  
	         Object v = entry.getValue();  
	         if(null != v && !"".equals(v)   
	                 && !"sign".equals(k) && !"key".equals(k)) {  
	             sb.append(k + "=" + v + "&");  
	         }  
	     }  
	     sb.append("key=" + api_key); //微信商户平台支付密钥key 
	     System.out.println("转换后的参数信息为：：："+sb.toString());
	     System.out.println("---------------------");
	     String sign = MUtil.getPwd(sb.toString()).toUpperCase();//MD5加密
	     return sign;  
	 }
	 /**
	   * 微信向网络发送xml数据
	   * @throws IOException 
	   * @throws JDOMException 
	   */
	  public static Map<String, Object> sendHttpXML(String xml,String api_key) throws IOException, JDOMException{
		  Map<String, Object> weiXinVo = null;
		  byte[] xmlbyte = xml.toString().getBytes("UTF-8");  
		  URL url = new URL("https://api.mch.weixin.qq.com/pay/unifiedorder");  
		  HttpURLConnection conn = (HttpURLConnection) url.openConnection();  
		  conn.setConnectTimeout(6* 1000);  
		  conn.setDoOutput(true);//允许输出  
		  conn.setUseCaches(false);//不使用Cache  
		  conn.setRequestMethod("POST");             
		  conn.setRequestProperty("Connection", "Keep-Alive");//维持长连接  
		  conn.setRequestProperty("Charset", "UTF-8");
		  conn.setRequestProperty("Content-Length", String.valueOf(xmlbyte.length));  
		  conn.setRequestProperty("Content-Type", "text/xml; charset=UTF-8");  
		  DataOutputStream outStream = new DataOutputStream(conn.getOutputStream());  
		  outStream.write(xmlbyte);//发送xml数据  
		  outStream.flush();  
		  if (conn.getResponseCode() != 200)   
		      throw new RuntimeException("请求url失败");  
		  BufferedReader input = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"));
		  outStream.close();
		  StringBuffer buffer = new StringBuffer();
		  String str = null;
		  while ((str = input.readLine()) != null) {
			  buffer.append(str);
		  }
		  System.out.println(buffer.toString());
		  weiXinVo = readResponse(buffer.toString(),api_key);//处理返回的xml的数据
		  if (input != null) {
				 input.close();
		 }
		  if (outStream != null) {
			  outStream.close();
		  }
		  if (conn != null) {
			  conn.disconnect();
		  }
		  return weiXinVo;
	  }
	  /**
	   * 微信返回结果
	   * @param encode
	   * @return
	   * @throws IOException
	   */
	  private static Map<String, Object> readResponse(String input , String shop_API_KEY) throws JDOMException,IOException{
		  String inputXml = input.toString();
		  Map map = XMLUtils.doXMLParse(inputXml);
		  String return_code=(String) map.get("return_code");
		  String prepay_id =null;
		  String uuid = MUtil.getRandomString(32);//RandomStringUtils.randomAlphanumeric(32)32位随机字符串
		  Map<String, Object> weiXinVo=new HashMap<String,Object>();
		  String seconds = MUtil.genTimeStamp();
		  if (return_code.contains("SUCCESS")){
		     prepay_id=(String) map.get("prepay_id");//获取到prepay_id
		     //uuid = (String) map.get("nonce_str");//获取到返回的随机字符串
		     
		     SortedMap<String, Object> signParam = new TreeMap<String, Object>();
			  signParam.put("appid", WEIXINAPPID);//app_id
			  signParam.put("partnerid", WEIXINMCHID);//微信商户账号
			  signParam.put("prepayid", prepay_id);//预付订单id
			  signParam.put("package", "Sign=WXPay");//默认sign=WXPay
			  signParam.put("noncestr", uuid);//自定义不重复的长度不长于32位
			  signParam.put("timestamp",seconds);//北京时间时间戳
			  String signAgain = createSign("UTF-8", signParam,WEIXIN_KEY);//再次生成签名
			  signParam.put("sign", signAgain);
			  weiXinVo.put("appid", WEIXINAPPID);
			  weiXinVo.put("partnerid", WEIXINMCHID);
			  weiXinVo.put("prepayid", prepay_id);
			  weiXinVo.put("package", "Sign=WXPay");
			  weiXinVo.put("noncestr", uuid);
			  weiXinVo.put("timestamp", seconds);
			  weiXinVo.put("sign",signAgain);
		  }
		  return weiXinVo;
	  }

	
}
