package org.haier.shopUpdate.util;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.Socket;

/**
 * 服务器创建多线程，用于接收不同用户的数据传输
 * <AUTHOR>
 */
@Slf4j
public class BuyhooSocketServiceThread implements Runnable{
	public Socket client;
	public BuyhooSocketServiceThread(Socket client) {
		super();
		this.client = client;
	}
	public void run() {
		InputStream inputStream = null;
		try {
			//获取用户输入的信息，
			inputStream = client.getInputStream();//获取客户端输入流
			InputStreamReader isr=new InputStreamReader(inputStream);//提高效率，将字节流转换为字符流
			BufferedReader br=new BufferedReader(isr);//将输入加入缓冲区
			System.out.println(client.getInetAddress().getHostName());

			//由Socket对象得到输入流，并构造相应的BufferedReader对象
			PrintWriter writer=new PrintWriter(client.getOutputStream());
//			BufferedReader sysin=new BufferedReader(new InputStreamReader(System.in));//从键盘获取输入

			String line=br.readLine();
			System.out.println(line);
			while(!(line=br.readLine()).equals("bye")){
				System.out.println("SERVERLINE:::"+line);
				writer.println(2);
				writer.flush();
//				System.out.println(sysin.readLine());
			}


			System.out.println("连接结束！");
			br.close();
			isr.close();
			inputStream.close();
		} catch (IOException e) {
			log.error("异常信息：",e);
		}finally {
			try {
				if(client!=null){
					client.close();//关闭客户端
					System.out.println("客户端已关闭！");
				}
			} catch (IOException e) {
				System.out.println("测试失败！");
				log.error("异常信息：",e);
			}
		}
	}

}
