package org.haier.shopUpdate.util;

import java.io.UnsupportedEncodingException;
import java.util.Map;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.domain.AlipayTradeAppPayModel;
import com.alipay.api.request.AlipayFundTransToaccountTransferRequest;
import com.alipay.api.request.AlipayTradeAppPayRequest;
import com.alipay.api.request.AlipayTradeRefundRequest;
import com.alipay.api.response.AlipayFundTransToaccountTransferResponse;
import com.alipay.api.response.AlipayTradeAppPayResponse;
import com.alipay.api.response.AlipayTradeRefundResponse;
import com.alipay.api.DefaultAlipayClient;
import org.haier.shopUpdate.project.ProjectConfig;

/**
 * 支付宝工具类
 * <AUTHOR>
 *
 */
public class AlipayPayUtil {
	//appId
	public static final String APP_ID = "****************";
	
	//应用私钥（密钥生成工具生成）
	public static final String APP_PRIVATE_KEY = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCBPM09SXFnqurjip1bzNA7z1LS6gexB7c8sT+BzBY/Gc4LsOHIUKr+GxMAt1UM1yg5smRkVYPWBW0Tc1a0gplOZeppLUXN5xKHK747SpUhDw6Eu4Yv1gE4uo8NQAAjHsKv1nZ5O30ZQIJtLTHvlv5vSg+HY/790pKkSQHKpPpiHWqJgFJJwtkkFl6+6Y9rvrKs2DXWMYRE4kq7XzRXkS9YtCBOIPrQPXYjd/tXwaSwArR2HVyxr3eq+iimfEyQIn7nd6GKlWsCS20EvesfYVEwk6m7Hir40FYxqckHQm3Z70MfxY2Zc2goTHRQK5WEEL1/8LEzLu2VqDTNc8Mpzmz9AgMBAAECggEAfg8FjrM1h84CgWIyrVlw//XxLsOSLw58oBmv/XZJeNE91s943AA7WdKkzX0F25l+phCotelnx/nQ9Dj7qOT+jqcAaAj93qJ2wxxa2NCd9/oMylFzyPMwi9oFMQJtX9RgSE2jkAsDsf7neYCtBas9kOYnkYNrNMARw2dm9PfJNqtQg3oWHqV0+ZzKCk1IjJ9o+82jhjfk0iUShBwElKQmGrFs1fMdVUy6r7Jd2Abels6iijK3eoiYSTaXyZdr5pmFf10hdsIxXLNEPB75CovbRFhXWF97l8OepZ2n7WG7kjO4qr+KWwmnxE6648MUM4MHob/jBGnRW8rsj2a0e3pFrQKBgQD4djmM/VLCDfwwlUC/ayrYoST9KslNKr84CoWV+2kzam6DD9k/yzwi0DcMDQ+2G9Q8L+Jo7XXmNbo/4jA37KxdKFaM+obKeIhdwdoRY5lqoZX/aNql6IGAEIagaL3A42/hiSP/nEFKJPWSHPeEbAvvcysmQ8beYYfDpRS9bbvZowKBgQCFKJMzrlaZCteOPjK9HdLwcnVAXf2eQDXFtDEdRzBOj2omo4fy2/0mDfCxzVlr+Jgj6Hu/207A9Vw59xeFY/xBsUdKL54UJ2+rjxQYKynup73PzWqwl/5+HgtkhGrC7tHFMPWHVLfh/aijfAt8xADeIS52bQrY2Xekjn9v1ZJI3wKBgQD4BaRrfpF3T1iY8tTAgG0z9KCk7GooUuyxN6ekUWTxw2PwBwkaKnmsUucwa3Vsl5kkP7Smg6y2kpmKnfL+UI67K8JCVu/o+6s81H8n7qG+FPFUFcp15YQoX9bUF9qbyy3rGOhLiCKQrhZJtgrLMMQ1UmUXfH7vjLJm9cqfUgQ8OwKBgASQPWs6AFHCY1zGmOqOSRpgcdBA6F4yCSW7ZSuBsxAeLh8g35ndHDHfrG4LgZMHs/8XCpaqp2pJfl2mMDIsGQsPTkgmlZ5PM66HesLRR+Cb8w9aFNv0eY5M7UsGlroTgTDm2qNGrf6auNrRd3tRJE54iKUaW5LO8SC8mXV1tku1AoGBAMwDfL+laH/8T+M2oSlUJD3fPxcEv4QXARdppdW/J826vDNWKHHJt3HXZxBhHQgB4CipNNzaXvUcYtCr4BlUn9p0xj5QxAOp/a1wk1lMOgHzlKPGn8VPL14VLRfhgvcyXlNDyifoPoiBl1S785AO7az3O5HembS49rxFOkHQXnLv";
	
	//支付宝公钥
//	public static final String ALIPAY_PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEArVNj2x0RUOw6tc5xK4EAel/qbQhCIz/chTBiTKobbQVR/d/xFQd5wAkmzwJ5OQpV/QrEOxOcbdEs81rVe3ll8Pv8Ui2vgupHpsypGWncVZnCBOOfJDzHzdGQl6wnvIqsn0Y1C8hR7gNq+SKTPWaR4dJ7FjFoua0BfhBZPglutbuyX9ry7tZ5KpcWJvpgBtHhQa0XTSvu6CaTOs2L7vvMFovHyO1fN/DFiC4FSot8GG7FLnCVulefFXOJTh293PJIzh3zpAkmZcOv4IpG62po82AsWrl4YSr6740kY2ZH0GH7WMuDx0D5UGRxi3+era4SELi88ppOJ/rIlxKfVq5TlwIDAQAB";
	public static final String ALIPAY_PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEArtoufKmx7+WgkDu0aBMNFwMgVccqRlotNCncluqShHfdKnESvE0qWOqkcvmQw8IKV93OuPnma5z39KLgAegdD4L2XTkf8s1LJUD/w1iV8mbaiAmvI05v5pHXK4sThtc0BR64lMaqL+lsQUZgh4mW29bDYLvvmvXFKusXhrSbxbSryTA+HSYc/u++JT8AUGpC0AIOy6ItfFK/p9Nb52EhexNEdEggHQMInQX/qjwKT1rAVWIzVd21QN5RNOAjnFt3uD4zCbOqR451C2pLK/LGicLezPA32K9eqONcRUKOnZpu/mUgU2iCI12MmSeLtYwDisMlHJrHt7gii23sn062jwIDAQAB";
	//回调地址
	public static final String notify_url = ProjectConfig.PROJECT_URL + "/shopUpdate/diamonds/aliPayCallBack.do";
	
	
	/**
	 * 支付宝支付,统一下单
	 * @param paramsMap 请求参数Map
	 *        {
	 *        	subject 商品标题,
	 *        	order_number 商户端订单号，唯一性,
	 *        	total_amount 支付金额，单位为元,
	 *        	notify_url 支付宝回调地址，公网可访问地址,
	 *        	param 附加参数，回调中原样返回，任意形式的字符串可为空
	 *        }
	 * @return
	 * @throws AlipayApiException
	 * @throws UnsupportedEncodingException 
	 */
	public static String alipayPay(Map<String,Object> paramsMap) throws AlipayApiException, UnsupportedEncodingException{
		//实例化客户端
		AlipayClient alipayClient = new DefaultAlipayClient("https://openapi.alipay.com/gateway.do", APP_ID, APP_PRIVATE_KEY, "JSON", "utf-8", ALIPAY_PUBLIC_KEY, "RSA2");
		//实例化具体API对应的request类,类名称和接口名称对应,当前调用接口名称：alipay.trade.app.pay
		AlipayTradeAppPayRequest request = new AlipayTradeAppPayRequest();
		//SDK已经封装掉了公共参数，这里只需要传入业务参数。以下方法为sdk的model入参方式(model和biz_content同时存在的情况下取biz_content)。
		AlipayTradeAppPayModel model = new AlipayTradeAppPayModel();
		model.setSubject(paramsMap.get("subject").toString());//交易标题
		model.setOutTradeNo(paramsMap.get("out_trade_no").toString());//商户网站唯一订单号
		model.setTimeoutExpress("30m");
		model.setTotalAmount(paramsMap.get("total_amount").toString());//支付金额
		model.setProductCode("QUICK_MSECURITY_PAY");//销售产品码，商家和支付宝签约的产品码，为固定值QUICK_MSECURITY_PAY
//		if(paramsMap.containsKey("param")){
//			String param = paramsMap.get("param").toString();//json字符串
//			model.setPassbackParams(URLEncoder.encode(param,"UTF-8"));//添加附加数据
//		}
		request.setBizModel(model);
		request.setNotifyUrl(notify_url);
		String orderString = null;
		//这里和普通的接口调用不同，使用的是sdkExecute
        AlipayTradeAppPayResponse response = alipayClient.sdkExecute(request);
        orderString = response.getBody();
		return orderString;
	}
	/**
	 * 支付宝自动转账
	 * @param out_biz_no 商户端订单号，唯一性
	 * @param payee_account 收款放支付宝账户
	 * @param amount  转账金额，单位为元
	 * @return
	 * @throws AlipayApiException 支付宝接口调用异常
	 */
	public static boolean alipayAutoPay(String out_biz_no, String payee_account, Double amount) throws AlipayApiException{
		AlipayClient alipayClient = new DefaultAlipayClient("https://openapi.alipay.com/gateway.do",AlipayPayUtil.APP_ID, APP_PRIVATE_KEY,"json","UTF-8",AlipayPayUtil.ALIPAY_PUBLIC_KEY,"RSA2");
		AlipayFundTransToaccountTransferRequest request = new AlipayFundTransToaccountTransferRequest();
		request.setBizContent("{" +
		"    \"out_biz_no\":\""+ out_biz_no +"\"," +
		"    \"payee_type\":\"ALIPAY_LOGONID\"," +
		"    \"payee_account\":\""+ payee_account +"\"," +
		"    \"amount\":\""+ amount +"\"" +
		"  }");
		AlipayFundTransToaccountTransferResponse response = alipayClient.execute(request);
		if(response.isSuccess()){
			return true;
		} else {
			return false;
		}
	}
	
	/**
	 * 支付宝退款
	 * @param out_trade_no 商户端订单号，唯一性
	 * @param refund_amount 需要退款的金额，该金额不能大于订单金额,单位为元
	 * @return
	 * @throws AlipayApiException
	 */
	public static boolean alipayTradeRefund(String out_trade_no,Double refund_amount) throws AlipayApiException{
		AlipayClient alipayClient = new DefaultAlipayClient("https://openapi.alipay.com/gateway.do",APP_ID, APP_PRIVATE_KEY,"json","UTF-8",AlipayPayUtil.ALIPAY_PUBLIC_KEY,"RSA2");
		AlipayTradeRefundRequest request = new AlipayTradeRefundRequest();
		request.setBizContent("{" +
		"\"out_trade_no\":\""+ out_trade_no +"\"," +
		"\"refund_amount\":\""+ refund_amount +"\"," +
		"\"refund_reason\":\"正常退款\"" +
		"  }");
		AlipayTradeRefundResponse response = alipayClient.execute(request);
		if(response.isSuccess()){
			return true;
		} else {
			return false;
		}
	}
}
