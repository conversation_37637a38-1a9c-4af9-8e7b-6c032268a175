package org.haier.shopUpdate.util;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.servlet.http.HttpServletRequest;

import lombok.extern.slf4j.Slf4j;
import org.apache.log4j.Logger;
@Slf4j
public class UtilForJAVA {

	/**
	 * 将对象转换为Map，排序后返回，去掉sign和空值
	 * @param obj
	 * @return
	 */
	public static SortedMap<String,Object> objectToSortMap(Object obj) {
		Field[] fields = obj.getClass().getDeclaredFields();
		SortedMap<String,Object> map = new TreeMap<String,Object>();
		for (Field f : fields) {
			try {
				f.setAccessible(true);
				if (!f.getName().equals("sign") && f.get(obj) != null) {
					map.put(f.getName(), f.get(obj));
				}
			} catch (Exception e) {
				log.error("objectToSortMap error",e);
			}
		}
		return map;
	}
	public static String getSystemTimeNow() {
		Calendar c = Calendar.getInstance();
		int year = c.get(Calendar.YEAR);
		return year + "-" + (c.get(Calendar.MONTH)+1) +"-" + c.get(Calendar.DAY_OF_MONTH) +" " + c.get(Calendar.HOUR_OF_DAY) + ":" + c.get(Calendar.MINUTE) +":"+ c.get(Calendar.SECOND);
	}

	public static String removeZeroHead(String str) {
		str = str.substring(1);
		if (str.startsWith("0") && str.length() > 1) {
			str = removeZeroHead(str);
		}
		return str;
	}
	  /**
     * 判断输入的字符串参数是否为空
     * @return boolean 空则返回true,非空则flase
     */
    public static boolean isEmpty(String input) {
        return null==input || 0==input.length() || 0==input.replaceAll("\\s", "").length();
    }


    /**
     * 判断输入的字节数组是否为空
     * @return boolean 空则返回true,非空则flase
     */
    public static boolean isEmpty(byte[] bytes){
        return null==bytes || 0==bytes.length;
    }

    /**
     * 获取Map中的属性
     * @see 由于Map.toString()打印出来的参数值对,是横着一排的...参数多的时候,不便于查看各参数值
     * @see 故此仿照commons-lang.jar中的ReflectionToStringBuilder.toString()编写了本方法
     * @return String key11=value11 \n key22=value22 \n key33=value33 \n......
     */
    public static String getStringFromMap(Map<String, String> map){
        StringBuilder sb = new StringBuilder();
        sb.append(map.getClass().getName()).append("@").append(map.hashCode()).append("[");
        for(Map.Entry<String,String> entry : map.entrySet()){
            sb.append("\n").append(entry.getKey()).append("=").append(entry.getValue());
        }
        return sb.append("\n]").toString();
    }

    /**
     * 金额元转分
     * @see 注意:该方法可处理贰仟万以内的金额,且若有小数位,则不限小数位的长度
     * @see 注意:如果你的金额达到了贰仟万以上,则不推荐使用该方法,否则计算出来的结果会令人大吃一惊,越界的问题
     * @param amount  金额的元进制字符串
     * @return String 金额的分进制字符串
     */
    public static String moneyYuanToFen(String amount){
        if(isEmpty(amount)){
            return amount;
        }
        //传入的金额字符串代表的是一个整数
        if(-1 == amount.indexOf(".")){
            return Integer.parseInt(amount) * 100 + "";
        }
        //传入的金额字符串里面含小数点-->取小数点前面的字符串,并将之转换成单位为分的整数表示
        int money_fen = Integer.parseInt(amount.substring(0, amount.indexOf("."))) * 100;
        //取到小数点后面的字符串
        String pointBehind = (amount.substring(amount.indexOf(".") + 1));
        //amount=12.3
        if(pointBehind.length() == 1){
            return money_fen + Integer.parseInt(pointBehind)*10 + "";
        }
        //小数点后面的第一位字符串的整数表示
        int pointString_1 = Integer.parseInt(pointBehind.substring(0, 1));
        //小数点后面的第二位字符串的整数表示
        int pointString_2 = Integer.parseInt(pointBehind.substring(1, 2));
        //amount==12.03,amount=12.00,amount=12.30
        if(pointString_1 == 0){
            return money_fen + pointString_2 + "";
        }else{
            return money_fen + pointString_1*10 + pointString_2 + "";
        }
    }

    /**
     * 获取一个字符串的简明效果
     * @return String 返回的字符串格式类似于"abcd***hijk"
     */
    public static String getStringSimple(String data){
        return data.substring(0,4) + "***" + data.substring(data.length()-4);
    }

    /**
     * 字符编码
     * @see 该方法通常用于对中文进行编码
     * @see 若系统不支持指定的编码字符集,则直接将<code>chinese</code>原样返回
     */
    public static String encode(String chinese, String charset){
        chinese = (chinese==null ? "" : chinese);
        try {
            return URLEncoder.encode(chinese, charset);
        } catch (UnsupportedEncodingException e) {
//            LogUtil.getLogger().error("编码字符串[" + chinese + "]时发生异常:系统不支持该字符集[" + charset + "]");
            return chinese;
        }
    }

    /**
     * 字符解码
     * @see 该方法通常用于对中文进行解码
     * @see 若系统不支持指定的解码字符集,则直接将<code>chinese</code>原样返回
     */
    public static String decode(String chinese, String charset){
        chinese = (chinese==null ? "" : chinese);
        try {
            return URLDecoder.decode(chinese, charset);
        } catch (UnsupportedEncodingException e) {
//            LogUtil.getLogger().error("解码字符串[" + chinese + "]时发生异常:系统不支持该字符集[" + charset + "]");
            return chinese;
        }
    }

    /**
     * 获取指定格式的日期
     * @param date Date格式的日期
     * @param pattern 期望的格式：
     * @return
     */
    public static String getFormatDate(Date date,String pattern){
    	SimpleDateFormat sdf=new SimpleDateFormat(pattern);
    	return sdf.format(date);
    }


    /**
     * HTML字符转义
     * @see 对输入参数中的敏感字符进行过滤替换,防止用户利用JavaScript等方式输入恶意代码
     * @see String input = <img src='http://t1.baidu.com/it/fm=0&gp=0.jpg'/>
     * @see HtmlUtils.htmlEscape(input);         //from spring.jar
     * @see StringEscapeUtils.escapeHtml(input); //from commons-lang.jar
     * @see 尽管Spring和Apache都提供了字符转义的方法,但Apache的StringEscapeUtils功能要更强大一些
     * @see StringEscapeUtils提供了对HTML,Java,JavaScript,SQL,XML等字符的转义和反转义
     * @see 但二者在转义HTML字符时,都不会对单引号和空格进行转义,而本方法则提供了对它们的转义
     * @return String 过滤后的字符串
     */
    public static String htmlEscape(String input) {
        if(isEmpty(input)){
            return input;
        }
        input = input.replaceAll("&", "&amp;");
        input = input.replaceAll("<", "&lt;");
        input = input.replaceAll(">", "&gt;");
        input = input.replaceAll(" ", "&nbsp;");
        input = input.replaceAll("'", "&#39;");   //IE暂不支持单引号的实体名称,而支持单引号的实体编号,故单引号转义成实体编号,其它字符转义成实体名称
        input = input.replaceAll("\"", "&quot;"); //双引号也需要转义，所以加一个斜线对其进行转义
        input = input.replaceAll("\n", "<br/>");  //不能把\n的过滤放在前面，因为还要对<和>过滤，这样就会导致<br/>失效了
        return input;
    }

    /**
     * 计算途经点信息
     * @param list
     * @param map
     * @return
     */
    public static List<Map<String,Object>> sortDistanceMessage(List<Map<String,Object>> list,Map<String,Object> map){
    	List<Map<String,Object>> res=new ArrayList<Map<String,Object>>();
    	res.add(map);
    	List<Map<String,Object>> startList=new ArrayList<>();
    	List<Map<String,Object>> endList=new ArrayList<>();
    	List<Map<String,Object>> source=new ArrayList<>();
    	for(int i=0;i<list.size();i++){
    		Map<String,Object> o=list.get(i);
    		Map<String,Object> m=new HashMap<>();
    		Map<String,Object> n=new HashMap<>();
    		m.putAll(o);
    		m.put("type", "1");
    		m.put("longitude", o.get("shop_longitude"));
    		m.put("latitude", o.get("shop_latitdue"));
    		m.put("index", i);
    		startList.add(m);
    		n.putAll(o);
    		n.put("type", "2");
    		n.put("longitude", o.get("shipping_longitude"));
    		n.put("latitude", o.get("shipping_latitude"));
    		n.put("index", i);
    		endList.add(n);
    	}
    	source.addAll(startList);
//    	System.out.println("源数据：："+source);
    	if(source.size()==1){
    		res.addAll(startList);
    		res.addAll(endList);
    		return res;
    	}
    	for(int j=0;j<source.size();){
    		Integer key=sortDistanceMessageSort(source, map);//最近点的坐标信息
    		if(key==-1){
    			break;
    		}
    		res.add(source.get(key));
//    		System.out.println(j+"次循环：：："+res);
    		if(source.get(key).get("type").toString().equals("1")){//如果是起点左边，则将终点坐标添加
    			Integer index=Integer.parseInt(source.get(key).get("index").toString());
    			source.add(endList.get(index));
    		}
    		map=source.get(key);
    		System.out.println("当前地点"+map);
    		source.remove(source.get(key));
    		j=0;
    	}
    	return res;
    }


    public static Integer sortDistanceMessageSort(List<Map<String,Object>> list,Map<String,Object> map){
    	int index=-1;//最小坐标的序号
    	if(null==map||map.isEmpty()){
    		return index;
    	}
    	if(list==null||list.isEmpty()||list.size()==0){
    		return index;
    	}

    	Double distance=10000000.0;//最小距离

    	for(int i=0;i<list.size();i++){
    		Double dis=calculationDis(map,list.get(i));
    		//若此距离小于最小距离，更新最小距离和记录值
    		if(dis<distance){
    			index=i;
    			distance=dis;
    		}
    	}
    	return index;
    }
    /**
     * 计算两点间的距离
     * @param start
     * @param end
     * @return
     */
    public static Double calculationDis(Map<String,Object> start,Map<String,Object> end){
    	Double lng=Double.parseDouble(start.get("longitude").toString());//起点经度
    	Double lat=Double.parseDouble(start.get("latitude").toString());//起点维度
    	Double endLng=Double.parseDouble(end.get("longitude").toString());
		Double endLat=Double.parseDouble(end.get("latitude").toString());
		Double dis=Math.sqrt(addDouble(mulDouble(subDouble(lng, endLng),subDouble(lng, endLng)),mulDouble(subDouble(lat,endLat),subDouble(lat,endLat))));

		return dis;
    }

    /**
     * Double相加
     * @param a
     * @param b
     * @return
     */
    public static Double addDouble(Double a,Double b){
    	BigDecimal a1=new BigDecimal(Double.toString(a));
    	BigDecimal b1=new BigDecimal(Double.toString(b));
    	return a1.add(b1).doubleValue();
    }
	/**
	 * Double相加
	 * @param a
	 * @param b
	 * @return
	 */
	public static Double addDouble(Object a,Object b,Integer count) {
		BigDecimal a1 = new BigDecimal(a.toString());
		BigDecimal b1 = new BigDecimal(b.toString());

		return a1.add(b1).setScale(count == null ? 2 : count,BigDecimal.ROUND_HALF_UP).doubleValue();
	}

    public static Double adDouble(Object a,Object b) {
    	BigDecimal a1 = new BigDecimal(a.toString());
    	BigDecimal b1 = new BigDecimal(b.toString());
    	return a1.add(b1).doubleValue();
    }

    /**
     * Double相乘
     * @param a
     * @param b
     * @return
     */
    public static Double mulDouble(Double a,Double b){
    	BigDecimal a1=new BigDecimal(Double.toString(a));
    	BigDecimal b1=new BigDecimal(Double.toString(b));
    	return a1.multiply(b1).doubleValue();
    }

    /**
     * Double相减
     * @param a
     * @param b
     * @return
     */
    public static Double subDouble (Double a,Double b){
    	BigDecimal a1=new BigDecimal(Double.toString(a));
    	BigDecimal b1=new BigDecimal(Double.toString(b));
    	return a1.subtract(b1).doubleValue();
    }

    /**
     * Double相除
     * @param a
     * @param b
     * @return
     */
    public static Double divideDouble(Double a,Double b){
    	BigDecimal a1=new BigDecimal(Double.toString(a));
    	BigDecimal b1=new BigDecimal(Double.toString(b));
    	return a1.divide(b1).doubleValue();
    }


    /**
	 * @Description WGS84 to 高斯投影(6度分带)
	 * @param longitude 经度
	 * @param latitude 纬度
	 * @return double[] x y
	 */
	public static double[] wgs84_To_Gauss6(double longitude, double latitude) {
		int ProjNo = 0;
		int ZoneWide; // //带宽
		double[] output = new double[2];
		double longitude1, latitude1, longitude0, X0, Y0, xval, yval;
		double a, f, e2, ee, NN, T, C, A, M, iPI;
		iPI = 0.0174532925199433; // //3.1415926535898/180.0;
		ZoneWide = 6; //6度带宽
		a = 6378137.0;
		f = 1.0 / 298.257223563; //WGS84坐标系参数
		//a = 6378245.0;f = 1.0 / 298.3; // 54年北京坐标系参数
		// //a=6378140.0; f=1/298.257; //80年西安坐标系参数
		ProjNo = (int) (longitude / ZoneWide);
		longitude0 = (double)(ProjNo * ZoneWide + ZoneWide / 2);
		longitude0 = longitude0 * iPI;
		longitude1 = longitude * iPI; // 经度转换为弧度
		latitude1 = latitude * iPI; // 纬度转换为弧度
		e2 = 2 * f - f * f;
		ee = e2 / (1.0 - e2);
		NN = a
				/ Math.sqrt(1.0 - e2 * Math.sin(latitude1)
						* Math.sin(latitude1));
		T = Math.tan(latitude1) * Math.tan(latitude1);
		C = ee * Math.cos(latitude1) * Math.cos(latitude1);
		A = (longitude1 - longitude0) * Math.cos(latitude1);
		M = a
				* ((1 - e2 / 4 - 3 * e2 * e2 / 64 - 5 * e2 * e2 * e2 / 256)
						* latitude1
						- (3 * e2 / 8 + 3 * e2 * e2 / 32 + 45 * e2 * e2 * e2
								/ 1024) * Math.sin(2 * latitude1)
						+ (15 * e2 * e2 / 256 + 45 * e2 * e2 * e2 / 1024)
						* Math.sin(4 * latitude1) - (35 * e2 * e2 * e2 / 3072)
						* Math.sin(6 * latitude1));
		// 因为是以赤道为Y轴的，与我们南北为Y轴是相反的，所以xy与高斯投影的标准xy正好相反;
		xval = NN
				* (A + (1 - T + C) * A * A * A / 6 + (5 - 18 * T + T * T + 14
						* C - 58 * ee)
						* A * A * A * A * A / 120);
		yval = M
				+ NN
				* Math.tan(latitude1)
				* (A * A / 2 + (5 - T + 9 * C + 4 * C * C) * A * A * A * A / 24 + (61
						- 58 * T + T * T + 270 * C - 330 * ee)
						* A * A * A * A * A * A / 720);
		X0 = 1000000L * (ProjNo + 1) + 500000L;
		Y0 = 0;
		xval = xval + X0;
		yval = yval + Y0;
		output[0] = xval;
		output[1] = yval;
		return output;
	}

	/**
	 * 记录接口的请求记录
	 * @param log
	 * @param request
	 */
	public static void recordLog(Logger log,HttpServletRequest request){
		//获取请求路径
		String url=request.getRequestURI();
		Enumeration<String> enu=request.getParameterNames();
		List<String> list=new ArrayList<>();
		list.add("online.do");
		list.add("macOnLine.do");
		list.add("provingUpdate.do");
		list.add("getNewVersionNumber.do");
		list.add("pcGoods.do");
		list.add("pcSaleList.do");
		list.add("queryGoodsCountInShop.do");
		list.add("pcQuerySaleList.do");
		list.add("pcGoodsKind.do");
		list.add("appNewGoods.do");
		list.add("getListCountByStatus.do");
		for(int i=0;i<list.size();i++){
			if(url.indexOf(list.get(i))>=0){
				return;
			}
		}
		String msg="请求的参数内容为:";
		while (enu.hasMoreElements()) {
			String string = (String) enu.nextElement();
			msg+=string+"="+request.getParameter(string)+"&";
		}
		if(url.indexOf("appNewGoods.do")>=0&&msg.indexOf("1546564935088")>=0){
			return;
		}
		log.info("请求的路径为："+url);
		if(log.isDebugEnabled()){
			log.debug(msg.substring(0,msg.length()-1));
		}
		if(log.isInfoEnabled()){
			log.info(msg.substring(0, msg.length()-1));
		}
	}

	public static void recordLog(HttpServletRequest request){
		//获取请求路径
		String url=request.getRequestURI();
		Enumeration<String> enu=request.getParameterNames();
		List<String> list=new ArrayList<>();
		list.add("online.do");
		list.add("macOnLine.do");
		list.add("provingUpdate.do");
		list.add("getNewVersionNumber.do");
		list.add("pcGoods.do");
		list.add("pcSaleList.do");
		list.add("queryGoodsCountInShop.do");
		list.add("pcQuerySaleList.do");
		list.add("pcGoodsKind.do");
		list.add("appNewGoods.do");
		for(int i=0;i<list.size();i++){
			if(url.indexOf(list.get(i))>=0){
				return;
			}
		}
		String msg="请求的参数内容为:";
		while (enu.hasMoreElements()) {
			String string = (String) enu.nextElement();
			msg+=string+"="+request.getParameter(string)+"&";
		}
		if(url.indexOf("appNewGoods.do")>=0&&msg.indexOf("1546564935088")>=0){
			return;
		}
//		log.info("请求的路径为："+url);
		System.out.println(System.currentTimeMillis()+"请求的路径为："+url);

		System.out.println(msg.substring(0,msg.length()-1));
//		if(log.isDebugEnabled()){
//			log.debug(msg.substring(0,msg.length()-1));
//		}
//		if(log.isInfoEnabled()){
//			log.info(msg.substring(0, msg.length()-1));
//		}
	}
	/**
	 * 记录接口的请求记录
	 * @param log
	 * @param request
	 */
	public static Integer  recordLog(Logger log,HttpServletRequest request,Integer k){
		//获取请求路径
		String url=request.getRequestURI();
		Enumeration<String> enu=request.getParameterNames();
		log.info("请求的路径为："+url);
		String msg="请求的参数内容为:";
		while (enu.hasMoreElements()) {
			String string = (String) enu.nextElement();
			msg+=string+"="+request.getParameter(string)+"&";
		}
		if(log.isDebugEnabled()){
			log.debug(msg.substring(0,msg.length()-1));
		}
		if(log.isInfoEnabled()){
			log.info(msg.substring(0, msg.length()-1));
		}
		return ++k;
	}

	public static boolean isDouble(String str){
		try {
			if(null==str||str.equals("")){
				return false;
			}
			Double.parseDouble(str);
			return true;
		} catch (Exception e) {
			log.error("异常信息：",e);
			return false;
		}
	}

	/**
	 * 将所接口上传的参数放入到一个Map中
	 * @param request
	 * @return
	 */
	public static Map<String,Object> getParamMap(HttpServletRequest request){
		Map<String,Object> map=new HashMap<>();
		Enumeration<String> enu=request.getParameterNames();
		while (enu.hasMoreElements()) {
			String string = (String) enu.nextElement();
//			msg+=string+"="+request.getParameter(string)+"&";
			map.put(string, request.getParameter(string));
		}
		return map;
	}

	/**
	 * 校验手机号格式是否正确
	 * @param mobile
	 * @return
	 */
	public static boolean checkPhone(String mobile){
		String mobileRegEx = "^1[3,4,5,6,7,8,9][0-9]{9}$";

		Pattern pattern = Pattern.compile(mobileRegEx);
		Matcher matcher = pattern.matcher(mobile);

		return matcher.matches();
	}

	/**
	 * bigDecimal格式化
	 * @param decimal
	 * @return
	 */
	public static BigDecimal formatBigDecimal(BigDecimal decimal){
		return new BigDecimal(decimal.stripTrailingZeros().toPlainString());
	}

}
