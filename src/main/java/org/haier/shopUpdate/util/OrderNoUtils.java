package org.haier.shopUpdate.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;

/**
 * 订单编号生产工具
 * @ClassName OrderNoUtils
 * <AUTHOR>
 * @Date 2023/9/9 16:11
 **/
public class OrderNoUtils {
    private static int sequence = 0; // 编码因子

    /**
     * 生成编号
     * @return
     */
    public static synchronized String createOrderNo(String prefix) {
        String time = DateUtil.format(DateUtil.date(), "yyyyMMddHHmmssSSS");
        if (sequence > 9999) {
          sequence = 0;
        }
        return StrUtil.concat(true, prefix, time, String.format("%04d", sequence++));
    }
}
