package org.haier.shopUpdate.util;
/**
 * 用来存放返回结果集
 * <AUTHOR>
 *
 */
public class PurResult {
	//返回状态
	private Integer status;
	//返回提示信息
	private String msg="成功";
	//返回的数据
	private Object data;
	//返回的辅助信息
	private Object cord;
	//总共多少条
	private Integer count;
	
	private Integer countNum;
	
	private Integer total;
	private Object rows;
	
	public Integer getTotal() {
		return total;
	}
	public void setTotal(Integer total) {
		this.total = total;
	}
	public Object getRows() {
		return rows;
	}
	public void setRows(Object rows) {
		this.rows = rows;
	}
	public PurResult() {
		super();
	}
	public PurResult(Integer status, String msg) {
		super();
		this.status = status;
		this.msg = msg;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public String getMsg() {
		return msg;
	}
	public void setMsg(String msg) {
		this.msg = msg;
	}
	public Object getData() {
		return data;
	}
	public void setData(Object data) {
		this.data = data;
	}
	public Object getCord() {
		return cord;
	}
	public void setCord(Object cord) {
		this.cord = cord;
	}
	public Integer getCount() {
		return count;
	}
	public void setCount(Integer count) {
		this.count = count;
	}
	public Integer getCountNum() {
		return countNum;
	}
	public void setCountNum(Integer countNum) {
		this.countNum = countNum;
	}
	@Override
	public String toString() {
		return "PurResult [status=" + status + ", msg=" + msg + ", data=" + data + ", cord=" + cord + ", count=" + count
				+ ", countNum=" + countNum + ", total=" + total + ", rows=" + rows + "]";
	}
	
}	
