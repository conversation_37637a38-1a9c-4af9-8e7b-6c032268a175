package org.haier.shopUpdate.util;

import java.util.List;

/**
 * 用来记录订单
 * <AUTHOR>
 *
 */
public class ShopsResult {
	//订单返回结果判断:1、成功；0、失败；
	private Integer status;
	//提示信息
	private String msg="操作成功";
	//返回的结果集合
	private Object data;
	//分页查询的当前页数
	private Integer pageIndex;
	//分页查询的当前页数量
	private Integer pageSize;
	//分页查询的总页数
	private Integer pageCount;
	//辅助参数返回值
	private Object Redundant;
	//辅助参数返回值
	private Object object;
	
	private Integer count;
	
    public Integer getCount() {
		return count;
	}

	public void setCount(Integer count) {
		this.count = count;
	}

	//总数量
    private Integer total;
    
    private List<?> rows;
	
	public ShopsResult() {
		super();
	}
	
	public ShopsResult(Integer status, String msg) {
		super();
		this.status = status;
		this.msg = msg;
	}

	public ShopsResult(Integer status, String msg, Object data, Integer pageIndex, Integer pageSize, Integer pageCount,
			Object redundant, Object object) {
		super();
		this.status = status;
		this.msg = msg;
		this.data = data;
		this.pageIndex = pageIndex;
		this.pageSize = pageSize;
		this.pageCount = pageCount;
		Redundant = redundant;
		this.object = object;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public String getMsg() {
		return msg;
	}
	public void setMsg(String msg) {
		this.msg = msg;
	}
	public Object getData() {
		return data;
	}
	public void setData(Object data) {
		this.data = data;
	}
	public Integer getPageIndex() {
		return pageIndex;
	}
	public void setPageIndex(Integer pageIndex) {
		this.pageIndex = pageIndex;
	}
	public Integer getPageSize() {
		return pageSize;
	}
	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}
	public Integer getPageCount() {
		return pageCount;
	}
	public void setPageCount(Integer pageCount) {
		this.pageCount = pageCount;
	}
	public Object getRedundant() {
		return Redundant;
	}
	public void setRedundant(Object redundant) {
		Redundant = redundant;
	}
	public Object getObject() {
		return object;
	}
	public void setObject(Object object) {
		this.object = object;
	}
	public Integer getTotal() {
		return total;
	}

	public void setTotal(Integer total) {
		this.total = total;
	}

	public List<?> getRows() {
		return rows;
	}

	public void setRows(List<?> rows) {
		this.rows = rows;
	}

	@Override
	public String toString() {
		return "ShopsResult [status=" + status + ", msg=" + msg + ", data=" + data + ", pageIndex=" + pageIndex
				+ ", pageSize=" + pageSize + ", pageCount=" + pageCount+ "]";
	}
	public static final Integer SUCCESS = 1;
	public static final Integer FAIL = 0;
	public static ShopsResult ok(){
		ShopsResult sr = new ShopsResult();
		sr.setStatus(SUCCESS);
		return sr;
	}
	public static ShopsResult ok(Object data){
		ShopsResult sr = new ShopsResult();
		sr.setStatus(SUCCESS);
		sr.setData(data);
		return sr;
	}
	public static ShopsResult ok(String msg,Object data){
		ShopsResult sr = new ShopsResult();
		sr.setStatus(SUCCESS);
		sr.setMsg(msg);
		sr.setData(data);
		return sr;
	}

	public static ShopsResult fail(String msg){
		ShopsResult sr = new ShopsResult();
		sr.setStatus(FAIL);
		sr.setMsg(msg);
		return sr;
	}

	//判断成功还是失败
	public boolean hasSuccess() {
		return status.equals(SUCCESS);
	}
	public boolean hasFailed() {
		return !status.equals(SUCCESS);
	}
}
