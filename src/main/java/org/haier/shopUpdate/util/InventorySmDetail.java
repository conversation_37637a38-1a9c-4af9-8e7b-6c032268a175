package org.haier.shopUpdate.util;

/**
 * 盘库-扫码添加商品详情
 * <AUTHOR>
 *
 */
public class InventorySmDetail {
	//条码
	private String goodsBarcode;
	//库存
	private String goodsCount;// 查询的数据条数
	//名称
	private String goodsName;
	//进价
	private String goodsInPrice;
	//图片
	private String goodsPicturePath;
	//单位
	private String unit;
	//上次盘库时间
	private String endTime;
	//上次盘库数量
	private String inventoryCount;
	//上次盘库 到今天的时间
	private String dayCount;
	//上次盘库时间-到今天的入库数量
	private String inBoundCount;
	//上次盘库时间-到今天的出库数量
	private String outBoundCount;
	//商品计件类型：0、按件；1、按重量
	private Integer goodsChengType;

	public Integer getGoodsChengType() {
		return goodsChengType;
	}

	public void setGoodsChengType(Integer goodsChengType) {
		this.goodsChengType = goodsChengType;
	}

	public String getGoodsBarcode() {
		return goodsBarcode;
	}
	public void setGoodsBarcode(String goodsBarcode) {
		this.goodsBarcode = goodsBarcode;
	}
	public String getGoodsCount() {
		return goodsCount;
	}
	public void setGoodsCount(String goodsCount) {
		this.goodsCount = goodsCount;
	}
	public String getGoodsName() {
		return goodsName;
	}
	public void setGoodsName(String goodsName) {
		this.goodsName = goodsName;
	}
	public String getGoodsInPrice() {
		return goodsInPrice;
	}
	public void setGoodsInPrice(String goodsInPrice) {
		this.goodsInPrice = goodsInPrice;
	}
	public String getGoodsPicturePath() {
		return goodsPicturePath;
	}
	public void setGoodsPicturePath(String goodsPicturePath) {
		this.goodsPicturePath = goodsPicturePath;
	}
	public String getUnit() {
		return unit;
	}
	public void setUnit(String unit) {
		this.unit = unit;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	public String getInventoryCount() {
		return inventoryCount;
	}
	public void setInventoryCount(String inventoryCount) {
		this.inventoryCount = inventoryCount;
	}
	public String getDayCount() {
		return dayCount;
	}
	public void setDayCount(String dayCount) {
		this.dayCount = dayCount;
	}
	public String getInBoundCount() {
		return inBoundCount;
	}
	public void setInBoundCount(String inBoundCount) {
		this.inBoundCount = inBoundCount;
	}
	public String getOutBoundCount() {
		return outBoundCount;
	}
	public void setOutBoundCount(String outBoundCount) {
		this.outBoundCount = outBoundCount;
	}
	
	
}
