package org.haier.shopUpdate.util;

import cn.hutool.core.lang.Singleton;
import cn.hutool.core.lang.Snowflake;

public class SnowIdUtils {
    private static final Snowflake SNOWFLAKE = getSnowflake(1L, 1L);

    public SnowIdUtils() {
    }

    public static Long getNextId() {
        return SNOWFLAKE.nextId();
    }

    public static Snowflake getSnowflake(long workerId, long datacenterId) {
        return (Snowflake)Singleton.get(Snowflake.class, new Object[]{workerId, datacenterId});
    }

    public static void main(String[] args) {
        System.out.println(getNextId());
    }
}
