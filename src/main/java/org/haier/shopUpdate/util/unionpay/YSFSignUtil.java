package org.haier.shopUpdate.util.unionpay;

import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.SortedMap;

@Slf4j
public class YSFSignUtil {

//	private static final Logger log = LoggerFactory.getLogger(YSFSign.class);

	// 网关请求 签名字符串key值
	private static final String KEY_GATEWAY_SIGNATURE = "signature";

    /**
     * 按上传公钥信息对签名串进行验签
     *
     * @param publickKey
     * @param paramMap
     * @return
     */
    public static boolean verifySignByPublick(String signature, String publickKey, Map<String, Object> paramMap) {
        // 1. 剔除paramMap中，不参与签名的字段，签名字段
        Map<String, Object> tmpMap = new HashMap<String, Object>();
        tmpMap.putAll(paramMap);
        // 2.剔除Constants.KEY_GATEWAY_SIGNATURE字段内容
        tmpMap.remove(KEY_GATEWAY_SIGNATURE);

        // 3 签名参数按文档规则拼装
        String linkStr = SignBuildUtils.createLinkString(tmpMap);
        System.out.println(linkStr);
        // 4. ，对待签名串使用SHA-256（或SM3）做摘要（摘要结果转16进制小写字符串）
        String digest = MessageDigestUtils.getDigest(linkStr, "sha-256");
        // 5. 摘要结果转16进制小写字符串
        if (!StringUtils.isEmpty(digest)) {
            digest = digest.toLowerCase();
        }

        boolean verify = false;
        try {
            verify = RSAUtils.rsa256CheckContent(digest,signature,publickKey,"UTF-8");
        } catch (ApiException e) {
            log.error("异常信息：",e);
        }
        return verify;
    }

    /**
     * 按照文档要求，使用cert，对paramMap进行签名, 得到destSign
     *
     * @param privateKey 证书信息
     * @param paramMap   参数
     * @return 签名信息
     */
    public static String getSignParamByPrivateKey(String privateKey, Map<String, Object> paramMap) {
        // 1. 剔除paramMap中，不参与签名的字段，签名字段
        Map<String, Object> tmpMap = new HashMap<String, Object>();
        tmpMap.putAll(paramMap);
        // 2.剔除Constants.KEY_GATEWAY_SIGNATURE字段内容
        tmpMap.remove(KEY_GATEWAY_SIGNATURE);

        // 3 签名参数按文档规则拼装
        String linkStr = SignBuildUtils.createLinkString(tmpMap);

        // 4. ，对待签名串使用SHA-256（或SM3）做摘要（摘要结果转16进制小写字符串）
        String digest = MessageDigestUtils.getDigest(linkStr, "sha-256");
        // 5. 摘要结果转16进制小写字符串
        if (!StringUtils.isEmpty(digest)) {
            digest = digest.toLowerCase();
        }

        // 6. 使用银联颁发给服务商的RSA私钥（或国密证书私钥）对摘要做签名
        String destSign = null;
        try {
            // 7. 对签名做Base64编码 rsa256SignByPrivateKey 方法已经做了base64的转换
            destSign = RSAUtils.rsa256SignByPrivateKey(digest, privateKey, "utf-8");
        } catch (ApiException e) {
            log.error("异常信息：",e);
        }
        return destSign;
    }
    public static String getSignParamByPrivateKey2(String privateKey, Map<String, Object> paramMap) {
        // 1. 剔除paramMap中，不参与签名的字段，签名字段
        Map<String, Object> tmpMap = new HashMap<String, Object>();
        tmpMap.putAll(paramMap);
        // 2.剔除Constants.KEY_GATEWAY_SIGNATURE字段内容
        tmpMap.remove(KEY_GATEWAY_SIGNATURE);

        // 3 签名参数按文档规则拼装
        String linkStr = SignBuildUtils.createLinkString(tmpMap);

        // 4. ，对待签名串使用SHA-256（或SM3）做摘要（摘要结果转16进制小写字符串）
        String digest = MessageDigestUtils.getDigest(linkStr, "sha-256");
        // 5. 摘要结果转16进制小写字符串
        if (!StringUtils.isEmpty(digest)) {
            digest = digest.toLowerCase();
        }

        // 6. 使用银联颁发给服务商的RSA私钥（或国密证书私钥）对摘要做签名
        String destSign = null;
        try {
            // 7. 对签名做Base64编码 rsa256SignByPrivateKey 方法已经做了base64的转换
            destSign = RSAUtils.rsa256SignByPrivateKey(digest, privateKey, "utf-8");
        } catch (ApiException e) {
            log.error("异常信息：",e);
        }
        return destSign;
    }

}
