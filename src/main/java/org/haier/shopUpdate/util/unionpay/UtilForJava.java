package org.haier.shopUpdate.util.unionpay;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
* @author: 作者:王恩龙
* @version: 2020年7月28日 上午9:31:24
* 工具类，存放常用的java静态方法
*
*/
public class UtilForJava {
	
	/**
	 * 将指定时间转换成指定格式
	 * @param format 时间格式：默认yyyy-MM-dd HH:mm:ss
	 * @param time 需要转换的时间，默认当前时间
	 * @return
	 */
	public static String getNow(String format,String time) {
		if(null == format) {
			format = "yyyy-MM-dd HH:mm:ss";
		}
		SimpleDateFormat  sdf = new SimpleDateFormat(format);
		if(null == time) {
			return sdf.format(new Date());
		}
		
		return sdf.format(new Date(time));
	}
	/**
	 * 
	 * @param a
	 * @param b
	 * @return
	 */
	public static Double mulDoubleToDouble(Object a,Object b) {
		BigDecimal a1 = new BigDecimal(a.toString());
		BigDecimal b1 = new BigDecimal(b.toString());
		return (a1.multiply(b1).setScale(2, BigDecimal.ROUND_HALF_UP)).doubleValue();// 返回两位小数的结果;
	}
	
	public static String mulDoubleToInteger(Object a,Object b) {
		BigDecimal a1 = new BigDecimal(a.toString());
		BigDecimal b1 = new BigDecimal(b.toString());
		return (a1.multiply(b1).setScale(0, BigDecimal.ROUND_HALF_UP)).intValue()+"";// 返回两位小数的结果;
	}
	
	public static Integer mulDoubleToInt(Object a,Object b) {
		BigDecimal a1 = new BigDecimal(a.toString());
		BigDecimal b1 = new BigDecimal(b.toString());
		return (a1.multiply(b1).setScale(0, BigDecimal.ROUND_HALF_UP)).intValue();// 返回整数
	}
	
	public static Double addDouble(Object a,Object b) {
		BigDecimal a1 = new BigDecimal(a.toString());
		BigDecimal b1 = new BigDecimal(b.toString());
		return (a1.add(b1).setScale(2, BigDecimal.ROUND_HALF_UP)).doubleValue();// 返回两位小数的结果;
	}
	
	/**
	 * 保留两位小数的除法
	 * @param a
	 * @param b
	 * @return
	 */
	public static Double divideDouble(Object a,Object b) {
		BigDecimal a1 = new BigDecimal(a.toString());
		BigDecimal b1 = new BigDecimal(b.toString());
		return (a1.divide(b1).setScale(2,BigDecimal.ROUND_HALF_UP)).doubleValue();
	}
	
	
	/**
	 * double相减
	 * @param a
	 * @param b
	 * @return
	 */
	public static Double subDouble(Object a,Object b) {
		BigDecimal a1 = new BigDecimal(a.toString());
		BigDecimal b1 = new BigDecimal(b.toString());
		
		return (a1.subtract(b1).setScale(2,BigDecimal.ROUND_HALF_UP)).doubleValue();
	}
	/**
	 * 测试
	 * @param args
	 */
	
	/**
	 * map转String
	 * @param map
	 * @return
	 */
	public static String getJsonMap(Map<String,Object> map){
		StringBuffer sbf = new StringBuffer();
		Set<String> keys = map.keySet();
		sbf.append("{");
		for(String str : keys){
		sbf.append("\"");
		sbf.append(str);
		sbf.append("\":");
		if(map.get(str) instanceof Map){
			sbf.append(getJsonMap((Map)map.get(str)));
		}else if(map.get(str) instanceof List){
			sbf.append(getJsonList((List) map.get(str)));
		}else{
			sbf.append("\"");
			sbf.append(map.get(str));
			sbf.append("\"");
		}
		sbf.append(",");
		}
		sbf = new StringBuffer(sbf.substring(0,sbf.length()-1));
		sbf.append("}");
		return sbf.toString();
	}
	
	/**
	 * map转String
	 * @param map
	 * @return
	 */
	public static String getJsonMapString(Map<String,String> map){
		StringBuffer sbf = new StringBuffer();
		Set<String> keys = map.keySet();
		sbf.append("{");
		for(String str : keys){
			sbf.append("\"");
			sbf.append(str);
			sbf.append("\":");
			sbf.append("\"");
			sbf.append(map.get(str));
			sbf.append("\"");
			sbf.append(",");
		}
		sbf = new StringBuffer(sbf.substring(0,sbf.length()-1));
		sbf.append("}");
		return sbf.toString();
	}
	
	/**
	 * List集合转String
	 * @param list
	 * @return
	 */
	public static String getJsonList(List list){
		StringBuffer sbf = new StringBuffer("[");
		for (Object object : list) {
			if(object instanceof List){
				sbf.append(getJsonList((List) object));
			}else if(object instanceof Map){
				sbf.append(getJsonMap((Map)object));
			}else{
				sbf.append(object.toString());
			}
			sbf.append(",");
		}
		sbf = new StringBuffer(sbf.substring(0,sbf.length()-1));
		sbf.append("]");
		return sbf.toString();
	}
}
