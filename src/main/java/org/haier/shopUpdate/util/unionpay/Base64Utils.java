package org.haier.shopUpdate.util.unionpay;


/**
 * 
 * 注意：无需引入codec包下的类，可直接使用spring自带的同名工具类Base64Utils。
 * @see com.uninpay.demo.util.springframework.util.Base64Utils
 * <AUTHOR>
 *
 * 2018年10月9日
 */
public class Base64Utils {

	/**
	 * base64的byte转换为普通字符串的byte
	 * @param src
	 * @return
	 */
	public static byte[] decode(byte[] src) {
		return Base64.decodeBase64(src);
	}
	
	/**
	 * byte转为base64字符串
	 */
	public static String encodeToString(byte[] src) {
		return new String(Base64.encodeBase64(src));
	}
	
	/**
	 * base64字符串转为byte
	 * @param src
	 * @return
	 */
	public static byte[] decodeFromString(String src) {
		return decode(src.getBytes());
	}
}
