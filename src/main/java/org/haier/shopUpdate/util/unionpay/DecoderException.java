/*
 * Copyright 2001-2004 The Apache Software Foundation.
 * 
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * 
 *      http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ 



package org.haier.shopUpdate.util.unionpay;

/**
 * Thrown when a Decoder has encountered a failure condition during a decode. 
 * 
 * <AUTHOR> Software Foundation
 * @version $Id: DecoderException.java,v 1.9 2004/02/29 04:08:31 tobrien Exp $
 */
public class DecoderException extends Exception {

	private static final long serialVersionUID = -8524444543640582985L;

	/**
     * Creates a DecoderException
     * 
     * @param pMessage A message with meaning to a human
     */
    public DecoderException(String pMessage) {
        super(pMessage);
    }

}  

