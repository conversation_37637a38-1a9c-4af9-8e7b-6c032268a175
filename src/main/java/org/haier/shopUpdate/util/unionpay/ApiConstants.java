package org.haier.shopUpdate.util.unionpay;

public class ApiConstants {
    public static final String SIGN_TYPE                      = "sign_type";

    public static final String SIGN_TYPE_RSA                  = "RSA";

    /**
     * sha256WithRsa 算法请求类型
     */
    public static final String SIGN_TYPE_RSA2                 = "RSA2";

    public static final String SIGN_ALGORITHMS                = "SHA1WithRSA";

    public static final String SIGN_SHA256RSA_ALGORITHMS      = "SHA256WithRSA";

    public static final String ENCRYPT_TYPE_AES               = "AES";

    public static final String APP_ID                         = "appid";

    public static final String SERVICE                        = "service";

    public static final String TIMESTAMP                      = "timestamp";

    public static final String VERSION                        = "version";

    public static final String SIGN                           = "sign";

    public static final String CHARSET                        = "charset";

    public static final String NOTIFY_URL                     = "notify_url";

    public static final String CONTENT_KEY                    = "content";

    /**  Date默认时区 **/
    public static final String DATE_TIMEZONE                  = "GMT+8";

    /** UTF-8字符集 **/
    public static final String CHARSET_UTF8                   = "UTF-8";

    /** GBK字符集 **/
    public static final String CHARSET_GBK                    = "GBK";

    /** JSON 应格式 */
    public static final String FORMAT_JSON                    = "json";

    /** 默认时间格式 **/
    public static final String DATE_TIME_FORMAT               = "yyyy-MM-dd HH:mm:ss";

    /**
     * 签名验签错误
     */
    public static final int API_STATUS_ERROR_SIGN = 101;

    /**
     * 加解密错误
     */
    public static final int API_STATUS_ERROR_ENCRYT_DECYPT = 102;
    
    public static final String API_MSG_ERROR_RESPONSE_SIGN_FAIL = "数据验签失败";
    public static final String API_MSG_ERROR_RESPONSE_SIGN_EXCEPTION = "数据验签异常";
}
