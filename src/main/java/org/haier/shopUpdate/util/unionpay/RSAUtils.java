package org.haier.shopUpdate.util.unionpay;

import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayInputStream;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.ObjectInputStream;
import java.io.StringWriter;
import java.security.Key;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

import javax.crypto.Cipher;


/**
 * rsa加解密工具类
 */
@Slf4j
public class RSAUtils {

    /**
     * sha256WithRsa 加签
     *
     * @param content
     * @param privateKey
     * @param charset
     * @return
     * @throws ApiException
     */
    public static String rsa256Sign(String content, String privateKey, String charset) throws ApiException {
        try {
            PrivateKey priKey = getPrivateKeyFromPKCS8(ApiConstants.SIGN_TYPE_RSA,
                    new ByteArrayInputStream(privateKey.getBytes()));

            Signature signature = Signature.getInstance(ApiConstants.SIGN_SHA256RSA_ALGORITHMS);
            signature.initSign(priKey);

            if (StringUtils.isEmpty(charset)) {
                signature.update(content.getBytes());
            } else {
                signature.update(content.getBytes(charset));
            }

            byte[] signed = signature.sign();
//            String a = new String(Base64.encodeBase64(signed));
//            String b = Base64Util.getBase64(signed);
            String c = Base64Utils.encodeToString(signed);
            return c;
        } catch (Exception e) {
            throw new ApiException(ApiConstants.API_STATUS_ERROR_SIGN + "", "数据签名失败:" + e.getLocalizedMessage());
        }
    }

    /**
     * sha256WithRsa 加签
     *
     * @param content
     * @param privateKey
     * @param charset
     * @return
     * @throws ApiException
     */
    public static String rsa256SignByPrivateKey(String content, String privateKey, String charset) throws ApiException {
        try {
            PrivateKey priKey = getPrivateKeyFromPKCS8(ApiConstants.SIGN_TYPE_RSA,
                    new ByteArrayInputStream(privateKey.getBytes()));

            Signature signature = Signature.getInstance(ApiConstants.SIGN_SHA256RSA_ALGORITHMS);
            signature.initSign(priKey);

            if (StringUtils.isEmpty(charset)) {
                signature.update(content.getBytes());
            } else {
                signature.update(content.getBytes(charset));
            }

            byte[] signed = signature.sign();

            return Base64Utils.encodeToString(signed);
        } catch (Exception e) {
            throw new ApiException(ApiConstants.API_STATUS_ERROR_SIGN + "", "数据签名失败:" + e.getLocalizedMessage());
        }
    }

    public static PrivateKey getPrivateKeyFromPKCS8(String algorithm, InputStream ins) throws Exception {
        if (ins == null || StringUtils.isEmpty(algorithm)) {
            return null;
        }
        byte[] encodedKey = StreamUtil.readText(ins).getBytes();
        encodedKey = Base64Utils.decode(encodedKey);

        KeyFactory keyFactory = KeyFactory.getInstance(algorithm);

        return keyFactory.generatePrivate(new PKCS8EncodedKeySpec(encodedKey));
    }

    /**
     * rsa内容验签,包括rsa、rea2
     *
     * @param content
     * @param sign
     * @param publicKey
     * @param charset
     * @param signType
     * @return
     * @throws ApiException
     */
    public static boolean rsaCheck(String content, String sign, String publicKey, String charset, String signType)
            throws ApiException {

        if (ApiConstants.SIGN_TYPE_RSA.equals(signType)) {
            return rsaCheckContent(content, sign, publicKey, charset);
        } else if (ApiConstants.SIGN_TYPE_RSA2.equals(signType)) {
            return rsa256CheckContent(content, sign, publicKey, charset);
        } else {
            throw new ApiException(ApiConstants.API_STATUS_ERROR_SIGN + "", ApiConstants.API_MSG_ERROR_RESPONSE_SIGN_FAIL);
        }
    }

    /**
     * rsa内容验签
     *
     * @param content
     * @param sign
     * @param publicKey
     * @param charset
     * @return
     * @throws ApiException
     */
    public static boolean rsaCheckContent(String content, String sign, String publicKey, String charset)
            throws ApiException {
        try {
            PublicKey pubKey = getPublicKeyFromX509("RSA", new ByteArrayInputStream(publicKey.getBytes()));

            Signature signature = Signature.getInstance(ApiConstants.SIGN_ALGORITHMS);
            signature.initVerify(pubKey);

            if (StringUtils.isEmpty(charset)) {
                signature.update(content.getBytes());
            } else {
                signature.update(content.getBytes(charset));
            }
            return signature.verify(Base64Utils.decodeFromString(sign));
        } catch (Exception e) {
            throw new ApiException(ApiConstants.API_STATUS_ERROR_SIGN + "", ApiConstants.API_MSG_ERROR_RESPONSE_SIGN_EXCEPTION);
        }
    }

    /**
     * rsa2内容验签
     *
     * @param content
     * @param sign
     * @param publicKey
     * @param charset
     * @return
     * @throws ApiException
     */
    public static boolean rsa256CheckContent(String content, String sign, String publicKey, String charset)
            throws ApiException {
        try {
            PublicKey pubKey = getPublicKeyFromX509("RSA", new ByteArrayInputStream(publicKey.getBytes()));

            Signature signature = Signature.getInstance(ApiConstants.SIGN_SHA256RSA_ALGORITHMS);
            signature.initVerify(pubKey);

            if (StringUtils.isEmpty(charset)) {
                signature.update(content.getBytes());
            } else {
                signature.update(content.getBytes(charset));
            }

            return signature.verify(Base64Utils.decodeFromString(sign));
        } catch (Exception e) {
            throw new ApiException("RSA content = " + content + ",sign=" + sign + ",charset = " + charset, e);
        }
    }


    public static PublicKey getPublicKeyFromX509(String algorithm, InputStream ins) throws Exception {
        KeyFactory keyFactory = KeyFactory.getInstance(algorithm);

        StringWriter writer = new StringWriter();
        StreamUtil.io(new InputStreamReader(ins), writer);

        byte[] encodedKey = writer.toString().getBytes();

        encodedKey = Base64Utils.decode(encodedKey);
        return keyFactory.generatePublic(new X509EncodedKeySpec(encodedKey));
    }

    /*生成公私钥对*/
//    public static void generateKeyPair() throws Exception {
//
//        //     /** RSA算法要求有一个可信任的随机数源 */
//        //     SecureRandom secureRandom = new SecureRandom();
//        /** 为RSA算法创建一个KeyPairGenerator对象 */
//        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
//
//        /** 利用上面的随机数据源初始化这个KeyPairGenerator对象 */
//        //     keyPairGenerator.initialize(KEYSIZE, secureRandom);
//        keyPairGenerator.initialize(1024);
//
//        /** 生成密匙对 */
//        KeyPair keyPair = keyPairGenerator.generateKeyPair();
//
//        /** 得到公钥 */
//        Key publicKey = keyPair.getPublic();
//
//        /** 得到私钥 */
//        Key privateKey = keyPair.getPrivate();
//
//        ObjectOutputStream oos1 = null;
//        ObjectOutputStream oos2 = null;
//        try {
//            /** 用对象流将生成的密钥写入文件 */
//            oos1 = new ObjectOutputStream(new FileOutputStream("PublicKey"));
//            oos2 = new ObjectOutputStream(new FileOutputStream("PrivateKey"));
//            oos1.writeObject(publicKey);
//            oos2.writeObject(privateKey);
//        } catch (Exception e) {
//            throw e;
//        } finally {
//            /** 清空缓存，关闭文件输出流 */
//            oos1.close();
//            oos2.close();
//        }
//    }

    /**
     * 指定加密算法为RSA
     */
    private static final String ALGORITHM = "RSA";
    /**
     * 指定公钥存放文件
     */
    public static String PUBLIC_KEY_FILE = "PublicKey";
    /**
     * 指定私钥存放文件
     */
    public static String PRIVATE_KEY_FILE = "PrivateKey";
    /**
     * 指定AES秘钥存放文件
     */
    public static String AES_KEY_FILE = "AESKey";

    /**
     * 公钥加密方法
     *
     * @param source 源数据
     * @return
     * @throws Exception
     */
    public static String encrypt(String source) throws Exception {
        Key publicKey;
        ObjectInputStream ois = null;
        try {
            /** 将文件中的公钥对象读出 */
            ois = new ObjectInputStream(new FileInputStream(PUBLIC_KEY_FILE));
            publicKey = (Key) ois.readObject();
        } catch (Exception e) {
            throw e;
        } finally {
            ois.close();
        }

        /** 得到Cipher对象来实现对源数据的RSA加密 */
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        byte[] b = source.getBytes();
        /** 执行加密操作 */
        byte[] b1 = cipher.doFinal(b);

        return Base64Utils.encodeToString(b1);
//        return encodeBase64String(b1);
    }

    /**
     * 私钥加密方法
     *
     * @param source 源数据
     * @return
     * @throws Exception
     */
    public static String encryptPriKey(String source) throws Exception {
        Key privateKey;
        ObjectInputStream ois = null;
        try {
            /** 将文件中的私钥对象读出 */
            ois = new ObjectInputStream(new FileInputStream(PRIVATE_KEY_FILE));
            privateKey = (Key) ois.readObject();
        } catch (Exception e) {
            throw e;
        } finally {
            ois.close();
        }

        /** 得到Cipher对象来实现对源数据的RSA加密 */
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, privateKey);
        byte[] b = source.getBytes();
        /** 执行加密操作 */
        byte[] b1 = cipher.doFinal(b);
        return Base64Utils.encodeToString(b1);
//        return encodeBase64String(b1);
    }

    /**
     * 私钥解密算法
     *
     * @param cryptograph 密文
     * @return
     * @throws Exception
     */
    public static String decrypt(String cryptograph) throws Exception {
        Key privateKey;
        ObjectInputStream ois = null;
        try {
            /** 将文件中的私钥对象读出 */
            ois = new ObjectInputStream(new FileInputStream(PRIVATE_KEY_FILE));
            privateKey = (Key) ois.readObject();
        } catch (Exception e) {
            throw e;
        } finally {
            ois.close();
        }

        /** 得到Cipher对象对已用公钥加密的数据进行RSA解密 */
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, privateKey);

        byte[] b1 = Base64Utils.decodeFromString(cryptograph);

        /** 执行解密操作 */
        byte[] b = cipher.doFinal(b1);
        String a = new String(b);

        return a;
    }

    /**
     * 公钥解密算法
     *
     * @param cryptograph 密文
     * @return
     * @throws Exception
     */
    public static String decryptPubKey(String cryptograph) throws Exception {
        Key publicKey;
        ObjectInputStream ois = null;
        try {
            /** 将文件中的公钥钥对象读出 */
            ois = new ObjectInputStream(new FileInputStream(PUBLIC_KEY_FILE));
            publicKey = (Key) ois.readObject();
        } catch (Exception e) {
            throw e;
        } finally {
            ois.close();
        }

        /** 得到Cipher对象对已用公钥加密的数据进行RSA解密 */
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, publicKey);
        byte[] b1 = Base64Utils.decodeFromString(cryptograph);

        /** 执行解密操作 */
        byte[] b = cipher.doFinal(b1);
        return new String(b);
    }


    public static void main(String[] arg){
        String privateString = "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBALjyvAMo0QGx/jqZ\n" +
                "CJg604A4LbYz2xVk95tkJ97OMmfIRQWxSJpH/6EttikwOuJez+PEEKLP67HvrXAN\n" +
                "uxk4JSQIYhgz7CQyRq9g2QZYHoTqOJ29YpuMH9WsAj0wqfsl1cP6/+7w7gv543ui\n" +
                "DdQGQ068VD9+BSuuSL1Qm4JMs0qzAgMBAAECgYA2a5ZPB6+YZBt+epFA2e5qCB3L\n" +
                "Qy7v+KmzG6FvuhSy3QcSSbwnSZA6iQzUydBJ08QwLP0IK04bKX9LPX8USKUEmRfn\n" +
                "wYyAJAxEidXSSO0I1IsO0gThgI+RW5gcFbGZ9Mdl9HmGc92HrRTbsOExulRZ3hJn\n" +
                "NkYTfZjn1PikK1wagQJBAOL6fPqeSRuxVOzPE4D5yZsiPUfZE8CAMtp+f44QSAzC\n" +
                "Vi6hpyDV0B53ry7ckfhnVP5vLvdlaQ6PF7WpcaSwehECQQDQmIBIRnPMqU/Ahq5o\n" +
                "6jDoIEMUePgjVQ6EtggcqEEBX/bajZuWevRmzd2vSn/euygRGblFnH9nAg7GARPX\n" +
                "o5SDAkEAgI59QPlr0PqEbkfu9QsFLA+dg9cof8xcgTJKjq0cEdX0OB8o6MkRQmBp\n" +
                "YXJeXsIv+pgAkvDmKu/S7vpUuoGV8QJANso3t8Q5mmJGfm1h1lDdM7t1oyT7GFy5\n" +
                "CYa+rrhLCs9oCZP0cBwvoekk92v8FkbWiVdWYMBwfl/9/Wg/C/79fQJBAM5F3GsT\n" +
                "wmd9tDwoWDitjV0V0Uz6XS4gLjHT7gyYuUDm7uCMSA3yn89qxfXnDuSNk8pECRTy\n" +
                "8ePQF8JH2lccUX0=";

        String publicString = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC1/PEe01xHvf4wNKz/04Yz1YklGXDp6nmlsIih9xCjdlYhCBnIxIoy2tgi54DyNFn+5H7HTH3+8g0xs2RVVydOdVFGUeWhTWl4LdDS0JR5hNl0o6y7dPfblP9TZ/BLBtcjzWiE4Z4kuoyein7SF2O9LQmGTyaqvHRkr7UnoYHGYwIDAQAB";

        try {
            String content = "batchNo=11168798&encoding=UTF-8&respCode=10000&respMsg=成功&signMethod=RSA2&version=1.0";

            String sign = rsa256Sign(content,privateString.replace("\n",""),"UTF-8");

            System.out.println(sign);

            boolean flag = RSAUtils.rsa256CheckContent(content, "c4fdor38g7AJPpfDOf9G0uFt0frTIClhfWuOTuw7stQnlsw8gsyWuAcXgWdYozLZAZAgjQ94x8IPOmfG0RoKZq5v/asmXsIbWm4PhUftChH1ufLbMtOzf1UaVW10EJ11XViA8+qKIZWBvQVZ/xa3scq7ZvfQT1qtIHONbURKAqY=", publicString.replace("\n",""), "UTF-8");

            System.out.println("校验结果=="+flag);

        } catch (ApiException e) {
            log.error("异常信息：",e);
        }
    }

}
