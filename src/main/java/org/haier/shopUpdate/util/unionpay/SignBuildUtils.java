package org.haier.shopUpdate.util.unionpay;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;


public class SignBuildUtils {

    /**
     * 把数组所有元素排序，并按照“参数=参数值”的模式用“&”字符拼接成字符串
     *
     * @param params 需要排序并参与字符拼接的参数组
     * @return 拼接后字符串
     */
    public static String createLinkString(Map<String, Object> params) {
        List<String> keys = new ArrayList<String>(params.keySet());
        // 对所有传入参数按照字段名的 ASCII 码从小到大排序（字典序）
        Collections.sort(keys);
        String prestr = "";
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            Object value = params.get(key);
            if (value==null){
                continue;
            }
            prestr = prestr + key + "=" + value + "&";
//            if (i == keys.size() - 1) {//拼接时，不包括最后一个&字符
//                prestr = prestr + key + "=" + value;
//            } else {
//                prestr = prestr + key + "=" + value + "&";
//            }
        }
        prestr = new StringBuilder(prestr).deleteCharAt(prestr.length() -1 ).toString();
        return prestr;
    }
}
