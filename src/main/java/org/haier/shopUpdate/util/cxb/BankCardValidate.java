package org.haier.shopUpdate.util.cxb;


import lombok.extern.slf4j.Slf4j;
import org.haier.shopUpdate.util.unionpay.HttpUtil;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;


/**
  * 银行卡的校验
  * 绿光
 * ********
 */
@Slf4j
public class BankCardValidate {
	private static final String REPORT_URL = "https://ccdcapi.alipay.com/validateAndCacheCardInfo.json";

	public static String requestGETData(String cardNO) {
		try {
			//{"bank":"BOC","stat":"ok","validated":true,"cardType":"DC","messages":[],"key":"6217856000064365109"}
			String ret = HttpUtil.doGet(REPORT_URL +"?cardNo=" + cardNO + "&cardBinCheck=true",null);
			return ret;
		} catch (Exception e) {
			log.error("异常信息：",e);
			return null;
		}
	}
	public static void main(String args[])
	{
		String data=requestGETData("6217856000064365109");
		JSONObject jsonObject =  JSON.parseObject(data);
		System.out.println(jsonObject);
		System.out.println(jsonObject.get("validated"));

//		System.out.println(checkBankCard("6217856000064365109"));

	}

        /**
        * 校验银行卡卡号
        */
       public static boolean checkBankCard(String bankCard) {
                if(bankCard.length() < 12 || bankCard.length() > 19) {
                    return false;
                }
                return true;
       }


}
