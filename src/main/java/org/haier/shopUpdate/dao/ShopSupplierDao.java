package org.haier.shopUpdate.dao;

import org.haier.shopUpdate.entity.ShopSupGoodsEntity;
import org.haier.shopUpdate.entity.ShopSupSupplierEntity;
import org.haier.shopUpdate.entity.ShopSupSupplierExamineEntity;
import org.haier.shopUpdate.entity.ShopSupSupplierKindEntity;
import org.haier.shopUpdate.params.shopSupBill.BillIdParams;
import org.haier.shopUpdate.params.shopSupplier.*;
import org.haier.shopUpdate.params.shopSupplier.paramsDao.QuerySupKindInfoByKuParams;
import org.haier.shopUpdate.params.shopSupplier.paramsDao.SupKindSortParams;
import org.haier.shopUpdate.params.shopSupplier.paramsDao.SupKindUniqueParams;
import org.haier.shopUpdate.result.shopSupplier.*;

import java.math.BigDecimal;
import java.util.Map;
import java.util.List;

/**
 * 供应商相关接口
 */
public interface ShopSupplierDao {

    /**
     * 根据供货商分类编号查询分类信息
     *
     * @param params
     * @return
     */
    ShopSupSupplierKindEntity querySupKindInfo(QuerySupKindInfoByKuParams params);

    /**
     * 根据分类名称查询分类信息
     * @param params
     * @return
     */
    ShopSupSupplierKindEntity querySupKindInfoByKindName(SupKindAddParams params);
    /**
     * 根据供货商分类父节点编号查询子类分类信息中最大排序号
     * @param params
     * @return
     */
    Map<String, Object> querySupKindSort(QuerySupKindInfoByKuParams params);

    /**
     * 根据店铺编号查询所有供货商分类信息
     * @param params
     * @return
     */
    List<ShopSupplierKindResult> querySupKindList(ShopUniqueParams params);

    List<ShopSupSupplierKindEntity> querySupKindInfoFromSupplier(SupKindUniqueParams params);
    /**
     * 添加供货商分类信息
     * @param entity
     * @return
     */
    int addShopSupSupplierKindEntity(ShopSupSupplierKindEntity entity);

    /**
     * 根据供货商分类编号删除分类信息
     * @param params
     * @return
     */
    int deleteSupKindBySupKindUnique(SupKindUniqueParams params);

    int updateSupKindSortList(List<SupKindSortParams> sortList);
    /**
     * 根据供货商分类父节点编号查询子类分类信息集合
     * @return
     */
    List<ShopSupSupplierKindEntity> querySupKindListByKindPreUnique(SupKindUniqueParams params);

    /**
     * 更新供应商分类信息
     * @param entity
     * @return
     */
    int updateShopSupSupplierKindEntity(ShopSupSupplierKindEntity entity);

    /**
     * 外部调用-供货商端添加供应商信息
     * @param entity
     * @return
     */
    int addShopSupRel(ShopSupSupplierEntity entity);

    /**
     * 新增供应商信息
     * @param entity
     * @return
     */
    int addShopSupSupplierEntity(ShopSupSupplierEntity entity);

    /**
     * 更新供货商信息
     * @param entity
     * @return
     */
    int updateShopSupSupplierEntity(ShopSupSupplierEntity entity);

    /**
     * 更新供货商审核信息
     * @param entity
     * @return
     */
    int updateShopSupSupplierExamineEntity(ShopSupSupplierExamineEntity entity);
    /**
     * 根据供货商ID物理删除供货商信息
     * @param params
     * @return
     */
    int deleteShopSupSupplierEntity(SupplierIdParams params);

    /**
     * 根据供货商ID逻辑删除供货商信息
     * @param params
     * @return
     */
    int deleteShopSupSupplierEntityLogic(SupplierIdParams params);

    /**
     * 查询供货商管理页面列表
     * @param params
     * @return
     */
    List<ShopSupplierListResult> querySupList(QuerySupListParams params);

    /**
     * 查询供货商管理页面信息
     * @param params
     * @return
     */
    ShopSupplierListResult querySupInfo(SupplierUniqueParams params);
    /**
     * 查询供货商业务详细信息
     * @param params
     */
    ShopSupplierBussResult querySupBusinessInfo(SupplierUniqueParams params);

    /**
     * 查询供货商购销单详细信息
     * @param params
     * @return
     */
    List<ShopSupplierBillResult> querySupBillInfo(QueryBillListParams params);

    /**
     * 查询与供货商付款信息
     * @param params
     * @return
     */
    List<ShopSupplierPayResult> querySupPaymentInfo(SupplierUniqueWithPageParams params);

    /**
     * 根据付款ID查询付款详细信息
     * @param params
     * @return
     */
    ShopSupplierPayDetailResult queryQepaymentInfo(QueryRepayHisInfoParams params);

    /**
     * 查询未付款购销单
     * @param params
     * @return
     */
    List<ShopSupplierBillResult> queryUnpaidBillList(SupplierUniqueParams params);

    /**
     * 查询供货商新增过来的商品
     * @param params
     * @return
     */
    List<ShopSupplierRecordGoodsResult> querySupRecordGoodList(QueryRecordGoodsListParams params);

    /**
     * 查询商品月销量
     * @param map
     * @return
     */
    BigDecimal queryMonthlySales(Map<String,Object> map);

    /**
     * 根据店铺编号和手机号查询供货商信息
     * @param map
     * @return
     */
    ShopSupSupplierEntity queryShopSupplierByMobile(Map<String,Object> map);
    /**
     * 根据店铺编号和供货商编号查询供货商信息
     * @param map
     * @return
     */
    ShopSupSupplierExamineEntity queryShopSupplierByUnique(Map<String,Object> map);
    /**
     * 根据店铺编号、供货商编号、商品条码查询商品信息
     * @param map
     * @return
     */
    ShopSupGoodsEntity queryShopSupGoodsByGoodsBarcode(Map<String,Object> map);
    /**
     * 根据店铺编号、ID查询商品信息
     * @param map
     * @return
     */
    ShopSupGoodsEntity queryShopSupGoodsById(Map<String,Object> map);

    /**
     * 添加未建档商品信息
     * @param entity
     * @return
     */
    int addShopSupGoodsEntity(ShopSupGoodsEntity entity);

    /**
     * 更换供应商
     * @param map
     * @return
     */
    int updateSupplier(Map<String,Object> map);

    /**
     * 获取供货商列表
     * @param shopUnique
     * @return
     */
    List<Map<String, Object>> getGoodsSupplierMsg(String shopUnique);

    /**
     * 更新商品信息
     * @param entity
     * @return
     */
    int updateShopSupGoodsEntity(ShopSupGoodsEntity entity);

    ShopSupSupplierExamineEntity queryShopSupplierExamineById(BindSupParams params);

    List<ShopSupSupplierExamineEntity> queryShopSupplierExamineList(QuerySupListParams params);

    ShopSupSupplierExamineEntity queryShopSupplierExamineByUnique(BillIdParams params);

    ShopSupSupplierExamineEntity queryShopSupplierExamineBySupplierUnique(SupplierUniqueParams params);

    int deleteShopSupGoodsEntity(ShopSupGoodsDeleteParams params);

    /**
     * 根据店铺编号和ID查询供货商信息
     * @param map
     * @return
     */
    ShopSupSupplierEntity querySupplierById(Map<String,Object> map);

    ShopSupSupplierEntity querySupplierByUnique(Map<String,Object> map);
    int addSupGood(Map<String, Object> params);

    int deleteSupplier(SupplierUniqueParams params);

    int deleteShopSupGoodsEntityRecordFlag(SupplierUniqueParams params);

    String querySupplierDebts(QuerySupListParams params);
}
