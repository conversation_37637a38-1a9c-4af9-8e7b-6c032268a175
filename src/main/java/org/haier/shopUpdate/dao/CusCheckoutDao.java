package org.haier.shopUpdate.dao;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.haier.shopUpdate.entity.CusCheckout;

/**
 * 类名：com.palmshop.online.dao.CusCheckoutDao;
 * 描述：收银端会员 信息相关的dao
 * 包括注册会员信息、修改会员信息、查询会员信息等
 * 方法顺序：增、删、改、查
 * <AUTHOR>
 */
public interface CusCheckoutDao {
	/**
	 * 用户注册
	 * @param cus:用户实体类
	 * @return int
	 */
	public int addCus(CusCheckout cus);
	
	/**
	 * 根据用户的id修改对应的用户基本信息
	 * @param cus:用户的实体类
	 * @return int
	 */
	public int updateCusById(CusCheckout cus);
	
	/**
	 * 根据用户的id修改对应的用户基本信息
	 * @param cus:用户的实体类
	 * @return int
	 */
	public int updateCusPointsById(CusCheckout cus);
	
	
	/**
	 * 根据店铺id和用户编号 查询该用户是否已存在
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>>findCusExist(CusCheckout cus);
	
	
	
	/**
	 * 查询用户的基本信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>>findCusById(Map<String,Object> map);

	public int updateRechargeMoneyByShopUniqueAndCusUnique(CusCheckout cus);

	public List<Map<String, Object>> findCusByShopUniqueAndCusType(Map<String, Object> map);

	public int updateMemberCard(Map<String, Object> params);

	public int updateStoredCard(Map<String, Object> params);

	public Map<String, Object> getCusLevelByShopUnique(Map<String, Object> map);

	public List<Map<String, Object>> getCustLevelList(Map<String, Object> map);

	public int editCustLevel(Map<String, Object> map);

	public Map<String, Object> getAllCustCountByShop(Map<String, Object> params);

	public Map<String, Object> getAddCustCountByShop(Map<String, Object> params);

	public List<Map<String, Object>> getCustList(Map<String, Object> params);

	public int editCus(CusCheckout cus);

	public List<Map<String, Object>> getMsgList(Map<String, Object> params);

	public int editMsgById(Map<String, Object> params);

	public Long saveUserEvaluate(HashMap<String, Object> params);

	public void saveUserEvaluateDetail(HashMap<String, Object> params);

	public void saveUserEvaluateImg(HashMap<String, Object> map);

	public List<HashMap<String, Object>> getScoreList(HashMap<String, Object> params);

	public List<HashMap<String, Object>> getUserEvaluatePage(HashMap<String, Object> params);

	public List<HashMap<String, Object>> getUserEvaluateDetail(HashMap<String, Object> params);

	public List<HashMap<String, Object>> getUserEvaluateImgList(HashMap<String, Object> params);

	public List<HashMap<String, Object>> getImages(Map<String,Object> map);

	public List<HashMap<String, Object>> getPromotionList(HashMap<String, Object> params);

	public List<HashMap<String, Object>> getBusinessHead(HashMap<String, Object> params);

	public List<HashMap<String, Object>> getBusinessHeadGoods(HashMap<String, Object> params);

	public int updateClickCount(HashMap<String, Object> params);
	
	/**
	 * 会员充值记录
	 * @param map
	 * @return
	 */
	public List<Map<String, Object>> queryCusConRecord(Map<String,Object> map);
	/**
	 * 消费方式查询
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryConType();

	public List<Map<String, Object>> getWeekToShopCusNum(Map<String, Object> params);

	public Map<String, Object> findSaleListOrder(Map<String, Object> params);

	public Map<String, Object> findSinglePrice(Map<String, Object> orderMap);

	public Map<String, Object> findSaleDay(Map<String, Object> orderMap);

	public List<Map<String, Object>> findSaleHistory(Map<String, Object> orderMap);

	public Map<String, Object> findSaleCount(Map<String, Object> orderMap);

	public List<Map<String, Object>> findSaleKindList(Map<String, Object> orderMap);

	public List<Map<String, Object>> findSaleGoodsList(Map<String, Object> orderMap);

	public int editCusStatus(Map<String, Object> params);

	public int editCusPassword(Map<String, Object> params);

	public int editCusLevel(Map<String, Object> params);

	public List<Map<String, Object>> findCusByCusPhone(Map<String, Object> params);

	public Map<String, Object> queryCusSynchroStatus(String shop_unique);

	public List<Map<String, Object>> getMemberLevel(Map<String, Object> map);

	public Integer updateMemberLevel(Map<String, Object> map);

	public List<Map<String, Object>> queryCusCheckOut(Map<String, Object> map);

	public List<Map<String, Object>> queryCusRechargeLog(Map<String, Object> map);

	/**
	 * 根据会员编号查询会员信息
	 * @param cusCheckout
	 * @return
	 */
	public CusCheckout selectByCusUnique(CusCheckout cusCheckout);

	/**
	 * 根据会员id修改数据
	 * @param cusCheckout
	 */
	public void updaetCusBalanceByCusId(CusCheckout cusCheckout);
}

	
