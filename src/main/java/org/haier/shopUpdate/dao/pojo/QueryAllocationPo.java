package org.haier.shopUpdate.dao.pojo;

public class QueryAllocationPo {
    /**
     * @see org.haier.shopUpdate.enums.AllocationShopStatus
     */
    private Integer allocationStatus;
    private String searchKey;
    private String startTime;

    private String endTime;
    private Long storeOfId;
    /**
     * 查询调出 店铺 ID
     */
    private Long outStoreOfId;
    /**
     * 调入店铺id
     */
    private Long inStoreOfId;

    private Integer pageIndex;

    private Integer pageSize;

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Long getOutStoreOfId() {
        return outStoreOfId;
    }

    public void setOutStoreOfId(Long outStoreOfId) {
        this.outStoreOfId = outStoreOfId;
    }

    public Long getInStoreOfId() {
        return inStoreOfId;
    }

    public void setInStoreOfId(Long inStoreOfId) {
        this.inStoreOfId = inStoreOfId;
    }

    public Integer getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Integer pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Long getStoreOfId() {
        return storeOfId;
    }

    public void setStoreOfId(Long storeOfId) {
        this.storeOfId = storeOfId;
    }

    public Integer getAllocationStatus() {
        return allocationStatus;
    }

    public void setAllocationStatus(Integer allocationStatus) {
        this.allocationStatus = allocationStatus;
    }

    public String getSearchKey() {
        return searchKey;
    }

    public void setSearchKey(String searchKey) {
        this.searchKey = searchKey;
    }
}
