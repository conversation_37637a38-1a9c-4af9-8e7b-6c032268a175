package org.haier.shopUpdate.dao.pojo;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

public class AddGoodsPo {

    private Integer goodsId;
    private String shopUnique;
    /**
     * 条形码
     */
    private String goodsBarcode;
    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 别名
     */
    private String goodsAlias;
    /**
     * 进价
     */
    private BigDecimal goodsInPrice;
    /**
     * 售价
     */
    private BigDecimal goodsSalePrice;
    /**
     * 会员价
     */
    private BigDecimal goodsCusPrice;

    /**
     * 网售价
     */
    private BigDecimal goodsWebSalePrice;
    /**
     * 批发价
     */
    private BigDecimal wholesalePrice;
    /**
     * 是否设为批发价 0 否 1，是
     */
    private Integer wholesalePriceFlg;
    /**
     * 起批数量
     */
    private BigDecimal wholesaleCount;

    /**
     * 货位
     */
    private String goodsPosition;
    /**
     * 默认供应商编号
     */
    private String supplierUnique;
    /**
     * 默认供货商商品编码
     */
    private BigDecimal supGoodsBarcode;
    /**
     * 线上上架状态：1、已上架；2、已下架
     */
    private BigDecimal shelfState;
    /**
     * pc收银上架状态：1、已上架；2、已下架
     */
    private BigDecimal pcShelfState;
    /**
     * 包含子商品数量
     */
    private BigDecimal goodsContain;
    /**
     * 单位
     */
    private String goodsUnit;
    /**
     * 折扣
     */
    private String goodsDiscount;

    /**
     * 规格
     */
    private String goodsStandard;
    /**
     * 数量
     */
    private BigDecimal goodsCount;
    /**
     * 图片
     */
    private String goodsPicturePath;
    private String goodsRemarks;
    /**
     * 称重商品类型：0、按件；1、按重量
     */
    private Integer goodsScaleType;

    /**
     * 商品分类
     */
    private String goodsKindUnique;

    /**
     * 品牌
     */
    private String goodsBrand;
    /**
     * 商品促销状态：1，不促销，2促销
     */
    private String goodsPromotion;
    /**
     * 保质期
     */
    private String goodsLife;
    /**
     * 积分
     */
    private Integer goodsPoints;
    /**
     * 地址
     */
    private String goodsAddress;
    /**
     * '1:已同步；2：未同步'
     */
    private Integer sameType;
    /**
     * 包装外键
     */
    private Long foreignKey;
    /**
     * 0:没有库存预警 1:有库存预警
     */
    private String stockWarningStatus;
    /**
     * 提醒库存量，低于该库存时，提醒缺货
     */
    private String outStockWaringCount;
    /**
     * 滞销库存量，高于该值是，商品滞销
     */

    private String unsalableCount;

    private BigDecimal minSaleCount;

    /**
     * 划线价
     */
    private BigDecimal underlinedPrice;
    public String getShopUnique() {
        return shopUnique;
    }

    public Integer getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Integer goodsId) {
        this.goodsId = goodsId;
    }
    public void setShopUnique(String shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsAlias() {
        return goodsAlias;
    }

    public void setGoodsAlias(String goodsAlias) {
        this.goodsAlias = goodsAlias;
    }

    public BigDecimal getGoodsInPrice() {
        return goodsInPrice;
    }

    public void setGoodsInPrice(BigDecimal goodsInPrice) {
        this.goodsInPrice = goodsInPrice;
    }

    public BigDecimal getGoodsSalePrice() {
        return goodsSalePrice;
    }

    public void setGoodsSalePrice(BigDecimal goodsSalePrice) {
        this.goodsSalePrice = goodsSalePrice;
    }

    public BigDecimal getGoodsCusPrice() {
        return goodsCusPrice;
    }

    public void setGoodsCusPrice(BigDecimal goodsCusPrice) {
        this.goodsCusPrice = goodsCusPrice;
    }

    public BigDecimal getGoodsWebSalePrice() {
        return goodsWebSalePrice;
    }

    public void setGoodsWebSalePrice(BigDecimal goodsWebSalePrice) {
        this.goodsWebSalePrice = goodsWebSalePrice;
    }

    public String getSupplierUnique() {
        return supplierUnique;
    }

    public void setSupplierUnique(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }

    public BigDecimal getSupGoodsBarcode() {
        return supGoodsBarcode;
    }

    public void setSupGoodsBarcode(BigDecimal supGoodsBarcode) {
        this.supGoodsBarcode = supGoodsBarcode;
    }

    public BigDecimal getShelfState() {
        return shelfState;
    }

    public void setShelfState(BigDecimal shelfState) {
        this.shelfState = shelfState;
    }

    public BigDecimal getPcShelfState() {
        return pcShelfState;
    }

    public void setPcShelfState(BigDecimal pcShelfState) {
        this.pcShelfState = pcShelfState;
    }

    public BigDecimal getGoodsContain() {
        return goodsContain;
    }

    public void setGoodsContain(BigDecimal goodsContain) {
        this.goodsContain = goodsContain;
    }

    public String getGoodsUnit() {
        return goodsUnit;
    }

    public void setGoodsUnit(String goodsUnit) {
        this.goodsUnit = goodsUnit;
    }

    public String getGoodsDiscount() {
        return goodsDiscount;
    }

    public void setGoodsDiscount(String goodsDiscount) {
        this.goodsDiscount = goodsDiscount;
    }

    public String getGoodsStandard() {
        return goodsStandard;
    }

    public void setGoodsStandard(String goodsStandard) {
        this.goodsStandard = goodsStandard;
    }

    public BigDecimal getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(BigDecimal goodsCount) {
        this.goodsCount = goodsCount;
    }

    public String getGoodsPicturePath() {
        return goodsPicturePath;
    }

    public void setGoodsPicturePath(String goodsPicturePath) {
        this.goodsPicturePath = goodsPicturePath;
    }

    public String getGoodsRemarks() {
        return goodsRemarks;
    }

    public void setGoodsRemarks(String goodsRemarks) {
        this.goodsRemarks = goodsRemarks;
    }

    public Integer getGoodsScaleType() {
        return goodsScaleType;
    }

    public void setGoodsScaleType(Integer goodsScaleType) {
        this.goodsScaleType = goodsScaleType;
    }

    public String getGoodsKindUnique() {
        return goodsKindUnique;
    }

    public void setGoodsKindUnique(String goodsKindUnique) {
        this.goodsKindUnique = goodsKindUnique;
    }

    public String getGoodsBrand() {
        return goodsBrand;
    }

    public void setGoodsBrand(String goodsBrand) {
        this.goodsBrand = goodsBrand;
    }

    public String getGoodsPromotion() {
        return goodsPromotion;
    }

    public void setGoodsPromotion(String goodsPromotion) {
        this.goodsPromotion = goodsPromotion;
    }

    public String getGoodsLife() {
        return goodsLife;
    }

    public void setGoodsLife(String goodsLife) {
        this.goodsLife = goodsLife;
    }

    public Integer getGoodsPoints() {
        return goodsPoints;
    }

    public void setGoodsPoints(Integer goodsPoints) {
        this.goodsPoints = goodsPoints;
    }

    public String getGoodsAddress() {
        return goodsAddress;
    }

    public void setGoodsAddress(String goodsAddress) {
        this.goodsAddress = goodsAddress;
    }

    public Integer getSameType() {
        return sameType;
    }

    public void setSameType(Integer sameType) {
        this.sameType = sameType;
    }

    public Long getForeignKey() {
        return foreignKey;
    }

    public void setForeignKey(Long foreignKey) {
        this.foreignKey = foreignKey;
    }

    public String getStockWarningStatus() {
        return stockWarningStatus;
    }

    public void setStockWarningStatus(String stockWarningStatus) {
        this.stockWarningStatus = stockWarningStatus;
    }

    public String getOutStockWaringCount() {
        return outStockWaringCount;
    }

    public void setOutStockWaringCount(String outStockWaringCount) {
        this.outStockWaringCount = outStockWaringCount;
    }

    public String getUnsalableCount() {
        return unsalableCount;
    }

    public void setUnsalableCount(String unsalableCount) {
        this.unsalableCount = unsalableCount;
    }
    public BigDecimal getMinSaleCount() {
        return minSaleCount;
    }

    public void setMinSaleCount(BigDecimal minSaleCount) {
        this.minSaleCount = minSaleCount;
    }

    public BigDecimal getWholesalePrice() {
        return wholesalePrice;
    }

    public void setWholesalePrice(BigDecimal wholesalePrice) {
        this.wholesalePrice = wholesalePrice;
    }

    public Integer getWholesalePriceFlg() {
        return wholesalePriceFlg;
    }

    public void setWholesalePriceFlg(Integer wholesalePriceFlg) {
        this.wholesalePriceFlg = wholesalePriceFlg;
    }

    public BigDecimal getWholesaleCount() {
        return wholesaleCount;
    }

    public void setWholesaleCount(BigDecimal wholesaleCount) {
        this.wholesaleCount = wholesaleCount;
    }

    public String getGoodsPosition() {
        return goodsPosition;
    }

    public void setGoodsPosition(String goodsPosition) {
        this.goodsPosition = goodsPosition;
    }

    public BigDecimal getUnderlinedPrice() {
        return underlinedPrice;
    }

    public void setUnderlinedPrice(BigDecimal underlinedPrice) {
        this.underlinedPrice = underlinedPrice;
    }
}
