package org.haier.shopUpdate.dao.pojo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName GoodsBatchUpdatePo
 * <AUTHOR>
 * @Date 2024/4/29 15:00
 */

public class GoodsSaleBatchUpdatePo implements Serializable {
    private Long goodsSaleBatchId;
    private Long shopUnique;
    private String batchUnique;
    private String stockListUnique;
    private String goodsBarcode;
    private BigDecimal goodsInPrice;
    private BigDecimal goodsOutPrice;
    private BigDecimal goodsOutCount;
    private Date updateTime;
    private Long updateId;

    public Long getGoodsSaleBatchId() {
        return goodsSaleBatchId;
    }

    public void setGoodsSaleBatchId(Long goodsSaleBatchId) {
        this.goodsSaleBatchId = goodsSaleBatchId;
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getBatchUnique() {
        return batchUnique;
    }

    public void setBatchUnique(String batchUnique) {
        this.batchUnique = batchUnique;
    }

    public String getStockListUnique() {
        return stockListUnique;
    }

    public void setStockListUnique(String stockListUnique) {
        this.stockListUnique = stockListUnique;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public BigDecimal getGoodsInPrice() {
        return goodsInPrice;
    }

    public void setGoodsInPrice(BigDecimal goodsInPrice) {
        this.goodsInPrice = goodsInPrice;
    }

    public BigDecimal getGoodsOutPrice() {
        return goodsOutPrice;
    }

    public void setGoodsOutPrice(BigDecimal goodsOutPrice) {
        this.goodsOutPrice = goodsOutPrice;
    }

    public BigDecimal getGoodsOutCount() {
        return goodsOutCount;
    }

    public void setGoodsOutCount(BigDecimal goodsOutCount) {
        this.goodsOutCount = goodsOutCount;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUpdateId() {
        return updateId;
    }

    public void setUpdateId(Long updateId) {
        this.updateId = updateId;
    }
}
