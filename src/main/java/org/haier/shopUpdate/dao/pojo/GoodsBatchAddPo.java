package org.haier.shopUpdate.dao.pojo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName GoodsBatchAddPo
 * <AUTHOR>
 * @Date 2024/4/29 15:00
 */

public class GoodsBatchAddPo implements Serializable {
    private Long shopUnique;
    private String goodsBarcode;
    private String batchUnique;
    private String stockListUnique;
    private BigDecimal goodsCount;
    private BigDecimal goodsInCount;
    private BigDecimal goodsInPrice;
    private Date goodsProd;
    private Date goodsExp;
    private Integer goodsLife;
    private String sourceBarcode;
    private Long createId;
    private Date createTime;
    private Long updateId;
    private Date updateTime;

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public String getBatchUnique() {
        return batchUnique;
    }

    public void setBatchUnique(String batchUnique) {
        this.batchUnique = batchUnique;
    }

    public String getStockListUnique() {
        return stockListUnique;
    }

    public void setStockListUnique(String stockListUnique) {
        this.stockListUnique = stockListUnique;
    }

    public BigDecimal getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(BigDecimal goodsCount) {
        this.goodsCount = goodsCount;
    }

    public BigDecimal getGoodsInCount() {
        return goodsInCount;
    }

    public void setGoodsInCount(BigDecimal goodsInCount) {
        this.goodsInCount = goodsInCount;
    }

    public BigDecimal getGoodsInPrice() {
        return goodsInPrice;
    }

    public void setGoodsInPrice(BigDecimal goodsInPrice) {
        this.goodsInPrice = goodsInPrice;
    }

    public Date getGoodsProd() {
        return goodsProd;
    }

    public void setGoodsProd(Date goodsProd) {
        this.goodsProd = goodsProd;
    }

    public Date getGoodsExp() {
        return goodsExp;
    }

    public void setGoodsExp(Date goodsExp) {
        this.goodsExp = goodsExp;
    }

    public Integer getGoodsLife() {
        return goodsLife;
    }

    public void setGoodsLife(Integer goodsLife) {
        this.goodsLife = goodsLife;
    }

    public String getSourceBarcode() {
        return sourceBarcode;
    }

    public void setSourceBarcode(String sourceBarcode) {
        this.sourceBarcode = sourceBarcode;
    }

    public Long getCreateId() {
        return createId;
    }

    public void setCreateId(Long createId) {
        this.createId = createId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateId() {
        return updateId;
    }

    public void setUpdateId(Long updateId) {
        this.updateId = updateId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
