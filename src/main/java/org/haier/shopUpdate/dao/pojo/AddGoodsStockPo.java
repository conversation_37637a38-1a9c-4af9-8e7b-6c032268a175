package org.haier.shopUpdate.dao.pojo;

import java.math.BigDecimal;

public class AddGoodsStockPo {
    private String goodsBarcode;
    private BigDecimal goodsCount;
    private BigDecimal stockCount;
    private BigDecimal stockType;
    private String shopUnique;
    private BigDecimal goodsInPrice;


    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public BigDecimal getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(BigDecimal goodsCount) {
        this.goodsCount = goodsCount;
    }

    public BigDecimal getStockCount() {
        return stockCount;
    }

    public void setStockCount(BigDecimal stockCount) {
        this.stockCount = stockCount;
    }

    public BigDecimal getStockType() {
        return stockType;
    }

    public void setStockType(BigDecimal stockType) {
        this.stockType = stockType;
    }

    public String getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(String shopUnique) {
        this.shopUnique = shopUnique;
    }

    public BigDecimal getGoodsInPrice() {
        return goodsInPrice;
    }

    public void setGoodsInPrice(BigDecimal goodsInPrice) {
        this.goodsInPrice = goodsInPrice;
    }
}
