package org.haier.shopUpdate.dao;

import org.haier.shopUpdate.entity.ShopStaffEntity;

import java.util.List;
import java.util.Map;

public interface ShopStaffDao {

    /**
     * 根据id查询
     * @param entity
     * @return
     */
    public ShopStaffEntity findByStaffId(ShopStaffEntity entity);

    /**
     * 根据id集合查询
     * @param map
     * @return
     */
    public List<ShopStaffEntity> findByStaffIdList(Map<String,Object> map);

}
