package org.haier.shopUpdate.dao;

import java.util.List;
import java.util.Map;

/**
 * 浏览量相关SQL
 * <AUTHOR>
 */
public interface CollectionPageDao {
	
	/**
	 * 照抄shopmanager，如果有修改，记得同步修改shopmanager
	 * @param map
	 * @return
	 */
	public Map<String,Object> signOutStatisticsAll(Map<String,Object> map);
	/**
	 * 浏览量查询
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryBrowserCount(Map<String,Object> map);

	/**
	 * 经营助手（统计各个分类在指定时间内的进销金额）
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> turnover(Map<String,Object> map);
	
	/**
	 * 经营助手，流水统计
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> turnOverBytime(Map<String,Object> map);
	
	/**
	 * 
	 * @param map
	 * @return
	 */
	public  List<Map<String,Object>> turnOverByDay(Map<String,Object> map);

	public Map<String,Object> queryStatisticsMessageInMainCanyin(Map<String,Object> map);
	/**
	 * 查询商店内今日营业状况及相比于昨日的营业状况
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryStatisticsMessageInMain(Map<String,Object> map);
	
	/**
	 * 新版APP：经营总览
	 * @param map
	 * @return
	 */
	public Map<String,Object> businessOverview(Map<String,Object> map);
	/**
	 * 新版：分类销量占比
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> querySaleSumByKind(Map<String,Object> map);
	/**
	 * 新版：销售额走势-今日走势
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> querySaleSumTrend(Map<String,Object> map);
	
	/**
	 * 新版：销售额走势图-本周，本月
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> querySaleSumTrendDay(Map<String,Object> map);
	/**
	 * 新版:热销商品TOP5
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryPreSaleFiveGoods(Map<String,Object> map);
	/**
	 * 新版：热销商品前五销售总额
	 * @param map
	 * @return
	 */
	public Double  querySaleSum(Map<String,Object> map);
}
