package org.haier.shopUpdate.dao;

import java.util.Map;

/**
 * 功能模块相关SQL
 * <AUTHOR>
 */
public interface ShopFunctionDao {
	/**
	 * 查询店铺功能列表
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryShopFunction(Map<String,Object> map);
	/**
	 * 修改店铺功能
	 * @param map
	 * @return
	 */
	public Integer modifyShopFunction(Map<String,Object> map);
	
	/**
	 * 查询店铺收款码信息
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryShopPayCode(Map<String,Object> map); 
	/**
	 * 修改店铺付款码信息
	 * @param map
	 * @return
	 */
	public Integer modifyPayPic(Map<String,Object> map);
}
