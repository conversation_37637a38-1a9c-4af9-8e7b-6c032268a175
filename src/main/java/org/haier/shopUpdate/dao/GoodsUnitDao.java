package org.haier.shopUpdate.dao;

import java.util.List;
import java.util.Map;

public interface GoodsUnitDao {

	List<Map<String, Object>> findGoodsUnitList(Map<String, Object> map);

	List<Map<String, Object>> findGoodsUnitListByShopUnique(Map<String, Object> map);

	void addGoodsUnit(Map<String, Object> map);

	void editGoodsUnit(Map<String, Object> map);

	void deleteGoodsUnit(Map<String, Object> map);

	
	
}
