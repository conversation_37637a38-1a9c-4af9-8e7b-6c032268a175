package org.haier.shopUpdate.dao;

import java.util.List;
import java.util.Map;

public interface AppPayDao {

	Integer updateGoodsCount(Map<String,Object> map);
	/**
	 * 获取会员信息
	 * @param map
	 * @return
	 */
	Map<String, Object> queryCusMsg(Map<String,Object> map);
	Map<String, Object> querySaleListUniqueExist(Map<String, Object> map);

	Map<String, Object> queryGoodsExist(Map<String, Object> map);

	void addSaleListWait(Map<String, Object> map);

	void addSaleListDetailWait(Map<String, Object> map);

	Map<String, Object> querySaleListDetailByGoodsExist(Map<String, Object> map);

	void updateSaleListDetailWait(Map<String, Object> saleListDetailMap);

	List<Map<String, Object>> findAllSaleListDetail(Map<String, Object> map);

	void deleteGoodsBySaleListDetail(Map<String, Object> map);

	void updateGoodsBySaleListDetail(Map<String, Object> map);

	List<Map<String, Object>> findAllSaleListWait(Map<String, Object> map);

	int cancelSaleListWait(Map<String, Object> map);

	List<Map<String, Object>> querySaleListDetailWait(Map<String, Object> map);

	List<Map<String, Object>> queryLatelySaleListDetailWait(Map<String, Object> map);

	int addCusJoinSaleList(Map<String, Object> map);

	void editGoodsToCar(Map<String, Object> map);

	List<Map<String, Object>> searchGoods(Map<String, Object> map);

	void deleteGoods(Map<String, Object> map);

	Map<String, Object> findGoodsPointSet(Map<String, Object> map);

	List<Map<String, Object>> findCusById(Map<String, Object> params);

	List<Map<String, Object>> getCustLevelList(Map<String, Object> map);

	void updateMemberCard(Map<String, Object> params);

	Map<String, Object> findGoodsCommissionSet(Map<String, Object> map);

	Map<String, Object> getSmallGoodsInfo(Map<String, Object> maps);

	int batchStocks(List<Map<String, Object>> listGoods);

	int newStockRecords(List<Map<String, Object>> stockList);

	void addLog(Map<String, Object> params5);

	void addrSaleList(Map<String, Object> saleList);

	void addrSaleListDetail(Map<String, Object> detail);

	Map<String, Object> findShopInfo(Map<String, Object> params);

	void updateSaleListWait(Map<String, Object> map);

	void addSaleListPayDetail(Map<String, Object> params);

	List<Map<String, Object>> queryFoodTableList(Map<String, Object> map);

	void addFoodTable(Map<String, Object> map);

	void deleteFoodTable(Map<String, Object> map);

	void upadatePrintCount(Map<String, Object> map);
	
}
