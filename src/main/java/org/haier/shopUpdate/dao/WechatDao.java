package org.haier.shopUpdate.dao;

import java.util.List;
import java.util.Map;

/**
* @author: 作者:王恩龙
* @version: 2020年11月10日 下午3:54:08
*
*/
public interface WechatDao {
	
	/**
	 * 1\添加订单详情子详情
	 * @param map
	 * @return
	 */
	public Integer addSaleListDetailTotal(Map<String,Object> map);
	/**
	 * 1、添加订单详情
	 * @param map
	 * @return
	 */
	public Integer addSaleListDetail(Map<String,Object> map);
	/**
	 * 1、添加一个订单
	 * @param map
	 * @return
	 */
	public Integer addNewOrder(Map<String,Object> map);
	public Map<String,Object> queryShopMsg(String shopUnique);
	/**
	 * 查询会员充值记录
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> searchRechargeRecord(Map<String,Object> map);
	public Integer searchRechargeRecordCount(String cusId);
	public Integer queryCusConsumptionRecordCount(String cusId);
	/**
	 * 查询会员的消费记录
	 * @param cusId
	 * @return
	 */
	public List<Map<String,Object>> queryCusConsumptionRecord(Map<String,Object> map);
	/**
	 * 查询当前充值是否有优惠活动
	 * @return
	 */
	public Map<String,Object> queryRechargeActiveMsg();
	
	/**
	 * 查询宁宇充值赠送活动名单
	 * @return
	 */
	public List<Map<String,Object>> queryNingYuRechargeGive();
	/**
	 * 查询当前充值是否有优惠活动
	 * @return
	 */
	public Map<String,Object> queryRechargeActive();
	/**
	 * 添加会员的充值使用记录
	 * @param map
	 * @return
	 */
	public Integer addNewCustomerRechargeUse(Map<String,Object> map);
	/**
	 * 查询会员充值使用记录，防止重复添加
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryCusRechargeUse(Map<String,Object> map);
	/**
	 * 更新会员余额，积分，赠送金额信息
	 * @param map
	 * @return
	 */
	public Integer updateCusMsg(Map<String,Object> map);
	/**
	 * 更新会员充值记录信息
	 * @param map
	 * @return
	 */
	public Integer updateCustomerRechargeMsg(Map<String,Object> map);
	/**
	 * 根据会员充值单号，查询对应的会员充值信息和会员信息
	 * @param saleListUnique
	 * @return
	 */
	public Map<String,Object> queryCustomerRechargeMsg(String saleListUnique);
	
	/**
	 * 添加充值使用记录
	 * @param map
	 * @return
	 */
	public Integer addRechargeUse(Map<String,Object> map);
	/**
	 * 添加充值记录
	 * @param map
	 * @return
	 */
	public Integer addRecharge(Map<String,Object> map);
	
	/**
	 * 查询店铺的会员等级最低编号
	 * @param map
	 * @return
	 */
	public Integer getCusLevelId(Map<String,Object> map);
	/**
	 * 修改会员名称或会员生日信息
	 * @param map
	 * @return
	 */
	public Integer modifyCusMsg(Map<String,Object> map);
	/**
	 * 根据店铺编号，会员手机号，或会员微信OPENID查询会员信息
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryCusMsg(Map<String,Object> map);
	
	/**
	 * 添加新的会员信息
	 * @param map
	 * @return
	 */
	public Integer addNewCusMsg(Map<String,Object> map);
}
