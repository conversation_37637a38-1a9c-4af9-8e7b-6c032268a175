package org.haier.shopUpdate.dao;

import java.util.List;
import java.util.Map;

import org.haier.shopUpdate.entity.BaseGoods;
import org.haier.shopUpdate.entity.goods.GoodsDict;

public interface GoodsDictDao {

	public int addNewGoodsDict(GoodsDict goodsDict);
	/**
	 * 更新字典数据库中的商品信息
	 * @param map
	 * @return
	 */
	public int moifyGoodsDictMessage(Map<String,Object> map);
	/**
	 * 添加新的商品字典信息
	 * @param map
	 * @return
	 */
	public int newGoodsDict(Map<String,Object> map);
	
	/**
	 * 查询商品的基本信息
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryDictGoodsMessage(Map<String,Object> map);
	
	/**
	 * 查询商品信息，单规格
	 * @param map
	 * @return
	 */
	public BaseGoods queryBaseGoodsMessageSelf(Map<String,Object> map);
	/**
	 * 查询店铺商品信息
	 * @param map
	 * @return
	 */
	public BaseGoods queryBaseGoodsMessage(Map<String,Object> map);
	
	/**
	 * 输入商品后六位查询商品信息
	 * @param map
	 * @return
	 */
	public List<BaseGoods> queryBaseGoodsMessageByCode(List<Map<String,Object>> list);
//	public List<Map<String,Object>> queryBaseGoodsMessageByCode(Map<String,Object> map);

	/**
	 * 本店是否有该商品
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryBaseMessageF(Map<String,Object> map);

	/**
	 * 本店是否有该商品带限制
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryBaseMessageLimit(Map<String,Object> map);

	public List<Map<String,Object>> queryBaseMessageDict(Map<String,Object> map);

	public List<BaseGoods> queryBaseMessageDictS(List<Map<String,Object>> list);
	
	/**
	 * 查询大库商品信息
	 * @param map
	 * @return
	 */
	public String queryGoodsMessageByBarcode(String goodsBarcode);
	
	
} 
