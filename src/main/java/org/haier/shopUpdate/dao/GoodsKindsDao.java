package org.haier.shopUpdate.dao;

import org.apache.ibatis.annotations.Param;
import org.haier.shopUpdate.dto.GoodsKindByShopUniqueDto;
import org.haier.shopUpdate.entity.GoodsKind;
import org.haier.shopUpdate.entity.ShopsGroupGoodsKinds;
import org.haier.shopUpdate.util.ShopsResult;

import java.util.List;
import java.util.Map;
/**
 * 商品分类相关SQL
 * <AUTHOR>
 */
public interface GoodsKindsDao {

	/**
	 *
	 * 商品分类查询
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryGoodsKind(Map<String,Object> map);

	/**
	 * 查询商品是启用还是禁用
	 *
	 * @param map
	 * @return
	 */
	public Integer queryGoodsKindValidType(Map<String,Object> map);

	/**
	 * 查询商品分类的子分类ID编号
	 * @param map
	 * @return
	 */
	public List<Long> queryKindMsg(Map<String,Object> map);

	/**
	 * 查询商品分类的副分类ID编号
	 * @param map
	 * @return
	 */
	public List<Long> queryGroupMsg(Map<String,Object> map);
	/**
	 * 查询店铺所有相关商品分类
	 * @param map
	 * @return
	 */
	
	public List<ShopsGroupGoodsKinds> queryGoodsKindsByShop(Map<String,Object> map);
	
	public List<Map<String,Object>> appQueryGoodsKinds(Map<String,Object> map);

	public List<Map<String, Object>> queryGoodsBigKindsByShop(Map<String, Object> map);

	public List<Map<String, Object>> queryMoreGoodsBigKinds(Map<String, Object> map);

	public Map<String, Object> queryGoodsKinds(Map<String, Object> map);

	public int saveGoodsBigKinds(Map<String, Object> result2);

	public int saveSmallGoodsKind(Map<String, Object> map2);

	public int updateBigGoodsKind(Map<String, Object> map);

	public int updateBatchBigGoodsKind(Map<String, Object> params);

	public int updateBatchBigGoodsKindAll(Map<String, Object> params);
	/**
	 * 查询店铺分类使用类型；
	 * 1、公共分类；2、自定义分类
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryShopKindType(Map<String,Object> map);
	
	/**
	 * 开启自定义商品分类信息
	 * @param map
	 * @return
	 */
	public Integer useCustomeKind(Map<String,Object> map);
	
	/**
	 * 恢复商品公共分类，查询商品的公共分类信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryGoodsDictKind(Map<String,Object> map);
	
	/**
	 * 将查询出来的商品分类信息恢复到goods表
	 * @param list
	 * @return
	 */
	public Integer rebackSystemKind(List<Map<String,Object>> list);
	
	/**
	 * 添加新的商品自定义分类信息
	 * @param map
	 * @return
	 */
	public Integer addNewCustomKinds(GoodsKind goodsKind);
	/**
	 * 更新商品分类信息
	 * @param goodsKind
	 * @return
	 */
	public Integer updateCustomKind(GoodsKind goodsKind);
	
	/**
	 * 删除商品分类前，查询商品该分类下的商品数量
	 * @param goodsKind
	 * @return
	 */
	public Integer getGoodsCountByKindUnique(GoodsKind goodsKind);
	/**
	 * 查询店铺当前分类定义使用状态（自定义2；系统定义；1）
	 * @param goodsKind
	 * @return
	 */
	public Integer getNowKindStatus(GoodsKind goodsKind);
	
	public Integer queryGoodsKindCount(GoodsKind goodsKind);

	public List<ShopsGroupGoodsKinds> queryGoodsKindsByShop2(Map<String, Object> map);
	
	public List<Map<String,Object>> queryMqttKind (GoodsKind data);

	List<GoodsKindByShopUniqueDto> queryGoodsKindByShop(@Param("shopUnique") String shopUnique);

	public List<Map<String,Object>> queryListByIconTypeAndShopUnique(Map<String,Object> map);

	public List<Map<String,Object>> queryListByIconType(Map<String,Object> map);


	int enableKind(@Param("kindUnique") Long kindUnique, @Param("shopUnique")String shopUnique);
	/**
	 * 公共- 根据当前分类查询所有子集的分类
	 *
	 * @param map
	 * @return
	 */
	public List<Long> selectCommonLevelKind(Map<String,Object> map);

}
