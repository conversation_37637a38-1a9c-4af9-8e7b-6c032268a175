package org.haier.shopUpdate.dao;

import org.haier.shopUpdate.entity.*;
import org.haier.shopUpdate.params.shopSupBill.*;
import org.haier.shopUpdate.params.shopSupBill.externalCall.CancelSupBillParams;
import org.haier.shopUpdate.params.shopSupBill.externalCall.UpdateSupBillStatusParams;
import org.haier.shopUpdate.params.shopSupBill.supExternalCall.BillDetailSyncParams;
import org.haier.shopUpdate.params.shopSupplier.SupplierUniqueParams;
import org.haier.shopUpdate.result.shopSupBill.QueryShopSupBillResult;
import org.haier.shopUpdate.result.shopSupBill.ShopSupBillDetailResult;

import java.util.List;
import java.util.Map;

/**
 * 购销单相关接口
 */
public interface ShopSupBillDao {

    List<QueryShopSupBillResult> querySupBillList(QueryBillListParams params);

    ShopSupBillDetailResult querySupBillGoodsList(QueryBillGoodsListParams params);

    ShopSupBillEntity queryShopSupBillEntity(QueryBillGoodsListParams params);
    ShopSupBillEntity queryShopSupBillEntityByBillNo(UpdateSupBillStatusParams params);

    ShopSupBillDetailEntity queryShopSupBillDetailEntity(StorageGoodsParams params);
    void updatePrice(UpdatePriceParams params);
    void addStorageRecord(ShopSupStorageGoodsEntity entity);
    ShopSupStorageGoodsEntity queryShopSupStorageGoodsEntity(QueryShopSupStorageGoodsEntityParams params);
    void updateStorageRecord(UpdateStorageGoodsParams params);
    void updateShopSupBillDetailEntity(ShopSupBillDetailEntity entity);
    int addShopSupPaymentOrderEntity(ShopSupPaymentOrderEntity entity);

    int addShopSupPaymentEvidenceEntity(ShopSupPaymentEvidenceEntity entity);
    void updateShopSupPaymentOrderEntity(ShopSupPaymentOrderEntity entity);
    void deleteShopSupPaymentOrderEntity(Long id);

    int updateBillStatus(UpdateBillStatusParams params);
    Long addSupBill(ShopSupBillEntity entity);
    int addSupBillDetail(ShopSupBillDetailEntity entity);
    List<String> queryVoucherPicturepath(String paymentId);
    List<String> queryBillEvidence(String billId);
    Map<String,Object> querySupBillNo(Long id);
    Map<String,Object> queryPayment(Map<String,Object> map);
    List<BillDetailSyncParams> queryGoodsActualCount(Long id);
    Long addSupBillEvidence(ShopSupBillEvidenceEntity entity);
    int cancelSupBill(CancelSupBillParams params);
    int updateSupBill(ShopSupBillEntity entity);
    List<ShopSupBillDetailEntity> querySupBillDetailByBillId(Long billId);
    int updateSupBillDetail(ShopSupBillDetailEntity entity);
    int deleteSupBillDetail(Long detailId);
    int  deleteSupBillDetailByBillId(Long billId);
    int deleteSupBillEvidenceByBillId(Long billId);

    Long selectBillCount(SupplierUniqueParams params);

    List<ShopSupBillEntity> queryShopSupBillEntityBySupplierUnique(SupplierUniqueParams params);
}
