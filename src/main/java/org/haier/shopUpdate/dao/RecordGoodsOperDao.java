package org.haier.shopUpdate.dao;

import org.haier.shopUpdate.entity.RecordGoodsOperEntity;
import org.haier.shopUpdate.entity.RecordGoodsOperForDetailEntity;

import java.util.List;
import java.util.Map;


/**
 * 查询商品修改记录
 */
public interface RecordGoodsOperDao {

	/**
	 * 查询供货商名称
	 * @param map
	 * @return
	 */
	String queryGoodsSupplier(Map<String, Object> map);
	/**
	 * 获取商品名称
	 * @param map
	 * @return
	 */
	String queryGoodsName(Map<String, Object> map);
	/**
	 * 查询分类对应的名称
	 * @param map
	 * @return
	 */
	String queryGoodsKindMsg(Map<String, Object> map);
	public List<RecordGoodsOperEntity> queryRecordGoodsOperList(Map<String,Object> map);
	public RecordGoodsOperForDetailEntity queryRecordGoodsOperDetail(Map<String,Object> map);

	}
