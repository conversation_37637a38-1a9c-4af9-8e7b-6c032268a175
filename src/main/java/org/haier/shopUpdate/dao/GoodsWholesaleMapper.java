package org.haier.shopUpdate.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.haier.shopUpdate.entity.GoodsWholesaleEntity;

import java.util.List;
import java.util.Map;

public interface GoodsWholesaleMapper extends BaseMapper<GoodsWholesaleEntity> {

    List<GoodsWholesaleEntity> findList(GoodsWholesaleEntity entity);

    List<GoodsWholesaleEntity> queryByGoodsBarcode(Map<String, Object> map);

    int insert(GoodsWholesaleEntity entity);

    void deleteByGoodsBarcode(Map<String, Object> map);
}