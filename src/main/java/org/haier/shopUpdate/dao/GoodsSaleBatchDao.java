package org.haier.shopUpdate.dao;

import org.haier.shopUpdate.dao.pojo.GoodsSaleBatchAddPo;
import org.haier.shopUpdate.dao.pojo.GoodsSaleBatchUpdatePo;
import org.haier.shopUpdate.params.goodsSaleBatch.GoodSaleBatchByListUniqueQueryParams;
import org.haier.shopUpdate.params.goodsSaleBatch.GoodsSaleBatchUpdateParams;
import org.haier.shopUpdate.result.goodsSaleBatch.GoodSaleBatchQueryDto;

import java.util.List;

public interface GoodsSaleBatchDao {

    void saveGoodsSaleBatch(GoodsSaleBatchAddPo goodsSaleBatchAddPo);

    void saveGoodsSaleBatchList(List<GoodsSaleBatchAddPo> list);

    List<GoodSaleBatchQueryDto> queryGoodsSaleBatchByListUnique(GoodSaleBatchByListUniqueQueryParams params);

    void updateGoodsSaleBatchList(List<GoodsSaleBatchUpdateParams> list);

    void deleteGoodsSaleBatchList(List<Long> ids);
}
