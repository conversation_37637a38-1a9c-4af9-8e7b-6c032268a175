package org.haier.shopUpdate.dao;

import org.apache.ibatis.annotations.Param;
import org.haier.shopUpdate.entity.InventoryTaskDetailEntity;
import org.haier.shopUpdate.entity.InventoryTaskDetailKindCount;
import org.haier.shopUpdate.result.inventoryTask.InventoryGoodsRecordInventory;

import java.util.List;
import java.util.Map;

public interface InventoryTaskDetailDao {

    /**
     * 根据任务单id和员工id查询
     * @param entity
     * @return
     */
    public List<InventoryTaskDetailEntity> findByParam(InventoryTaskDetailEntity entity);

    /**
     * 详情分页查询
     * @param entity
     * @return
     */
    public List<InventoryTaskDetailEntity> findByPage(Map<String,Object> map);

    /**
     * 预览分页
     * @param map
     * @return
     */
    public List<InventoryTaskDetailEntity> findByPagePreview(Map<String,Object> map);

    /**
     * 详情统计
     * @param map
     * @return
     */
    public int findByPageCount(Map<String,Object> map);

    /**
     * 预览统计
     * @param map
     * @return
     */
    public int findByPagePreviewCount(Map<String,Object> map);

    /**
     * 根据id查询
     * @param id
     * @return
     */
    public InventoryTaskDetailEntity findById(Long id);

    /**
     * 删除
     * @param entity
     */
    public void deleteByParam(InventoryTaskDetailEntity entity);

    /**
     * 根据id删除
     */
    public void deleteById(Long id);

    /**
     * 批量保存
     * @param detailList
     */
    public void insertBatch(@Param("detailList") List<InventoryTaskDetailEntity> detailList);

    public void insert(InventoryTaskDetailEntity entity);

    /**
     * 根据员工编号和任务查询
     * @param map
     * @return
     */
    public List<InventoryTaskDetailEntity> findByStaffIdAndTaskIdLimit4(Map<String,Object> map);

    /**
     * 盘库列表
     * @param map
     * @return
     */
    public List<InventoryGoodsRecordInventory> findInventoryGoodsRecordPage(Map<String,Object> map);

    /**
     * 根据任务单查询商品种类数
     * @return
     */
    public List<InventoryTaskDetailKindCount> findKindCount(@Param("taskIdList") List<Long> taskIdList);

    /**
     * 根据id修改
     * @param entity
     */
    public void updateById(InventoryTaskDetailEntity entity);

    /**
     * 根据goods_id查询
     * @param map
     * @return
     */
    public List<InventoryTaskDetailEntity> findDetailByGoodsIdList(Map<String,Object> map);

}
