package org.haier.shopUpdate.dao;

import org.haier.shopUpdate.entity.RestockPlanEntity;
import org.haier.shopUpdate.entity.RestockPlanGoodsEntity;
import org.haier.shopUpdate.entity.RestockPlanSupplierEntity;
import org.haier.shopUpdate.params.restockPlan.QueryGoodsByIdParams;
import org.haier.shopUpdate.params.shopSupplier.SupplierUniqueParams;
import org.haier.shopUpdate.result.restockPlan.*;

import java.util.List;
import java.util.Map;

/**
 * 补货计划相关接口
 */
public interface ShopsRestockPlanDao {
    int addRestockPlan(RestockPlanEntity entity);
    RestockPlanEntity queryRestockPlanById(Map<String,Object> map);
    int deleteRestockPlan(Map<String,Object> map);
    int deleteRestockPlanPresent(Map<String,Object> map);
    List<RestockPlanListResult> queryRestockPlanList(Map<String,Object> map);
    List<RestockPlanGoodsResult> queryGoodsListByPlanId(Map<String,Object> map);
    int addRestockPlanSupplier(RestockPlanSupplierEntity supplierEntity);
    int addRestockPlanGoods(RestockPlanGoodsEntity goodsEntity);
    int updatePlanStatus(Map<String,Object> map);
    List<RestockPlanSuppliersResult> getPresentListByPlanId(Map<String,Object> map);
    RestockPlanGoodsEntity queryGoodsById(QueryGoodsByIdParams params);
    int updateRestockPlanGoods(Map<String,Object> map);
    int deleteRestockPlanGoods(Map<String,Object> map);
    int updateRestockPlanSupplier(Map<String,Object> map);
    List<RestockPlanSupplierResult> queryGoodsListBySupplierId(Map<String,Object> map);
    RestockPlanGoodsDetailResult queryGoodsSaleCount(Map<String,Object> map);
    Map<String, Object> querySupplierByPlanId(Map<String,Object> map);
    List<RestockPlanGoodsEntity> queryGoods(Map<String,Object> map);
    List<RestockPlanSupplierEntity> querySupplier(Map<String,Object> map);
    Map<String,Object> queryGoodsListByGoodsId(Map<String,Object> map);
    List<RestockPlanEntity> queryRestockPlanBySupplierUnique(SupplierUniqueParams params);
}
