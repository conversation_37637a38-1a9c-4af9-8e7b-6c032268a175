package org.haier.shopUpdate.dao;

import org.haier.shopUpdate.entity.ShopAggregationCodeEntity;

public interface ShopAggregationCodeDao {

    /**
     * 根据条件查询
     * @param entity
     * @return
     */
    public ShopAggregationCodeEntity queryByEntity(ShopAggregationCodeEntity entity);

    /**
     * 保存数据
     * @param entity
     */
    public void insert(ShopAggregationCodeEntity entity);

    /**
     * 根据id更新
     * @param entity
     */
    public void updateById(ShopAggregationCodeEntity entity);
}
