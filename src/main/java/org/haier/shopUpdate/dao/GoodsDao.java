package org.haier.shopUpdate.dao;

import org.apache.ibatis.annotations.Param;
import org.haier.shopUpdate.dao.dojo.QueryGoodsInfoDo;
import org.haier.shopUpdate.dao.pojo.AddGoodsPo;
import org.haier.shopUpdate.dao.pojo.AddGoodsStockPo;
import org.haier.shopUpdate.dto.QueryAdjustmentRecodingCountDto;
import org.haier.shopUpdate.dto.QueryGoodsByGoodsBarcodeDto;
import org.haier.shopUpdate.entity.BaseGoods;
import org.haier.shopUpdate.entity.GoodsEntity;
import org.haier.shopUpdate.entity.GoodsPriceAdjustmentEntity;
import org.haier.shopUpdate.entity.goods.goodsRecord.RecordGoods;
import org.haier.shopUpdate.entity.promotion.PromotionActivity;
import org.haier.shopUpdate.params.goods.QueryGoodsLastInPriceParams;
import org.haier.shopUpdate.params.goods.QueryPromotionActivityListParams;
import org.haier.shopUpdate.result.goods.allocation.QueryGoodsLastInPriceResult;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 商品相关接口
 *
 * <AUTHOR>
 */
public interface GoodsDao {

    List<PromotionActivity> queryPromotionActivityOrderMarkdown(List<Integer> ids);
    List<PromotionActivity> queryPromotionActivitySingle(List<Integer> ids);
    List<PromotionActivity> queryPromotionActivityGiftlList(List<Integer> ids);

    List<PromotionActivity> queryPromotionActivityGoodsMarkdown(List<Integer> ids);

    List<PromotionActivity> queryPromotionActivityList(QueryPromotionActivityListParams params);
    /**
     * 查询商品最近入库价
     * @return
     */
    List<QueryGoodsLastInPriceResult> queryGoodsLastInPrice(QueryGoodsLastInPriceParams params);
    /**
     * 查询店铺内，相关联的产品信息
     * @param goods
     * @return
     */
    List<GoodsEntity> selectGoodsByParam(GoodsEntity goods);

    GoodsEntity selectOneByParam(GoodsEntity goods);
    /**
     * 查询商品画像
     * @param map
     * @return
     */
    RecordGoods querySourceGoods(Map<String, Object> map);
	/**
	 * 1、查询最大商品编码
	 * @param map
	 * @return
	 */
	public Long queryMaxGoodsBarcode(Map<String,Object> map);
	
	/**
	 * 农批产品数量查询
	 * @param map
	 * @return
	 */
	public Integer queryFarmGoodsCount(Map<String, Object> map);
	 /**
     * 农批产品列表查询
     *
     * @param map
     * @return
     */
    public List<Map<String, Object>> queryFarmGoodsList(Map<String, Object> map);
    /**
     * 商品分类查询
     *
     * @param map
     * @return
     */
    public List<Map<String, Object>> queryGoodsMessage(Map<String, Object> map);

    List<Map<String, Object>> queryGoodsMessageInvented(Map<String, Object> map);

    /**
     * 删除商家商品
     *
     * @param map
     * @return
     */
    public int deleteShopsGoods(Map<String, Object> map);

    /**
     * 修改商品信息
     *
     * @param map
     * @return
     */

    public int modifyGoods(Map<String, Object> map);

    /**
     * 商品详情查询
     *
     * @param map
     * @return
     */
    public BaseGoods goodsDetail(Map<String, String> map);
//	public List<Map<String,Object>> goodsDetail(Map<String,Object> map);

    /**
     * 商品供货商查询
     *
     * @param map
     * @return
     */
    public List<Map<String, Object>> goodsSupplierQuery(Map<String, Object> map);

    /**
     * 商品供货商信息查询（新）
     * 包含供货商赠品信息的查询及促销信息的查询
     *
     * @param map
     * @return
     */
    public List<Map<String, Object>> goodsSupplierQueryNew(Map<String, Object> map);

    /**
     * 更新商品信息
     *
     * @param map
     * @return
     */
    public int updateGoodsMessage(Map<String, Object> map);

    /**
     * 添加商品信息
     *
     * @param map
     * @return
     */
    public int newGoodsMessage(Map<String, Object> map);

    public int addGoodsStock(AddGoodsStockPo addGoodsStockPo);
    public int addGoodsInfo(AddGoodsPo addGoodsPo);

    public int updateGoodsInfo(AddGoodsPo addGoodsPo);
    /**
     * 更新所有店铺商品的foreign_key
     *
     * @param map
     * @return
     */
    public int updateGoodsForeignKey(Map<String, Object> map);

    public int updateSgoodsForeignKey(Map<String, Object> map);

    /**
     * 直接修改商品库存
     *
     * @param map
     * @return
     */
    public int modifyGoodsCount(Map<String, Object> map);

    /**
     * 商品基本信息查询
     *
     * @param map
     * @return
     */
    public Map<String, Object> baseGoodsMessage(Map<String, Object> map);

    /**
     * 供货端促销商品采购时，查询本店铺是否有相同产品（不同包装规格的商品算同一种商品）
     *
     * @param map
     * @return
     */
    public List<Map<String, Object>> queryHavingPromotionGoods(Map<String, Object> map);

    /**
     * 批量更新商品信息
     *
     * @param list
     * @return
     */
    public int updateGoodsSuppliers(List<Map<String, Object>> list);

    /**
     * 查询供货商的产品信息，以便录入到采购店铺中
     *
     * @param map
     * @return
     */
    public Map<String, Object> selectNewGoods(Map<String, Object> map);

    /**
     * 商品出入库记录查询
     *
     * @param map
     * @return
     */
    public List<Map<String, Object>> stockRecord(Map<String, Object> map);

    public Map<String, Object> goodsSaleStatisticsTotal(Map<String, Object> map);

    public List<Map<String, Object>> goodsSaleStatistics(Map<String, Object> map);

    /**
     * 添加新的商品入库信息
     *
     * @param map
     * @return
     */
    public int addNewShopStock(Map<String, Object> map);

    public Map<String, Object> queryGoodsCount(Map<String, Object> map);

    public Map<String, Object> queryGoodsCountWarningNum(Map<String, Object> map);

    public List<Map<String, Object>> queryGoodsCountList(Map<String, Object> map);

    public Map<String, Object> querySmallGoods(Map<String, Object> params);

    /**
     * 商品存在性查询
     */
    public String queryGoodsMessageByBarcode(Map<String, Object> map);

    /**
     * 查询以0开头的商品数据
     *
     * @param map
     * @return
     */
    public List<String> queryGoodsStartWithZero(Map<String, Object> map);


    /**
     * 新增商品删除记录
     *
     * @param map
     * @return
     */
    public Integer addNewGoodsDeleteRecord(Map<String, Object> map);

    /**
     * 查询商品信息
     *
     * @param shopUnique
     * @param goodsBarcode
     * @return
     */
    Map<String, Object> queryGoodsByGoodsBarcode(@Param("shopUnique") Long shopUnique, @Param("goodsBarcode") String goodsBarcode);

    List<QueryGoodsByGoodsBarcodeDto> queryGoodsAndClassByGoodsCode(@Param("goodsCode") List<String> goodsCode, @Param("shopUnique") String shopUnique);

    List<QueryAdjustmentRecodingCountDto> queryAdjustmentRecoding(@Param("goodsBarcode") List<String> goodsBarcode, @Param("shopUnique") String shopUnique,
                                                                  @Param("startDate") String startDate,@Param("endDate") String endDate);

    int addAdjustmentRecoding(GoodsPriceAdjustmentEntity entity);

    List<QueryGoodsInfoDo> queryListByGoodsBarcode(@Param("shopUnique") Long shopUnique, @Param("list") List<String> goodsBarcodeList);

    /**
     * 根据商品id集合查询
     * @param map
     * @return
     */
    List<GoodsEntity> queryGoodsByParam(Map<String,Object> map);

    /**
     * 根据商品id查询
     * @param map
     * @return
     */
    GoodsEntity queryOneByParam(Map<String,Object> map);

    /**
     * 查询商品是否存在
     * @param addGoodsParam
     * @return
     */
    public int queryGoodsExists(AddGoodsPo addGoodsParam);

    List<Map<String, Object>> queryGoodsByGoodsBarcodes(@Param("shopUnique") Long shopUnique, @Param("goodsBarcodeList") List<Object> goodsBarcodeList);

    GoodsEntity queryGoodByBarcode(Map<String,Object> map);

    /**
     * 查询商品是否本店商品
     * @param map
     * @return
     */
    String queryGoodsBarcodeSameForeignkey(Map<String,Object> map);

    /**
     * 查询该店铺中最大的商品条码
     * @param map
     * @return
     */
    String queryGoodsBarcodeSame(Map<String,Object> map);

    List<Map<String, Object>> queryShopGoodsIds(@Param("shopUnique") Long shopUnique, @Param("goodsBarcodeList") Set<String> goodsBarcodeList);
    /**
     * 校验产品是否在货位上
     * @param id
     * @return
     */
    Integer checkgoodsPosition(Long id);

    List<String> check(Long id);

    /**
     * 根据商品编码和店铺查询商品信息
     *
     * @param shopUnique
     * @param goodsKindUniques
     * @return
     */
    List<Map<String,Object>> queryGoodsByKindAndShopUnique(@Param("shopUnique") String shopUnique, @Param("goodsKindUniques") List<Long> goodsKindUniques);
}
