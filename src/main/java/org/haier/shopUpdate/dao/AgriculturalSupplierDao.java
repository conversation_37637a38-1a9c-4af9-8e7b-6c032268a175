package org.haier.shopUpdate.dao;

import org.haier.shopUpdate.entity.AgriculturalSupplierEntity;
import org.haier.shopUpdate.params.agriculturalSupplier.QuerySupplierListParams;

import java.util.List;

public interface AgriculturalSupplierDao {

    /**
     * 根据姓名查询
     * @param entity
     * @return
     */
    public AgriculturalSupplierEntity findByName(AgriculturalSupplierEntity entity);

    /**
     * 保存数据
     * @param entity
     */
    public void insert(AgriculturalSupplierEntity entity);

    /**
     * 根据id查询
     * @param entity
     * @return
     */
    public AgriculturalSupplierEntity findById(AgriculturalSupplierEntity entity);

    /**
     * 根据id修改
     * @param entity
     */
    public void updateById(AgriculturalSupplierEntity entity);

    /**
     * 分页列表
     * @param params
     * @return
     */
    public List<AgriculturalSupplierEntity> findPageList(QuerySupplierListParams params);

}

