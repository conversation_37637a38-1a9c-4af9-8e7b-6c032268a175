package org.haier.shopUpdate.dao;

import org.apache.ibatis.annotations.Param;
import org.haier.shopUpdate.entity.InventoryTaskBucketWeightEntity;

import java.util.List;
import java.util.Map;

public interface InventoryTaskBucketWeightDao {

    /**
     * 新增
     * @param entity
     * @return
     */
    public int insert(InventoryTaskBucketWeightEntity entity);

    /**
     * 根据id删除
     * @param id
     * @return
     */
    public int deleteById(@Param("id") Long id);

    /**
     * 根据id更新
     * @param entity
     * @return
     */
    public int updateById(InventoryTaskBucketWeightEntity entity);

    /**
     * 分页查询
     * @param map
     * @return
     */
    public List<InventoryTaskBucketWeightEntity> findByPage(Map<String,Object> map);

    /**
     * 根据条件查询
     * @param entity
     * @return
     */
    public InventoryTaskBucketWeightEntity findOneByParam(InventoryTaskBucketWeightEntity entity);

}
