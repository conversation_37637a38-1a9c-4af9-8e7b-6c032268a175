package org.haier.shopUpdate.dao;

import java.util.List;
import java.util.Map;

public interface UtilDao {
	/**
	 * 查询店铺重复商品信息
	 * @param map
	 * @return
	 */
	public List<String> getSameGoodsInShops(Map<String,Object> map);
	
	/**
	 * 删除重复的商品信息
	 * @param list
	 * @return
	 */
	public int deleteSameGoods(List<String> list);
	
	/**
	 * 将店铺中不存在于云库的商品添加到云库
	 * @param map
	 * @return
	 */
	public int addShopsGoodsToCloud(Map<String,Object> map);
	
	/**
	 * 将店铺中新商品的barcode修改为不高于14为，并同时修改其key
	 */
	
}
