package org.haier.shopUpdate.dao;

import java.util.Map;

/**
* @author: 作者:王恩龙
* @version: 2020年7月4日 上午11:22:37
*
*/
public interface GoldDao {
	/**
	 * 新增店铺金圈币信息
	 * @param map
	 * @return
	 */
	public Integer addNewShopGold(Map<String,Object> map);
	/**
	 * 修改店铺金圈币余额
	 * @param map
	 * @return
	 */
	public Integer modifyShopGold(Map<String,Object> map);
	/**
	 * 获取当前店铺的金圈币余额
	 * @param map
	 * @return
	 */
	public Double queryShopJQB(Map<String,Object> map);
	
	/**
	 * 获取未发放的金圈币信息
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryGoldGrant(Map<String,Object> map);
	
	/**
	 * 修改领取状态
	 * @param map
	 * @return
	 */
	public Integer grantGoldById(Map<String,Object> map);
}
