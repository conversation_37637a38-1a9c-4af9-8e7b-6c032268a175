package org.haier.shopUpdate.dao;

import java.util.List;
import java.util.Map;

import org.haier.shopUpdate.entity.Binding;
import org.haier.shopUpdate.entity.ShopEntity;

public interface CashDao {

	/**
	 * 添加商品促销活动时，查询是否有单品促销活动，防止重复添加
	 * @param map
	 * @return
	 */
	public Integer checkGoodsActiveRepert(Map<String,Object> map);
	/**
	 * 扣除已自动发放的优惠券数量
	 * @param map
	 * @return
	 */
	public Integer deductAutoGrantCount(Map<String, Object> map);
	
	/**
	 * 优惠券自动发放
	 * @param map
	 * @return
	 */
	public Integer autoGrantShopCoupon(Map<String, Object> map);
	
	List<Map<String, Object>> queryPayMethod();

	List<Map<String, Object>> queryCarList(Map<String, Object> map);

	void addCar(Map<String, Object> map);

	void updateCar(Map<String, Object> map);

	void deleteCar(Map<String, Object> map);

	List<Map<String, Object>> queryRechargeConfig(Map<String, Object> params);

	void insertCustomer_recharge_config(Map<String, Object> params);

	void updateCustomer_recharge_config(Map<String, Object> params);

	List<Map<String, Object>> queyRechargeLog(Map<String, Object> params);

	List<Map<String, Object>> getMemberLevel(Map<String, Object> map);

	void setMemberLevel(Map<String, Object> map);

	List<Map<String, Object>> queryCusCheckOut(Map<String, Object> map);

	List<Map<String, Object>> queryPointUseList(Map<String, Object> map);

	Map<String, Object> queryPointUseCount(Map<String, Object> map);

	List<Map<String, Object>> queryOrderListByPage2(Map<String, Object> params);

	Map<String, Object> queryOrderListByPageCount2(Map<String, Object> params);

	Map<String, Object> queryShopBeans(Map<String, Object> params);

	List<Map<String, Object>> queryTransactionListPage2(Map<String, Object> params);

	List<Map<String, Object>> queryShopBeansPromation(Map<String, Object> params);

	List<Map<String, Object>> queryPcActivityMenuList(Map<String, Object> params);

	Map<String, Object> queryGoldByShop2(Map<String, Object> params);

	void saveBindingJiGuang(Map<String, Object> params);

	List<Map<String, Object>> queryBankName();

	Map<String, Object> queryDownload();

	List<Map<String, Object>> queryFuncitonImage(Map<String, Object> params);

	List<Map<String, Object>> queryShelfStateGoodsMessage(Map<String, Object> params);

	Integer updateShelfState(Map<String, Object> params);

	Map<String, Object> queryShopDelivery(String shop_unique);

	int updateShopDelivery(Map<String, Object> params);

	List<Map<String, Object>> getShopCourierList(Map<String, Object> map);

	int addShopCourier(Map<String, Object> map);

	int updateShopCourier(Map<String, Object> map);

	int deleteShopCourier(String courier_id);

	List<Map<String, Object>> queryShopCouponList(Map<String, Object> map);
	
	int  queryShopCouponCount(Map<String, Object> params);
	
	void addShopCoupon(Map<String, Object> params);

	void addShopCouponTime(Map<String, Object> timeParams);

	void addShopCouponEffective(List<Map<String, Object>> dayList);

	void deleteShopCoupon(Map<String, Object> map);

	List<Map<String, Object>> queryCouponRecordList(Map<String, Object> map);
	/**
	 * 查询优惠券领取数量
	 * @param map
	 * @return
	 */
	public Integer queryCouponRecordCount(Map<String, Object> map);

	List<Map<String, Object>> queryPromotionList(Map<String, Object> map);

	Integer getTimeCoincideGoodsMarkdown(Map<String, Object> params);

	int submitSupplierStorageOrder(Map<String, Object> map);

	void addPromotionGoodsMarkdown(List<Map<String, Object>> map3List);

	void updateActivityStatus(Map<String, Object> map);

	Map<String, Object> queryGoodsMarkdownDetail(Map<String, Object> map);

	List<Map<String, Object>> queryGoodsMarkdownListDetail(Map<String, Object> map);

	void deleteActivity(Map<String, Object> map);

	void deleteGoodsMarkdown(Map<String, Object> map);

	void deleteGoodsGift(Map<String, Object> map);

	void deleteOrderMarkdown(Map<String, Object> map);

	void deleteSingleGoodsMarkdown(Map<String, Object> map);

	Integer getTimeCoincideGoodsGift(Map<String, Object> params);

	void addGoodsGift(List<Map<String, Object>> map3List);

	List<Map<String, Object>> queryGoodsGiftDetail(Map<String, Object> map);

	Integer getTimeGoodsPromotionOnline(Map<String, Object> params);

	void addPromotionGoodsSingle(List<Map<String, Object>> map3List);

	List<Map<String, Object>> querySingleGoodsPromotionDetail(Map<String, Object> map);

	Integer getTimeCoincideOrderMarkdown(Map<String, Object> params);

	void addOrderMarkdown(Map<String, Object> map3);

	Map<String, Object> queryOrderMarkdownDetail(Map<String, Object> map);

	List<Binding> queryShopsBinding(Map<String, Object> map);

	int newBindingGoods(List<Map<String, Object>> resource);

	int modifyBinding(Map<String, Object> map);

	int deleteBindingGoods(Map<String, Object> map);

	List<Map<String, Object>> queryOurShopGoods(Map<String, Object> map);

	void submitSingleGoodsPromotion(Map<String, Object> map);

	void updateActitiy(Map<String, Object> map);

	Map<String, Object> queryOurShopGoodsDetail(Map<String, Object> map);

	Map<String, Object> queryGoodsPromotion(Map<String, Object> map);

	Map<String, Object> queryGoodsMeetGive(Map<String, Object> map);

	Map<String, Object> querySingeGoodsPromotion(Map<String, Object> map);

	Map<String, Object> queryOrderMeetMoney(Map<String, Object> map);

	List<Binding> queryShopsBindingList(Map<String, Object> map);

	Map<String, Object> queryShopOpenStatus(Map<String, Object> map);

	int queryShopCouponListCount(Map<String, Object> map);

	int queryOrderListByPageCount(Map<String, Object> params);

	int queryTransactionListPageCount(Map<String, Object> params);

	int queryShopBeansPromationCount(Map<String, Object> params);

	Map<String, Object> queryBeansDiKu(Map<String, Object> map);

	void updateBankRoot(Map<String, Object> map);

	List<Map<String, Object>> getGoodsSupplierMsg(String shopUnique);

	int queryGoodsCount(Map<String, Object> map);

	List<Map<String, Object>> queryGoodsByPage(Map<String, Object> map);

	int queryOurShopGoodsCount(Map<String, Object> map);

	Map<String, Object> queryRechargeConfigByMoneyAndTime(Map<String, Object> params);
	
	
	
}
