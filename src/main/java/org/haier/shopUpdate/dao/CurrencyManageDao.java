package org.haier.shopUpdate.dao;

import org.apache.ibatis.annotations.Param;
import org.haier.shopUpdate.entity.CusRecharge;
import org.haier.shopUpdate.entity.ShopManager;
import org.haier.shopUpdate.entity.ShopStaffEntity;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 店铺登录相关接口
 * <AUTHOR>
 */
public interface CurrencyManageDao {

String getCurrencySymbolByCurrencyCode(String currencyCode);
    /**
     * 通过货币code批量获取CurrencySymbols
     */
    List<Map<String,Object>> getCurrencySymbolsByCurrencyCodes(@Param("currencys") List<String> currencys);

}
