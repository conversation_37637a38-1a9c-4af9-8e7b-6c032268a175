package org.haier.shopUpdate.dao;

import java.util.List;

import org.haier.shopUpdate.entity.ShopTitle;
import org.haier.shopUpdate.entity.ShopTitleMain;
import org.haier.shopUpdate.entity.ShopTitleMainNew;

public interface TitleDao {
	/**
	 * 查询店铺内的标题信息
	 * @param shopTitle
	 * @return
	 */
	public List<ShopTitleMain> queryMainPageTitle(ShopTitle shopTitle);
	
	/**
	 * 修改店铺的排序
	 * @param list
	 * @return
	 */
	public Integer modifyTitle(List<ShopTitle> list);
	
	
	public List<ShopTitleMainNew> queryMainPageTitle_v2(ShopTitle shopTitle);
}
