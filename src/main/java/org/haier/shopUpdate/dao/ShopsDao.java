package org.haier.shopUpdate.dao;

import org.apache.ibatis.annotations.Param;
import org.haier.rabbitmq.entity.ShopEntity;
import org.haier.shopUpdate.entity.ShopsEntity;

public interface ShopsDao {

    /**
     * 根据shop_unique查询
     * @param shopUnique
     * @return
     */
    public ShopsEntity getByShopUnique(Long shopUnique);

    ShopEntity selectByShopUnique(@Param("shopUnique") Long shopUnique);

}
