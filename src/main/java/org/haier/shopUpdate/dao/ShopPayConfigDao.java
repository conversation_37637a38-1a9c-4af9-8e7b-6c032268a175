package org.haier.shopUpdate.dao;

import org.haier.shopUpdate.entity.ShopQuickPayConfig;
import org.haier.shopUpdate.params.shopQuickPay.ConfigListParams;

import java.util.List;

public interface ShopPayConfigDao {

    /**
     * 查询支付配置
     * @return
     */
    List<ShopQuickPayConfig> configList(ConfigListParams configListParams);

    /**
     * 查询支付配置数量
     * @return
     */
    Integer configCount(ConfigListParams configListParams);
    /**
     * 添加支付配置
     * @param shopQuickPayConfig
     * @return
     */
    int addConfig(ShopQuickPayConfig shopQuickPayConfig);
    /**
     * 修改支付配置
     * @param shopQuickPayConfig
     * @return
     */
    int updateConfig(ShopQuickPayConfig shopQuickPayConfig);
}
