package org.haier.shopUpdate.dao;

import java.util.List;
import java.util.Map;

/**
 * 测试接口，本接口内所有方法均为测试使用
 * <AUTHOR>
 *
 */
public interface TestDao {
	public void addNewTestGoods();
	public void  dropTable();
	public int createTemTable();
	public int addTemRows(List<Map<String,String>> list);
	public List<Map<String,Object>> queryTemRows();
	
	public List<Map<String,Object>> queryShopList();
	public Integer modifyShopTime(List<Map<String,Object>> list);
	
	public List<Map<String,Object>> queryGoodsForUpdate();
	public Integer modifyGoods(List<Map<String,Object>> list);
	
	public List<Map<String,Object>> queryShopsLong();
	public List<Map<String,Object>> queryShopNeedLong();
	
	public Integer modifyLong(List<Map<String,Object>> l);
	
	public List<Map<String,Object>> getNewGoodsKind();
	public List<String> queryShopUnique(Map<String,Object> map);
	
	public Integer addNewGoodsKind(List<Map<String,Object>> list);
	
	public List<Map<String,Object>> queryGoodsMsg();
	public Integer addGoodsToShop(List<Map<String,Object>> list);
	
	public Integer addGoods(Map<String,Object> params);
	
	public Integer getCount(Map<String,Object> params);
	
	public List<Map<String,Object>> querySaleListUnique();
	
	public int addNewPayDetail(List<Map<String,Object>> list);
	
	public List<Map<String,Object>> queryGoodsForUpdateShop();
	public Integer modifyShopGoods(List<Map<String,Object>> list);
}
