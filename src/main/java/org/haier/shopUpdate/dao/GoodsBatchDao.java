package org.haier.shopUpdate.dao;

import org.haier.shopUpdate.dao.pojo.GoodsBatchAddPo;
import org.haier.shopUpdate.dao.pojo.GoodsBatchUpdatePo;
import org.haier.shopUpdate.params.goodBatch.GoodBatchListQueryParams;
import org.haier.shopUpdate.params.goodBatch.GoodBatchQueryParams;
import org.haier.shopUpdate.params.goodBatch.GoodBatchUpdateParams;
import org.haier.shopUpdate.result.goodBatch.GoodBatchListQueryDto;
import org.haier.shopUpdate.result.goodBatch.GoodBatchQueryResult;

import java.util.List;

public interface GoodsBatchDao {

    void saveGoodsBatch(GoodsBatchAddPo goodsBatchAddPo);

    void saveGoodsBatchList(List<GoodsBatchAddPo> list);

    void updateGoodsBatch(GoodsBatchUpdatePo goodsBatchUpdatePo);

    void updateGoodsBatchList(List<GoodsBatchUpdatePo> list);

    GoodBatchQueryResult queryGoodsBatch(GoodBatchQueryParams params);

    List<GoodBatchListQueryDto> queryGoodsBatchOutList(GoodBatchListQueryParams params);

    int queryGoodsBatchOutListCount(GoodBatchListQueryParams params);

    List<GoodBatchListQueryDto> queryGoodsBatchInList(GoodBatchListQueryParams params);

    int queryGoodsBatchInListCount(GoodBatchListQueryParams params);

    void batchUpdateGoodsBatch(List<GoodBatchUpdateParams> list);
}
