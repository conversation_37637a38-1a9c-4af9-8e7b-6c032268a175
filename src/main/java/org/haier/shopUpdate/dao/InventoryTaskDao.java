package org.haier.shopUpdate.dao;

import org.apache.ibatis.annotations.Param;
import org.haier.shopUpdate.entity.InventoryTaskEntity;

import java.util.List;
import java.util.Map;

public interface InventoryTaskDao {

    /**
     * 根据task_name查询
     * @param entity
     * @return
     */
    public InventoryTaskEntity findByParam(InventoryTaskEntity entity);

    /**
     * 保存数据
     * @param entity
     * @return
     */
    public int insert(InventoryTaskEntity entity);

    /**
     * 根据id删除
     * @param id
     * @return
     */
    public int deleteById(@Param("id") Long id);

    /**
     * 根据id更新
     * @param entity
     * @return
     */
    public int updateById(InventoryTaskEntity entity);

    /**
     * 分页查询
     * @param map
     * @return
     */
    public List<InventoryTaskEntity> queryByPage(Map<String,Object> map);

    /**
     * 分页查询统计
     * @param map
     * @return
     */
    public int queryByPageCount(Map<String,Object> map);

}
