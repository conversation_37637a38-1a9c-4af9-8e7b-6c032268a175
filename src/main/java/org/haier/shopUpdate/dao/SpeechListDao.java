package org.haier.shopUpdate.dao;

import org.haier.shopUpdate.entity.SpeechListEntity;
import org.haier.shopUpdate.params.speech.QuerySpeechListParams;

import java.util.List;

public interface SpeechListDao {
    /**
     * 查询满足条件的语音列表
     * @param speechEntity
     * @return
     */
    public SpeechListEntity querySpeechEntity(SpeechListEntity speechEntity);

    /**
     * 查询语音操作命令列表
     * @param params
     * @return
     */
    public List<SpeechListEntity> querySpeechList(QuerySpeechListParams params);

    /**
     * 新增一条语音操作命令
     * @param speechEntity
     * @return
     */
    public Integer addNewSpeechEntity(SpeechListEntity speechEntity);

    /**
     * 更新单条语音操作记录
     * @param speechEntity
     * @return
     */
    public Integer updateSpeechListById(SpeechListEntity speechEntity);
}
