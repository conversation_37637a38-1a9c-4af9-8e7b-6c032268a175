package org.haier.shopUpdate.dao;

import java.util.List;
import java.util.Map;

import org.haier.shopUpdate.entity.BeansExchange;
import org.haier.shopUpdate.entity.BeansGetRule;
import org.haier.shopUpdate.entity.PageQuery;
import org.haier.shopUpdate.entity.ShopBeansVO;
import org.haier.shopUpdate.entity.ShopCard;

public interface DiamondsDao {
	

	public Map<String,Object> queryShopDiamondsTotal(Map<String,Object> map);
	/**
	 * 查询周期时间段内，店铺的钻石收支状况及余额
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> statisticsShopsDiamondsByTime(Map<String,Object> map);
	
	/**
	 * 查询店铺银行卡列表信息
	 * @param map
	 * @return
	 */
	public List<ShopCard> getShopCardList(ShopCard shopCard);
//	public List<Map<String,Object>> getShopCardList(ShopCard shopCard);
	
	//将已有的银行卡信息设置无效状态
	public Integer modifyShopCard(ShopCard shopCard);
	/**
	 * 添加新的银行卡信息
	 * @param card
	 * @return
	 */
	public Integer addNewShopCard(ShopCard card);
	
	/**
	 * 查询支持的银行列表信息
	 * @return
	 */
	public List<Map<String,Object>> getBankListMsg();
	
	/**
	 * 获取提现相关讯息
	 * @param card
	 * @return
	 */
	public Map<String,Object> getShopBeansAndRule(Object card);
	
	/**
	 * 根据规则，查询规则时间内的取现次数；
	 * @param map
	 * @return
	 */
	public Integer getCashCount(Map<String,Object> map);
	
	/**
	 * 添加新的提现记录
	 * @param beansExchange
	 * @return
	 */
	public Integer addNewCashRecord(BeansExchange beansExchange);
	
	/**
	 * 获取店铺的规则设置
	 * @param card
	 * @return
	 */
	public Map<String,Object> getDiamondsRule(ShopCard card);
	
	/**
	 * 添加新的规则设置信息
	 * @param beansGetRule
	 * @return
	 */
	public Integer addNewGetRule(BeansGetRule beansGetRule);
	
	/**
	 * 更新店铺的使用百货豆规则
	 * @param beansGetRule
	 * @return
	 */
	public Integer modifyShopUseRule(BeansGetRule beansGetRule);
	/**
	 * 若店铺不存在百货豆使用规则，新建
	 * @param beansGetRule
	 * @return
	 */
	public Integer addNewShopUseRule(BeansGetRule beansGetRule);
	
	/**
	 * 店铺购买记录信息查询
	 * @param map
	 * @return
	 */
	public Map<String,Object> beansBuyRecordTotal(PageQuery pageQuery);
	
	/**
	 * 店铺购买记录列表
	 * @param pageQuery
	 * @return
	 */
	public List<Map<String,Object>> beansBuyRecordList(PageQuery pageQuery);
	
	/**
	 * 抵扣记录统计
	 * @param pageQuery
	 * @return
	 */
	public List<Map<String,Object>> dikouTotal(PageQuery pageQuery);
	
	/**
	 * 抵扣记录分页查询
	 * @param pageQuery
	 * @return
	 */
	public List<Map<String,Object>> dikouList(PageQuery pageQuery);
	
	/**
	 * 免密赠送统计
	 * @param pageQuery
	 * @return
	 */
	public Map<String,Object> mmGiveBeansTotal(PageQuery pageQuery);
	
	/**
	 * 免密赠送分页查询
	 * @param pageQuery
	 * @return
	 */
	public List<Map<String,Object>> mmGiveBeansList(PageQuery pageQuery);
	
	/**
	 * 取现统计
	 * @param pageQuery
	 * @return
	 */
	public Map<String,Object> takeCashTotal(PageQuery pageQuery);
	
	/**
	 * 提现记录分页查询
	 * @param pageQuery
	 * @return
	 */
	public  List<Map<String,Object>> takeCashList(PageQuery pageQuery);
	/**
	 * 钻石赠送记录统计
	 */
	public  List<Map<String,Object>> giveTotal(PageQuery pageQuery);
	
	/**
	 * 钻石赠送记录分页查询
	 * @param pageQuery
	 * @return
	 */
	public List<Map<String,Object>> giveList(PageQuery pageQuery);
	
	/**
	 * 钻石购买设置信息
	 * @param pageQuery
	 * @return
	 */
	public List<Map<String,Object>> beansBuyRule();
	
	/**
	 * 更新购买定的支付状态
	 * @param beansExchange
	 * @return
	 */
	public Integer modifyBeansExchangeRecord(BeansExchange beansExchange);
	/**
	 * 
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryRulrForDiamonds(Map<String,Object> map);
	/**
	 * 回调函数，根据支付宝反馈的结果，查询购买信息，用来修改店铺的钻石数量信息
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryExchangeMsgByOrderId(BeansExchange map);
	
	public Integer modifyShopDiamonds(Map<String,Object> map);
	
	public Integer modifyBeansRule(BeansGetRule b);
	
	//********
	public List<Map<String,Object>> queryBeanList(Map<String,Object> map);
	
	
	public ShopBeansVO getShopBeans(Map<String,Object> map);
	
	public int queryTransactionCount(Map<String,Object> map);
	
	//查询银行是否存在
	public int queryBankById(ShopCard card);
	
}
