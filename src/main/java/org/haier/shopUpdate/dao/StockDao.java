package org.haier.shopUpdate.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.haier.shopUpdate.entity.ShopStock;
import org.haier.shopUpdate.entity.StockReason;
import org.haier.shopUpdate.params.shopStock.ShopStockAddParams;
import org.haier.shopUpdate.params.shopStockDetail.ShopStockDetailAddParams;

/**
 * 出入库记录
 * <AUTHOR>
 */
public interface StockDao {
	/**
	 * 查询店铺设置信息
	 * @param shopUnique
	 * @return
	 */
	public Map<String,Object> queryShopSetting(String shopUnique);
	/**
	 * 商品出入库记录添加
	 * @param map
	 * @return
	 */
	public int newStockRecord(Map<String,Object> map);
	/**
	 * 商品出入库记录添加
	 * @param map
	 * @return
	 */
	public int stockRecordV3(ShopStockAddParams shopStockAddParams);
	/**
	 * 批量插入订单信息！
	 * @param list
	 * @return
	 */
	public int newStockRecords(@Param("list") List<Map<String,Object>> list,@Param("check_time_flg") String checkTimeFlg);
	/**
	 * 查询店铺的出入库记录
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryShopStockRecord(Map<String,Object> map);
	/**
	 * 商品出入库，查询商品的基本信息
	 * @param map
	 * @return
	 */
	public Map<String,Object> getBottomGoodsMessage(Map<String,Object> map);
	
	/**
	 * 出入库时，其他规格商品查询
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryGoodsStand(Map<String,Object> map);
	
	/**
	 * 
	 * @param map
	 * @return
	 */
	public Integer modifyGoodsCount(Map<String,Object> map);
	public List<Map<String, Object>> queryShopStockRecordList(Map<String, Object> map);
	public List<Map<String, Object>> queryGoodsImageByFailId(Map<String, Object> map2);
	public Map<String, Object> queryShopStockRecordDetail(Map<String, Object> params);
	public Map<String, Object> queryGoodsStockLast(Map<String, Object> params);
	public int queryStockListUnique(Map<String, Object> params);
	public void addIntoStockDetail(Map<String, Object> params);
	void addIntoStockDetailV2(ShopStockDetailAddParams shopStockDetailAddParams);
	public int querStockAuditStatus(Map<String, Object> params);
	public List<Map<String, Object>> queryStockDetail(Map<String, Object> params);
	public int modifyGoodsCount2(Map<String, Object> map);
	public void updateStock(Map<String, Object> map);
	public void updateStockStatus(Map<String, Object> params);
	public int queryStockGoods(Map<String, Object> map);
	public void updateIntoStock(Map<String, Object> map);
	public void addIntoStock(Map<String, Object> map);
	public List<Map<String, Object>> qeryGoodsStockLog(Map<String, Object> params);
	public void updateStockDetail(Map<String, Object> params);
	public int newStockRecord2(Map<String, Object> map);
	public void deleteStockGoods(Map<String, Object> params);
	
	public List<StockReason> reason(Map<String, Object> params);

	public int addIntoStockHandReason(Map<String, Object> map);

	/**
	 * 批量更新库存
	 * @param map
	 */
	public void batchUpdateGoodsCount(Map<String,Object> map);

	List<ShopStock> findList(@Param("params") ShopStock shopStock);
}
