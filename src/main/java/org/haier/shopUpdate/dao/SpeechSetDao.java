package org.haier.shopUpdate.dao;

import org.haier.shopUpdate.entity.SpeechSetEntity;

import java.util.List;

public interface SpeechSetDao {

    /**
     * 查询所有满足条件的设置信息
     * @param speechSetEntity
     * @return
     */
    public List<SpeechSetEntity> querySpeechSetList(SpeechSetEntity speechSetEntity);

    /**
     * 查询指定的单条配置信息
     * @param speechSetEntity
     * @return
     */
    public SpeechSetEntity querySpeechSetEntity(SpeechSetEntity speechSetEntity);
}
