package org.haier.shopUpdate.dao;

import org.apache.ibatis.annotations.Param;
import org.haier.shopUpdate.entity.Inventory;
import org.haier.shopUpdate.entity.InventoryBwDetail;
import org.haier.shopUpdate.util.InventorySmDetail;

import java.util.List;
import java.util.Map;

public interface InventoryDao {
	
	/**
	 * 获取盘点商品的售价
	 * @param map
	 * @return
	 */
	public Double queryGoodsSalePriceForInven(Map<String,Object> map);
	/**
	 * 获取盘点店铺的信息
	 * @param map
	 * @return
	 */
	public String queryInventoryShop(Map<String,Object> map);
	/**
	 * 根据员工ID获取对应店铺的盘点类型
	 * @param staffId
	 * @return
	 */
	public Integer queryStockTypeByStaffId(String staffId);
	/**
	 * 各状态的订单数量查询
	 * @param map
	 * @return
	 */
	public List<Map<String, Long>> queryInvertoryCountByType(Map<String,Object> map);
	
	/**
	 * 商家端分页查询盘点信息
	 * @param map
	 * @return
	 */
	public List<Inventory> queryInventoryList(Map<String,Object> map);
	
	/**
	 * 盘点详情
	 * @param map
	 * @return
	 */
	public Inventory queryInventoryDetail(Map<String,Object> map);
//	public List<Map<String,Object>> queryInventoryDetail(Map<String,Object> map);
	/**
	 * 创建新的盘点草稿
	 * @param inven
	 * @return
	 */
	public int createNewInvenDraft(Inventory inven);
	
	/**
	 * 扫码查询商品库存信息
	 * @param goodsBarcode
	 * @return
	 */
	public InventorySmDetail queryGoodsByBarcode(Map<String,Object> map);
	
	public InventoryBwDetail queryBoundCount(Map<String,Object> map);
	
	/**
	 * 扫码查询商品库存信息
	 * @param goodsBarcode
	 * @return
	 */
	public List<Map<String,Object>> queryGoodsByGoodsMessage(Map<String,Object> map);
	
	/**
	 * 商品盘点记录详情添加
	 * @param map
	 * @return
	 */
	public int addNewGoodsStockRecord(Map<String,Object> map);
	/**
	 * 更新已有的商品盘点记录
	 * @param map
	 * @return
	 */
	public int updateGoodsStockRecord(Map<String,Object> map);
	
	/**
	 * 查看盘点定的提交状态，防止被篡改
	 * @param invenId
	 * @return
	 */
	public Map<String,Object> queryInvenStatus(Map<String,Object> map);
	
	/**
	 * 更新盘点订单状态
	 * @param map
	 * @return
	 */
	public Integer updateGoodsInven(Map<String,Object> map) ;
	/**
	 * 批量更新商品库存信息
	 * @param list
	 * @return
	 */
	public Integer updateGoodsStock(List<Map<String,Object>> list);
	
	/**
	 * 盘单信息查询
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryDetailForUpdate(Map<String,Object> map);
	
	/**
	 * 店铺商品盘点详情查询
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryGoodsInvenRecord(Map<String,Object> map);
	
	/**
	 * 编辑界面删除已选中的商品信息
	 * @param list
	 * @return
	 */
	public int deleteInvenGoods(Map<String,Object> map);
	
	/**
	 * 删除未提交的草稿信息
	 * @param map
	 * @return
	 */
	public int deleteDraftLists(Map<String,Object> map);
	
	/**
	 * 删除未提交的草稿详情
	 * @param map
	 * @return
	 */
	public int deleteDrafListDetail(Map<String,Object> map);
	
	/**
	 * 修改盘库详情后，统计商品详情信息，用于更新主订单
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryDetailCount(Map<String,Object> map);
	/**
	 * 跟新盘点草稿详情
	 * @param map
	 * @return
	 */
	public int updateInventoryCount(Map<String,Object> map);

	/**
	 * 上次盘库数据
	 * 查询上次盘库数据
	 * @param shopUnique
	 * @param goodsBarcode
	 * @return
	 */
	Map<String,Object> queryInventoryByGoodsBarcode(@Param("shopUnique") Long shopUnique, @Param("goodsBarcode") String goodsBarcode);
}
