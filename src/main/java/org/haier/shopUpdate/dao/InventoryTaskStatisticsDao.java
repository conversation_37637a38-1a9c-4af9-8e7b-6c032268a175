package org.haier.shopUpdate.dao;

import org.apache.ibatis.annotations.Param;
import org.haier.shopUpdate.entity.InventoryTaskStatisticsEntity;
import org.haier.shopUpdate.result.inventoryTask.InventoryGoodsRecordInventory;

import java.util.List;
import java.util.Map;

public interface InventoryTaskStatisticsDao {

    /**
     * 批量保存
     * @param list
     * @return
     */
    public int insertBatch(@Param("list") List<InventoryTaskStatisticsEntity> list);

    /**
     * 根据条件查询
     * @param entity
     * @return
     */
    public List<InventoryTaskStatisticsEntity> findByParam(Map<String,Object> map);

    /**
     * 盘库记录
     * @param map
     * @return
     */
    public List<InventoryGoodsRecordInventory> findInventoryGoodsRecordPage(Map<String,Object> map);

}
