package org.haier.shopUpdate.dao;

import java.util.List;
import java.util.Map;

import org.haier.shopUpdate.entity.PurCartDetail;
import org.haier.shopUpdate.entity.PurCartGoods;
import org.haier.shopUpdate.entity.PurListDetailUpdate;
import org.haier.shopUpdate.entity.SaleListTaskEntity;

/**
 * 采购订单相关接口
 * <AUTHOR>
 */
public interface PurDao {
	/**
	 * 购物车商品详情查询
	 * @param map
	 * @return
	 */
	public List<PurCartGoods> purCartGoodsSearch(Map<String,Object> map);
	/**
	 * 购物车商品详情查询（新）
	 * @param map
	 * @return
	 */
	public List<PurCartGoods> purCartGoodsSearchNew(Map<String,Object> map);
	/**
	 * 查询购物车中的商品信息简要
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryPurCartGoods(Map<String,Object> map);
	/**
	 * 创建新的购物车信息
	 * @param map
	 * @return
	 */
	public int createNewShopCart(Map<String,Object> map);
	
	/**
	 * 向购物车添加新商品
	 * @param map
	 * @return
	 */
	public int addNewGoodsToCart(Map<String,Object> map);
	/**
	 * 移除购物车商品
	 * @param map
	 * @return
	 */
	public int deleteCartGoods(Map<String,Object> map);
	/**
	 * 修改购物车商品数量
	 * @param map
	 * @return
	 */
	public int modifyCartGoods(Map<String,Object> map);
	/**
	 * 购物车商品数量查询
	 * @param map
	 * @return
	 */
	public Double cartGoodsCount(Map<String,Object> map);
	public List<Map<String, Object>> countSurplusGoods(Map<String, Object> map);
	public List<Map<String,Object>> unsalableGoods(Map<String,Object> map);
	public List<Map<String, Object>> bigKindCountCost(Map<String, Object> map);
	public List<Map<String, Object>> smallKindCountCost(Map<String, Object> map);
	public List<Map<String, Object>> smallKindCountProportion(Map<String, Object> map);
	public List<Map<String, Object>> bigKindCountProportion(Map<String, Object> map);
	public List<Map<String, Object>> getSupList(Map<String, Object> map);
	public List<Map<String, Object>> getSupOrderList(Map<String, Object> map);
	public List<PurCartDetail> queryPurList(Map<String, Object> map);
	public List<Map<String, Object>> queryGoodsMessage(Map<String, Object> gmap);
	public List<Map<String,Object>> queryGoodsMessageNew(Map<String,Object> gmap);
	public int updateMainPurList(Map<String, Object> cmap);
	public void addSubPurLists(List<Map<String, Object>> purlists);
	public int  updateCartDetail(List<Map<String, Object>> ngoodsList);
	public int updateCartDetailNew(List<Map<String,Object>> ngoodsList);
	public int updateCartGiftDetail(List<Map<String,Object>> ngoodsList);
	public void newPurchase_list(Map<String, Object> nmap);
	public void updateToCartDetail(Map<String, Object> nmap);
	public List<Map<String, Object>> getSupOrderListPage(Map<String, Object> map);
	
	public int updatePayStatus(Map<String, Object> map);
	/**
	 * 采购订单订单详情查询
	 * @param map
	 * @return
	 */
	public PurListDetailUpdate queryPurListDetail(Map<String,Object> map);
	/**
	 * 采购订单订单详情查询
	 * @param map
	 * @return
	 */
	public PurListDetailUpdate queryPurListDetailNew(Map<String,Object> map);
	/**
	 * 更新订单的处理状态
	 * @param map
	 * @return
	 */
	public int updateReceiptStatus(Map<String,Object> map);
	
	/**
	 * 商家端向供货端推送信息
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryPurPushMessage(Map<String,Object> map);
	/**
	 * 
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> shopPurListCount(Map<String,Object> map);
	/**
	 * 删除购物车上的商品
	 * @param goodsBarcodes
	 * @return
	 */
	public int purGoodsDelete(Map<String,Object> map);
	
	/**
	 * 查询购物车商品详情对应的基本商品
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryBottomGoodsMessage(Map<String,Object> map);
	/**
	 * 更新商品的数量
	 * @param map
	 * @return
	 */
	public int sureGoodsReceipt(Map<String,Object> map);
	/**
	 * 生产供货商订单时，将对应信息添加到supplier_msg表中，用于web页面定时查询 
	 * @param map
	 * @return
	 */
	public int addNewPurOrderMessage(Map<String,Object> map);
	/**
	 * 提交订单时推送网页消息信息查询
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryPurPushMessageForSub(Map<String,Object> map);
	/**
	 * 修改商品信息时，修改购物车中商品的默认供货商信息
	 * @param map
	 * @return
	 */
	public int updateCartGoodsSupplier(Map<String,Object> map);
	/**
	 * 同一物流订单中，未完成派送的订单数量
	 * @param map
	 * @return
	 */
	public Map<String,Object> getPurListStatusSameLogistics(Map<String,Object> map);
	/**
	 * 所有订单完成后，更新物流订单的完成状态
	 * @param map
	 * @return
	 */
	public int completeLogistics(Map<String,Object> map);
	/**
	 * 订单提交时，更新商家的库存
	 * @param map
	 * @return
	 */
	public int updateSupGoodsCount(List<Map<String,Object>> map);
	
	/**
	 * 取消订单时，恢复库存查询
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> getCancelPurDetail(Map<String,Object> map);
	/**
	 * 取消订单时，恢复供货商库存
	 * @param map
	 * @return
	 */
	public int rebackSupStock(List<Map<String,Object>> map);
	public List<Map<String, Object>> sellListBeforeYesterday(Map<String, Object> map);
	public List<Map<String, Object>> sellList(Map<String, Object> dateMap);
	public void saveSaleList(Map<String, Object> map2);
	public List<SaleListTaskEntity> querySellList(Map<String, Object> map);
	public List<Map<String, Object>> queryShopsList();
	public void addNewTitle(Map<String, Object> map2);
	public void closeEndTimeOrder();
}
