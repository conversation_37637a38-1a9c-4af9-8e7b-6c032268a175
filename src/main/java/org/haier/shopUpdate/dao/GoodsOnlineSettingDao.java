package org.haier.shopUpdate.dao;

import org.haier.shopUpdate.params.goodsOnlineSetting.MinSaleCountAddParams;
import org.haier.shopUpdate.params.goodsOnlineSetting.MinSaleCountUpdateParams;
import org.haier.shopUpdate.result.goodsOnlineSetting.GoodsOnlineSettingDto;

import java.util.List;
import java.util.Map;

public interface GoodsOnlineSettingDao {
    void saveGoodsOnlineSetting(MinSaleCountAddParams params);

    void saveGoodsOnlineSettingList(List<MinSaleCountAddParams> params);

    void updateGoodsOnlineSetting(MinSaleCountUpdateParams params);

    void updateGoodsOnlineSettingList(List<MinSaleCountUpdateParams> params);

    GoodsOnlineSettingDto queryGoodsOnlineSettingByGoodsId(Integer goodsId);

    GoodsOnlineSettingDto queryGoodsOnlineSetting(Map<String, Object> params);
}
