package org.haier.shopUpdate.dao;


import org.apache.ibatis.annotations.Param;
import org.haier.shopUpdate.entity.GoodsPositionEntity;

import java.util.List;

/**
 * 货币管理mapper
 * 
 * <AUTHOR>
 * @date 2024-12-18
 */

public interface GoodsPositionDao {
     /**
      * 货位查询列表
      */
     List<GoodsPositionEntity> selectList(@Param("positionName") String positionName, @Param("shopUnique") String shopUnique);

    /**
     * 添加货位
      */
    int add(GoodsPositionEntity vo);

    /**
      * 查询货位详情
     */
    GoodsPositionEntity queryDetail(@Param("id") Long id);

    /**
     * 通过父parentId查询货位详情
     */
    Integer queryCountByParent(Long id);

    /**
      * 修改货位
     */
     int update(GoodsPositionEntity vo);

    /**
     * 删除货位
      */
     int delete(Long id);

    /**
     * 同级货位名称不能重复
     */
    int cheakName(@Param("parentId") Long parentId, @Param("name") String name, @Param("shopUnique") Long shopUnique);
}
