package org.haier.shopUpdate.dao.dojo;

import java.math.BigDecimal;

public class QueryGoodsInfoDo {
    private String shopsUnique;
    private String goodsBarcode;
    private String goodsKindUnique;
    private String goodsBrand;
    private String goodsName;
    private String goodsAlias;
    private BigDecimal goodsInPrice;
    private BigDecimal goodsSalePrice;
    private BigDecimal goodsWebSalePrice;
    private BigDecimal goodsCusPrice;
    private BigDecimal wholesalePrice;
    private Integer wholesalePriceFlg;
    private BigDecimal wholesaleCount;
    private String goodsLife;
    private String goodsStandard;
    private String goodsUnit;
    private String goodsPicturePath;

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public String getGoodsKindUnique() {
        return goodsKindUnique;
    }

    public void setGoodsKindUnique(String goodsKindUnique) {
        this.goodsKindUnique = goodsKindUnique;
    }

    public String getGoodsBrand() {
        return goodsBrand;
    }

    public void setGoodsBrand(String goodsBrand) {
        this.goodsBrand = goodsBrand;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsAlias() {
        return goodsAlias;
    }

    public void setGoodsAlias(String goodsAlias) {
        this.goodsAlias = goodsAlias;
    }

    public BigDecimal getGoodsInPrice() {
        return goodsInPrice;
    }

    public void setGoodsInPrice(BigDecimal goodsInPrice) {
        this.goodsInPrice = goodsInPrice;
    }

    public BigDecimal getGoodsSalePrice() {
        return goodsSalePrice;
    }

    public void setGoodsSalePrice(BigDecimal goodsSalePrice) {
        this.goodsSalePrice = goodsSalePrice;
    }

    public String getGoodsLife() {
        return goodsLife;
    }

    public void setGoodsLife(String goodsLife) {
        this.goodsLife = goodsLife;
    }

    public String getGoodsStandard() {
        return goodsStandard;
    }

    public void setGoodsStandard(String goodsStandard) {
        this.goodsStandard = goodsStandard;
    }

    public String getGoodsUnit() {
        return goodsUnit;
    }

    public void setGoodsUnit(String goodsUnit) {
        this.goodsUnit = goodsUnit;
    }

    public String getGoodsPicturePath() {
        return goodsPicturePath;
    }

    public void setGoodsPicturePath(String goodsPicturePath) {
        this.goodsPicturePath = goodsPicturePath;
    }

    public String getShopsUnique() {
        return shopsUnique;
    }

    public void setShopsUnique(String shopsUnique) {
        this.shopsUnique = shopsUnique;
    }

    public BigDecimal getGoodsWebSalePrice() {
        return goodsWebSalePrice;
    }

    public void setGoodsWebSalePrice(BigDecimal goodsWebSalePrice) {
        this.goodsWebSalePrice = goodsWebSalePrice;
    }

    public BigDecimal getGoodsCusPrice() {
        return goodsCusPrice;
    }

    public void setGoodsCusPrice(BigDecimal goodsCusPrice) {
        this.goodsCusPrice = goodsCusPrice;
    }

    public BigDecimal getWholesalePrice() {
        return wholesalePrice;
    }

    public void setWholesalePrice(BigDecimal wholesalePrice) {
        this.wholesalePrice = wholesalePrice;
    }

    public Integer getWholesalePriceFlg() {
        return wholesalePriceFlg;
    }

    public void setWholesalePriceFlg(Integer wholesalePriceFlg) {
        this.wholesalePriceFlg = wholesalePriceFlg;
    }

    public BigDecimal getWholesaleCount() {
        return wholesaleCount;
    }

    public void setWholesaleCount(BigDecimal wholesaleCount) {
        this.wholesaleCount = wholesaleCount;
    }
}
