package org.haier.shopUpdate.dao;

import java.util.List;
import java.util.Map;

import org.haier.shopUpdate.entity.*;
import org.haier.shopUpdate.entity.ret.ReturnListMain;
import org.haier.shopUpdate.entity.ret2.ReturnMain;
import org.haier.shopUpdate.params.QuerySaleListByPayMethodParams;
import org.haier.shopUpdate.params.QuerySaleListPayMethodBySaleListUniqueParams;
import org.haier.shopUpdate.params.QueryStatisticsByShopParams;
import org.haier.shopUpdate.result.statistics.*;

/**
 * 订单相关接口
 * <AUTHOR>
 */
public interface SaleListDao {


	/**
	 * 根据订单编号查询支付或退款的详情
	 * @param params
	 * @return
	 */
	List<QuerySaleListPayMethodBySaleListUniqueResilt> querySaleListPayMethodBySaleListUnique(QuerySaleListPayMethodBySaleListUniqueParams params);
	Integer querySaleListByPaymethodCountSale(QuerySaleListByPayMethodParams params);
	Integer querySaleListByPaymethodCountRet(QuerySaleListByPayMethodParams params);
	/**
	 * 查询包含指定支付方式的订单列表
	 * @param params
	 * @return
	 */
	List<QuerySaleListByPayMethodEntity> querySaleListByPaymethodSale(QuerySaleListByPayMethodParams params);
	/**
	 * 查询包含指定支付方式的订单列表
	 * @param params
	 * @return
	 */
	List<QuerySaleListByPayMethodEntity> querySaleListByPaymethodRet(QuerySaleListByPayMethodParams params);
	/**
	 * 统计各中支付方式的支付金额
	 * @param params
	 * @return
	 */
	public List<QueryStatisticsSubEntity> queryPayMethodStatisCanyin(QueryStatisticsByShopParams params);
	/**
	 * 统计各中支付方式的支付金额
	 * @param params
	 * @return
	 */
	public List<QueryStatisticsSubEntity> queryPayMethodStatis(QueryStatisticsByShopParams params);
	/**
	 * 统计当前条件下的营业数据
	 * @param params
	 * @return
	 */
	public QueryStatisticsStatis queryStatisticsByShopCanyin(QueryStatisticsByShopParams params);
	/**
	 * 统计当前条件下的营业数据
	 * @param params
	 * @return
	 */
	public QueryStatisticsStatis queryStatisticsByShop(QueryStatisticsByShopParams params);
	/**
	 * 查询订单的支付信息
	 * @param sale_list_unique
	 * @return
	 */
	public Map<String,Object> querySaleListPayMsg(String sale_list_unique);
	/**
	 * 查询店铺消费时返回的百货豆比率
	 * @param shop_unique
	 * @return
	 */
	public Integer queryRewardList(String shop_unique);
	public void modifyPlatcusMsg(Map<String, Object> cusMap);
	/**
	 * 添加入库记录
	 * @param map
	 * @return
	 */
	public Integer addShopStockDetail(Map<String,Object> map);
	/**
	 * 添加商品库存修改记录
	 * @param list
	 * @return
	 */
	public Integer addShopStockList(Map<String,Object> map);
	/**
	 * 
	 * @param list
	 * @return
	 */
	public Integer modifyGoodsMsg(List<Map<String,Object>> list);
	
	/**
	 * 查询退款商品的小规格信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> querySmallGoodsMsg(Map<String,Object> map);
	/**
	 * 修改店铺的百货豆，余额，lkl余额信息
	 * @param map
	 * @return
	 */
	public Integer modifyShopsMsg(Map<String,Object> map);
	/**
	 * 添加会员余额，百货豆变动记录
	 * @param list
	 * @return
	 */
	public Integer addCusChangRecord(List<Map<String,Object>> list);
	/**
	 * 查询退款订单详情
	 * @param map
	 * @return
	 */
	public ReturnMain queryReturnDetail(Map<String,Object> map);
	
	/**
	 * 
	 * @param map
	 * @return
	 */
	public Integer modifyReturnMsg(Map<String,Object> map);
	
	
	
	/**
	 * 查询各种退款方式的金额小计
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryRetStatisticsDetail(Map<String,Object> map);
	/**
	 * 统计退款信息
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryRetStatistics(Map<String,Object> map);
	/**
	 * 查询退款订单数量
	 * @param map
	 * @return
	 */
	public Integer queryRetListsCount(Map<String,Object> map);
	/**
	 * 查询退款列表
	 * @param map
	 * @return
	 */
	public List<ReturnListMain> queryRetLists(Map<String,Object> map);
	/**
	 * 查询店铺退款订单数量（待处理）
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryReturnListCount(Map<String,Object> map);
	
	public Integer modifyReturnListMsg(Map<String,Object> map);
	/**
	 * 添加新的话术
	 * @param map
	 * @return
	 */
	public Integer addNewReturnListMsg(Map<String,Object> map);
	/**
	 * 查询现有话术
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryReturnListMsg(Map<String,Object> map);
	/**
	 * 店铺各类型订单数量查询
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> shopsSaleListCount(Map<String,Object> map);
	
	/**
	 * 订单查询
	 * @param map
	 * @return
	 */
	public List<SaleList> querySaleList(Map<String,Object> map);
	
	/**
	 * 订单详情查询
	 * @param map
	 * @return
	 */
	public SaleList querySaleListDetail(Map<String,Object> map);
	
	/**
	 * 修改订单支付状态或付款状态
	 * @param map
	 * @return
	 * 
	 */
	public int updateSaleList(Map<String,Object> map);
	
	/**
	 * 查询顾客信息，用于百度推送消息
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryManagerMessage(Map<String,Object> map);
	
	/**
	 * 经营助手统计信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> querySaleGoodsCount(Map<String,Object> map);
	/**
	 * 周期内商品销量排行
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryGroupsGoodsSaleMessage(Map<String,Object> map);
	/**
	 * 每天24小时内各个时间段的营业额
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryShopsSaleVolumeByHour(Map<String,Object> map);
	public List<Map<String,Object>> queryShopsSaleVolumeByDay(Map<String,Object> map);
	/**
	 * 商品销量前十
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryGoodsBeforeTen(Map<String,Object> map);

	public void updateSaleListPayDetail(Map<String, Object> map);

	public Map<String, Object> getPeiSongMoney(Map<String, Object> map);

	public Map<String, Object> shopsSaleListCountAll(Map<String, Object> map);

	/**
	 * 农批统计
	 * @param vo
	 * @return
	 */
	public GetSaleMoneyNOEntity getSaleMoneyNP(GetSaleMoneyNPVo vo);

	/**
	 * 根据商品编号获取商品详情
	 * @param vo
	 * @return
	 */
	public List<GetSaleListDetailBySaleListUniqueEntity> getSaleListDetailBySaleListUnique(GetSaleListDetailBySaleListUniqueVo vo);

}
