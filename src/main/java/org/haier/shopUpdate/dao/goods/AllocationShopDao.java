package org.haier.shopUpdate.dao.goods;

import org.apache.ibatis.annotations.Param;
import org.haier.shopUpdate.dao.pojo.QueryAllocationPo;
import org.haier.shopUpdate.entity.goods.AllocationShopEntity;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AllocationShopDao {

    /**
     * 按条件查询
     * @param allocationShop
     * @return
     */
    List<AllocationShopEntity> findList(AllocationShopEntity allocationShop);
    List<AllocationShopEntity> queryListWhere(QueryAllocationPo allocationShop);

    Integer addAllocationShop(AllocationShopEntity allocationShop);

    Integer updateAllocation(AllocationShopEntity allocationShop);

    AllocationShopEntity queryOneById(@Param("id") Integer id);
}