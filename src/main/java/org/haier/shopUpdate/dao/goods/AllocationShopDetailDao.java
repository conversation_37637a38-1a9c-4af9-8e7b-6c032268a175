package org.haier.shopUpdate.dao.goods;

import org.apache.ibatis.annotations.Param;
import org.haier.shopUpdate.entity.goods.AllocationShopDetailEntity;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AllocationShopDetailDao {

    /**
     * 按条件查询
     * @param allocationShopDetail
     * @return
     */
    List<AllocationShopDetailEntity> findList(AllocationShopDetailEntity allocationShopDetail);
    List<AllocationShopDetailEntity> queryListByPurchaseListUnique(@Param("purchaseListUnique") Long purchaseListUnique);

    Integer batchAddAllocationShopDetail(@Param("list") List<AllocationShopDetailEntity> list);

}