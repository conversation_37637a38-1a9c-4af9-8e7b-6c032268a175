package org.haier.shopUpdate.dao;

import java.util.List;
import java.util.Map;

import org.haier.shopUpdate.entity.CusCheckout;

public interface BaiDuFaceDao {
	/**
	 * 根据线上会员编号，查询线上会员人脸信息
	 * @param cus
	 * @return
	 */
	public List<Map<String,String>> queryCusMsgByUnique(String cusUnique,CusCheckout cus);
	
	/**
	 * 添加新的平台会员信息
	 * @return
	 */
	public Integer addNewCustomerMsg(Map<String,Object> map);
}
