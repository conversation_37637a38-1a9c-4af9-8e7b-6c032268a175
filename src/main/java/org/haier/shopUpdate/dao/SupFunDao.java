package org.haier.shopUpdate.dao;

import java.util.List;
import java.util.Map;

import org.haier.shopUpdate.entity.PurListCart;

public interface SupFunDao {
	/**
	 * 商品购物车商品供货商信息查询
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryGiftPriority(Map<String,Object> map);
	

	/**
	 * 删除购物车赠品列表中的赠品
	 * @param map
	 * @return
	 */
	public int deleteCartPresent(Map<String,Object> map);
	
	/**
	 * 删除购物车详情列表中的赠品
	 * @param map
	 * @return
	 */
	public int deleteCartPurPresent(Map<String,Object> map);
	/**
	 * 查询购物车中，有赠品的商品赠品信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryPresentFromSupGoods(Map<String,Object> map);
	/**
	 * 订单满赠查询
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> selectFullCartGoodsMessage(Map<String,Object> map);
	/**
	 * 将商品满赠的赠品添加到购物车中
	 * @param list
	 * @return
	 */
	public int addGiftsToCart(List<Map<String,Object>> list);
	/**
	 * 添加订单满赠商品至订单
	 * @param list
	 * @return
	 */
	public int addGiftToCartDetail(List<Map<String,Object>> list);
	
	/**
	 * 购物车商品信息查询
	 * @param map
	 * @return
	 */
	public List<PurListCart> purCartGoodsSearchNew(Map<String,Object> map);
	/**
	 * 修改购物车商品选中状态
	 * @param map
	 * @return
	 */
	public int modifyCartChecked(Map<String,Object> map);
	/**
	 * 购物车中关于某供货商的选中商品总金额
	 * @param map
	 * @return
	 */
	public Double queryCartGoodsTotal(Map<String,Object> map);
	/**
	 * 查询该金额下，商品的赠品信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryGiftDelete(Map<String,Object> map);
	
	/**
	 * 查询购物车中某商品的商品赠品信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryGiftCountByGoodsChange(Map<String,Object> map);
}
