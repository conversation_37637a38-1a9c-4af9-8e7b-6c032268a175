package org.haier.shopUpdate.dao;

import org.haier.shopUpdate.entity.ShopAggregationCodeUnionEntity;

public interface ShopAggregationCodeUnionDao {

    /**
     * 根据shopUnique查询
     * @param entity
     * @return
     */
    public ShopAggregationCodeUnionEntity queryByShopUnique(ShopAggregationCodeUnionEntity entity);

    /**
     * 保存数据
     * @param entity
     */
    public void insert(ShopAggregationCodeUnionEntity entity);

    /**
     * 根据id更新
     * @param entity
     */
    public void updateById(ShopAggregationCodeUnionEntity entity);
}
