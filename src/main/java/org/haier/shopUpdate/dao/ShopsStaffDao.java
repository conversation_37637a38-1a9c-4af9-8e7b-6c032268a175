package org.haier.shopUpdate.dao;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.haier.shopUpdate.entity.CusRecharge;
import org.haier.shopUpdate.entity.ShopManager;
import org.haier.shopUpdate.entity.ShopStaffEntity;

/**
 * 店铺登录相关接口
 * <AUTHOR>
 */
public interface ShopsStaffDao {
	/**
	 * 查询当前员工的信息
	 * @param staff_id
	 * @return
	 */
	ShopStaffEntity queryStaffByStaffId(Long staff_id);

	/**
	 * 查询店铺员工信息
	 * @param shopUnique
	 * @return
	 */
	List<ShopStaffEntity> queryStaffListByStaffId(String shopUnique);
	/*
	 * 验证是否超级管理员
	 */
	public String checkIsManager(Map<String,Object> map);
	/**
	 * 登录接口，查询管理员信息
	 * <AUTHOR>
	 * @param map
	 * @return
	 */
	public Map<String, Object> staffLoginByAccountPwd(Map<String,Object> map);
	/**
	 * 登录后，查询管理员管理的店铺信息
	 * <AUTHOR>
	 * @param map
	 * @return
	 */
	public List<ShopManager> queryShopManagerPower(Map<String,Object> map);
	
	/**
	 * 更新管理员信息
	 * @param map
	 * @return
	 */
	public int updateShopsStaffMessage(Map<String,Object> map);
	/**
	 * 更新店铺基本信息
	 * @param map
	 * @return
	 */
	public int updateShopsMessage(Map<String,Object> map);
	/**
	 * 
	 * @param map
	 * @return
	 */
	public int editShopsInfo(Map<String, Object> map);
	
	/**
	 * 查询店铺员工信息
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> shopsStaffsSearchByShopUnique(Map<String,Object> map);
	
	/**
	 * 修改员工权限
	 * @param map
	 * @return
	 */
	public int modifyStaffPower(Map<String,Object> map);
	/**
	 * 管理员账户信息查询（用于添加评论）
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryManagerMessage(Map<String,Object> map);
	public int editStafPwd(Map<String, Object> map);
	public int updateRegistrationId(Map<String, Object> map);
	
	public List<Map<String,Object>> queryShopsByManager(Map<String, Object> map);
	public Map<String, Object> queryHandoverRecord(Map<String, Object> map);
	public List<Map<String, Object>> queryPayMethodList(Map<String, Object> map);
	public Map<String, Object> queryRechargeMap(Map<String, Object> map);
	public Long submitFeedBack(HashMap<String, Object> params);
	public void saveFeedBackImage(HashMap<String, Object> map);
	public List<Map<String, Object>> queryStaffList(Map<String, Object> map);
	public List<Map<String, Object>> getFeedBackPhoneList(Map<String, Object> pp);
	public Map<String, Object> getStaffName(HashMap<String, Object> params);
	public int submitShopQualification(HashMap<String, Object> params);
	public Map<String, Object> queryShopQualification(HashMap<String, Object> params);
	public int updateShopQualification(HashMap<String, Object> params);
	/**
	 * 交接班记录查询-会员充值信息统计
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryRechargeRecord(Map<String,Object> map);
	
	public List<CusRecharge> queryCusRechargeRecord(Map<String,Object> map);
	
	/**
	 * 获取店铺当前分类系统
	 * @param shopUnique
	 * @return
	 */
	public Integer getShopNowKindType(String shopUnique);

	List<Map<String, Object>> querySaleList(Map<String, Object> map);

//	List<Map<String, Object>> queryBHDSaleType(Map<String, Object> map);
//
//	List<Map<String, Object>> queryJQPaySaleType(Map<String, Object> map);
//
//	List<Map<String, Object>> queryOnlinePaySaleType(Map<String, Object> map);

    List<Map<String, Object>> queryTotalInfo(Map<String, Object> map);

	List<Map<String, Object>> queryReturnList(Map<String, Object> map);
}
