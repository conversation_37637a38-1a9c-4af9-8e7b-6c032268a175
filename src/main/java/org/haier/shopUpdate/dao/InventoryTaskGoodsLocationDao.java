package org.haier.shopUpdate.dao;

import org.apache.ibatis.annotations.Param;
import org.haier.shopUpdate.entity.InventoryTaskGoodsLocationEntity;

import java.util.List;

public interface InventoryTaskGoodsLocationDao {

    /**
     * 新增
     * @param entity
     * @return
     */
    public int insert(InventoryTaskGoodsLocationEntity entity);

    /**
     * 删除
     * @param id
     * @return
     */
    public int delete(@Param("id") Long id);

    /**
     * 修改
     * @param entity
     * @return
     */
    public int updateById(InventoryTaskGoodsLocationEntity entity);

    /**
     * 根据条件查询
     * @param entity
     * @return
     */
    public List<InventoryTaskGoodsLocationEntity> findByParam(InventoryTaskGoodsLocationEntity entity);

}
