package org.haier.shopUpdate.dao;

import java.util.List;
import java.util.Map;

public interface LoanMoneyDao {
	
	/**
	 * 查询赊销订单分期日志中应还和已经还款的数量
	 * @param map
	 * @return
	 */
	public Map<String,Object> queryOrderFenqiCount(Map<String,Object> map);
	/**
	 * 查询分期日志
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryOrderFenqiList(Map<String,Object> map);
	/**
	 * 恢复店铺赊销额度
	 * @param map
	 * @return
	 */
	public Integer updateSxShopMsg(Map<String,Object> map);
	/**
	 * 批量更新赊销日志
	 * @param list
	 * @return
	 */
	public Integer updateSxFenQiLog(List<Map<String,Object>> list);
	
	/**
	 * 查询店铺待还款的金额
	 * @param map
	 * @return
	 */
	public List<Map<String,Object>> queryWaitReturnLoan(Map<String,Object> map);
	/**
	 * 1统计店铺的赊销未还款信息
	 * @param map
	 * @return
	 */
	Map<String,Object> queryShopLoanMsg(Map<String,Object> map);
	/*
	 * 1查询平台设置的赊销提前还款规则信息
	 */
	Map<String,Object> querySxPolicySet();

	public Map<String, Object> queryIsOpenLoan(Map<String, Object> params);

	public void editOpenLoan(Map<String, Object> map);

	public void addOpenLoan(Map<String, Object> map);

	public void saveReturnMoney(Map<String, Object> params);

	public Map<String, Object> queryReturnMoneyOrder(Map<String, Object> params);

	public void updateReturnMoneyOrderStatus(Map<String, Object> params);

	public void updateShopReturnMoney(Map<String, Object> order);

	public List<Map<String, Object>> queryLoanReturnList(Map<String, Object> map);

	public Integer queryLoanReturnListCount(Map<String, Object> map);

	public List<Map<String, Object>> queryLoanList(Map<String, Object> map);

	public Integer queryLoanListCount(Map<String, Object> map);

	public Map<String, Object> queryAdvanceMoney(Map<String, Object> params);

	public Map<String, Object> queryLoanMoneyRate(Map<String, Object> params);
	public Map<String, Object> queryShopJGId(Map<String, Object> sxShopMsg);
	
}
