package org.haier.shopUpdate.dao;

import org.haier.shopUpdate.entity.SpeechCmdEntity;
import org.haier.shopUpdate.params.speech.QueryCommonSpeechListParams;

import java.util.List;

public interface SpeechCmdDao {

    /**
     * 查詢單條語音命令，
     * @param speechEntity
     * @return
     */
    public SpeechCmdEntity querySpeechCmdEntity(SpeechCmdEntity speechEntity);

    /**
     * 查询店铺常用的命令
     * @param queryCommonSpe
     * @return
     */
    public List<SpeechCmdEntity> queryCommonSpeechList(QueryCommonSpeechListParams queryCommonSpe);
}
