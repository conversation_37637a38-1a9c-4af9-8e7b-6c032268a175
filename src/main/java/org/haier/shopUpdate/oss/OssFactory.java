package org.haier.shopUpdate.oss;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 文件上传Factory
 *
 * <AUTHOR>
 */
@Component
public final class OssFactory {

    private static final Map<String, OssClient> CLIENT_CACHE = new ConcurrentHashMap<>();

    @Resource
    private OssProperties properties;
    private static OssProperties PROPERTIES;

    @PostConstruct
    public void init() {
        PROPERTIES = this.properties;
    }


    private OssFactory() {
    }

    /**
     * 获取默认实例
     */
    public static void instance(OssProperties properties) {
        if (ObjectUtil.isNull(properties)||
                StrUtil.hasEmpty(properties.getEndpoint()
                                ,properties.getRegion()
                        ,properties.getAccessKey()
                        ,properties.getSecretKey()
                        ,properties.getBucketName())){

        }
        setProperties(properties);
        CLIENT_CACHE.put(properties.getConfigKey(), new OssClient(properties));
    }
    /**
     * 根据类型获取实例
     */
    public static OssClient getInstance() {

        OssClient client = CLIENT_CACHE.get(PROPERTIES.getConfigKey());
        if (client == null) {
            CLIENT_CACHE.put(PROPERTIES.getConfigKey(), new OssClient(PROPERTIES));
            client = CLIENT_CACHE.get(PROPERTIES.getConfigKey());
        }
        return client;
    }

    public static void setProperties(OssProperties properties) {
        OssFactory.PROPERTIES = properties;
    }
}
