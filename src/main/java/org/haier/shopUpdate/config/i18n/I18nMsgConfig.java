package org.haier.shopUpdate.config.i18n;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * @ClassName I18nMsgConfig
 * <AUTHOR>
 * @Date 2025/1/21 8:57
 */
@Configuration
public class I18nMsgConfig {
    public static final String REDIS_LOCALE_MESSAGE_KEY = "i18n_message";
    public static final String REDIS_LOCALE_STATIC_KEY = "i18n_static_return_params";
    @Value("${spring.application.name}")
    public String _appName;
    public static String appName;

    @PostConstruct
    public void init () {
        appName = _appName;
    }
}
