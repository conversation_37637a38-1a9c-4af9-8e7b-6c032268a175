package org.haier.shopUpdate.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Data
public class ElectronicConfig {
    //基础路径信息
    @Value("${electronic.baseurl}")
    private String baseurl;
    //同步单个商品信息
    @Value("${electronic.sameSigleGoods}")
    private String sameSigleGoods;
    @Value("${electronic.createOrUpdateGoodsList}")
    private String createOrUpdateGoodsList;
    @Value("${electronic.deleteGoods}")
    private String deleteGoods;
}
