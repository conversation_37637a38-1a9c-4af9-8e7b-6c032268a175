package org.haier.shopUpdate.config;

import org.haier.shopUpdate.constant.PayCenterConstant;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * @ClassName PayCenterConfig
 * <AUTHOR>
 * @Date 2025/2/15 13:51
 */
@Component
public class PayCenterConfig {
    private String payCenterUrl;
    private String projectUrl;

    public static String REFUNDURL;
    public static String QUERYREFUNDRESULTURL;

    @PostConstruct
    public void init() {
        REFUNDURL = this.payCenterUrl + PayCenterConstant.REFUNDURL;
        QUERYREFUNDRESULTURL = this.payCenterUrl + PayCenterConstant.QUERYREFUNDRESULTURL;
    }

    public String getPayCenterUrl() {
        return payCenterUrl;
    }

    public void setPayCenterUrl(String payCenterUrl) {
        this.payCenterUrl = payCenterUrl;
    }

    public String getProjectUrl() {
        return projectUrl;
    }

    public void setProjectUrl(String projectUrl) {
        this.projectUrl = projectUrl;
    }
}
