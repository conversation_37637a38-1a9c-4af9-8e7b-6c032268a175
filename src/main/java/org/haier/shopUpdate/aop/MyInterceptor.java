package org.haier.shopUpdate.aop;

//import java.io.PrintWriter;
import java.util.Enumeration;
import java.util.Map;

import javax.servlet.http.HttpServletMapping;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
//import javax.servlet.http.HttpSession;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.log4j.Logger;
//import org.json.JSONObject;
import org.haier.shopUpdate.util.common.utils.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;


public class MyInterceptor implements HandlerInterceptor{
	private Logger logger=Logger.getLogger(MyInterceptor .class);
	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
			throws Exception {
		//接口每次调用之前，先调用此方法
		response.setContentType("text/json;charset=UTF-8");
		response.setCharacterEncoding("UTF-8");

		//排除validate包下的出入参打印
		JSONObject object = JSON.parseObject(JSON.toJSONString(handler));
		if (!object.isEmpty()){
			String beanType = object.getString("beanType");
			if (StringUtils.isNotBlank(beanType) && beanType.startsWith("org.haier.shopUpdate.controller.validate")) return true;
		}

		//记录日志
		String url=request.getRequestURI();//请求路径
		Enumeration<String> enu=request.getParameterNames();//参数列表
		String msg="请求的参数内容为：";
		while (enu.hasMoreElements()) {
			String string = (String) enu.nextElement();
			msg+=string+"="+request.getParameter(string)+"&";//
		}
		msg=msg.substring(0,msg.length()-1);//去除最后一个&符号
		if(logger.isDebugEnabled()){
			logger.debug("请求路径为："+url);
			logger.debug(msg);
		}
		if(logger.isInfoEnabled()){
			logger.info("请求的路径为："+url);
			logger.info(msg);
		}
		//记录日志结束
		
		
		//验证是否登录
//		HttpSession session=  request.getSession(true);//如果session不存在，则创建新的session
//		if(session.getAttribute("shopUnique")==null){//如果该session未存储
//			//情形一：通过流向回返数据,适用于接口类调用，
////			JSONObject json=new JSONObject();
////			json.put("status", "1");
////			json.put("msg", "登录失效!");
////			
////			PrintWriter out=response.getWriter();
////			out.append(json.toString());
////			out.close();
//			
//			//情形二：不符合条件的，跳转到登录界面  
////			request.getRequestDispatcher("/testPicture.html").forward(request, response); 
////			return false;
//		}
		return true;
	}

	@Override
	public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
			ModelAndView modelAndView) throws Exception {
		
	}

	@Override
	public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
			throws Exception {
	}

}
