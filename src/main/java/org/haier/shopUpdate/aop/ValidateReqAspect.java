package org.haier.shopUpdate.aop;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.log4j.Logger;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.haier.shopUpdate.dao.ShopsDao;
import org.haier.shopUpdate.entity.ShopsEntity;
import org.haier.shopUpdate.util.ShopsResult;
import org.haier.shopUpdate.util.ValidateUtils;
import org.haier.shopUpdate.util.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

@Component
@Aspect
@Slf4j
public class ValidateReqAspect {

    private Logger logger = Logger.getLogger(ValidateReqAspect.class);

    @Autowired
    private ShopsDao shopsDao;

    /**
     * hibernate-validator参数校验
     * @param joinPoint
     * @return
     * @throws Throwable
     */
    @Around(value = "execution(* org.haier.shopUpdate.controller.validate..*.*(..))")
    public ShopsResult validateReq(ProceedingJoinPoint joinPoint) throws Throwable {
        ShopsResult result = new ShopsResult(0,"系统繁忙");
        // 注入上下文对象
        try {

            //获取请求信息
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();

            Object arg = joinPoint.getArgs()[0];
            logger.info(StringUtils.join("请求地址：",request.getRequestURI(),"，请求参数：",JSON.toJSONString(arg)));

            // 校验参数
            ValidateUtils.Result validateResult = ValidateUtils.validate(arg);
            if (validateResult.isHasError()) {
                result.setMsg(validateResult.getMessage());
                logger.info(StringUtils.join("请求地址：",request.getRequestURI(),"，响应结果：",JSON.toJSONString(result)));
                return result;
            }

            //如果存在shopUnique，校验shopUnique是否正确
            JSONObject obj = JSON.parseObject(JSON.toJSONString(arg));
            Long shopUnique = obj.getLong("shopUnique");
            if (shopUnique != null){
                ShopsEntity shops = shopsDao.getByShopUnique(shopUnique);
                if (shops == null){
                    result.setMsg("请输入正确的店铺编号");
                    logger.info(StringUtils.join("请求地址：",request.getRequestURI(),"，响应结果：",JSON.toJSONString(result)));
                    return result;
                }
            }

            result = (ShopsResult) joinPoint.proceed();
            logger.info(StringUtils.join("请求地址：",request.getRequestURI(),"，响应结果：",JSON.toJSONString(result)));
        } catch (Exception e){
            log.error("请求异常",e);
        }

        return result;
    }

}
