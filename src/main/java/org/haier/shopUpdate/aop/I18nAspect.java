package org.haier.shopUpdate.aop;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.haier.shopUpdate.entity.PalmResult;
import org.haier.shopUpdate.util.I18nLanguageMsgUtil;
import org.haier.shopUpdate.util.ShopsResult;
import org.haier.shopUpdate.util.common.CommonResult;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Aspect
@Slf4j
public class I18nAspect {
    @Resource
    private I18nLanguageMsgUtil i18nLanguageMsgUtil;
    //对切点方法进行前置增强，就是在调用切点方法前进行做一些必要的操作，这就成为增强
    @Pointcut("execution(* org.haier.shopUpdate.controller..*.*(..))")
    public void getPointcut() {
    }
    /**
     * 拦截controller层返回的结果，修改msg字段
     *
     * @param point
     * @param result
     */
    @AfterReturning(value = "getPointcut()", returning = "result")
    public void around(JoinPoint point, Object result) {
        try {
            if (result instanceof ShopsResult) {
                ShopsResult r = (ShopsResult) result;
                if (ObjectUtil.isNotEmpty(r.getMsg())) {
                    r.setMsg(i18nLanguageMsgUtil.getMessage(r.getMsg()));
                }
            }
            if (result instanceof CommonResult) {
                CommonResult r = (CommonResult) result;
                if (ObjectUtil.isNotEmpty(r.getMsg())) {
                    r.setMsg(i18nLanguageMsgUtil.getMessage(r.getMsg()));
                }
            }
            if (result instanceof PalmResult) {
                PalmResult r = (PalmResult) result;
                if (ObjectUtil.isNotEmpty(r.getMsg())) {
                    r.setMsg(i18nLanguageMsgUtil.getMessage(r.getMsg()));
                }
            }
        } catch (Exception e) {
            log.error("I18nAspect around error", e);
        }
    }
}
