package org.haier.shopUpdate.aop;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import org.haier.shopUpdate.dao.nova.I18nLanguageDao;
import org.haier.shopUpdate.entity.nova.I18nLanguageEntity;
import org.haier.shopUpdate.redism.RedisCache;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 启动监听器
 *
 * <AUTHOR>
 */
@Service
public class StartupListener implements ApplicationListener<ContextRefreshedEvent> {

    @Resource
    private RedisCache rc;
    @Resource
    private I18nLanguageDao i18nLanguageDao;

    @Override
    public void onApplicationEvent(ContextRefreshedEvent evt) {
        if (evt.getApplicationContext().getParent() == null) {
            createSitemap();
        }
    }

    private void createSitemap() {
        List<I18nLanguageEntity> i18nLanguageList = i18nLanguageDao.queryI18nLanguage();
        if (ObjectUtil.isNotEmpty(i18nLanguageList)) {
            JSONArray ja = new JSONArray();
            for (I18nLanguageEntity i18nLanguageEntity : i18nLanguageList) {
                boolean flag = false;
                if (ObjectUtil.isNotEmpty(ja)) {
                    for (int i = 0; i < ja.size(); i++) {
                        JSONObject jo = ja.getJSONObject(i);
                        if (ObjectUtil.isNotEmpty(jo.get("keyName")) && String.valueOf(jo.get("keyName")).equals(i18nLanguageEntity.getKeyName())) {
                            jo.set(i18nLanguageEntity.getLanguageCode(), i18nLanguageEntity.getContent());
                            flag = true;
                            break;
                        }
                    }
                    if (!flag) {
                        JSONObject jo = new JSONObject();
                        jo.set("keyName", i18nLanguageEntity.getKeyName());
                        jo.set(i18nLanguageEntity.getLanguageCode(), i18nLanguageEntity.getContent());
                        ja.add(jo);
                    }
                } else {
                    JSONObject jo = new JSONObject();
                    jo.set("keyName", i18nLanguageEntity.getKeyName());
                    jo.set(i18nLanguageEntity.getLanguageCode(), i18nLanguageEntity.getContent());
                    ja.add(jo);
                }
            }
            rc.putObject("i18nLanguage", ja, 365*24*60*60);
        }
    }
}