package org.haier.shopUpdate.project;

import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.Serializable;

/**
 * @Description 项目配置
 * @ClassName ProjectConfig
 */
@Component
public class ProjectConfig implements Serializable {
    private static final long serialVersionUID = -2829796057261986645L;
    /**
     * 项目当前环境
     */
    private String projectActive;
    /**
     * 日志推送地址
     */
    private String rometeLogUrl;
    /**
     * 钉钉KEY
     */
    private String dingDingTalkKey;
    /**
     * 项目地址
     */
    private String projectUrl;
    /**
     * 项目地址
     */
    public static String PROJECT_URL;
    /**
     * 百货豆使用说明
     */
    private String beanInstructions;
    /**
     * 百货豆使用说明
     */
    public static String BEAN_INSTRUCTION_URL;

    /**
     * 文件访问地址
     */
    private String fileUrl;

    public static String FILE_URL;

    private String mqtt_host;
    private String mqtt_topicmain;
    private String mqtt_topic;
    private String mqtt_clientid;
    private String mqtt_username;
    private String mqtt_password;
    public static String MQTT_HOST;
    public static String MQTT_TOPICMAIN;
    public static String MQTT_TOPIC;
    public static String MQTT_CLIENTID;
    public static String MQTT_USERNAME;
    public static String MQTT_PASSWORD;

    @PostConstruct
    public void init() {
        PROJECT_URL = this.projectUrl;
        BEAN_INSTRUCTION_URL = this.beanInstructions;
        FILE_URL = this.fileUrl;
        MQTT_HOST = this.mqtt_host;
        MQTT_TOPICMAIN = this.mqtt_topicmain;
        MQTT_TOPIC = this.mqtt_topic;
        MQTT_CLIENTID = this.mqtt_clientid;
        MQTT_USERNAME = this.mqtt_username;
        MQTT_PASSWORD = this.mqtt_password;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public String getProjectActive() {
        return projectActive;
    }

    public void setProjectActive(String projectActive) {
        this.projectActive = projectActive;
    }

    public String getRometeLogUrl() {
        return rometeLogUrl;
    }

    public void setRometeLogUrl(String rometeLogUrl) {
        this.rometeLogUrl = rometeLogUrl;
    }

    public String getDingDingTalkKey() {
        return dingDingTalkKey;
    }

    public void setDingDingTalkKey(String dingDingTalkKey) {
        this.dingDingTalkKey = dingDingTalkKey;
    }

    public String getProjectUrl() {
        return projectUrl;
    }

    public void setProjectUrl(String projectUrl) {
        this.projectUrl = projectUrl;
    }

    public String getBeanInstructions() {
        return beanInstructions;
    }

    public void setBeanInstructions(String beanInstructions) {
        this.beanInstructions = beanInstructions;
    }

    public String getMqtt_host() {
        return mqtt_host;
    }

    public void setMqtt_host(String mqtt_host) {
        this.mqtt_host = mqtt_host;
    }

    public String getMqtt_topic() {
        return mqtt_topic;
    }

    public void setMqtt_topic(String mqtt_topic) {
        this.mqtt_topic = mqtt_topic;
    }

    public String getMqtt_clientid() {
        return mqtt_clientid;
    }

    public void setMqtt_clientid(String mqtt_clientid) {
        this.mqtt_clientid = mqtt_clientid;
    }

    public String getMqtt_topicmain() {
        return mqtt_topicmain;
    }

    public void setMqtt_topicmain(String mqtt_topicmain) {
        this.mqtt_topicmain = mqtt_topicmain;
    }

    public String getMqtt_username() {
        return mqtt_username;
    }

    public void setMqtt_username(String mqtt_username) {
        this.mqtt_username = mqtt_username;
    }

    public String getMqtt_password() {
        return mqtt_password;
    }

    public void setMqtt_password(String mqtt_password) {
        this.mqtt_password = mqtt_password;
    }
}
