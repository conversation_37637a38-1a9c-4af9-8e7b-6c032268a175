package org.haier.shopUpdate.dto;

import java.math.BigDecimal;

public class QueryGoodsByGoodsBarcodeDto {
    private Integer goodsId;
    private String goodsBarcode;
    private String goodsName;
    private String goodsPicturePath;
    private Long goodsKindUnique;
    /**
     * 商品数量
     */
    private Integer goodsCount;
    /**
     * 进价
     */
    private BigDecimal goodsInPrice;
    /**
     * 售价
     */
    private BigDecimal goodsSalePrice;
    /**
     * 网单价
     */
    private BigDecimal goodsWebSalePrice;
    /**
     * 会员价
     */
    private BigDecimal goodsCusPrice;
    /**
     * 建议价
     */
    private BigDecimal goodsCostPrice;
    /**
     * 调整次数
     */
    private Integer adjustmentCount;

    public Integer getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Integer goodsId) {
        this.goodsId = goodsId;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public Long getGoodsKindUnique() {
        return goodsKindUnique;
    }

    public void setGoodsKindUnique(Long goodsKindUnique) {
        this.goodsKindUnique = goodsKindUnique;
    }

    public Integer getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(Integer goodsCount) {
        this.goodsCount = goodsCount;
    }

    public BigDecimal getGoodsInPrice() {
        return goodsInPrice;
    }

    public void setGoodsInPrice(BigDecimal goodsInPrice) {
        this.goodsInPrice = goodsInPrice;
    }

    public BigDecimal getGoodsSalePrice() {
        return goodsSalePrice;
    }

    public void setGoodsSalePrice(BigDecimal goodsSalePrice) {
        this.goodsSalePrice = goodsSalePrice;
    }

    public BigDecimal getGoodsWebSalePrice() {
        return goodsWebSalePrice;
    }

    public void setGoodsWebSalePrice(BigDecimal goodsWebSalePrice) {
        this.goodsWebSalePrice = goodsWebSalePrice;
    }

    public BigDecimal getGoodsCusPrice() {
        return goodsCusPrice;
    }

    public void setGoodsCusPrice(BigDecimal goodsCusPrice) {
        this.goodsCusPrice = goodsCusPrice;
    }

    public BigDecimal getGoodsCostPrice() {
        return goodsCostPrice;
    }

    public void setGoodsCostPrice(BigDecimal goodsCostPrice) {
        this.goodsCostPrice = goodsCostPrice;
    }

    public String getGoodsPicturePath() {
        return goodsPicturePath;
    }

    public void setGoodsPicturePath(String goodsPicturePath) {
        this.goodsPicturePath = goodsPicturePath;
    }

    public Integer getAdjustmentCount() {
        return adjustmentCount;
    }

    public void setAdjustmentCount(Integer adjustmentCount) {
        this.adjustmentCount = adjustmentCount;
    }
}
