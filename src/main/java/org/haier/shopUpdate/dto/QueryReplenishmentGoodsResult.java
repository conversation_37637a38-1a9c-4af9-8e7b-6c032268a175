package org.haier.shopUpdate.dto;

import java.io.Serializable;
import java.math.BigDecimal;

public class QueryReplenishmentGoodsResult implements Serializable {
    private static final long serialVersionUID = 5514347406413682778L;
    private String goodsId;
    private String goodsCode;
    private String goodsName;
    /**
     * 建议价
     */
    private BigDecimal goodsCost;

    public String getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId;
    }

    public String getGoodsCode() {
        return goodsCode;
    }

    public void setGoodsCode(String goodsCode) {
        this.goodsCode = goodsCode;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public BigDecimal getGoodsCost() {
        return goodsCost;
    }

    public void setGoodsCost(BigDecimal goodsCost) {
        this.goodsCost = goodsCost;
    }
}
