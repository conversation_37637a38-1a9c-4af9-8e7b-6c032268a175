package org.haier.shopUpdate.dto;

import java.util.List;

public class GoodsKindByShopUniqueDto {
    private Integer goodsKindId;
    private Long shopUnique;
    private Long goodsKindUnique;
    private Long goodsKindParunique;
    private String goodsKindName;
    private String goodsKindPicture;
    private String goodsKindOrder;

    private List<GoodsKindByShopUniqueDto> goodsKindList;
    private List<QueryGoodsByGoodsBarcodeDto> goodsList;
    public Integer getGoodsKindId() {
        return goodsKindId;
    }

    public void setGoodsKindId(Integer goodsKindId) {
        this.goodsKindId = goodsKindId;
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public Long getGoodsKindUnique() {
        return goodsKindUnique;
    }

    public void setGoodsKindUnique(Long goodsKindUnique) {
        this.goodsKindUnique = goodsKindUnique;
    }

    public Long getGoodsKindParunique() {
        return goodsKindParunique;
    }

    public void setGoodsKindParunique(Long goodsKindParunique) {
        this.goodsKindParunique = goodsKindParunique;
    }

    public String getGoodsKindName() {
        return goodsKindName;
    }

    public void setGoodsKindName(String goodsKindName) {
        this.goodsKindName = goodsKindName;
    }

    public List<GoodsKindByShopUniqueDto> getGoodsKindList() {
        return goodsKindList;
    }

    public void setGoodsKindList(List<GoodsKindByShopUniqueDto> goodsKindList) {
        this.goodsKindList = goodsKindList;
    }

    public List<QueryGoodsByGoodsBarcodeDto> getGoodsList() {
        return goodsList;
    }

    public void setGoodsList(List<QueryGoodsByGoodsBarcodeDto> goodsList) {
        this.goodsList = goodsList;
    }

    public String getGoodsKindPicture() {
        return goodsKindPicture;
    }

    public void setGoodsKindPicture(String goodsKindPicture) {
        this.goodsKindPicture = goodsKindPicture;
    }

    public String getGoodsKindOrder() {
        return goodsKindOrder;
    }

    public void setGoodsKindOrder(String goodsKindOrder) {
        this.goodsKindOrder = goodsKindOrder;
    }
}
