package org.haier.shopUpdate.dto;

import org.haier.shopUpdate.enums.StockResourceEnums;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

public class AddGoodsDto {
    /**
     * 条形码
     */
    private Long shopsUnique;
    /**
     * 条形码
     */
    private String goodsBarcode;
    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 别名
     */
    private String goodsAlias;
    /**
     * 进价
     */
    private BigDecimal goodsInPrice;
    /**
     * 售价
     */
    private BigDecimal goodsSalePrice;
    /**
     * 会员价
     */
    private BigDecimal goodsCusPrice;
    /**
     * 线上销售价
     */
    private BigDecimal goodsWebSalePrice;
    /**
     * 包含子商品数量
     */
    private String goodsContain;
    /**
     * 单位
     */
    private String goodsUnit;
    /**
     * 折扣
     */
    private String goodsDiscount;

    /**
     * 规格
     */
    private String goodsStandard;
    /**
     * 数量
     */
    private BigDecimal goodsCount;
    /**
     * 图片
     */
    private String goodsPicturePath;
    /**
     * 称重商品类型：0、按件；1、按重量
     */
    private String goodsScaleType;

    /**
     * 商品分类
     */
    private String goodsKindUnique;

    /**
     * 品牌
     */
    private String goodsBrand;

    /**
     * 保质期
     */
    private String goodsLife;
    /**
     * 积分
     */
    private String goodsPoints;
    /**
     * 地址
     */
    private String goodsAddress;
    /**
     * '1:已同步；2：未同步'
     */
    private String sameType;

    /**
     * 批发价
     */
    private BigDecimal wholesalePrice;
    /**
     * 批发价标识
     */
    private Integer wholesalePriceFlg;
    /**
     * 批发数量
     */
    private BigDecimal wholesaleCount;
    /**
     * 库存来源
     * @see org.haier.shopUpdate.enums.StockResourceEnums
     */
    @NotNull
    private StockResourceEnums stockResource;
    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsAlias() {
        return goodsAlias;
    }

    public void setGoodsAlias(String goodsAlias) {
        this.goodsAlias = goodsAlias;
    }

    public BigDecimal getGoodsInPrice() {
        return goodsInPrice;
    }

    public void setGoodsInPrice(BigDecimal goodsInPrice) {
        this.goodsInPrice = goodsInPrice;
    }

    public BigDecimal getGoodsSalePrice() {
        return goodsSalePrice;
    }

    public void setGoodsSalePrice(BigDecimal goodsSalePrice) {
        this.goodsSalePrice = goodsSalePrice;
    }

    public BigDecimal getGoodsCusPrice() {
        return goodsCusPrice;
    }

    public void setGoodsCusPrice(BigDecimal goodsCusPrice) {
        this.goodsCusPrice = goodsCusPrice;
    }

    public String getGoodsContain() {
        return goodsContain;
    }

    public void setGoodsContain(String goodsContain) {
        this.goodsContain = goodsContain;
    }

    public String getGoodsUnit() {
        return goodsUnit;
    }

    public void setGoodsUnit(String goodsUnit) {
        this.goodsUnit = goodsUnit;
    }

    public String getGoodsDiscount() {
        return goodsDiscount;
    }

    public void setGoodsDiscount(String goodsDiscount) {
        this.goodsDiscount = goodsDiscount;
    }

    public String getGoodsStandard() {
        return goodsStandard;
    }

    public void setGoodsStandard(String goodsStandard) {
        this.goodsStandard = goodsStandard;
    }

    public BigDecimal getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(BigDecimal goodsCount) {
        this.goodsCount = goodsCount;
    }

    public String getGoodsPicturePath() {
        return goodsPicturePath;
    }

    public void setGoodsPicturePath(String goodsPicturePath) {
        this.goodsPicturePath = goodsPicturePath;
    }

    public String getGoodsScaleType() {
        return goodsScaleType;
    }

    public void setGoodsScaleType(String goodsScaleType) {
        this.goodsScaleType = goodsScaleType;
    }

    public String getGoodsKindUnique() {
        return goodsKindUnique;
    }

    public void setGoodsKindUnique(String goodsKindUnique) {
        this.goodsKindUnique = goodsKindUnique;
    }

    public String getGoodsBrand() {
        return goodsBrand;
    }

    public void setGoodsBrand(String goodsBrand) {
        this.goodsBrand = goodsBrand;
    }

    public String getGoodsLife() {
        return goodsLife;
    }

    public void setGoodsLife(String goodsLife) {
        this.goodsLife = goodsLife;
    }

    public String getGoodsPoints() {
        return goodsPoints;
    }

    public void setGoodsPoints(String goodsPoints) {
        this.goodsPoints = goodsPoints;
    }

    public String getGoodsAddress() {
        return goodsAddress;
    }

    public void setGoodsAddress(String goodsAddress) {
        this.goodsAddress = goodsAddress;
    }

    public String getSameType() {
        return sameType;
    }

    public void setSameType(String sameType) {
        this.sameType = sameType;
    }

    public Long getShopsUnique() {
        return shopsUnique;
    }

    public void setShopsUnique(Long shopsUnique) {
        this.shopsUnique = shopsUnique;
    }

    public StockResourceEnums getStockResource() {
        return stockResource;
    }

    public void setStockResource(StockResourceEnums stockResource) {
        this.stockResource = stockResource;
    }

    public BigDecimal getGoodsWebSalePrice() {
        return goodsWebSalePrice;
    }

    public void setGoodsWebSalePrice(BigDecimal goodsWebSalePrice) {
        this.goodsWebSalePrice = goodsWebSalePrice;
    }

    public BigDecimal getWholesalePrice() {
        return wholesalePrice;
    }

    public void setWholesalePrice(BigDecimal wholesalePrice) {
        this.wholesalePrice = wholesalePrice;
    }

    public Integer getWholesalePriceFlg() {
        return wholesalePriceFlg;
    }

    public void setWholesalePriceFlg(Integer wholesalePriceFlg) {
        this.wholesalePriceFlg = wholesalePriceFlg;
    }

    public BigDecimal getWholesaleCount() {
        return wholesaleCount;
    }

    public void setWholesaleCount(BigDecimal wholesaleCount) {
        this.wholesaleCount = wholesaleCount;
    }
}
