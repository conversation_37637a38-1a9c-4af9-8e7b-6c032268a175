package org.haier.shopUpdate.dto;

import org.haier.shopUpdate.enums.StockOriginEnums;
import org.haier.shopUpdate.enums.StockResourceEnums;
import org.haier.shopUpdate.enums.StockTypeEnums;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

public class AddGoodsStockDto {
    /**
     * 店铺id
     */
    @NotNull
    private Long shopUnique;
    /**
     * 条形码
     */
    @NotEmpty
    private String goodsBarcode;
    /**
     * 修改数量
     */
    @NotNull
    private BigDecimal changeCount;
    /**
     * 出入库类型
     * @see org.haier.shopUpdate.enums.StockTypeEnums
     */
    @NotNull
    private StockTypeEnums stockType;
    /**
     * 出入库价格
     */
    @NotNull
    private BigDecimal stockPrice;
    /**
     * 操作来源
     * @see org.haier.shopUpdate.enums.StockOriginEnums
     */
    @NotNull
    private StockOriginEnums stockOrigin;
    /**
     * 出入库类型
     * @see org.haier.shopUpdate.enums.StockResourceEnums
     */
    @NotNull
    private StockResourceEnums stockResource;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 员工id
     */
    private String staffId;

    /**
     * 出入库说明
     */
    private String reason;
    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public BigDecimal getChangeCount() {
        return changeCount;
    }

    public void setChangeCount(BigDecimal changeCount) {
        this.changeCount = changeCount;
    }

    public StockTypeEnums getStockType() {
        return stockType;
    }

    public void setStockType(StockTypeEnums stockType) {
        this.stockType = stockType;
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public StockOriginEnums getStockOrigin() {
        return stockOrigin;
    }

    public void setStockOrigin(StockOriginEnums stockOrigin) {
        this.stockOrigin = stockOrigin;
    }

    public String getStaffId() {
        return staffId;
    }

    public void setStaffId(String staffId) {
        this.staffId = staffId;
    }

    public BigDecimal getStockPrice() {
        return stockPrice;
    }

    public void setStockPrice(BigDecimal stockPrice) {
        this.stockPrice = stockPrice;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public StockResourceEnums getStockResource() {
        return stockResource;
    }

    public void setStockResource(StockResourceEnums stockResource) {
        this.stockResource = stockResource;
    }
}
