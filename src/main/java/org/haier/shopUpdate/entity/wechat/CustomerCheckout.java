package org.haier.shopUpdate.entity.wechat;

import java.io.Serializable;

/**
* @author: 作者:王恩龙
* @version: 2020年11月14日 上午9:56:06
*
*/
public class CustomerCheckout implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private String cus_phone;
	private String cus_weixin;
	private String cus_name;
	private String cus_birthday;
	private Integer cus_sex;
	public String getCus_phone() {
		return cus_phone;
	}
	public void setCus_phone(String cus_phone) {
		this.cus_phone = cus_phone;
	}
	public String getCus_weixin() {
		return cus_weixin;
	}
	public void setCus_weixin(String cus_weixin) {
		this.cus_weixin = cus_weixin;
	}
	public String getCus_name() {
		return cus_name;
	}
	public void setCus_name(String cus_name) {
		this.cus_name = cus_name;
	}
	public String getCus_birthday() {
		return cus_birthday;
	}
	public void setCus_birthday(String cus_birthday) {
		this.cus_birthday = cus_birthday;
	}
	public Integer getCus_sex() {
		return cus_sex;
	}
	public void setCus_sex(Integer cus_sex) {
		this.cus_sex = cus_sex;
	}
	@Override
	public String toString() {
		return "CustomerCheckout [cus_phone=" + cus_phone + ", cus_weixin=" + cus_weixin + ", cus_name=" + cus_name
				+ ", cus_birthday=" + cus_birthday + ", cus_sex=" + cus_sex + "]";
	}
}
