package org.haier.shopUpdate.entity;

import java.util.List;

/**
 * 查询店铺所有商品分类信息结果封装实体类
 * <AUTHOR>
 */
public class ShopsGroupGoodsKinds {
	private String groupId;
	//商品一级分类名称
	private String groupName;
	private String shopUnique;
	//商品一级分类编号
	private String groupUnique;
	/**
	 * 父级分类编号
	 */
	private String parentUnique;
	//分类别名
	private String groupAlias;
	//可编辑状态
	private Integer editType;

	//分类图标ID
	private String kindIconId;
	//分类图标路径
	private String kindIcon;
	private Integer valid_type;
	/**
	 * 子集
	 */
	//private List<ShopsGroupGoodsKinds> kindDetail;
	//商品二级分类详情
	private List<ShopsKindGoodsKinds> kindDetail;
	
	
	public Integer getValid_type() {
		return valid_type;
	}
	public void setValid_type(Integer valid_type) {
		this.valid_type = valid_type;
	}
	public String getShopUnique() {
		return shopUnique;
	}
	public void setShopUnique(String shopUnique) {
		this.shopUnique = shopUnique;
	}

	public String getParentUnique() {
		return parentUnique;
	}
	public void setParentUnique(String parentUnique) {
		this.parentUnique = parentUnique;
	}
	public String getGroupId() {
		return groupId;
	}
	public void setGroupId(String groupId) {
		this.groupId = groupId;
	}
	public String getGroupName() {
		return groupName;
	}
	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}
	public String getGroupUnique() {
		return groupUnique;
	}
	public void setGroupUnique(String groupUnique) {
		this.groupUnique = groupUnique;
	}
	public List<ShopsKindGoodsKinds> getKindDetail() {
		return kindDetail;
	}
	public void setKindDetail(List<ShopsKindGoodsKinds> kindDetail) {
		this.kindDetail = kindDetail;
	}
	public String getGroupAlias() {
		return groupAlias;
	}
	public void setGroupAlias(String groupAlias) {
		this.groupAlias = groupAlias;
	}
	public Integer getEditType() {
		return editType;
	}
	public void setEditType(Integer editType) {
		this.editType = editType;
	}
	public String getKindIconId() {
		return kindIconId;
	}
	public void setKindIconId(String kindIconId) {
		this.kindIconId = kindIconId;
	}
	public String getKindIcon() {
		return kindIcon;
	}
	public void setKindIcon(String kindIcon) {
		this.kindIcon = kindIcon;
	}
	@Override
	public String toString() {
		return "ShopsGroupGoodsKinds [groupId=" + groupId + ", groupName=" + groupName + ", groupUnique=" + groupUnique
				+ ", groupAlias=" + groupAlias + ", editType=" + editType + ", kindDetail=" + kindDetail + ", kindIconId=" + kindIconId + ", kindIcon=" + kindIcon + "]";
	}
	
}
