package org.haier.shopUpdate.entity;

import java.util.List;

public class Inventory {
	//盘点订单编号
	private Integer id;
	//店铺编号
	private Long shopUnique;
	//盘点订单开始日期
	private String startTime;
	//盘点订单提交日期；
	private String endTime;
	//盘点订单员工名称
	private String staffName;
	//盘点订单员工编号
	private Integer staffId;
	//状态
	private Integer inventoryType;
	//实际的商品数量
	private double  inventoryCount;
	//盘点盈亏额
	private Double inventoryAmount;
	//盘点总数量（实际数量）
	private Double inventoryAcount;
	//商品种类
	private Integer goodsCount;
	
	private String remarks;
	//盘点订单详情
	private List<InventoryDetail> listDetail;


	public Integer getGoodsCount() {
		return goodsCount;
	}
	public void setGoodsCount(Integer goodsCount) {
		this.goodsCount = goodsCount;
	}
	public String getRemarks() {
		return remarks;
	}
	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}
	public Integer getInventoryType() {
		return inventoryType;
	}
	public void setInventoryType(Integer inventoryType) {
		this.inventoryType = inventoryType;
	}
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getStartTime() {
		return startTime;
	}
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	public String getStaffName() {
		return staffName;
	}
	public void setStaffName(String staffName) {
		this.staffName = staffName;
	}
	public Integer getStaffId() {
		return staffId;
	}
	public void setStaffId(Integer staffId) {
		this.staffId = staffId;
	}
	public double getInventoryCount() {
		return inventoryCount;
	}
	public void setInventoryCount(double inventoryCount) {
		this.inventoryCount = inventoryCount;
	}
	public Double getInventoryAcount() {
		return inventoryAcount;
	}
	public void setInventoryAcount(Double inventoryAcount) {
		this.inventoryAcount = inventoryAcount;
	}
	public Double getInventoryAmount() {
		return inventoryAmount;
	}
	public void setInventory_amount(Double inventoryAmount) {
		this.inventoryAmount = inventoryAmount;
	}
	public List<InventoryDetail> getListDetail() {
		return listDetail;
	}
	public void setListDetail(List<InventoryDetail> listDetail) {
		this.listDetail = listDetail;
	}
	public void setInventoryAmount(Double inventoryAmount) {
		this.inventoryAmount = inventoryAmount;
	}
	public Long getShopUnique() {
		return shopUnique;
	}
	public void setShopUnique(Long shopUnique) {
		this.shopUnique = shopUnique;
	}				
}
