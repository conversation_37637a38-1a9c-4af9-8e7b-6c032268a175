package org.haier.shopUpdate.entity;

import java.math.BigDecimal;

/**
 * @description 盘点任务统计数据
 */
public class InventoryTaskStatisticsEntity {
    /**
    * id
    */
    private Long id;

    /**
    * 任务单id
    */
    private Long taskId;

    /**
    * 商品条码
    */
    private String goodsBarcode;

    /**
    * 商品名称
    */
    private String goodsName;

    /**
    * 盘点前库存
    */
    private BigDecimal preStock;

    /**
    * 盘点数
    */
    private BigDecimal inventoryCount;

    /**
    * 盈亏数
    */
    private BigDecimal diffCount;

    /**
    * 盈亏金额
    */
    private BigDecimal diffMoney;

    /**
    * 商品进价
    */
    private BigDecimal goodsInPrice;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public BigDecimal getPreStock() {
        return preStock;
    }

    public void setPreStock(BigDecimal preStock) {
        this.preStock = preStock;
    }

    public BigDecimal getInventoryCount() {
        return inventoryCount;
    }

    public void setInventoryCount(BigDecimal inventoryCount) {
        this.inventoryCount = inventoryCount;
    }

    public BigDecimal getDiffCount() {
        return diffCount;
    }

    public void setDiffCount(BigDecimal diffCount) {
        this.diffCount = diffCount;
    }

    public BigDecimal getDiffMoney() {
        return diffMoney;
    }

    public void setDiffMoney(BigDecimal diffMoney) {
        this.diffMoney = diffMoney;
    }

    public BigDecimal getGoodsInPrice() {
        return goodsInPrice;
    }

    public void setGoodsInPrice(BigDecimal goodsInPrice) {
        this.goodsInPrice = goodsInPrice;
    }
}