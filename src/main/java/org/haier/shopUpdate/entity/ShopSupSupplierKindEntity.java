package org.haier.shopUpdate.entity;

import java.sql.Timestamp;

public class ShopSupSupplierKindEntity {
    private Long id;
    private Long shopUnique;
    private String supplierKindUnique;
    private String supplierKindName;
    private Integer orderSort;
    private Long createId;
    private String createBy;
    private Timestamp createTime;
    private Long modifyId;
    private String modifyBy;
    private Timestamp modifyTime;
    private Integer delFlag;
    private String supplierKindParunique;
    private Integer supplierKindLevel;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getSupplierKindUnique() {
        return supplierKindUnique;
    }

    public void setSupplierKindUnique(String supplierKindUnique) {
        this.supplierKindUnique = supplierKindUnique;
    }

    public String getSupplierKindName() {
        return supplierKindName;
    }

    public void setSupplierKindName(String supplierKindName) {
        this.supplierKindName = supplierKindName;
    }

    public Integer getOrderSort() {
        return orderSort;
    }

    public void setOrderSort(Integer orderSort) {
        this.orderSort = orderSort;
    }

    public Long getCreateId() {
        return createId;
    }

    public void setCreateId(Long createId) {
        this.createId = createId;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Long getModifyId() {
        return modifyId;
    }

    public void setModifyId(Long modifyId) {
        this.modifyId = modifyId;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public Timestamp getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Timestamp modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public String getSupplierKindParunique() {
        return supplierKindParunique;
    }

    public void setSupplierKindParunique(String supplierKindParunique) {
        this.supplierKindParunique = supplierKindParunique;
    }

    public Integer getSupplierKindLevel() {
        return supplierKindLevel;
    }

    public void setSupplierKindLevel(Integer supplierKindLevel) {
        this.supplierKindLevel = supplierKindLevel;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ShopSupSupplierKindEntity that = (ShopSupSupplierKindEntity) o;

        if (id != that.id) return false;
        if (shopUnique != null ? !shopUnique.equals(that.shopUnique) : that.shopUnique != null)
            return false;
        if (supplierKindUnique != null ? !supplierKindUnique.equals(that.supplierKindUnique) : that.supplierKindUnique != null)
            return false;
        if (supplierKindName != null ? !supplierKindName.equals(that.supplierKindName) : that.supplierKindName != null)
            return false;
        if (orderSort != null ? !orderSort.equals(that.orderSort) : that.orderSort != null) return false;
        if (createId != null ? !createId.equals(that.createId) : that.createId != null) return false;
        if (createBy != null ? !createBy.equals(that.createBy) : that.createBy != null) return false;
        if (createTime != null ? !createTime.equals(that.createTime) : that.createTime != null) return false;
        if (modifyId != null ? !modifyId.equals(that.modifyId) : that.modifyId != null) return false;
        if (modifyBy != null ? !modifyBy.equals(that.modifyBy) : that.modifyBy != null) return false;
        if (modifyTime != null ? !modifyTime.equals(that.modifyTime) : that.modifyTime != null) return false;
        if (delFlag != null ? !delFlag.equals(that.delFlag) : that.delFlag != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = (int) (id ^ (id >>> 32));
        result = 31 * result + (shopUnique != null ? shopUnique.hashCode() : 0);
        result = 31 * result + (supplierKindUnique != null ? supplierKindUnique.hashCode() : 0);
        result = 31 * result + (supplierKindName != null ? supplierKindName.hashCode() : 0);
        result = 31 * result + (orderSort != null ? orderSort.hashCode() : 0);
        result = 31 * result + (createId != null ? createId.hashCode() : 0);
        result = 31 * result + (createBy != null ? createBy.hashCode() : 0);
        result = 31 * result + (createTime != null ? createTime.hashCode() : 0);
        result = 31 * result + (modifyId != null ? modifyId.hashCode() : 0);
        result = 31 * result + (modifyBy != null ? modifyBy.hashCode() : 0);
        result = 31 * result + (modifyTime != null ? modifyTime.hashCode() : 0);
        result = 31 * result + (delFlag != null ? delFlag.hashCode() : 0);
        return result;
    }
}
