package org.haier.shopUpdate.entity;

public class ShopCard {
	private Integer cardId;//ID
	private String shopUnique;//所属店铺编号
	private Integer bank;//所属银行编号
	private String bankCard;//银行卡号；
	private String bankName;//银行名称
	private String bankPhone;//绑定手机号
	private String creatTime;//绑定时间
	private Integer defaultType;//是否默认银行卡；1、默认；2、非默认
	private String bankImg;//银行图标信息
	private Integer validType;//是否有效；1、有效；2、无效
	private String bankLogo;//银行图标
	private String cardName;//持卡人姓名
	private String remarks;
	private String cardType;//银行卡类型：CD 储值  CC 信用
	
	
	public String getCardType() {
		return cardType;
	}
	public void setCardType(String cardType) {
		this.cardType = cardType;
	}
	public String getRemarks() {
		return remarks;
	}
	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}
	public Integer getCardId() {
		return cardId;
	}
	public void setCardId(Integer cardId) {
		this.cardId = cardId;
	}
	public String getShopUnique() {
		return shopUnique;
	}
	public void setShopUnique(String shopUnique) {
		this.shopUnique = shopUnique;
	}
	public Integer getBank() {
		return bank;
	}
	public void setBank(Integer bank) {
		this.bank = bank;
	}
	public String getBankCard() {
		return bankCard;
	}
	public void setBankCard(String bankCard) {
		this.bankCard = bankCard;
	}
	public String getBankName() {
		return bankName;
	}
	public void setBankName(String bankName) {
		this.bankName = bankName;
	}
	public String getBankPhone() {
		return bankPhone;
	}
	public void setBankPhone(String bankPhone) {
		this.bankPhone = bankPhone;
	}
	public String getCreatTime() {
		return creatTime;
	}
	public void setCreatTime(String creatTime) {
		this.creatTime = creatTime;
	}
	public Integer getDefaultType() {
		return defaultType;
	}
	public void setDefaultType(Integer defaultType) {
		this.defaultType = defaultType;
	}
	public String getBankImg() {
		return bankImg;
	}
	public void setBankImg(String bankImg) {
		this.bankImg = bankImg;
	}
	public Integer getValidType() {
		return validType;
	}
	public void setValidType(Integer validType) {
		this.validType = validType;
	}
	public String getBankLogo() {
		return bankLogo;
	}
	public void setBankLogo(String bankLogo) {
		this.bankLogo = bankLogo;
	}
	public String getCardName() {
		return cardName;
	}
	public void setCardName(String cardName) {
		this.cardName = cardName;
	}
	@Override
	public String toString() {
		return "ShopCard [cardId=" + cardId + ", shopUnique=" + shopUnique + ", bank=" + bank + ", bankCard=" + bankCard
				+ ", bankName=" + bankName + ", bankPhone=" + bankPhone + ", creatTime=" + creatTime + ", defaultType="
				+ defaultType + ", bankImg=" + bankImg + ", validType=" + validType + ", bankLogo=" + bankLogo
				+ ", cardName=" + cardName + "]";
	}
}
