package org.haier.shopUpdate.entity;

public class InvenGoodsDetail {
	//盘货单编号
	private Integer id;
	//盘货单创建时间
	private String startTime;
	//盘货结束时间
	private String endTime;
	//盘前数
	private Double goodsCount;
	//盘点数
	private Double inventoryCount;
	//盘亏数
	private Double invenCount;
	//盘亏额
	private Double profitLoss;
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getStartTime() {
		return startTime;
	}
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	public Double getGoodsCount() {
		return goodsCount;
	}
	public void setGoodsCount(Double goodsCount) {
		this.goodsCount = goodsCount;
	}
	public Double getInventoryCount() {
		return inventoryCount;
	}
	public void setInventoryCount(Double inventoryCount) {
		this.inventoryCount = inventoryCount;
	}
	public Double getInvenCount() {
		return invenCount;
	}
	public void setInvenCount(Double invenCount) {
		this.invenCount = invenCount;
	}
	public Double getProfitLoss() {
		return profitLoss;
	}
	public void setProfitLoss(Double profitLoss) {
		this.profitLoss = profitLoss;
	}
}
