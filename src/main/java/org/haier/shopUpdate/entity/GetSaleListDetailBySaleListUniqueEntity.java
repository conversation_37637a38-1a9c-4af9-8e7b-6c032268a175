package org.haier.shopUpdate.entity;

import java.math.BigDecimal;

public class GetSaleListDetailBySaleListUniqueEntity {
    private Long goodsId; //商品id

    private String goodsBarcode; //商品条码

    private BigDecimal goodsPurprice; //商品进价

    private BigDecimal saleListDetailCount ; //商品数量

    private Integer saleListHandlestate; //发货状态：0-已删除-1无效订单-2待发货-3待收货-4已完成-5已取消-6待评论-7配送单待\r\n\r\n确认 8-待付款9-待自提 10-配送异常 11-已核单未发货

    public Long getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Long goodsId) {
        this.goodsId = goodsId;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public BigDecimal getGoodsPurprice() {
        return goodsPurprice;
    }

    public void setGoodsPurprice(BigDecimal goodsPurprice) {
        this.goodsPurprice = goodsPurprice;
    }

    public BigDecimal getSaleListDetailCount() {
        return saleListDetailCount;
    }

    public void setSaleListDetailCount(BigDecimal saleListDetailCount) {
        this.saleListDetailCount = saleListDetailCount;
    }

    public Integer getSaleListHandlestate() {
        return saleListHandlestate;
    }

    public void setSaleListHandlestate(Integer saleListHandlestate) {
        this.saleListHandlestate = saleListHandlestate;
    }
}
