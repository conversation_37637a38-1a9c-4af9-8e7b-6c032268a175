package org.haier.shopUpdate.entity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 库存明细表
 * <AUTHOR> 
 * @ClassName ShopStock
 * @Date 2024-04-29
 **/

public class ShopStock {
	/**
	* 入库记录编号
	*/
	private Integer stockId;

	/**
	* 商品条码
	*/
	private String goodsBarcode;

	/**
	* 商品数量
	*/
	private BigDecimal goodsCount;

	/**
	* 修改库存后商品数量
	*/
	private BigDecimal stockCount;

	/**
	* 出入库类型：1、入库；2、出库；
	*/
	private Integer stockType;

	/**
	* 出入库时间
	*/
	private Date stockTime;

	/**
	* 店铺编号
	*/
	private Long shopUnique;

	/**
	* 1：手动出入库；2：销售订单出入库，3：进货订单出入库（出库为退货）；4：盘库
5:网上订单出库;6：寄存；7、云商采购;8:调拨;9、退货
	*/
	private Integer stockResource;

	/**
	* 若入库类型为订单入库，则关联订单编号
	*/
	private String listUnique;

	/**
	* 出入库时的商品价格
	*/
	private BigDecimal stockPrice;

	/**
	* 操作来源：1、手机；2、PC端；3、web网页端；
4、小程序
	*/
	private Integer stockOrigin;

	/**
	* 操作员工编号
	*/
	private Long staffId;

	private Long failId;

	/**
	* 商品生产日期
	*/
	private Date goodsProd;

	/**
	* 商品过期日期
	*/
	private Date goodsExp;

	/**
	* 商品保质天数
	*/
	private Integer goodsLife;

	/**
	 * 出库成本价
	 */
	private BigDecimal goodsAvgOutPrice;

	public Integer getStockId() {
		return stockId;
	}

	public void setStockId(Integer stockId) {
		this.stockId = stockId;
	}

	public String getGoodsBarcode() {
		return goodsBarcode;
	}

	public void setGoodsBarcode(String goodsBarcode) {
		this.goodsBarcode = goodsBarcode;
	}

	public BigDecimal getGoodsCount() {
		return goodsCount;
	}

	public void setGoodsCount(BigDecimal goodsCount) {
		this.goodsCount = goodsCount;
	}

	public BigDecimal getStockCount() {
		return stockCount;
	}

	public void setStockCount(BigDecimal stockCount) {
		this.stockCount = stockCount;
	}

	public Integer getStockType() {
		return stockType;
	}

	public void setStockType(Integer stockType) {
		this.stockType = stockType;
	}

	public Date getStockTime() {
		return stockTime;
	}

	public void setStockTime(Date stockTime) {
		this.stockTime = stockTime;
	}

	public Long getShopUnique() {
		return shopUnique;
	}

	public void setShopUnique(Long shopUnique) {
		this.shopUnique = shopUnique;
	}

	public Integer getStockResource() {
		return stockResource;
	}

	public void setStockResource(Integer stockResource) {
		this.stockResource = stockResource;
	}

	public String getListUnique() {
		return listUnique;
	}

	public void setListUnique(String listUnique) {
		this.listUnique = listUnique;
	}

	public BigDecimal getStockPrice() {
		return stockPrice;
	}

	public void setStockPrice(BigDecimal stockPrice) {
		this.stockPrice = stockPrice;
	}

	public Integer getStockOrigin() {
		return stockOrigin;
	}

	public void setStockOrigin(Integer stockOrigin) {
		this.stockOrigin = stockOrigin;
	}

	public Long getStaffId() {
		return staffId;
	}

	public void setStaffId(Long staffId) {
		this.staffId = staffId;
	}

	public Long getFailId() {
		return failId;
	}

	public void setFailId(Long failId) {
		this.failId = failId;
	}

	public Date getGoodsProd() {
		return goodsProd;
	}

	public void setGoodsProd(Date goodsProd) {
		this.goodsProd = goodsProd;
	}

	public Date getGoodsExp() {
		return goodsExp;
	}

	public void setGoodsExp(Date goodsExp) {
		this.goodsExp = goodsExp;
	}

	public Integer getGoodsLife() {
		return goodsLife;
	}

	public void setGoodsLife(Integer goodsLife) {
		this.goodsLife = goodsLife;
	}

	public BigDecimal getGoodsAvgOutPrice() {
		return goodsAvgOutPrice;
	}

	public void setGoodsAvgOutPrice(BigDecimal goodsAvgOutPrice) {
		this.goodsAvgOutPrice = goodsAvgOutPrice;
	}
}