package org.haier.shopUpdate.entity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description 盘点任务详情
 */
public class InventoryTaskDetailEntity {
    /**
    * id
    */
    private Long id;

    /**
    * inventory_task.id
    */
    private Long taskId;

    /**
    * 盘点员工编号
    */
    private Integer staffId;

    /**
    * 商品编号
    */
    private Integer goodsId;

    /**
    * 商品条码
    */
    private String goodsBarcode;

    /**
    * 商品图片
    */
    private String goodsPicturepath;

    /**
    * 商品名称
    */
    private String goodsName;

    /**
    * 盘点商品的数量
    */
    private BigDecimal inventoryCount;

    /**
     * 筐重量
     */
    private BigDecimal bucketWeight;

    /**
    * 备注
    */
    private String remarks;

    /**
    * 创建时间
    */
    private Date createTime;
    /**
     * 单位
     */
    private String goodsUnit;
    /**
     * 称重商品类型：0、按件；1、按重量
     */
    private Integer goodsChengType;
    /**
     * 货位
     */
    private String goodsPosition;

    public Integer getGoodsChengType() {
        return goodsChengType;
    }

    public void setGoodsChengType(Integer goodsChengType) {
        this.goodsChengType = goodsChengType;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Integer getStaffId() {
        return staffId;
    }

    public void setStaffId(Integer staffId) {
        this.staffId = staffId;
    }

    public Integer getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Integer goodsId) {
        this.goodsId = goodsId;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public String getGoodsPicturepath() {
        return goodsPicturepath;
    }

    public void setGoodsPicturepath(String goodsPicturepath) {
        this.goodsPicturepath = goodsPicturepath;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public BigDecimal getInventoryCount() {
        return inventoryCount;
    }

    public void setInventoryCount(BigDecimal inventoryCount) {
        this.inventoryCount = inventoryCount;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public BigDecimal getBucketWeight() {
        return bucketWeight;
    }

    public void setBucketWeight(BigDecimal bucketWeight) {
        this.bucketWeight = bucketWeight;
    }

    public String getGoodsUnit() {
        return goodsUnit;
    }

    public void setGoodsUnit(String goodsUnit) {
        this.goodsUnit = goodsUnit;
    }

    public String getGoodsPosition() {
        return goodsPosition;
    }

    public void setGoodsPosition(String goodsPosition) {
        this.goodsPosition = goodsPosition;
    }
}