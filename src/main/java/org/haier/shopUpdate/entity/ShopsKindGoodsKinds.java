package org.haier.shopUpdate.entity;

import java.util.List;

/**
 * 商品二级分类实体类
 * <AUTHOR>
 */
public class ShopsKindGoodsKinds {
	private Integer kindId;
	//二级分类名称
	private String kindName;
	//二级分类条码
	private String kindUnique;

	//
	private String groupUnique;
	//分类别名
	private String kindAlias;
	//可编辑状态
	private String editType;

	private Integer valid_type;

	private List<ShopsKindGoodsKinds> kindDetail;

	public Integer getValid_type() {
		return valid_type;
	}
	public void setValid_type(Integer valid_type) {
		this.valid_type = valid_type;
	}
	public String getGroupUnique() {
		return groupUnique;
	}
	public void setGroupUnique(String groupUnique) {
		this.groupUnique = groupUnique;
	}
	public Integer getKindId() {
		return kindId;
	}
	public void setKindId(Integer kindId) {
		this.kindId = kindId;
	}
	public String getKindName() {
		return kindName;
	}
	public void setKindName(String kindName) {
		this.kindName = kindName;
	}
	public String getKindUnique() {
		return kindUnique;
	}
	public void setKindUnique(String kindUnique) {
		this.kindUnique = kindUnique;
	}
	public String getEditType() {
		return editType;
	}
	public void setEditType(String editType) {
		this.editType = editType;
	}
	public String getKindAlias() {
		return kindAlias;
	}
	public void setKindAlias(String kindAlias) {
		this.kindAlias = kindAlias;
	}
	public List<ShopsKindGoodsKinds> getKindDetail() {
		return kindDetail;
	}
	public void setKindDetail(List<ShopsKindGoodsKinds> kindDetail) {
		this.kindDetail = kindDetail;
	}

	@Override
	public String toString() {
		return "ShopsKindGoodsKinds [kindId=" + kindId + ", kindName=" + kindName + ", kindUnique=" + kindUnique
				+ ", kindAlias=" + kindAlias + ", editType=" + editType + "]";
	}
}
