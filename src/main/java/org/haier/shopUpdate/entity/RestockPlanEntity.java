package org.haier.shopUpdate.entity;

import java.util.Date;

/**
 * @description 补货计划
 */
public class RestockPlanEntity {
    /**
    * id
    */
    private Long shopRestockplanId;
    /**
     * 补货单编号
     */
    private String restockNo;
    /**
    * 店铺编号
    */
    private Long shopUnique;

    /**
    * 计划名称
    */
    private String shopRestockplanName;

    /**
    * 状态
    */
    private Integer status;

    /**
    * 创建人
    */
    private String createUser;

    /**
    * 创建时间
    */
    private String createTime;

    public String getRestockNo() {
        return restockNo;
    }

    public void setRestockNo(String restockNo) {
        this.restockNo = restockNo;
    }

    public Long getShopRestockplanId() {
        return shopRestockplanId;
    }

    public void setShopRestockplanId(Long shopRestockplanId) {
        this.shopRestockplanId = shopRestockplanId;
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getShopRestockplanName() {
        return shopRestockplanName;
    }

    public void setShopRestockplanName(String shopRestockplanName) {
        this.shopRestockplanName = shopRestockplanName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }
}