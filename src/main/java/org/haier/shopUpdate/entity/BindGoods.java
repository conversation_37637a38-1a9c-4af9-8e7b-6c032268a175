package org.haier.shopUpdate.entity;

/**
 * 捆绑商品详情
 * <AUTHOR>
 */
public class BindGoods {
	//商品条码信息
	private String goodsBarcode;
	//商品名称信息
	private String goodsName;
	//商品捆绑数量信息
	private Double goodsCount;
	//商品计价单位
	private String goodsUnit;
	//商品进价
	private Double goodsInPrice;
	//商品销售价格
	private Double goodsSalePrice;
	
	public String getGoodsBarcode() {
		return goodsBarcode;
	}
	public void setGoodsBarcode(String goodsBarcode) {
		this.goodsBarcode = goodsBarcode;
	}
	public String getGoodsName() {
		return goodsName;
	}
	public void setGoodsName(String goodsName) {
		this.goodsName = goodsName;
	}
	public Double getGoodsCount() {
		return goodsCount;
	}
	public void setGoodsCount(Double goodsCount) {
		this.goodsCount = goodsCount;
	}
	public String getGoodsUnit() {
		return goodsUnit;
	}
	public void setGoodsUnit(String goodsUnit) {
		this.goodsUnit = goodsUnit;
	}
	public Double getGoodsInPrice() {
		return goodsInPrice;
	}
	public void setGoodsInPrice(Double goodsInPrice) {
		this.goodsInPrice = goodsInPrice;
	}
	public Double getGoodsSalePrice() {
		return goodsSalePrice;
	}
	public void setGoodsSalePrice(Double goodsSalePrice) {
		this.goodsSalePrice = goodsSalePrice;
	}
}
