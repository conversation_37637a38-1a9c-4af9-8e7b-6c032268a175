package org.haier.shopUpdate.entity;

import java.math.BigDecimal;
import java.util.List;

public class AddConsignmentSaleReq {

    /**
     * 店铺编号
     */
    private Long shopUnique;

    /**
     * 供货商id：supplier.supplier_id
     */
    private Integer supplierId;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 佣金类型:1按整柜、2按比例
     */
    private Integer commissionType;

    /**
     * 佣金
     */
    private BigDecimal commission;

    /**
     * 车牌号
     */
    private String carNo;

    /**
     * 入库商品
     */
    private List<AddConsignmentSaleGoodsReq> goodsList;

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public Integer getCommissionType() {
        return commissionType;
    }

    public void setCommissionType(Integer commissionType) {
        this.commissionType = commissionType;
    }

    public BigDecimal getCommission() {
        return commission;
    }

    public void setCommission(BigDecimal commission) {
        this.commission = commission;
    }

    public String getCarNo() {
        return carNo;
    }

    public void setCarNo(String carNo) {
        this.carNo = carNo;
    }

    public List<AddConsignmentSaleGoodsReq> getGoodsList() {
        return goodsList;
    }

    public void setGoodsList(List<AddConsignmentSaleGoodsReq> goodsList) {
        this.goodsList = goodsList;
    }
}
