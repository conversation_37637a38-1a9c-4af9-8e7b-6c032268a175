package org.haier.shopUpdate.entity.nova;

import java.io.Serializable;

/**
 * @ClassName I18nLanguageEntity
 * <AUTHOR>
 * @Date 2024/7/30 10:03
 */

public class I18nLanguageEntity implements Serializable {
    private Long id;
    private String keyName;
    private String languageCode;

    private String content;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getKeyName() {
        return keyName;
    }

    public void setKeyName(String keyName) {
        this.keyName = keyName;
    }

    public String getLanguageCode() {
        return languageCode;
    }

    public void setLanguageCode(String languageCode) {
        this.languageCode = languageCode;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
