package org.haier.shopUpdate.entity;

import java.io.Serializable;

public class ShopTitleEntity implements Serializable {
    private Long id;
    //所属店铺编号
    private Long shopUnique;
    //模块名称
    private String titleName;
    //标题头像地址
    private String titleImg;
    //显示顺序
    private Integer titleSort;
    //模块编号
    private String modularNum;
    //创建时间
    private String addTime;
    //是否主页显示
    private Integer showType;
    //是否有效1、有效；2、无效
    private Integer validType;
    //所属大模块
    private Integer modularType;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getTitleName() {
        return titleName;
    }

    public void setTitleName(String titleName) {
        this.titleName = titleName;
    }

    public String getTitleImg() {
        return titleImg;
    }

    public void setTitleImg(String titleImg) {
        this.titleImg = titleImg;
    }

    public Integer getTitleSort() {
        return titleSort;
    }

    public void setTitleSort(Integer titleSort) {
        this.titleSort = titleSort;
    }

    public String getModularNum() {
        return modularNum;
    }

    public void setModularNum(String modularNum) {
        this.modularNum = modularNum;
    }

    public String getAddTime() {
        return addTime;
    }

    public void setAddTime(String addTime) {
        this.addTime = addTime;
    }

    public Integer getShowType() {
        return showType;
    }

    public void setShowType(Integer showType) {
        this.showType = showType;
    }

    public Integer getValidType() {
        return validType;
    }

    public void setValidType(Integer validType) {
        this.validType = validType;
    }

    public Integer getModularType() {
        return modularType;
    }

    public void setModularType(Integer modularType) {
        this.modularType = modularType;
    }
}
