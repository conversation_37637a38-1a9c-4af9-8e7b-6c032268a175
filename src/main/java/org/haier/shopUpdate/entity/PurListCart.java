package org.haier.shopUpdate.entity;

import java.util.List;

public class PurListCart {
	//订单编号
	private Long purListUnique;
	//供货商名称
	private String supplierName;
	//供货商编号
	private Long supplierUnique;
	//全选状态;
	private Integer checkaAll=1;
	//订单详情
	private List<PurListCartDetail> listDetail;
	//满赠提示信息
	private List<String> listPro;
	public Long getPurListUnique() {
		return purListUnique;
	}
	public void setPurListUnique(Long purListUnique) {
		this.purListUnique = purListUnique;
	}
	public String getSupplierName() {
		return supplierName;
	}
	public void setSupplierName(String supplierName) {
		this.supplierName = supplierName;
	}
	public Long getSupplierUnique() {
		return supplierUnique;
	}
	public void setSupplierUnique(Long supplierUnique) {
		this.supplierUnique = supplierUnique;
	}
	public List<PurListCartDetail> getListDetail() {
		return listDetail;
	}
	public void setListDetail(List<PurListCartDetail> listDetail) {
		this.listDetail = listDetail;
	}
	public List<String> getListPro() {
		return listPro;
	}
	public void setListPro(List<String> listPro) {
		this.listPro = listPro;
	}
	public Integer getCheckaAll() {
		return checkaAll;
	}
	public void setCheckaAll(Integer checkaAll) {
		this.checkaAll = checkaAll;
	}
	@Override
	public String toString() {
		return "PurListCart [purListUnique=" + purListUnique + ", supplierName=" + supplierName + ", supplierUnique="
				+ supplierUnique + ", checkaAll=" + checkaAll + ", listDetail=" + listDetail + ", listPro=" + listPro
				+ "]";
	}
	
}
