package org.haier.shopUpdate.entity.ret2;

public class ReturnPayDetail {
	private Integer retPayDetailId;
	private String saleListUnique;
	private String retListUnique;
	private Integer payType;
	private String payTypeMsg;
	private Double payMoney;
	private Integer serviceType;
	private String serviceTypeMsg;
	private String mchId;
	public Integer getRetPayDetailId() {
		return retPayDetailId;
	}
	public void setRetPayDetailId(Integer retPayDetailId) {
		this.retPayDetailId = retPayDetailId;
	}
	public String getSaleListUnique() {
		return saleListUnique;
	}
	public void setSaleListUnique(String saleListUnique) {
		this.saleListUnique = saleListUnique;
	}
	public String getRetListUnique() {
		return retListUnique;
	}
	public void setRetListUnique(String retListUnique) {
		this.retListUnique = retListUnique;
	}
	public Integer getPayType() {
		return payType;
	}
	public void setPayType(Integer payType) {
		this.payType = payType;
	}
	public String getPayTypeMsg() {
		return payTypeMsg;
	}
	public void setPayTypeMsg(String payTypeMsg) {
		this.payTypeMsg = payTypeMsg;
	}
	public Double getPayMoney() {
		return payMoney;
	}
	public void setPayMoney(Double payMoney) {
		this.payMoney = payMoney;
	}
	public Integer getServiceType() {
		return serviceType;
	}
	public void setServiceType(Integer serviceType) {
		this.serviceType = serviceType;
	}
	public String getServiceTypeMsg() {
		return serviceTypeMsg;
	}
	public void setServiceTypeMsg(String serviceTypeMsg) {
		this.serviceTypeMsg = serviceTypeMsg;
	}
	public String getMchId() {
		return mchId;
	}
	public void setMchId(String mchId) {
		this.mchId = mchId;
	}
}
