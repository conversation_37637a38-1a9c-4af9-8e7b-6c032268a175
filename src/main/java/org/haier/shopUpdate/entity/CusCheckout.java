package org.haier.shopUpdate.entity;



/**
 * 类名:com.palmshop.online.entity.CusCheckout;
 * 描述:收银端 用户信息的实体类
 * <AUTHOR>
 * @version 1.2
 *
 */
public class CusCheckout {
	
	private Long cusId;//用户自增长Id
	
	private String cusUnique;//用户会员编号
	
	private Long  shopUnique;//店铺id
	
	private String cusName;//用户真实姓名
	
	private String cusPhone;//联系电话
	
	private Double cusPoints;//用户积分，默认0
	
	private String cusRegeditDate;//用户创建时间
	
	private String cusBirthday;//用户生日
	
	private Integer cusAvailable;//用户可用状态：1，可用；2，不可用
	
	private Integer sameType;//1:PC已同步；2:PC未同步
	
	private Integer cusCount;//消费次数
	
	private Double cusAmount;//充值金额
	
	private Double cusUse;//已用金额
	
	private Double cusBalance;//账户余额
	
	private String cusType;//卡的类型
	
	private Double cusTotal;//消费总金额
	
	private Double totalPoints;//累计总积分
	
	private Integer cusLevelId;//会员等级ID
	
	private String cusHeadPath;//会员头像路径
	
	private String cusWeixin;//微信
	
	private String cusEmail;//邮箱
	
	private String cusQQ;//QQ
	
	private Integer cusSex;//性别
	
	private String cusPassword;//密码
	
	private String cus_status;//会员状态
	
	private String cus_remark;//备注
	
	private String cusPicPath;//用于人脸识别上传的图片信息
	
	private String imageMsg;//上传的图片二进制信息
	
	private String imgFormat;//上传的二进制文件图片类型：jpg,png等
	
	private String cus_face_token;//上传的用户人脸信息中人脸标识值
	
	private String validityStart;//有效期始；
	private String validityEnd;//有效期止；
	

	/**
	 * @return the cusId
	 */
	public Long getCusId() {
		return cusId;
	}

	/**
	 * @param cusId the cusId to set
	 */
	public void setCusId(Long cusId) {
		this.cusId = cusId;
	}

	/**
	 * @return the shopUnique
	 */
	public Long getShopUnique() {
		return shopUnique;
	}

	public String getCusUnique() {
		return cusUnique;
	}

	public void setCusUnique(String cusUnique) {
		this.cusUnique = cusUnique;
	}

	/**
	 * @param shopUnique the shopUnique to set
	 */
	public void setShopUnique(Long shopUnique) {
		this.shopUnique = shopUnique;
	}

	/**
	 * @return the cusName
	 */
	public String getCusName() {
		return cusName;
	}

	/**
	 * @param cusName the cusName to set
	 */
	public void setCusName(String cusName) {
		this.cusName = cusName;
	}

	/**
	 * @return the cusPhone
	 */
	public String getCusPhone() {
		return cusPhone;
	}

	/**
	 * @param cusPhone the cusPhone to set
	 */
	public void setCusPhone(String cusPhone) {
		this.cusPhone = cusPhone;
	}

	/**
	 * @return the cusPoints
	 */
	public Double getCusPoints() {
		return cusPoints;
	}

	/**
	 * @param cusPoints the cusPoints to set
	 */
	public void setCusPoints(Double cusPoints) {
		this.cusPoints = cusPoints;
	}

	/**
	 * @return the cusRegeditDate
	 */
	public String getCusRegeditDate() {
		return cusRegeditDate;
	}

	/**
	 * @param cusRegeditDate the cusRegeditDate to set
	 */
	public void setCusRegeditDate(String cusRegeditDate) {
		this.cusRegeditDate = cusRegeditDate;
	}

	/**
	 * @return the cusBirthday
	 */
	public String getCusBirthday() {
		return cusBirthday;
	}

	/**
	 * @param cusBirthday the cusBirthday to set
	 */
	public void setCusBirthday(String cusBirthday) {
		this.cusBirthday = cusBirthday;
	}

	/**
	 * @return the cusAvailable
	 */
	public Integer getCusAvailable() {
		return cusAvailable;
	}

	/**
	 * @param cusAvailable the cusAvailable to set
	 */
	public void setCusAvailable(Integer cusAvailable) {
		this.cusAvailable = cusAvailable;
	}

	/**
	 * @return the sameType
	 */
	public Integer getSameType() {
		return sameType;
	}

	/**
	 * @param sameType the sameType to set
	 */
	public void setSameType(Integer sameType) {
		this.sameType = sameType;
	}
	
	

	public Integer getCusSex() {
		return cusSex;
	}

	public void setCusSex(Integer cusSex) {
		this.cusSex = cusSex;
	}

	/* (non-Javadoc)
	 * @see java.lang.Object#hashCode()
	 */
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((cusAvailable == null) ? 0 : cusAvailable.hashCode());
		result = prime * result + ((cusBirthday == null) ? 0 : cusBirthday.hashCode());
		result = prime * result + ((cusId == null) ? 0 : cusId.hashCode());
		result = prime * result + ((cusName == null) ? 0 : cusName.hashCode());
		result = prime * result + ((cusPhone == null) ? 0 : cusPhone.hashCode());
		result = prime * result + ((cusPoints == null) ? 0 : cusPoints.hashCode());
		result = prime * result + ((cusRegeditDate == null) ? 0 : cusRegeditDate.hashCode());
		result = prime * result + ((cusUnique == null) ? 0 : cusUnique.hashCode());
		result = prime * result + ((sameType == null) ? 0 : sameType.hashCode());
		result = prime * result + ((shopUnique == null) ? 0 : shopUnique.hashCode());
		return result;
	}

	/* (non-Javadoc)
	 * @see java.lang.Object#equals(java.lang.Object)
	 */
	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		CusCheckout other = (CusCheckout) obj;
		if (cusAvailable == null) {
			if (other.cusAvailable != null)
				return false;
		} else if (!cusAvailable.equals(other.cusAvailable))
			return false;
		if (cusBirthday == null) {
			if (other.cusBirthday != null)
				return false;
		} else if (!cusBirthday.equals(other.cusBirthday))
			return false;
		if (cusId == null) {
			if (other.cusId != null)
				return false;
		} else if (!cusId.equals(other.cusId))
			return false;
		if (cusName == null) {
			if (other.cusName != null)
				return false;
		} else if (!cusName.equals(other.cusName))
			return false;
		if (cusPhone == null) {
			if (other.cusPhone != null)
				return false;
		} else if (!cusPhone.equals(other.cusPhone))
			return false;
		if (cusPoints == null) {
			if (other.cusPoints != null)
				return false;
		} else if (!cusPoints.equals(other.cusPoints))
			return false;
		if (cusRegeditDate == null) {
			if (other.cusRegeditDate != null)
				return false;
		} else if (!cusRegeditDate.equals(other.cusRegeditDate))
			return false;
		if (cusUnique == null) {
			if (other.cusUnique != null)
				return false;
		} else if (!cusUnique.equals(other.cusUnique))
			return false;
		if (sameType == null) {
			if (other.sameType != null)
				return false;
		} else if (!sameType.equals(other.sameType))
			return false;
		if (shopUnique == null) {
			if (other.shopUnique != null)
				return false;
		} else if (!shopUnique.equals(other.shopUnique))
			return false;
		return true;
	}
	public CusCheckout() {
		super();
	}

	/**
	 * @return the cusCount
	 */
	public Integer getCusCount() {
		return cusCount;
	}

	/**
	 * @param cusCount the cusCount to set
	 */
	public void setCusCount(Integer cusCount) {
		this.cusCount = cusCount;
	}

	/**
	 * @return the cusAmount
	 */
	public Double getCusAmount() {
		return cusAmount;
	}

	/**
	 * @param cusAmount the cusAmount to set
	 */
	public void setCusAmount(Double cusAmount) {
		this.cusAmount = cusAmount;
	}

	/**
	 * @return the cusUse
	 */
	public Double getCusUse() {
		return cusUse;
	}

	/**
	 * @param cusUse the cusUse to set
	 */
	public void setCusUse(Double cusUse) {
		this.cusUse = cusUse;
	}

	/**
	 * @return the cusBalance
	 */
	public Double getCusBalance() {
		return cusBalance;
	}

	/**
	 * @param cusBalance the cusBalance to set
	 */
	public void setCusBalance(Double cusBalance) {
		this.cusBalance = cusBalance;
	}

	/**
	 * @return the cusType
	 */
	public String getCusType() {
		return cusType;
	}

	/**
	 * @param cusType the cusType to set
	 */
	public void setCusType(String cusType) {
		this.cusType = cusType;
	}

	/**
	 * @return the cusTotal
	 */
	public Double getCusTotal() {
		return cusTotal;
	}

	/**
	 * @param cusTotal the cusTotal to set
	 */
	public void setCusTotal(Double cusTotal) {
		this.cusTotal = cusTotal;
	}

	/**
	 * @return the totalPoints
	 */
	public Double getTotalPoints() {
		return totalPoints;
	}

	/**
	 * @param totalPoints the totalPoints to set
	 */
	public void setTotalPoints(Double totalPoints) {
		this.totalPoints = totalPoints;
	}

	/**
	 * @return the cusLevelId
	 */
	public Integer getCusLevelId() {
		return cusLevelId;
	}

	/**
	 * @param cusLevelId the cusLevelId to set
	 */
	public void setCusLevelId(Integer cusLevelId) {
		this.cusLevelId = cusLevelId;
	}

	public String getCusHeadPath() {
		return cusHeadPath;
	}

	public void setCusHeadPath(String cusHeadPath) {
		this.cusHeadPath = cusHeadPath;
	}

	public String getCusWeixin() {
		return cusWeixin;
	}

	public void setCusWeixin(String cusWeixin) {
		this.cusWeixin = cusWeixin;
	}

	public String getCusEmail() {
		return cusEmail;
	}

	public void setCusEmail(String cusEmail) {
		this.cusEmail = cusEmail;
	}

	public String getCusQQ() {
		return cusQQ;
	}

	public void setCusQQ(String cusQQ) {
		this.cusQQ = cusQQ;
	}

	public String getCusPassword() {
		return cusPassword;
	}

	public void setCusPassword(String cusPassword) {
		this.cusPassword = cusPassword;
	}

	public String getCus_status() {
		return cus_status;
	}

	public void setCus_status(String cus_status) {
		this.cus_status = cus_status;
	}

	public String getCus_remark() {
		return cus_remark;
	}

	public void setCus_remark(String cus_remark) {
		this.cus_remark = cus_remark;
	}

	/**
	 * @return the imageMsg
	 */
	public String getImageMsg() {
		return imageMsg;
	}

	/**
	 * @param imageMsg the imageMsg to set
	 */
	public void setImageMsg(String imageMsg) {
		this.imageMsg = imageMsg;
	}

	/**
	 * @return the imgFormat
	 */
	public String getImgFormat() {
		return imgFormat;
	}

	/**
	 * @param imgFormat the imgFormat to set
	 */
	public void setImgFormat(String imgFormat) {
		this.imgFormat = imgFormat;
	}
	/**
	 * @return the cusPicPath
	 */
	public String getCusPicPath() {
		return cusPicPath;
	}
	/**
	 * @param cusPicPath the cusPicPath to set
	 */
	public void setCusPicPath(String cusPicPath) {
		this.cusPicPath = cusPicPath;
	}

	public String getCus_face_token() {
		return cus_face_token;
	}

	public void setCus_face_token(String cus_face_token) {
		this.cus_face_token = cus_face_token;
	}

	public String getValidityStart() {
		return validityStart;
	}

	public void setValidityStart(String validityStart) {
		this.validityStart = validityStart;
	}

	public String getValidityEnd() {
		return validityEnd;
	}

	public void setValidityEnd(String validityEnd) {
		this.validityEnd = validityEnd;
	}

	@Override
	public String toString() {
		return "CusCheckout [cusId=" + cusId + ", cusUnique=" + cusUnique + ", shopUnique=" + shopUnique + ", cusName="
				+ cusName + ", cusPhone=" + cusPhone + ", cusPoints=" + cusPoints + ", cusRegeditDate=" + cusRegeditDate
				+ ", cusBirthday=" + cusBirthday + ", cusAvailable=" + cusAvailable + ", sameType=" + sameType
				+ ", cusCount=" + cusCount + ", cusAmount=" + cusAmount + ", cusUse=" + cusUse + ", cusBalance="
				+ cusBalance + ", cusType=" + cusType + ", cusTotal=" + cusTotal + ", totalPoints=" + totalPoints
				+ ", cusLevelId=" + cusLevelId + ", cusHeadPath=" + cusHeadPath + ", cusWeixin=" + cusWeixin
				+ ", cusEmail=" + cusEmail + ", cusQQ=" + cusQQ + ", cusSex=" + cusSex + ", cusPassword=" + cusPassword
				+ ", cus_status=" + cus_status + ", cus_remark=" + cus_remark + ", cusPicPath=" + cusPicPath
				+ ", imageMsg=" + imageMsg + ", imgFormat=" + imgFormat + ", cus_face_token=" + cus_face_token + "]";
	}
}
