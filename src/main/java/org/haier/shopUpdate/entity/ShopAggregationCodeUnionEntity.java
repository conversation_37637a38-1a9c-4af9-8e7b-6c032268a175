package org.haier.shopUpdate.entity;

/**
 * @description 店铺与聚合码关联关系表
 */
public class ShopAggregationCodeUnionEntity {
    /**
    * id
    */
    private Long id;

    /**
    * 店铺编号
    */
    private Long shopUnique;

    /**
    * shop_aggregation_code.id
    */
    private Long aggregationCodeId;

    /**
    * 聚合码审核状态：0待申请，1审核中，2审核成功，3审核失败
    */
    private Integer aggregateAuditStatus;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public Long getAggregationCodeId() {
        return aggregationCodeId;
    }

    public void setAggregationCodeId(Long aggregationCodeId) {
        this.aggregationCodeId = aggregationCodeId;
    }

    public Integer getAggregateAuditStatus() {
        return aggregateAuditStatus;
    }

    public void setAggregateAuditStatus(Integer aggregateAuditStatus) {
        this.aggregateAuditStatus = aggregateAuditStatus;
    }
}