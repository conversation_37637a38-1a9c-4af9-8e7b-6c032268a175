package org.haier.shopUpdate.entity;


import java.io.Serializable;

/**
 * @author: wel
 * @date: 今夕和何年
 * @description: speech_cmd表实体类
 */
public class SpeechCmdEntity implements Serializable {
     //ID
    private Long id;
    //app类型,具体见AppTypeEnums
    private String appType;
    //解析后的指令类型
    private String cmdType;
    //页面序号
    private String pageIndex;
    //指令示例
    private String cmdDescribe;
    //指令完整说明
    private String remarks;
    //指令是否有效
    private Integer validType;
    //对应识别系统的指令
    private String cmdSys;
    //是否有权限限制:1、有权限限制；0、无权限限制
    private Integer permissionLimit;
    //创建时间
    private String createTime;
    //更新时间
    private String updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAppType() {
        return appType;
    }

    public void setAppType(String appType) {
        this.appType = appType;
    }

    public String getCmdType() {
        return cmdType;
    }

    public void setCmdType(String cmdType) {
        this.cmdType = cmdType;
    }

    public String getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(String pageIndex) {
        this.pageIndex = pageIndex;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public Integer getValidType() {
        return validType;
    }

    public void setValidType(Integer validType) {
        this.validType = validType;
    }

    public String getCmdSys() {
        return cmdSys;
    }

    public void setCmdSys(String cmdSys) {
        this.cmdSys = cmdSys;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getPermissionLimit() {
        return permissionLimit;
    }

    public void setPermissionLimit(Integer permissionLimit) {
        this.permissionLimit = permissionLimit;
    }

    public String getCmdDescribe() {
        return cmdDescribe;
    }

    public void setCmdDescribe(String cmdDescribe) {
        this.cmdDescribe = cmdDescribe;
    }
}
