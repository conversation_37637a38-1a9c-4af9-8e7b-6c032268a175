package org.haier.shopUpdate.entity;

import java.io.Serializable;

public class ShopBeansVO implements Serializable{
		
		/**
		 * 
		 */
		private static final long serialVersionUID = -9184294139120414646L;
		//提现前百货豆数量
		private long beans_old_count=0;
		//最小提现数
		private int s_moeny=0;
		//最大提现数
		private int m_moeny=0;
		//平台提现手续费单位万分之一
		private int pt_tax=0;
		//
		private int yh_tax=0;
		//提现日期：1、2、3、日
		private int tx_dtae=0;
		//提现次数：1、2、3..次
		private int tx_times=0;
		//最小处理日期：如3个工作日
		private int s_cl_date=0;
		//最大处理日期：如5个工作日
		private int m_cl_date=0;
		//免密-最小赠送数
		private int mm_small=0;
		//免密-最大赠送数
		private int mm_top=0;
		//非免密-最小赠送数
		private int fmm_small=0;
		//非免密-最大赠送数
		private int fmm_top=0;
		//商家最小赠送百分比
		private int small_present=0;
		
		private Double shop_balance;
		
		private int mm_probability=0;
		
		private int fmm_probability=0;
		//平台赠送商家非免密满足金额
		private int fmm_moeny=0;
		//平台赠送商家免密满足金额
		private int mm_moeny=0;
		//供货商，赠送订单满足金额
		private int sup_moeny=0;
		//供货商，随机赠送最小值
		private int sup_m_small=0;
		//供货商，随机赠送最大值
		private int sup_mm_top=0;
		
		private int diKou=0;
		
		private int share_register=0;
		//平台会员消费补贴：满
		private int pt_mem_moeny=0;
		//平台会员消费补贴：最小
		private int pt_mem_small=0;
		//平台会员消费补贴：最大
		private int pt_mem_top=0;
		
		//会员注册赠送百货豆数量
		private int pt_give_beans=0;

		public long getBeans_old_count() {
			return beans_old_count;
		}

		public void setBeans_old_count(long beans_old_count) {
			this.beans_old_count = beans_old_count;
		}

		public int getS_moeny() {
			return s_moeny;
		}

		public void setS_moeny(int s_moeny) {
			this.s_moeny = s_moeny;
		}

		public int getM_moeny() {
			return m_moeny;
		}

		public void setM_moeny(int m_moeny) {
			this.m_moeny = m_moeny;
		}

		public int getPt_tax() {
			return pt_tax;
		}

		public void setPt_tax(int pt_tax) {
			this.pt_tax = pt_tax;
		}

		public int getYh_tax() {
			return yh_tax;
		}

		public void setYh_tax(int yh_tax) {
			this.yh_tax = yh_tax;
		}

		public int getTx_dtae() {
			return tx_dtae;
		}

		public void setTx_dtae(int tx_dtae) {
			this.tx_dtae = tx_dtae;
		}

		public int getTx_times() {
			return tx_times;
		}

		public void setTx_times(int tx_times) {
			this.tx_times = tx_times;
		}

		public int getS_cl_date() {
			return s_cl_date;
		}

		public void setS_cl_date(int s_cl_date) {
			this.s_cl_date = s_cl_date;
		}

		public int getM_cl_date() {
			return m_cl_date;
		}

		public void setM_cl_date(int m_cl_date) {
			this.m_cl_date = m_cl_date;
		}

		public int getMm_small() {
			return mm_small;
		}

		public void setMm_small(int mm_small) {
			this.mm_small = mm_small;
		}

		public int getMm_top() {
			return mm_top;
		}

		public void setMm_top(int mm_top) {
			this.mm_top = mm_top;
		}

		public int getFmm_small() {
			return fmm_small;
		}

		public void setFmm_small(int fmm_small) {
			this.fmm_small = fmm_small;
		}

		public int getFmm_top() {
			return fmm_top;
		}

		public void setFmm_top(int fmm_top) {
			this.fmm_top = fmm_top;
		}

		public int getSmall_present() {
			return small_present;
		}

		public void setSmall_present(int small_present) {
			this.small_present = small_present;
		}

		public Double getShop_balance() {
			return shop_balance;
		}

		public void setShop_balance(Double shop_balance) {
			this.shop_balance = shop_balance;
		}

		public int getMm_probability() {
			return mm_probability;
		}

		public void setMm_probability(int mm_probability) {
			this.mm_probability = mm_probability;
		}

		public int getFmm_probability() {
			return fmm_probability;
		}

		public void setFmm_probability(int fmm_probability) {
			this.fmm_probability = fmm_probability;
		}

		public int getFmm_moeny() {
			return fmm_moeny;
		}

		public void setFmm_moeny(int fmm_moeny) {
			this.fmm_moeny = fmm_moeny;
		}

		public int getMm_moeny() {
			return mm_moeny;
		}

		public void setMm_moeny(int mm_moeny) {
			this.mm_moeny = mm_moeny;
		}

		public int getSup_moeny() {
			return sup_moeny;
		}

		public void setSup_moeny(int sup_moeny) {
			this.sup_moeny = sup_moeny;
		}

		public int getSup_m_small() {
			return sup_m_small;
		}

		public void setSup_m_small(int sup_m_small) {
			this.sup_m_small = sup_m_small;
		}

		public int getSup_mm_top() {
			return sup_mm_top;
		}

		public void setSup_mm_top(int sup_mm_top) {
			this.sup_mm_top = sup_mm_top;
		}

		public int getDiKou() {
			return diKou;
		}

		public void setDiKou(int diKou) {
			this.diKou = diKou;
		}

		public int getShare_register() {
			return share_register;
		}

		public void setShare_register(int share_register) {
			this.share_register = share_register;
		}

		public int getPt_mem_moeny() {
			return pt_mem_moeny;
		}

		public void setPt_mem_moeny(int pt_mem_moeny) {
			this.pt_mem_moeny = pt_mem_moeny;
		}

		public int getPt_mem_small() {
			return pt_mem_small;
		}

		public void setPt_mem_small(int pt_mem_small) {
			this.pt_mem_small = pt_mem_small;
		}

		public int getPt_mem_top() {
			return pt_mem_top;
		}

		public void setPt_mem_top(int pt_mem_top) {
			this.pt_mem_top = pt_mem_top;
		}

		public int getPt_give_beans() {
			return pt_give_beans;
		}

		public void setPt_give_beans(int pt_give_beans) {
			this.pt_give_beans = pt_give_beans;
		}

		public static long getSerialversionuid() {
			return serialVersionUID;
		}
		
		
	}
