package org.haier.shopUpdate.entity;

/**
 * 店铺标题信息
 * <AUTHOR>
 *
 */
public class ShopTitle {
	private Integer id;//ID
	private Long shopUnique;//店铺编号
	private String titleName;//模块名称
	private String titleImg;//模块图片
	private Integer titleSort;//排序序号
	private Integer modularNum;//指向的模块编号
	private String addTime;//功能添加的时间
	private Integer showType;//是否首页显示
	private Integer newTitle;//是否新增功能
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public Long getShopUnique() {
		return shopUnique;
	}
	public void setShopUnique(Long shopUnique) {
		this.shopUnique = shopUnique;
	}
	public String getTitleName() {
		return titleName;
	}
	public void setTitleName(String titleName) {
		this.titleName = titleName;
	}
	public String getTitleImg() {
		return titleImg;
	}
	public void setTitleImg(String titleImg) {
		this.titleImg = titleImg;
	}
	public Integer getTitleSort() {
		return titleSort;
	}
	public void setTitleSort(Integer titleSort) {
		this.titleSort = titleSort;
	}
	public Integer getModularNum() {
		return modularNum;
	}
	public void setModularNum(Integer modularNum) {
		this.modularNum = modularNum;
	}
//	public String getModularCode() {
//		return modularCode;
//	}
//	public void setModularCode(String modularCode) {
//		this.modularCode = modularCode;
//	}
	public String getAddTime() {
		return addTime;
	}
	public void setAddTime(String addTime) {
		this.addTime = addTime;
	}
	public Integer getShowType() {
		return showType;
	}
	public void setShowType(Integer showType) {
		this.showType = showType;
	}
	public Integer getNewTitle() {
		return newTitle;
	}
	public void setNewTitle(Integer newTitle) {
		this.newTitle = newTitle;
	}
//	@Override
//	public String toString() {
//		return "ShopTitle [id=" + id + ", shopUnique=" + shopUnique + ", titleName=" + titleName + ", titleImg="
//				+ titleImg + ", titleSort=" + titleSort + ", modularNum=" + modularNum + ", modularCode=" + modularCode
//				+ ", addTime=" + addTime + ", showType=" + showType + ", newTitle=" + newTitle + "]";
//	}
	@Override
	public String toString() {
		return "ShopTitle [id=" + id + ", shopUnique=" + shopUnique + ", titleName=" + titleName + ", titleImg="
				+ titleImg + ", titleSort=" + titleSort + ", modularNum=" + modularNum + ", addTime=" + addTime
				+ ", showType=" + showType + ", newTitle=" + newTitle + "]";
	}
	
}
