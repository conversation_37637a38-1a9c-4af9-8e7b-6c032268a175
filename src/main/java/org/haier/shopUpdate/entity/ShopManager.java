package org.haier.shopUpdate.entity;

import java.io.Serializable;
import java.util.List;

public class ShopManager implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	//店铺编号
	private Long shopUnique;
	//店铺名称
	private String shopName;
	//店铺电话
	private String shopPhone;
	//营业时间
	private String shopHours;
	//店铺头像路径
	private String shopImagePath;
	//店铺地址信息
	private String shopAddress;
	//
	private String area_dict_num;
	//币种code
	private String currency;
	//符号
	private String currencySymbol;
	//员工列表接权限
	private List<StaffPower> staffList;
	
	
	
	public String getArea_dict_num() {
		return area_dict_num;
	}
	public void setArea_dict_num(String area_dict_num) {
		this.area_dict_num = area_dict_num;
	}
	public String getShopAddress() {
		return shopAddress;
	}
	public void setShopAddress(String shopAddress) {
		this.shopAddress = shopAddress;
	}
	public Long getShopUnique() {
		return shopUnique;
	}
	public void setShopUnique(Long shopUnique) {
		this.shopUnique = shopUnique;
	}
	public String getShopName() {
		return shopName;
	}
	public void setShopName(String shopName) {
		this.shopName = shopName;
	}
	public List<StaffPower> getStaffList() {
		return staffList;
	}
	public void setStaffList(List<StaffPower> staffList) {
		this.staffList = staffList;
	}
	public String getShopPhone() {
		return shopPhone;
	}
	public void setShopPhone(String shopPhone) {
		this.shopPhone = shopPhone;
	}
	public String getShopHours() {
		return shopHours;
	}
	public void setShopHours(String shopHours) {
		this.shopHours = shopHours;
	}
	public String getShopImagePath() {
		return shopImagePath;
	}
	public void setShopImagePath(String shopImagePath) {
		this.shopImagePath = shopImagePath;
	}

	public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public String getCurrencySymbol() {
		return currencySymbol;
	}

	public void setCurrencySymbol(String currencySymbol) {
		this.currencySymbol = currencySymbol;
	}

	@Override
	public String toString() {
		return "ShopManager [shopUnique=" + shopUnique + ", shopName=" + shopName + ", shopPhone=" + shopPhone
				+ ", shopHours=" + shopHours + ", shopImagePath=" + shopImagePath + ", staffList=" + staffList + "]";
	}
}


