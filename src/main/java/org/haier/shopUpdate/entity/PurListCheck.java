package org.haier.shopUpdate.entity;

import java.util.List;

public class PurListCheck {
	//订单ID
	private Integer purchase_list_id;
	//订单编号
	private String purchase_list_unique;
	//订单商品数量
	private Double purchase_list_sum;
	//订单总金额
	private Double purchase_list_total;
//	供货商名称	
	private String supplier_name;
	//订单备注
	private String purchase_list_remark;
	//供货商编码
	private String supplier_unique;
	//订单处理状态
	private String receipt_status;
	
	private String purchase_list_date;
	private List<PurListCheckDetail> listDetail;
	public Integer getPurchase_list_id() {
		return purchase_list_id;
	}
	public void setPurchase_list_id(Integer purchase_list_id) {
		this.purchase_list_id = purchase_list_id;
	}
	public String getPurchase_list_unique() {
		return purchase_list_unique;
	}
	public void setPurchase_list_unique(String purchase_list_unique) {
		this.purchase_list_unique = purchase_list_unique;
	}
	public Double getPurchase_list_sum() {
		return purchase_list_sum;
	}
	public void setPurchase_list_sum(Double purchase_list_sum) {
		this.purchase_list_sum = purchase_list_sum;
	}
	public Double getPurchase_list_total() {
		return purchase_list_total;
	}
	public void setPurchase_list_total(Double purchase_list_total) {
		this.purchase_list_total = purchase_list_total;
	}
	public String getSupplier_name() {
		return supplier_name;
	}
	public void setSupplier_name(String supplier_name) {
		this.supplier_name = supplier_name;
	}
	public String getPurchase_list_remark() {
		return purchase_list_remark;
	}
	public void setPurchase_list_remark(String purchase_list_remark) {
		this.purchase_list_remark = purchase_list_remark;
	}
	public String getSupplier_unique() {
		return supplier_unique;
	}
	public void setSupplier_unique(String supplier_unique) {
		this.supplier_unique = supplier_unique;
	}
	public String getReceipt_status() {
		return receipt_status;
	}
	public void setReceipt_status(String receipt_status) {
		this.receipt_status = receipt_status;
	}
	public List<PurListCheckDetail> getListDetail() {
		return listDetail;
	}
	public void setListDetail(List<PurListCheckDetail> listDetail) {
		this.listDetail = listDetail;
	}
	public String getPurchase_list_date() {
		return purchase_list_date;
	}
	public void setPurchase_list_date(String purchase_list_date) {
		this.purchase_list_date = purchase_list_date;
	}
	
	
}
