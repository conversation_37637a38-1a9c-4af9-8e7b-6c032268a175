package org.haier.shopUpdate.entity;

import java.util.List;

public class ShopTitleMain {
	private Long shopUnique;//店铺编号
	private Integer showType;//是否首页显示
	private List<ShopTitleDetail> list;//模块详情
	public Long getShopUnique() {
		return shopUnique;
	}
	public void setShopUnique(Long shopUnique) {
		this.shopUnique = shopUnique;
	}
	public Integer getShowType() {
		return showType;
	}
	public void setShowType(Integer showType) {
		this.showType = showType;
	}
	public List<ShopTitleDetail> getList() {
		return list;
	}
	public void setList(List<ShopTitleDetail> list) {
		this.list = list;
	}
	@Override
	public String toString() {
		return "ShopTitleMain [shopUnique=" + shopUnique + ", showType=" + showType + ", list=" + list + "]";
	}
}
