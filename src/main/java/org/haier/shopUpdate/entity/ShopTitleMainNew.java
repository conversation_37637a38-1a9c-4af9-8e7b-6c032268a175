package org.haier.shopUpdate.entity;

import java.util.List;
/**
 * 商家app 首页 模块工具
 * <AUTHOR>
 *
 */
public class ShopTitleMainNew {
	
	private Long shopUnique;//店铺编号
	//所属模块 1 店铺管理 2商品管理 3 其他
	private Integer modularType;//是否首页显示
	
	private String modularName;
	private List<ShopTitleDetail> list;//模块详情
	
	public String getModularName() {
		return modularName;
	}
	public void setModularName(String modularName) {
		this.modularName = modularName;
	}
	public Long getShopUnique() {
		return shopUnique;
	}
	public void setShopUnique(Long shopUnique) {
		this.shopUnique = shopUnique;
	}

	public Integer getModularType() {
		return modularType;
	}
	public void setModularType(Integer modularType) {
		this.modularType = modularType;
	}
	public List<ShopTitleDetail> getList() {
		return list;
	}
	public void setList(List<ShopTitleDetail> list) {
		this.list = list;
	}
	@Override
	public String toString() {
		return "ShopTitleMainNew [shopUnique=" + shopUnique + ", modularType=" + modularType + ", list=" + list + "]";
	}


	
	
}
