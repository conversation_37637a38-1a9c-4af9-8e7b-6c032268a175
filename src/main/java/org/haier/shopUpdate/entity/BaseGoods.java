package org.haier.shopUpdate.entity;
import java.math.BigDecimal;
import java.util.List;
public class BaseGoods {
	//商品ID
	private int goodsId;
	//商品库存数量
	private Double goodsCount;
	//保质期
	private Integer goodsLife;
	//最小单位商品编码
	private String baseBarcode;
	//商品图片路径
	private String goodsPicturepath;
	//商品分类（一级分类名称）
	private String groupsName;
	//商品一级分类编号
	private String groupsUnique;
	//商品二级分类名称
	private String kindName;
	//商品二级分类编号
	private String kindUnique;
	//商品三级分类名称
	private String threeName;
	//商品三级分类编号
	private String threeUnique;
	//基础商品基本单位
	private String goodsUnit;
	//商品品牌
	private String goodsBrand;
	//商品产地
	private String goodsAddress;
	//商品备注信息
	private String goodsRemarks;
	//商品销量
	private Double  goodsSold;
	//商品供货商编号
	private String supplierUnique;
	//供货商名称
	private String supplierName;
	//供货价
	private Double goodsPrice;
	//供货商供货条码
	private String supGoodsBarcode;
	//商品规格条码
	private List<GoodsPacking> listDetail;
	//包装外键
	private String foreignKey;
	//分辨是从是否已有商品
	private int tableType;
	//出入库时，是否本店商品
	private Integer stockTableType;
	private Integer goodsChengType;//商品称重类型；1、非称重商品（按件）；2、称重商品（按重量）；
	/**
	 * 批发价
	 */
	private BigDecimal wholesalePrice;
	/**
	 * 是否设为批发价 0 否 1，是
	 */
	private Integer wholesalePriceFlg;
	/**
	 * 起批数量
	 */
	private BigDecimal wholesaleCount;

	/**
	 * 货位
	 */
	private BigDecimal goodsPosition;
	
	public Double getGoodsCount() {
		return goodsCount;
	}
	public void setGoodsCount(Double goodsCount) {
		this.goodsCount = goodsCount;
	}

	public Integer getGoodsLife() {
		return goodsLife;
	}

	public void setGoodsLife(Integer goodsLife) {
		this.goodsLife = goodsLife;
	}

	public String getGroupsName() {
		return groupsName;
	}
	public void setGroupsName(String groupsName) {
		this.groupsName = groupsName;
	}
	public String getGroupsUnique() {
		return groupsUnique;
	}
	public void setGroupsUnique(String groupsUnique) {
		this.groupsUnique = groupsUnique;
	}
	public String getKindName() {
		return kindName;
	}
	public void setKindName(String kindName) {
		this.kindName = kindName;
	}
	public String getKindUnique() {
		return kindUnique;
	}
	public void setKindUnique(String kindUnique) {
		this.kindUnique = kindUnique;
	}
	public String getThreeName() {
		return threeName;
	}
	public void setThreeName(String threeName) {
		this.threeName = threeName;
	}
	public String getThreeUnique() {
		return threeUnique;
	}
	public void setThreeUnique(String threeUnique) {
		this.threeUnique =  threeUnique;
	}
	public String getGoodsBrand() {
		return goodsBrand;
	}
	public void setGoodsBrand(String goodsBrand) {
		this.goodsBrand = goodsBrand;
	}
	public String getGoodsAddress() {
		return goodsAddress;
	}
	public void setGoodsAddress(String goodsAddress) {
		this.goodsAddress = goodsAddress;
	}
	public String getGoodsRemarks() {
		return goodsRemarks;
	}
	public void setGoodsRemarks(String goodsRemarks) {
		this.goodsRemarks = goodsRemarks;
	}
	public Double getGoodsSold() {
		return goodsSold;
	}
	public void setGoodsSold(Double goodsSold) {
		this.goodsSold = goodsSold;
	}
	public String getSupplierUnique() {
		return supplierUnique;
	}
	public void setSupplierUnique(String supplierUnique) {
		this.supplierUnique = supplierUnique;
	}
	public String getSupplierName() {
		return supplierName;
	}
	public void setSupplierName(String supplierName) {
		this.supplierName = supplierName;
	}
	public Double getGoodsPrice() {
		return goodsPrice;
	}
	public void setGoodsPrice(Double goodsPrice) {
		this.goodsPrice = goodsPrice;
	}
	public String getSupGoodsBarcode() {
		return supGoodsBarcode;
	}
	public void setSupGoodsBarcode(String supGoodsBarcode) {
		this.supGoodsBarcode = supGoodsBarcode;
	}
	public List<GoodsPacking> getListDetail() {
		return listDetail;
	}
	public void setListDetail(List<GoodsPacking> listDetail) {
		this.listDetail = listDetail;
	}
	public String getGoodsPicturepath() {
		return goodsPicturepath;
	}
	public void setGoodsPicturepath(String goodsPicturepath) {
		this.goodsPicturepath = goodsPicturepath;
	}
	public int getGoodsId() {
		return goodsId;
	}
	public void setGoodsId(int goodsId) {
		this.goodsId = goodsId;
	}
	public String getForeignKey() {
		return foreignKey;
	}
	public void setForeignKey(String foreignKey) {
		this.foreignKey = foreignKey;
	}
	public String getBaseBarcode() {
		return baseBarcode;
	}
	public void setBaseBarcode(String baseBarcode) {
		this.baseBarcode = baseBarcode;
	}
	public String getGoodsUnit() {
		return goodsUnit;
	}
	public void setGoodsUnit(String goodsUnit) {
		this.goodsUnit = goodsUnit;
	}
	public int getTableType() {
		return tableType;
	}
	public void setTableType(int tableType) {
		this.tableType = tableType;
	}
	public Integer getStockTableType() {
		return stockTableType;
	}
	public void setStockTableType(Integer stockTableType) {
		this.stockTableType = stockTableType;
	}
	public Integer getGoodsChengType() {
		return goodsChengType;
	}
	public void setGoodsChengType(Integer goodsChengType) {
		this.goodsChengType = goodsChengType;
	}

	public BigDecimal getWholesalePrice() {
		return wholesalePrice;
	}

	public void setWholesalePrice(BigDecimal wholesalePrice) {
		this.wholesalePrice = wholesalePrice;
	}

	public Integer getWholesalePriceFlg() {
		return wholesalePriceFlg;
	}

	public void setWholesalePriceFlg(Integer wholesalePriceFlg) {
		this.wholesalePriceFlg = wholesalePriceFlg;
	}

	public BigDecimal getWholesaleCount() {
		return wholesaleCount;
	}

	public void setWholesaleCount(BigDecimal wholesaleCount) {
		this.wholesaleCount = wholesaleCount;
	}

	public BigDecimal getGoodsPosition() {
		return goodsPosition;
	}

	public void setGoodsPosition(BigDecimal goodsPosition) {
		this.goodsPosition = goodsPosition;
	}

}
