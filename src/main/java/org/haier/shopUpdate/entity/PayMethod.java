package org.haier.shopUpdate.entity;

public class PayMethod {
	//支付方式id
	private Integer saleListPayDetailId;
	//支付方式编号
	private Integer payMethodStatus;
	//支付方式名称
	private String payMethod;
	//支付金额
	private Double payMoney;
	public Integer getSaleListPayDetailId() {
		return saleListPayDetailId;
	}
	public void setSaleListPayDetailId(Integer saleListPayDetailId) {
		this.saleListPayDetailId = saleListPayDetailId;
	}
	public Integer getPayMethodStatus() {
		return payMethodStatus;
	}
	public void setPayMethodStatus(Integer payMethodStatus) {
		this.payMethodStatus = payMethodStatus;
	}
	public String getPayMethod() {
		return payMethod;
	}
	public void setPayMethod(String payMethod) {
		this.payMethod = payMethod;
	}
	public Double getPayMoney() {
		return payMoney;
	}
	public void setPayMoney(Double payMoney) {
		this.payMoney = payMoney;
	}
	@Override
	public String toString() {
		return "PayMethod [saleListPayDetailId=" + saleListPayDetailId + ", payMethodStatus=" + payMethodStatus
				+ ", payMethod=" + payMethod + ", payMoney=" + payMoney + "]";
	}
}	
