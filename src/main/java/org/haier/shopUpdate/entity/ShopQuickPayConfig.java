package org.haier.shopUpdate.entity;

import java.math.BigDecimal;

/**
 * 快捷支付配置
 */
public class ShopQuickPayConfig {
    //ID
    private Long id;
    //店铺编号
    private Long shopUnique;
    //快捷支付金额
    private BigDecimal amountMoney;
    //操作员工ID
    private Integer staffId;
    // 创建时间
    private String createDatetime;
    //更新时间
    private String updateDatetime;
    //是否有效:1、有效；0、无效
    private Integer validStatus;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public BigDecimal getAmountMoney() {
        return amountMoney;
    }

    public void setAmountMoney(BigDecimal amountMoney) {
        this.amountMoney = amountMoney;
    }

    public Integer getStaffId() {
        return staffId;
    }

    public void setStaffId(Integer staffId) {
        this.staffId = staffId;
    }

    public String getCreateDatetime() {
        return createDatetime;
    }

    public void setCreateDatetime(String createDatetime) {
        this.createDatetime = createDatetime;
    }

    public String getUpdateDatetime() {
        return updateDatetime;
    }

    public void setUpdateDatetime(String updateDatetime) {
        this.updateDatetime = updateDatetime;
    }

    public Integer getValidStatus() {
        return validStatus;
    }

    public void setValidStatus(Integer validStatus) {
        this.validStatus = validStatus;
    }
}