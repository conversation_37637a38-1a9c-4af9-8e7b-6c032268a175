package org.haier.shopUpdate.entity;

import java.util.Date;

/**
 * @description 盘点操作记录
 */
public class InventoryTaskOperateLogEntity {
    /**
    * id
    */
    private Long id;

    /**
    * inventory_task.id
    */
    private Long taskId;

    /**
    * 操作类型:1创建盘点任务2提交盘点3提交盘点任务
    */
    private Integer operateType;

    /**
    * 操作内容
    */
    private String operateLog;

    /**
     * 操作人
     */
    private Integer staffId;

    /**
    * 创建时间
    */
    private Date createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Integer getOperateType() {
        return operateType;
    }

    public void setOperateType(Integer operateType) {
        this.operateType = operateType;
    }

    public String getOperateLog() {
        return operateLog;
    }

    public void setOperateLog(String operateLog) {
        this.operateLog = operateLog;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getStaffId() {
        return staffId;
    }

    public void setStaffId(Integer staffId) {
        this.staffId = staffId;
    }
}