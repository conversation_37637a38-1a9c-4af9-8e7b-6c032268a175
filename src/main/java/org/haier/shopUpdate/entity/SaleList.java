package org.haier.shopUpdate.entity;

import java.util.List;

public class SaleList {
	//订单ID
	private Integer saleListId;
	//订单编号
	private Long saleListUnique;
	/**
	 * 退款单号
	 */
	private String retListUnique;
	//下单时间
	private String dateTime;
	//店铺编号
	private Long shopUnique;
	//订单总金额
	private Double saleListTotal;
	//订单总进货金额
	private Double saleListPur;
	//订单配送费
	private Double saleListDelfee;
	//订单商品总数量
	private Double totalCount;
	//订单类型：1、实体店订单；2、网单；3、微信订单；4、APP订单
	private String saleType;
	//顾客姓名
	private String saleListName;
	//员工名称
	private String staffName;
	//联系方式
	private String saleListPhone;
	//送货地址
	private String saleListAddress;
	//订单付款状态
	private String saleListState;
	//订单付款状态码
	private Integer saleListStateCode;
	//订单处理状态
	private String handleState;
	//订单处理状态码
	private Integer handleStateCode;
	//订单支付方式
	private String saleListPayment;
	//订单支付方式组合
	private List<PayMethod> payMethods;
	//订单支付方式状态码
	private Integer saleListPaymentCode;
	//订单备注
	private String saleListRemarks;
	//订单完成时间
	private String receiptDateTime;
	//订单发货时间
	private String sendDateTime;
	//订单序号
	private Integer saleListNumber;
	//订单收银员编号
	private Integer saleListCashier;
	//实际收银价格
	private Double actuallyReceived;
	//收银机编号
	private Integer machineNum;
	//配送方式
	private Integer shipping_method; 
	//配送方式
	private Integer delivery_type; 
	//配送费
	private Double peisong_money;
	//优惠券金额
	private Double coupon_amount;
	private Double card_deduction;
	private Double beans_money;
	private String delivery_status;
	private String cusProtrait; //用户头像
	private String driverName; //配送员姓名
	private String driverPhone; //配送员电话

	private String payTime; //支付时间
	
	public String getStaffName() {
		return staffName;
	}
	public void setStaffName(String staffName) {
		this.staffName = staffName;
	}
	public String getRetListUnique() {
		return retListUnique;
	}
	public void setRetListUnique(String retListUnique) {
		this.retListUnique = retListUnique;
	}
	public String getDelivery_status() {
		return delivery_status;
	}
	public void setDelivery_status(String delivery_status) {
		this.delivery_status = delivery_status;
	}
	public Double getCard_deduction() {
		return card_deduction;
	}
	public void setCard_deduction(Double card_deduction) {
		this.card_deduction = card_deduction;
	}
	public Double getBeans_money() {
		return beans_money;
	}
	public void setBeans_money(Double beans_money) {
		this.beans_money = beans_money;
	}
	public Double getCoupon_amount() {
		return coupon_amount;
	}
	public void setCoupon_amount(Double coupon_amount) {
		this.coupon_amount = coupon_amount;
	}
	public Double getPeisong_money() {
		return peisong_money;
	}
	public void setPeisong_money(Double peisong_money) {
		this.peisong_money = peisong_money;
	}
	//订单详情
	private List<SaleListDetail> listDetail;
	
	
	public Integer getDelivery_type() {
		return delivery_type;
	}
	public void setDelivery_type(Integer delivery_type) {
		this.delivery_type = delivery_type;
	}
	public Integer getShipping_method() {
		return shipping_method;
	}
	public void setShipping_method(Integer shipping_method) {
		this.shipping_method = shipping_method;
	}
	public Integer getSaleListId() {
		return saleListId;
	}
	public void setSaleListId(Integer saleListId) {
		this.saleListId = saleListId;
	}
	public Long getSaleListUnique() {
		return saleListUnique;
	}
	public void setSaleListUnique(Long saleListUnique) {
		this.saleListUnique = saleListUnique;
	}
	public String getDateTime() {
		return dateTime;
	}
	public void setDateTime(String dateTime) {
		this.dateTime = dateTime;
	}
	public Long getShopUnique() {
		return shopUnique;
	}
	public Double getSaleListDelfee() {
		return saleListDelfee;
	}
	public void setSaleListDelfee(Double saleListDelfee) {
		this.saleListDelfee = saleListDelfee;
	}
	public void setShopUnique(Long shopUnique) {
		this.shopUnique = shopUnique;
	}
	public Double getSaleListTotal() {
		return saleListTotal;
	}
	public void setSaleListTotal(Double saleListTotal) {
		this.saleListTotal = saleListTotal;
	}
	public Double getSaleListPur() {
		return saleListPur;
	}
	public void setSaleListPur(Double saleListPur) {
		this.saleListPur = saleListPur;
	}
	public Double getTotalCount() {
		return totalCount;
	}
	public void setTotalCount(Double totalCount) {
		this.totalCount = totalCount;
	}
	public String getSaleType() {
		return saleType;
	}
	public void setSaleType(String saleType) {
		this.saleType = saleType;
	}
	public String getSaleListName() {
		return saleListName;
	}
	public void setSaleListName(String saleListName) {
		this.saleListName = saleListName;
	}
	public String getSaleListPhone() {
		return saleListPhone;
	}
	public void setSaleListPhone(String saleListPhone) {
		this.saleListPhone = saleListPhone;
	}
	public String getSaleListAddress() {
		return saleListAddress;
	}
	public void setSaleListAddress(String saleListAddress) {
		this.saleListAddress = saleListAddress;
	}
	public String getSaleListState() {
		return saleListState;
	}
	public void setSaleListState(String saleListState) {
		this.saleListState = saleListState;
	}
	public String getHandleState() {
		return handleState;
	}
	public void setHandleState(String handleState) {
		this.handleState = handleState;
	}
	public Integer getHandleStateCode() {
		return handleStateCode;
	}
	public void setHandleStateCode(Integer handleStateCode) {
		this.handleStateCode = handleStateCode;
	}
	public String getSaleListPayment() {
		return saleListPayment;
	}
	public void setSaleListPayment(String saleListPayment) {
		this.saleListPayment = saleListPayment;
	}
	public Integer getSaleListPaymentCode() {
		return saleListPaymentCode;
	}
	public void setSaleListPaymentCode(Integer saleListPaymentCode) {
		this.saleListPaymentCode = saleListPaymentCode;
	}
	public String getSaleListRemarks() {
		return saleListRemarks;
	}
	public void setSaleListRemarks(String saleListRemarks) {
		this.saleListRemarks = saleListRemarks;
	}
	public String getReceiptDateTime() {
		return receiptDateTime;
	}
	public void setReceiptDateTime(String receiptDateTime) {
		this.receiptDateTime = receiptDateTime;
	}
	public String getSendDateTime() {
		return sendDateTime;
	}
	public void setSendDateTime(String sendDateTime) {
		this.sendDateTime = sendDateTime;
	}
	public Integer getSaleListNumber() {
		return saleListNumber;
	}
	public void setSaleListNumber(Integer saleListNumber) {
		this.saleListNumber = saleListNumber;
	}
	public Double getActuallyReceived() {
		return actuallyReceived;
	}
	public void setActuallyReceived(Double actuallyReceived) {
		this.actuallyReceived = actuallyReceived;
	}
	public Integer getMachineNum() {
		return machineNum;
	}
	public void setMachineNum(Integer machineNum) {
		this.machineNum = machineNum;
	}
	public Integer getSaleListStateCode() {
		return saleListStateCode;
	}
	public void setSaleListStateCode(Integer saleListStateCode) {
		this.saleListStateCode = saleListStateCode;
	}
	public Integer getSaleListCashier() {
		return saleListCashier;
	}
	public void setSaleListCashier(Integer saleListCashier) {
		this.saleListCashier = saleListCashier;
	}
	public List<SaleListDetail> getListDetail() {
		return listDetail;
	}
	public void setListDetail(List<SaleListDetail> listDetail) {
		this.listDetail = listDetail;
	}
	public List<PayMethod> getPayMethods() {
		return payMethods;
	}
	public void setPayMethods(List<PayMethod> payMethods) {
		this.payMethods = payMethods;
	}

	public String getCusProtrait() {
		return cusProtrait;
	}

	public void setCusProtrait(String cusProtrait) {
		this.cusProtrait = cusProtrait;
	}

	public String getDriverName() {
		return driverName;
	}

	public void setDriverName(String driverName) {
		this.driverName = driverName;
	}

	public String getDriverPhone() {
		return driverPhone;
	}

	public void setDriverPhone(String driverPhone) {
		this.driverPhone = driverPhone;
	}

	public String getPayTime() {
		return payTime;
	}

	public void setPayTime(String payTime) {
		this.payTime = payTime;
	}

	@Override
	public String toString() {
		return "SaleList{" +
				"saleListId=" + saleListId +
				", saleListUnique=" + saleListUnique +
				", retListUnique='" + retListUnique + '\'' +
				", dateTime='" + dateTime + '\'' +
				", shopUnique=" + shopUnique +
				", saleListTotal=" + saleListTotal +
				", saleListPur=" + saleListPur +
				", saleListDelfee=" + saleListDelfee +
				", totalCount=" + totalCount +
				", saleType='" + saleType + '\'' +
				", saleListName='" + saleListName + '\'' +
				", saleListPhone='" + saleListPhone + '\'' +
				", saleListAddress='" + saleListAddress + '\'' +
				", saleListState='" + saleListState + '\'' +
				", saleListStateCode=" + saleListStateCode +
				", handleState='" + handleState + '\'' +
				", handleStateCode=" + handleStateCode +
				", saleListPayment='" + saleListPayment + '\'' +
				", payMethods=" + payMethods +
				", saleListPaymentCode=" + saleListPaymentCode +
				", saleListRemarks='" + saleListRemarks + '\'' +
				", receiptDateTime='" + receiptDateTime + '\'' +
				", sendDateTime='" + sendDateTime + '\'' +
				", saleListNumber=" + saleListNumber +
				", saleListCashier=" + saleListCashier +
				", actuallyReceived=" + actuallyReceived +
				", machineNum=" + machineNum +
				", shipping_method=" + shipping_method +
				", delivery_type=" + delivery_type +
				", peisong_money=" + peisong_money +
				", coupon_amount=" + coupon_amount +
				", card_deduction=" + card_deduction +
				", beans_money=" + beans_money +
				", delivery_status='" + delivery_status + '\'' +
				", cusProtrait='" + cusProtrait + '\'' +
				", driverName='" + driverName + '\'' +
				", driverPhone='" + driverPhone + '\'' +
				", payTime='" + payTime + '\'' +
				", listDetail=" + listDetail +
				'}';
	}
}
