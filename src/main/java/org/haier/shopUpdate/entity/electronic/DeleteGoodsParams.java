package org.haier.shopUpdate.entity.electronic;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import java.io.Serializable;
import java.util.List;


@Data
public class DeleteGoodsParams implements Serializable {
    @NotBlank(message = "店铺编号不能为空")
    private String shopUnique;
    @NotEmpty(message = "商品编号不能为空")
    //商品编号
    private List<String > goodsBarcodes;
}
