package org.haier.shopUpdate.entity.electronic;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class BaseGoodsMsg implements Serializable {
    public static final long serialVersionUID = 1L;
    //店铺唯一标识，必传
    private String shopUnique;
    //商品编码，必传
    private String goodsBarcode;
    //商品名称，必传
    private String goodsName;
    //商品规格，非必传
    private String goodsStandard;
    //商品登记，等级为1-5级，默认5
    private Integer level;
    //商品单位，非必传
    private String goodsUnit;
    //销售价，必传
    private BigDecimal goodsSalesPrice;
    //会员价，非必传
    private BigDecimal goodsCusPrice;
    //商品原价，就是销售价，非必传
    private BigDecimal originalPrice;
    //商品产地，限制10个汉字以内，非必传
    private String goodsAddress;
    //商品制造商，非必传，设置为品牌
    private String goodsBrand;
    //促销场景，非必传
    private Integer promotion;
    //商品图片地址，非必传,必须是外网可访问的全路径
    private String goodsPicturepath;
    //商品二维码地址，非必传，全路径地址
    private String pqr;
}
