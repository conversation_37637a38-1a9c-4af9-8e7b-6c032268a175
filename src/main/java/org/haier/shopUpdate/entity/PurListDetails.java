package org.haier.shopUpdate.entity;

public class PurListDetails {
	private Integer purchase_list_detail_id;
	private String purchase_list_unique;
	private String purchase_list_parunique;
	private String goods_name;
	private String goods_barcode;
	private String goods_standard;
	private Double sub_total;
	private Integer gift_type;
	private Double purchase_list_detail_count;
	private Double purchase_list_detail_price;
//	private String supplier_unique;
	private String goods_picturepath;
	
	public String getGoods_standard() {
		return goods_standard;
	}
	public void setGoods_standard(String goods_standard) {
		this.goods_standard = goods_standard;
	}
	public Integer getGift_type() {
		return gift_type;
	}
	public void setGift_type(Integer gift_type) {
		this.gift_type = gift_type;
	}
	public Integer getPurchase_list_detail_id() {
		return purchase_list_detail_id;
	}
	public void setPurchase_list_detail_id(Integer purchase_list_detail_id) {
		this.purchase_list_detail_id = purchase_list_detail_id;
	}
	public String getPurchase_list_unique() {
		return purchase_list_unique;
	}
	public void setPurchase_list_unique(String purchase_list_unique) {
		this.purchase_list_unique = purchase_list_unique;
	}
	public String getGoods_name() {
		return goods_name;
	}
	public void setGoods_name(String goods_name) {
		this.goods_name = goods_name;
	}
	public String getGoods_barcode() {
		return goods_barcode;
	}
	public void setGoods_barcode(String goods_barcode) {
		this.goods_barcode = goods_barcode;
	}
	public Double getPurchase_list_detail_count() {
		return purchase_list_detail_count;
	}
	public void setPurchase_list_detail_count(Double purchase_list_detail_count) {
		this.purchase_list_detail_count = purchase_list_detail_count;
	}
	public Double getPurchase_list_detail_price() {
		return purchase_list_detail_price;
	}
	public void setPurchase_list_detail_price(Double purchase_list_detail_price) {
		this.purchase_list_detail_price = purchase_list_detail_price;
	}
	public String getPurchase_list_parunique() {
		return purchase_list_parunique;
	}
	public void setPurchase_list_parunique(String purchase_list_parunique) {
		this.purchase_list_parunique = purchase_list_parunique;
	}
//	public String getSupplier_unique() {
//		return supplier_unique;
//	}
//	public void setSupplier_unique(String supplier_unique) {
//		this.supplier_unique = supplier_unique;
//	}
	
	public String getGoods_picturepath() {
		return goods_picturepath;
	}
	public Double getSub_total() {
		return sub_total;
	}
	public void setSub_total(Double sub_total) {
		this.sub_total = sub_total;
	}
	public void setGoods_picturepath(String goods_picturepath) {
		this.goods_picturepath = goods_picturepath;
	}
	
}
