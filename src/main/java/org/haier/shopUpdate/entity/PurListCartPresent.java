package org.haier.shopUpdate.entity;

public class PurListCartPresent {
	//赠品编号
	private String pGoodsBarcode;
	//赠品名称
	private String pGoodsName;
	//赠品数量
	private String pGoodsCount;
	//赠品单击
	private Double pGoodsPrice;
	//赠品选中状态
	private Integer pGoodsChecked;
	//赠品规格
	private String pGoodsStandard;
	//赠品图片
	private String pGoodsPicturePath;
	
	public String getpGoodsPicturePath() {
		return pGoodsPicturePath;
	}
	public void setpGoodsPicturePath(String pGoodsPicturePath) {
		this.pGoodsPicturePath = pGoodsPicturePath;
	}
	public String getpGoodsBarcode() {
		return pGoodsBarcode;
	}
	public void setpGoodsBarcode(String pGoodsBarcode) {
		this.pGoodsBarcode = pGoodsBarcode;
	}
	public String getpGoodsName() {
		return pGoodsName;
	}
	public void setpGoodsName(String pGoodsName) {
		this.pGoodsName = pGoodsName;
	}
	public String getpGoodsCount() {
		return pGoodsCount;
	}
	public void setpGoodsCount(String pGoodsCount) {
		this.pGoodsCount = pGoodsCount;
	}
	public Double getpGoodsPrice() {
		return pGoodsPrice;
	}
	public void setpGoodsPrice(Double pGoodsPrice) {
		this.pGoodsPrice = pGoodsPrice;
	}
	public Integer getpGoodsChecked() {
		return pGoodsChecked;
	}
	public void setpGoodsChecked(Integer pGoodsChecked) {
		this.pGoodsChecked = pGoodsChecked;
	}
	public String getpGoodsStandard() {
		return pGoodsStandard;
	}
	public void setpGoodsStandard(String pGoodsStandard) {
		this.pGoodsStandard = pGoodsStandard;
	}
	
}
