package org.haier.shopUpdate.entity.promotion;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 促销商品满级活动
 */
public class PromotionGoodsMarkdown extends PromotionGoodsBase implements Serializable {
    private static final long serialVersionUID = 1L;
    //优惠条件1
    private BigDecimal meetCount1;
    //优惠折扣1
    private BigDecimal discountPercent1;
    //优惠条件2
    private BigDecimal meetCount2;
    //优惠折扣2
    private BigDecimal discountPercent2;
    //优惠条件3
    private BigDecimal meetCount3;
    //优惠折扣3
    private BigDecimal discountPercent3;
    //每单限制购买的数量，超过该上限，按原价
    private Integer limitCount;

    public BigDecimal getMeetCount1() {
        return meetCount1;
    }

    public void setMeetCount1(BigDecimal meetCount1) {
        this.meetCount1 = meetCount1;
    }

    public BigDecimal getDiscountPercent1() {
        return discountPercent1;
    }

    public void setDiscountPercent1(BigDecimal discountPercent1) {
        this.discountPercent1 = discountPercent1;
    }

    public BigDecimal getMeetCount2() {
        return meetCount2;
    }

    public void setMeetCount2(BigDecimal meetCount2) {
        this.meetCount2 = meetCount2;
    }

    public BigDecimal getDiscountPercent2() {
        return discountPercent2;
    }

    public void setDiscountPercent2(BigDecimal discountPercent2) {
        this.discountPercent2 = discountPercent2;
    }

    public BigDecimal getMeetCount3() {
        return meetCount3;
    }

    public void setMeetCount3(BigDecimal meetCount3) {
        this.meetCount3 = meetCount3;
    }

    public BigDecimal getDiscountPercent3() {
        return discountPercent3;
    }

    public void setDiscountPercent3(BigDecimal discountPercent3) {
        this.discountPercent3 = discountPercent3;
    }

    public Integer getLimitCount() {
        return limitCount;
    }

    public void setLimitCount(Integer limitCount) {
        this.limitCount = limitCount;
    }
}
