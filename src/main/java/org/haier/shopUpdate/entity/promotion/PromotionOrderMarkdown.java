package org.haier.shopUpdate.entity.promotion;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 订单促销
 */
public class PromotionOrderMarkdown implements Serializable {
    private static final long serialVersionUID = 1L;
    private Integer detailId;
    //促销店铺编号
    private Long shopUnique;
    //满足金额1
    private BigDecimal meetPrice1;
    //优惠金额1
    private BigDecimal discountPrice1;
    //赠送商品ID1
    private Long giftGoodsId1;
    //赠送商品条码1
    private String giftGoodsBarcode1;
    //赠送商品名称1
    private String giftGoodsName1;
    //赠送商品数量1
    private Integer giftCount1;
    //满足金额2
    private BigDecimal meetPrice2;
    //优惠金额2
    private BigDecimal discountPrice2;
    //赠送商品ID2
    private Long giftGoodsId2;
    //赠送商品条码2
    private String giftGoodsBarcode2;
    //赠送商品名称2
    private String giftGoodsName2;
    //赠送商品数量2
    private Integer giftCount2;
    //满足金额3
    private BigDecimal meetPrice3;
    //优惠金额3
    private BigDecimal discountPrice3;
    //赠送商品ID3
    private Long giftGoodsId3;
    //赠送商品条码3
    private String giftGoodsBarcode3;
    //赠送商品名称3
    private String giftGoodsName3;
    //赠送商品数量3
    private Integer giftCount3;

    public Integer getDetailId() {
        return detailId;
    }

    public void setDetailId(Integer detailId) {
        this.detailId = detailId;
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public BigDecimal getMeetPrice1() {
        return meetPrice1;
    }

    public void setMeetPrice1(BigDecimal meetPrice1) {
        this.meetPrice1 = meetPrice1;
    }

    public BigDecimal getDiscountPrice1() {
        return discountPrice1;
    }

    public void setDiscountPrice1(BigDecimal discountPrice1) {
        this.discountPrice1 = discountPrice1;
    }

    public Long getGiftGoodsId1() {
        return giftGoodsId1;
    }

    public void setGiftGoodsId1(Long giftGoodsId1) {
        this.giftGoodsId1 = giftGoodsId1;
    }

    public String getGiftGoodsBarcode1() {
        return giftGoodsBarcode1;
    }

    public void setGiftGoodsBarcode1(String giftGoodsBarcode1) {
        this.giftGoodsBarcode1 = giftGoodsBarcode1;
    }

    public String getGiftGoodsName1() {
        return giftGoodsName1;
    }

    public void setGiftGoodsName1(String giftGoodsName1) {
        this.giftGoodsName1 = giftGoodsName1;
    }

    public Integer getGiftCount1() {
        return giftCount1;
    }

    public void setGiftCount1(Integer giftCount1) {
        this.giftCount1 = giftCount1;
    }

    public BigDecimal getMeetPrice2() {
        return meetPrice2;
    }

    public void setMeetPrice2(BigDecimal meetPrice2) {
        this.meetPrice2 = meetPrice2;
    }

    public BigDecimal getDiscountPrice2() {
        return discountPrice2;
    }

    public void setDiscountPrice2(BigDecimal discountPrice2) {
        this.discountPrice2 = discountPrice2;
    }

    public Long getGiftGoodsId2() {
        return giftGoodsId2;
    }

    public void setGiftGoodsId2(Long giftGoodsId2) {
        this.giftGoodsId2 = giftGoodsId2;
    }

    public String getGiftGoodsBarcode2() {
        return giftGoodsBarcode2;
    }

    public void setGiftGoodsBarcode2(String giftGoodsBarcode2) {
        this.giftGoodsBarcode2 = giftGoodsBarcode2;
    }

    public String getGiftGoodsName2() {
        return giftGoodsName2;
    }

    public void setGiftGoodsName2(String giftGoodsName2) {
        this.giftGoodsName2 = giftGoodsName2;
    }

    public Integer getGiftCount2() {
        return giftCount2;
    }

    public void setGiftCount2(Integer giftCount2) {
        this.giftCount2 = giftCount2;
    }

    public BigDecimal getMeetPrice3() {
        return meetPrice3;
    }

    public void setMeetPrice3(BigDecimal meetPrice3) {
        this.meetPrice3 = meetPrice3;
    }

    public BigDecimal getDiscountPrice3() {
        return discountPrice3;
    }

    public void setDiscountPrice3(BigDecimal discountPrice3) {
        this.discountPrice3 = discountPrice3;
    }

    public Long getGiftGoodsId3() {
        return giftGoodsId3;
    }

    public void setGiftGoodsId3(Long giftGoodsId3) {
        this.giftGoodsId3 = giftGoodsId3;
    }

    public String getGiftGoodsBarcode3() {
        return giftGoodsBarcode3;
    }

    public void setGiftGoodsBarcode3(String giftGoodsBarcode3) {
        this.giftGoodsBarcode3 = giftGoodsBarcode3;
    }

    public String getGiftGoodsName3() {
        return giftGoodsName3;
    }

    public void setGiftGoodsName3(String giftGoodsName3) {
        this.giftGoodsName3 = giftGoodsName3;
    }

    public Integer getGiftCount3() {
        return giftCount3;
    }

    public void setGiftCount3(Integer giftCount3) {
        this.giftCount3 = giftCount3;
    }
}
