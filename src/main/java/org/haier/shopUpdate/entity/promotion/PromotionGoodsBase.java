package org.haier.shopUpdate.entity.promotion;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 促销商品基本信息
 */
public class PromotionGoodsBase implements Serializable {
    //促销详情ID
    private Integer detailId;
    //商品ID
    private Long goodsId;
    //商品名称
    private String goodsName;
    //商品条码
    private String goodsBarcode;
    //商品当前售价
    private BigDecimal salePrice;

    public Integer getDetailId() {
        return detailId;
    }

    public void setDetailId(Integer detailId) {
        this.detailId = detailId;
    }

    public Long getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Long goodsId) {
        this.goodsId = goodsId;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public BigDecimal getSalePrice() {
        return salePrice;
    }

    public void setSalePrice(BigDecimal salePrice) {
        this.salePrice = salePrice;
    }
}
