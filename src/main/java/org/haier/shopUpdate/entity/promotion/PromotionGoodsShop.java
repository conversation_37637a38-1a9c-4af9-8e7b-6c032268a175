package org.haier.shopUpdate.entity.promotion;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 单片促销
 */
public class PromotionGoodsShop extends PromotionGoodsBase implements Serializable {
    //促销商品价格
    private Double promotionPrice;
    //促销商品数量
    private BigDecimal totalCount;
    //每人每日限购数量
    private BigDecimal perCount;
    //促销商品总数量
    private BigDecimal allCount;

    public Double getPromotionPrice() {
        return promotionPrice;
    }

    public void setPromotionPrice(Double promotionPrice) {
        this.promotionPrice = promotionPrice;
    }

    public BigDecimal getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(BigDecimal totalCount) {
        this.totalCount = totalCount;
    }

    public BigDecimal getPerCount() {
        return perCount;
    }

    public void setPerCount(BigDecimal perCount) {
        this.perCount = perCount;
    }

    public BigDecimal getAllCount() {
        return allCount;
    }

    public void setAllCount(BigDecimal allCount) {
        this.allCount = allCount;
    }
}
