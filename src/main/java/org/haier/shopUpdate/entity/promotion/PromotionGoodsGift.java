package org.haier.shopUpdate.entity.promotion;

import java.io.Serializable;

/**
 * 促销商品满赠活动
 */
public class PromotionGoodsGift extends PromotionGoodsBase implements Serializable {
    //赠送的商品ID1
    private Long goodsIdGift1;
    //赠送的商品ID2
    private Long goodsIdGift2;
    //赠送的商品ID3
    private Long goodsIdGift3;
    //赠送的商品条码1
    private String goodsBarcodeGift1;
    //赠送的商品条码2
    private String goodsBarcodeGift2;
    //赠送的商品条码3
    private String goodsBarcodeGift3;
    //赠送商品名称1
    private String goodsNameGift1;
    //赠送商品名称2
    private String goodsNameGift2;
    //赠送商品名称3
    private String goodsNameGift3;
    //赠送条件1
    private Integer meetCount1;
    //赠送数量1
    private Integer giftCount1;
    //赠送条件2
    private Integer meetCount2;
    //赠送数量2
    private Integer giftCount2;
    //赠送条件3
    private Integer meetCount3;
    //赠送数量3
    private Integer giftCount3;

    public Long getGoodsIdGift1() {
        return goodsIdGift1;
    }

    public void setGoodsIdGift1(Long goodsIdGift1) {
        this.goodsIdGift1 = goodsIdGift1;
    }

    public Long getGoodsIdGift2() {
        return goodsIdGift2;
    }

    public void setGoodsIdGift2(Long goodsIdGift2) {
        this.goodsIdGift2 = goodsIdGift2;
    }

    public Long getGoodsIdGift3() {
        return goodsIdGift3;
    }

    public void setGoodsIdGift3(Long goodsIdGift3) {
        this.goodsIdGift3 = goodsIdGift3;
    }

    public String getGoodsBarcodeGift1() {
        return goodsBarcodeGift1;
    }

    public void setGoodsBarcodeGift1(String goodsBarcodeGift1) {
        this.goodsBarcodeGift1 = goodsBarcodeGift1;
    }

    public String getGoodsBarcodeGift2() {
        return goodsBarcodeGift2;
    }

    public void setGoodsBarcodeGift2(String goodsBarcodeGift2) {
        this.goodsBarcodeGift2 = goodsBarcodeGift2;
    }

    public String getGoodsBarcodeGift3() {
        return goodsBarcodeGift3;
    }

    public void setGoodsBarcodeGift3(String goodsBarcodeGift3) {
        this.goodsBarcodeGift3 = goodsBarcodeGift3;
    }

    public String getGoodsNameGift1() {
        return goodsNameGift1;
    }

    public void setGoodsNameGift1(String goodsNameGift1) {
        this.goodsNameGift1 = goodsNameGift1;
    }

    public String getGoodsNameGift2() {
        return goodsNameGift2;
    }

    public void setGoodsNameGift2(String goodsNameGift2) {
        this.goodsNameGift2 = goodsNameGift2;
    }

    public String getGoodsNameGift3() {
        return goodsNameGift3;
    }

    public void setGoodsNameGift3(String goodsNameGift3) {
        this.goodsNameGift3 = goodsNameGift3;
    }

    public Integer getMeetCount1() {
        return meetCount1;
    }

    public void setMeetCount1(Integer meetCount1) {
        this.meetCount1 = meetCount1;
    }

    public Integer getGiftCount1() {
        return giftCount1;
    }

    public void setGiftCount1(Integer giftCount1) {
        this.giftCount1 = giftCount1;
    }

    public Integer getMeetCount2() {
        return meetCount2;
    }

    public void setMeetCount2(Integer meetCount2) {
        this.meetCount2 = meetCount2;
    }

    public Integer getGiftCount2() {
        return giftCount2;
    }

    public void setGiftCount2(Integer giftCount2) {
        this.giftCount2 = giftCount2;
    }

    public Integer getMeetCount3() {
        return meetCount3;
    }

    public void setMeetCount3(Integer meetCount3) {
        this.meetCount3 = meetCount3;
    }

    public Integer getGiftCount3() {
        return giftCount3;
    }

    public void setGiftCount3(Integer giftCount3) {
        this.giftCount3 = giftCount3;
    }
}
