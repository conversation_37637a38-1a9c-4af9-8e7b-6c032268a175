package org.haier.shopUpdate.entity.promotion;

import java.io.Serializable;
import java.util.List;

/**
 * 促销活动列表
 */
public class PromotionActivity implements Serializable {
    //促销活动ID
    private Integer promotionActivityId;
    //促销活动名称
    private String promotionActivityName;
    //促销商品是否同步参加订单促销：0、不参与；1、参与；
    private Integer orderActivity;
    //活动创建时间
    private String createTime;
    //活动开始时间
    private String startTime;
    //活动结束时间
    private String endTime;
    //活动类型：1、商品折扣；2、商品满赠；3、订单促销；4、单片促销；
    private Integer type;
    //促销的店铺编号
    private Long shopUnique;
    //活动投放范围
    private Integer activityRange;
    //活动状态：1、PC收银机；2、一刻钟到家小程序；
    private Integer status;
    //会员价是否参与活动：1、是；0、否
    private Integer cusActivity;
    //商品满赠列表
    private List<PromotionGoodsGift> promotionGoodsGiftList;
    //商品第几件打几折
    private List<PromotionGoodsSingle> promotionGoodsSingleList;
    //商品满减列表
    private List<PromotionGoodsMarkdown> promotionGoodsMarkdownList;
    //订单折扣列表
    private List<PromotionOrderMarkdown> promotionOrderMarkdownList;

    public Integer getPromotionActivityId() {
        return promotionActivityId;
    }

    public void setPromotionActivityId(Integer promotionActivityId) {
        this.promotionActivityId = promotionActivityId;
    }

    public String getPromotionActivityName() {
        return promotionActivityName;
    }

    public void setPromotionActivityName(String promotionActivityName) {
        this.promotionActivityName = promotionActivityName;
    }

    public Integer getOrderActivity() {
        return orderActivity;
    }

    public void setOrderActivity(Integer orderActivity) {
        this.orderActivity = orderActivity;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public Integer getActivityRange() {
        return activityRange;
    }

    public void setActivityRange(Integer activityRange) {
        this.activityRange = activityRange;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getCusActivity() {
        return cusActivity;
    }

    public void setCusActivity(Integer cusActivity) {
        this.cusActivity = cusActivity;
    }

    public List<PromotionGoodsGift> getPromotionGoodsGiftList() {
        return promotionGoodsGiftList;
    }

    public void setPromotionGoodsGiftList(List<PromotionGoodsGift> promotionGoodsGiftList) {
        this.promotionGoodsGiftList = promotionGoodsGiftList;
    }

    public List<PromotionGoodsSingle> getPromotionGoodsSingleList() {
        return promotionGoodsSingleList;
    }

    public void setPromotionGoodsSingleList(List<PromotionGoodsSingle> promotionGoodsSingleList) {
        this.promotionGoodsSingleList = promotionGoodsSingleList;
    }

    public List<PromotionGoodsMarkdown> getPromotionGoodsMarkdownList() {
        return promotionGoodsMarkdownList;
    }

    public void setPromotionGoodsMarkdownList(List<PromotionGoodsMarkdown> promotionGoodsMarkdownList) {
        this.promotionGoodsMarkdownList = promotionGoodsMarkdownList;
    }

    public List<PromotionOrderMarkdown> getPromotionOrderMarkdownList() {
        return promotionOrderMarkdownList;
    }

    public void setPromotionOrderMarkdownList(List<PromotionOrderMarkdown> promotionOrderMarkdownList) {
        this.promotionOrderMarkdownList = promotionOrderMarkdownList;
    }
}
