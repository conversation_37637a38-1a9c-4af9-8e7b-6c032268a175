package org.haier.shopUpdate.entity.promotion;

import java.io.Serializable;

/**
 * 单品打折促销
 */
public class PromotionGoodsSingle extends PromotionGoodsBase implements Serializable {
    //第几件
    private Integer number1;
    //折扣
    private Double discountPercent1;
    //满几件
    private Integer number2;
    //打几折
    private Double discountPercent2;
    //满几件
    private Integer number3;
    //打几折
    private Double discountPercent3;

    public Integer getNumber1() {
        return number1;
    }

    public void setNumber1(Integer number1) {
        this.number1 = number1;
    }

    public Double getDiscountPercent1() {
        return discountPercent1;
    }

    public void setDiscountPercent1(Double discountPercent1) {
        this.discountPercent1 = discountPercent1;
    }

    public Integer getNumber2() {
        return number2;
    }

    public void setNumber2(Integer number2) {
        this.number2 = number2;
    }

    public Double getDiscountPercent2() {
        return discountPercent2;
    }

    public void setDiscountPercent2(Double discountPercent2) {
        this.discountPercent2 = discountPercent2;
    }

    public Integer getNumber3() {
        return number3;
    }

    public void setNumber3(Integer number3) {
        this.number3 = number3;
    }

    public Double getDiscountPercent3() {
        return discountPercent3;
    }

    public void setDiscountPercent3(Double discountPercent3) {
        this.discountPercent3 = discountPercent3;
    }
}
