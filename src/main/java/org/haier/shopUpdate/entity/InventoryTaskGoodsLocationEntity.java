package org.haier.shopUpdate.entity;

import java.util.Date;

/**
 * @description 盘点货位
 */
public class InventoryTaskGoodsLocationEntity {

    /**
    * id
    */
    private Long id;

    /**
    * 员工编号
    */
    private Integer stafferId;

    /**
    * 货位名称
    */
    private String locationName;

    /**
    * 创建时间
    */
    private Date createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getStafferId() {
        return stafferId;
    }

    public void setStafferId(Integer stafferId) {
        this.stafferId = stafferId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}