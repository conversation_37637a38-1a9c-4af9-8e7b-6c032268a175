package org.haier.shopUpdate.entity.ret;
/**
* @author: 作者:王恩龙
* @version: 2022年11月17日 上午10:49:53
*
*/
public class ReturnDetail {
	private Integer retListDetailId;
	private String saleListUnique;
	private String retListUnique;
	private String goodsBarcode;
	private String goodsName;
	private Double retListDetailCount;
	private Double retListDetailPrice;
	private Double retListOriginPrice;
	private Double saleListDetailId;
	private String goodsPicturepath;
	public Integer getRetListDetailId() {
		return retListDetailId;
	}
	public void setRetListDetailId(Integer retListDetailId) {
		this.retListDetailId = retListDetailId;
	}
	public String getSaleListUnique() {
		return saleListUnique;
	}
	public void setSaleListUnique(String saleListUnique) {
		this.saleListUnique = saleListUnique;
	}
	public String getRetListUnique() {
		return retListUnique;
	}
	public void setRetListUnique(String retListUnique) {
		this.retListUnique = retListUnique;
	}
	public String getGoodsBarcode() {
		return goodsBarcode;
	}
	public void setGoodsBarcode(String goodsBarcode) {
		this.goodsBarcode = goodsBarcode;
	}
	public String getGoodsName() {
		return goodsName;
	}
	public void setGoodsName(String goodsName) {
		this.goodsName = goodsName;
	}
	public Double getRetListDetailCount() {
		return retListDetailCount;
	}
	public void setRetListDetailCount(Double retListDetailCount) {
		this.retListDetailCount = retListDetailCount;
	}
	public Double getRetListDetailPrice() {
		return retListDetailPrice;
	}
	public void setRetListDetailPrice(Double retListDetailPrice) {
		this.retListDetailPrice = retListDetailPrice;
	}
	public Double getRetListOriginPrice() {
		return retListOriginPrice;
	}
	public void setRetListOriginPrice(Double retListOriginPrice) {
		this.retListOriginPrice = retListOriginPrice;
	}
	public Double getSaleListDetailId() {
		return saleListDetailId;
	}
	public void setSaleListDetailId(Double saleListDetailId) {
		this.saleListDetailId = saleListDetailId;
	}
	public String getGoodsPicturepath() {
		return goodsPicturepath;
	}
	public void setGoodsPicturepath(String goodsPicturepath) {
		this.goodsPicturepath = goodsPicturepath;
	}
}
