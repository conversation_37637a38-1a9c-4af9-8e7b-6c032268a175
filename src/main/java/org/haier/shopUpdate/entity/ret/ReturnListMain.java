package org.haier.shopUpdate.entity.ret;

import java.util.List;

/**
* @author: 作者:王恩龙
* @version: 2022年11月17日 上午10:49:41
*
*/
public class ReturnListMain {
	private Integer id;
	private String saleListUnique;
	private String cusName;
	private String cusProtrait;
	private String shopUnique;
	private String retListDatetime;
	private Double retListTotal;
	private Double retListCount;
	private Integer retListState;
	private String retListStateMsg;
	private Integer retListHandlestate;
	private String retListHandlestateMsg;
	private String retListRemarks;
	private String staffId;
	private String macId;
	private Integer retOrigin;
	private String retOriginMsg;
	private Integer retMoneyType;
	private String retMoneyTypeMsg;
	private Double retListTotalMoney;
	private Double retListOriginTotal;
	private String outRefundNo;
	private String retBackDatetime;
	private String retListUnique;
	private Integer retListBean;
	private String retListReason;
	private Double retListDelfee;
	private String saleListPhone;
	private String saleListAddress;
	
	private List<ReturnDetail> goodsList;
	private List<ReturnPayDetail> payList;
	
	
	
	public String getSaleListPhone() {
		return saleListPhone;
	}
	public void setSaleListPhone(String saleListPhone) {
		this.saleListPhone = saleListPhone;
	}
	public String getSaleListAddress() {
		return saleListAddress;
	}
	public void setSaleListAddress(String saleListAddress) {
		this.saleListAddress = saleListAddress;
	}
	public String getCusName() {
		return cusName;
	}
	public void setCusName(String cusName) {
		this.cusName = cusName;
	}
	public String getCusProtrait() {
		return cusProtrait;
	}
	public void setCusProtrait(String cusProtrait) {
		this.cusProtrait = cusProtrait;
	}
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getSaleListUnique() {
		return saleListUnique;
	}
	public void setSaleListUnique(String saleListUnique) {
		this.saleListUnique = saleListUnique;
	}
	public String getShopUnique() {
		return shopUnique;
	}
	public void setShopUnique(String shopUnique) {
		this.shopUnique = shopUnique;
	}
	public String getRetListDatetime() {
		return retListDatetime;
	}
	public void setRetListDatetime(String retListDatetime) {
		this.retListDatetime = retListDatetime;
	}
	public Double getRetListTotal() {
		return retListTotal;
	}
	public void setRetListTotal(Double retListTotal) {
		this.retListTotal = retListTotal;
	}
	public Double getRetListCount() {
		return retListCount;
	}
	public void setRetListCount(Double retListCount) {
		this.retListCount = retListCount;
	}
	public Integer getRetListState() {
		return retListState;
	}
	public void setRetListState(Integer retListState) {
		this.retListState = retListState;
	}
	public String getRetListStateMsg() {
		return retListStateMsg;
	}
	public void setRetListStateMsg(String retListStateMsg) {
		this.retListStateMsg = retListStateMsg;
	}
	public Integer getRetListHandlestate() {
		return retListHandlestate;
	}
	public void setRetListHandlestate(Integer retListHandlestate) {
		this.retListHandlestate = retListHandlestate;
	}
	public String getRetListHandlestateMsg() {
		return retListHandlestateMsg;
	}
	public void setRetListHandlestateMsg(String retListHandlestateMsg) {
		this.retListHandlestateMsg = retListHandlestateMsg;
	}
	public String getRetListRemarks() {
		return retListRemarks;
	}
	public void setRetListRemarks(String retListRemarks) {
		this.retListRemarks = retListRemarks;
	}
	public String getStaffId() {
		return staffId;
	}
	public void setStaffId(String staffId) {
		this.staffId = staffId;
	}
	public String getMacId() {
		return macId;
	}
	public void setMacId(String macId) {
		this.macId = macId;
	}
	public Integer getRetOrigin() {
		return retOrigin;
	}
	public void setRetOrigin(Integer retOrigin) {
		this.retOrigin = retOrigin;
	}
	public String getRetOriginMsg() {
		return retOriginMsg;
	}
	public void setRetOriginMsg(String retOriginMsg) {
		this.retOriginMsg = retOriginMsg;
	}
	public Integer getRetMoneyType() {
		return retMoneyType;
	}
	public void setRetMoneyType(Integer retMoneyType) {
		this.retMoneyType = retMoneyType;
	}
	public String getRetMoneyTypeMsg() {
		return retMoneyTypeMsg;
	}
	public void setRetMoneyTypeMsg(String retMoneyTypeMsg) {
		this.retMoneyTypeMsg = retMoneyTypeMsg;
	}
	public Double getRetListTotalMoney() {
		return retListTotalMoney;
	}
	public void setRetListTotalMoney(Double retListTotalMoney) {
		this.retListTotalMoney = retListTotalMoney;
	}
	public Double getRetListOriginTotal() {
		return retListOriginTotal;
	}
	public void setRetListOriginTotal(Double retListOriginTotal) {
		this.retListOriginTotal = retListOriginTotal;
	}
	public String getOutRefundNo() {
		return outRefundNo;
	}
	public void setOutRefundNo(String outRefundNo) {
		this.outRefundNo = outRefundNo;
	}
	public String getRetBackDatetime() {
		return retBackDatetime;
	}
	public void setRetBackDatetime(String retBackDatetime) {
		this.retBackDatetime = retBackDatetime;
	}
	public String getRetListUnique() {
		return retListUnique;
	}
	public void setRetListUnique(String retListUnique) {
		this.retListUnique = retListUnique;
	}
	public Integer getRetListBean() {
		return retListBean;
	}
	public void setRetListBean(Integer retListBean) {
		this.retListBean = retListBean;
	}
	public String getRetListReason() {
		return retListReason;
	}
	public void setRetListReason(String retListReason) {
		this.retListReason = retListReason;
	}
	public Double getRetListDelfee() {
		return retListDelfee;
	}
	public void setRetListDelfee(Double retListDelfee) {
		this.retListDelfee = retListDelfee;
	}
	public List<ReturnDetail> getGoodsList() {
		return goodsList;
	}
	public void setGoodsList(List<ReturnDetail> goodsList) {
		this.goodsList = goodsList;
	}
	public List<ReturnPayDetail> getPayList() {
		return payList;
	}
	public void setPayList(List<ReturnPayDetail> payList) {
		this.payList = payList;
	}
}
