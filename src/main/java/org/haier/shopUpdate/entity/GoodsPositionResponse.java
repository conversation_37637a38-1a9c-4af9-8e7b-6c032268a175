package org.haier.shopUpdate.entity;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class GoodsPositionResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;
    /**
     * 父级id
     */

    private Long parentId;
    /**
     * 货位名称
     */
    private String positionName;
    /**
     * 完整货位名称
     */
    private String completePositionName="";
    /**
     * 完整货位id
     */
    private String completePositionId="";

    /**
     * 等级
     */
    private Integer level;

    /**
     * 子菜单
     */
    private final List<GoodsPositionResponse> children = new ArrayList<>();

    public void addChildren(GoodsPositionResponse response){
        children.add(response);
    }

    public void addCompletePositionName(String parentName,String completeName){
        if (StrUtil.isNotEmpty(parentName)) {
            completePositionName = parentName;
        }
        if(StrUtil.isNotEmpty(completeName)){
            completePositionName = completeName+"-"+parentName;
        }
    }

    public void addCompletePositionId(Long parentId,String completeId){
        if (ObjectUtil.isNotEmpty(parentId)) {
            completePositionId = String.valueOf(parentId);
        }
        if(StrUtil.isNotEmpty(completeId)){
            completePositionId = completeId+","+ parentId;
        }
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getPositionName() {
        return positionName;
    }

    public void setPositionName(String positionName) {
        this.positionName = positionName;
    }

    public String getCompletePositionName() {
        return completePositionName;
    }

    public List<GoodsPositionResponse> getChildren() {
        return children;
    }

    public String getCompletePositionId() {
        return completePositionId;
    }
}
