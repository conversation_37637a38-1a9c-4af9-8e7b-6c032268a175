package org.haier.shopUpdate.entity;

import java.util.List;

public class CusConsumption {
	//ID
	private Integer cusId;
	//会员编号
	private String cusUnique;
	//会员名称
	private String cusName;
	//会员余额；
	private Double cusBalance;
	//会员头像
	private String cusHeadPath;
	//会员剩余积分
	private Integer cusPoints;
	//充值记录详情
	private List<CusConsumptionRecord> list;
	
	public Integer getCusId() {
		return cusId;
	}
	public void setCusId(Integer cusId) {
		this.cusId = cusId;
	}
	public String getCusUnique() {
		return cusUnique;
	}
	public void setCusUnique(String cusUnique) {
		this.cusUnique = cusUnique;
	}
	public String getCusName() {
		return cusName;
	}
	public void setCusName(String cusName) {
		this.cusName = cusName;
	}
	public Double getCusBalance() {
		return cusBalance;
	}
	public String getCusHeadPath() {
		return cusHeadPath;
	}
	public void setCusHeadPath(String cusHeadPath) {
		this.cusHeadPath = cusHeadPath;
	}
	public void setCusBalance(Double cusBalance) {
		this.cusBalance = cusBalance;
	}
	public Integer getCusPoints() {
		return cusPoints;
	}
	public void setCusPoints(Integer cusPoints) {
		this.cusPoints = cusPoints;
	}
	public List<CusConsumptionRecord> getList() {
		return list;
	}
	public void setList(List<CusConsumptionRecord> list) {
		this.list = list;
	}
}
