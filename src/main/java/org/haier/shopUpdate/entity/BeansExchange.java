package org.haier.shopUpdate.entity;

/**
 * 百货豆提现相关
 * <AUTHOR>
 *
 */
public class BeansExchange {
	private Integer beansReceiveId;//提现ID
	private String shopUnique;//店铺编号
	private Integer beansOldCount;//修改前数量
	private Integer exchangeType;//操作方式
	private String exchangeCode;//操作方式；1、买豆；2、提现
	private Integer receiveCount;//豆数量
	private Integer payType;//支付方式
	private String payCode;//支付方式1、支付宝；2、微信
	private Double payMoney;//提现/购买金额
	private Integer serviceCharge;//手续费
	private Integer receiveState;//兑换状态
	private String receiveCode;//兑换状态；0、待处理；1、完成；2、失败；3、未支付；4、驳回
	private String receiveTime;//兑换时间
	private String remark;//备注
	private String orderId;//对应单号
	private Integer cardId;//对应银行卡ID
	private String changeTime;//处理时间
	private Integer giveCount;//赠送的数量
	private String spbillCreateIp;//微信支付终端IP
	public Integer getBeansReceiveId() {
		return beansReceiveId;
	}
	public void setBeansReceiveId(Integer beansReceiveId) {
		this.beansReceiveId = beansReceiveId;
	}
	public String getShopUnique() {
		return shopUnique;
	}
	public void setShopUnique(String shopUnique) {
		this.shopUnique = shopUnique;
	}
	public Integer getBeansOldCount() {
		return beansOldCount;
	}
	public void setBeansOldCount(Integer beansOldCount) {
		this.beansOldCount = beansOldCount;
	}
	public Integer getExchangeType() {
		return exchangeType;
	}
	public void setExchangeType(Integer exchangeType) {
		this.exchangeType = exchangeType;
	}
	public String getExchangeCode() {
		return exchangeCode;
	}
	public void setExchangeCode(String exchangeCode) {
		this.exchangeCode = exchangeCode;
	}
	public Integer getReceiveCount() {
		return receiveCount;
	}
	public void setReceiveCount(Integer receiveCount) {
		this.receiveCount = receiveCount;
	}
	public Integer getPayType() {
		return payType;
	}
	public void setPayType(Integer payType) {
		this.payType = payType;
	}
	public String getPayCode() {
		return payCode;
	}
	public void setPayCode(String payCode) {
		this.payCode = payCode;
	}
	public Double getPayMoney() {
		return payMoney;
	}
	public void setPayMoney(Double payMoney) {
		this.payMoney = payMoney;
	}
	public Integer getServiceCharge() {
		return serviceCharge;
	}
	public void setServiceCharge(Integer serviceCharge) {
		this.serviceCharge = serviceCharge;
	}
	public Integer getReceiveState() {
		return receiveState;
	}
	public void setReceiveState(Integer receiveState) {
		this.receiveState = receiveState;
	}
	public String getReceiveCode() {
		return receiveCode;
	}
	public void setReceiveCode(String receiveCode) {
		this.receiveCode = receiveCode;
	}
	public String getReceiveTime() {
		return receiveTime;
	}
	public void setReceiveTime(String receiveTime) {
		this.receiveTime = receiveTime;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public String getOrderId() {
		return orderId;
	}
	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}
	public Integer getCardId() {
		return cardId;
	}
	public void setCardId(Integer cardId) {
		this.cardId = cardId;
	}
	public String getChangeTime() {
		return changeTime;
	}
	public void setChangeTime(String changeTime) {
		this.changeTime = changeTime;
	}
	public Integer getGiveCount() {
		return giveCount;
	}
	public void setGiveCount(Integer giveCount) {
		this.giveCount = giveCount;
	}
	public String getSpbillCreateIp() {
		return spbillCreateIp;
	}
	public void setSpbillCreateIp(String spbillCreateIp) {
		this.spbillCreateIp = spbillCreateIp;
	}
	@Override
	public String toString() {
		return "BeansExchange [beansReceiveId=" + beansReceiveId + ", shopUnique=" + shopUnique + ", beansOldCount="
				+ beansOldCount + ", exchangeType=" + exchangeType + ", exchangeCode=" + exchangeCode
				+ ", receiveCount=" + receiveCount + ", payType=" + payType + ", payCode=" + payCode + ", payMoney="
				+ payMoney + ", serviceCharge=" + serviceCharge + ", receiveState=" + receiveState + ", receiveCode="
				+ receiveCode + ", receiveTime=" + receiveTime + ", remark=" + remark + ", orderId=" + orderId
				+ ", cardId=" + cardId + ", changeTime=" + changeTime + ", giveCount=" + giveCount + ", spbillCreateIp="
				+ spbillCreateIp + "]";
	}
}
