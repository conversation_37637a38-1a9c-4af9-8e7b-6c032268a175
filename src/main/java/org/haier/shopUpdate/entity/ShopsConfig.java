package org.haier.shopUpdate.entity;

import java.util.Date;

/**
 * 店铺配置
 * <AUTHOR> 
 * @ClassName ShopsConfig
 * @Date 2024-04-08
 **/

public class ShopsConfig {
	/**
	* ID
	*/
	private Integer id;

	/**
	* 店铺编号
	*/
	private String shopUnique;

	/**
	* 微信收款码图片路径
	*/
	private String wechatPic;

	/**
	* 支付宝图片上传路径
	*/
	private String alipayPic;

	/**
	* 免密支付申请状态：1、未提交申请；2、已提交申请；3、已安装；4、审核未通过；5、拒绝；6、审核通过未安装；
	*/
	private Integer mianmiStatus;

	/**
	* 免密支付失败原因
	*/
	private String mianmiRemarks;

	/**
	* 申请时间
	*/
	private Date mianmiApplyDatetime;

	/**
	* 审核时间
	*/
	private Date mianmiExamineDatetime;

	/**
	* teamviewer远程账号
	*/
	private String teamviewer;

	/**
	* anydesk远程账号
	*/
	private String anydesk;

	/**
	* 分店是否同步主店信息：0、不同步；1、同步；（修改主店信息时，同步修改分店信息，反之无效）
	*/
	private Integer sameGoods;

	/**
	* 会员信息同步：0、不同步；1、同步；
	*/
	private Integer sameCus;

	/**
	* 店铺绑定的推广员信息
	*/
	private String cusUnique;

	/**
	* 小程序类型：1、一刻钟到家；2、沃商赢
	*/
	private Integer smallType;

	/**
	* 商品进价类型：0-自定义，1-平均值，2-批次定价
	*/
	private Integer goodsInPriceType;

	private Integer isIoBoundInspect;
	/**
	 * 是否同步餐饮订单数据：0、不同步；1、同步
	 */
	private Integer sync_canyin_data;
	/**
	 * 是否同步普通订单数据：0、不同步；1、同步
	 */
	private Integer sync_buyhoo_data;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getShopUnique() {
		return shopUnique;
	}

	public void setShopUnique(String shopUnique) {
		this.shopUnique = shopUnique;
	}

	public String getWechatPic() {
		return wechatPic;
	}

	public void setWechatPic(String wechatPic) {
		this.wechatPic = wechatPic;
	}

	public String getAlipayPic() {
		return alipayPic;
	}

	public void setAlipayPic(String alipayPic) {
		this.alipayPic = alipayPic;
	}

	public Integer getMianmiStatus() {
		return mianmiStatus;
	}

	public void setMianmiStatus(Integer mianmiStatus) {
		this.mianmiStatus = mianmiStatus;
	}

	public String getMianmiRemarks() {
		return mianmiRemarks;
	}

	public void setMianmiRemarks(String mianmiRemarks) {
		this.mianmiRemarks = mianmiRemarks;
	}

	public Date getMianmiApplyDatetime() {
		return mianmiApplyDatetime;
	}

	public void setMianmiApplyDatetime(Date mianmiApplyDatetime) {
		this.mianmiApplyDatetime = mianmiApplyDatetime;
	}

	public Date getMianmiExamineDatetime() {
		return mianmiExamineDatetime;
	}

	public void setMianmiExamineDatetime(Date mianmiExamineDatetime) {
		this.mianmiExamineDatetime = mianmiExamineDatetime;
	}

	public String getTeamviewer() {
		return teamviewer;
	}

	public void setTeamviewer(String teamviewer) {
		this.teamviewer = teamviewer;
	}

	public String getAnydesk() {
		return anydesk;
	}

	public void setAnydesk(String anydesk) {
		this.anydesk = anydesk;
	}

	public Integer getSameGoods() {
		return sameGoods;
	}

	public void setSameGoods(Integer sameGoods) {
		this.sameGoods = sameGoods;
	}

	public Integer getSameCus() {
		return sameCus;
	}

	public void setSameCus(Integer sameCus) {
		this.sameCus = sameCus;
	}

	public String getCusUnique() {
		return cusUnique;
	}

	public void setCusUnique(String cusUnique) {
		this.cusUnique = cusUnique;
	}

	public Integer getSmallType() {
		return smallType;
	}

	public void setSmallType(Integer smallType) {
		this.smallType = smallType;
	}

	public Integer getGoodsInPriceType() {
		return goodsInPriceType;
	}

	public void setGoodsInPriceType(Integer goodsInPriceType) {
		this.goodsInPriceType = goodsInPriceType;
	}

	public Integer getIsIoBoundInspect() {
		return isIoBoundInspect;
	}

	public void setIsIoBoundInspect(Integer isIoBoundInspect) {
		this.isIoBoundInspect = isIoBoundInspect;
	}

	public Integer getSync_canyin_data() {
		return sync_canyin_data;
	}

	public void setSync_canyin_data(Integer sync_canyin_data) {
		this.sync_canyin_data = sync_canyin_data;
	}

	public Integer getSync_buyhoo_data() {
		return sync_buyhoo_data;
	}

	public void setSync_buyhoo_data(Integer sync_buyhoo_data) {
		this.sync_buyhoo_data = sync_buyhoo_data;
	}
}