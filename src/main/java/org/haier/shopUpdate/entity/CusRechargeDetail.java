package org.haier.shopUpdate.entity;

/**
 * 会员充值详情记录
 * <AUTHOR>
 *
 */
public class CusRechargeDetail {
	//记录编号
	private Integer rechargeId;
	//会员姓名
	private String cusName;
	//会员编号
	private String cusUnique;
	//充值金额
	private Double rechargeMoney;
	//充值时间
	private String rechargeTime;
	//充值前金额
	private Double cusMoney;
	//充值后金额
	private Double cusBalance;
	//充值方式
	private String rechargeMethod;
	//员工编号
	private Integer saleListCashier;
	//员工姓名
	private String staffName;
	public Integer getRechargeId() {
		return rechargeId;
	}
	public void setRechargeId(Integer rechargeId) {
		this.rechargeId = rechargeId;
	}
	public String getCusName() {
		return cusName;
	}
	public void setCusName(String cusName) {
		this.cusName = cusName;
	}
	public String getCusUnique() {
		return cusUnique;
	}
	public void setCusUnique(String cusUnique) {
		this.cusUnique = cusUnique;
	}
	public Double getRechargeMoney() {
		return rechargeMoney;
	}
	public void setRechargeMoney(Double rechargeMoney) {
		this.rechargeMoney = rechargeMoney;
	}
	public Double getCusMoney() {
		return cusMoney;
	}
	public void setCusMoney(Double cusMoney) {
		this.cusMoney = cusMoney;
	}
	public Double getCusBalance() {
		return cusBalance;
	}
	public void setCusBalance(Double cusBalance) {
		this.cusBalance = cusBalance;
	}
	public String getRechargeMethod() {
		return rechargeMethod;
	}
	public void setRechargeMethod(String rechargeMethod) {
		this.rechargeMethod = rechargeMethod;
	}
	public Integer getSaleListCashier() {
		return saleListCashier;
	}
	public void setSaleListCashier(Integer saleListCashier) {
		this.saleListCashier = saleListCashier;
	}
	public String getStaffName() {
		return staffName;
	}
	public void setStaffName(String staffName) {
		this.staffName = staffName;
	}
	public String getRechargeTime() {
		return rechargeTime;
	}
	public void setRechargeTime(String rechargeTime) {
		this.rechargeTime = rechargeTime;
	}
}	
