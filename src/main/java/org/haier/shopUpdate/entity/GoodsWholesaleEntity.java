package org.haier.shopUpdate.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;

/**
* 商品拓展表-批发价
*/
@TableName("goods_wholesale")
public class GoodsWholesaleEntity {

    private Long id;
    /**
    * 商品编号
    */
    private Integer goodsId;
    /**
    * 店铺唯一标识
    */
    private Long shopUnique;
    /**
    * 店铺唯一标识符
    */
    private String goodsBarcode;
    /**
    * 商品批发价
    */
    private BigDecimal wholesalePrice;
    /**
    * 批发数量
    */
    private BigDecimal wholesaleCount;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Integer goodsId) {
        this.goodsId = goodsId;
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public BigDecimal getWholesalePrice() {
        return wholesalePrice;
    }

    public void setWholesalePrice(BigDecimal wholesalePrice) {
        this.wholesalePrice = wholesalePrice;
    }

    public BigDecimal getWholesaleCount() {
        return wholesaleCount;
    }

    public void setWholesaleCount(BigDecimal wholesaleCount) {
        this.wholesaleCount = wholesaleCount;
    }
}