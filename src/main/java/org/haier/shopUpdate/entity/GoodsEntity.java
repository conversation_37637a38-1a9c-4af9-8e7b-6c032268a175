package org.haier.shopUpdate.entity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description 标准商品信息字典
 */
public class GoodsEntity {
    /**
    * 商品编号
    */
    private Integer goodsId;

    /**
    * 店铺唯一标识
    */
    private Long shopUnique;

    /**
    * 店铺唯一标识符
    */
    private String goodsBarcode;

    /**
    * 商品分类唯一标识符
    */
    private Long goodsKindUnique;

    /**
    * 商品品牌
    */
    private String goodsBrand;

    /**
    * 商品促销状态：1，不促销，2促销
    */
    private String goodsPromotion;

    /**
    * 商品名称
    */
    private String goodsName;

    /**
    * 商品别名
    */
    private String goodsAlias;

    /**
    * 商品进价
    */
    private BigDecimal goodsInPrice;

    /**
    * 商品售价
    */
    private BigDecimal goodsSalePrice;

    /**
    * 商品保质天数
    */
    private Integer goodsLife;

    /**
    * 商品积分
    */
    private Integer goodsPoints;

    /**
    * 商品生产地
    */
    private String goodsAddress;

    /**
    * 商品折扣率
    */
    private BigDecimal goodsDiscount;

    /**
    * 商品包含子商品数量
    */
    private BigDecimal goodsContain;

    /**
    * 商品图片保存路径
    */
    private String goodsPicturepath;

    /**
    * 商品特殊说明
    */
    private String goodsRemarks;

    /**
    * 商品包装分类外键
    */
    private Long foreignKey;

    /**
    * 商品库存数量
    */
    private BigDecimal goodsCount;

    /**
    * 已卖出的数量
    */
    private BigDecimal goodsSold;

    /**
    * 点击率
    */
    private Integer goodsHits;

    /**
    * 商品规格
    */
    private String goodsStandard;

    /**
    * 商品计价单位
    */
    private String goodsUnit;

    /**
    * 会员价
    */
    private String goodsCusPrice;

    /**
    * 默认供应商编号
    */
    private String defaultSupplierUnique;

    /**
    * 1:已同步；2：未同步
    */
    private Integer sametype;

    /**
    * 最后一次更新时间
    */
    private Date updateTime;

    /**
    * 线上上架状态：1、已上架；2、已下架
    */
    private Integer shelfState;

    /**
    * pc收银上架状态：1、已上架；2、已下架
    */
    private Integer pcShelfState;

    /**
    * 子商品id
    */
    private Integer childId;

    /**
    * 默认供货商商品编码
    */
    private String supGoodsBarcode;

    /**
    * 促销数量
    */
    private Integer promotionCount;

    /**
    * 捆绑关系
    */
    private Long bindingUnique;

    /**
    * 缺货提醒类型：1、按照库维持天数设置；2、按商品设定值；
    */
    private Integer outStockRemindType;

    /**
    * 提醒库存量，低于该库存时，提醒缺货
    */
    private Integer outStockWaringCount;

    /**
    * 滞销库存量，高于该值是，商品滞销
    */
    private Integer unsalableCount;

    /**
    * 最低库存量，若库存量低于该值，自动进货
    */
    private Integer outStockCount;

    /**
    * 已采购待入库数量
    */
    private Integer stayStockCount;

    /**
    * 商品保质期
    */
    private String sameTime;

    /**
    * 积分算法类型 1:商品售价 2:固定积分 3:商品利润比例
    */
    private Integer goodsPointsType;

    /**
    * 积分
    */
    private BigDecimal goodsPointsVal;

    /**
    * 提成算法类型 1:金额比例 2:固定金额
    */
    private Integer goodsCommissionType;

    /**
    * 提成
    */
    private BigDecimal goodsCommissionVal;

    /**
    * 0:没有库存预警 1:有库存预警
    */
    private Integer stockWarningStatus;

    /**
    * 同步外卖平台：0未同步，1美团，2饿了么，3美团、饿了么同时同步
    */
    private Integer synchronousPlatform;

    /**
    * goods_points_unit
    */
    private BigDecimal goodsPointsUnit;

    /**
    * goods_points_unit_val
    */
    private BigDecimal goodsPointsUnitVal;

    /**
    * 商品提成单位
    */
    private BigDecimal goodsCommissionUnit;

    /**
    * goods_commission_unit_val
    */
    private BigDecimal goodsCommissionUnitVal;

    /**
    * 商品子编码，用户自定义编码
    */
    private String selfEncoding;

    /**
    * 重量(克)
    */
    private BigDecimal goodsWeight;

    /**
    * 是否特殊商品：0否 1是
    */
    private Integer isSpecial;

    /**
    * 是否单件不配送：0否 1是
    */
    private Integer isOneDelivery;

    /**
    * 特殊商品单件配送费
    */
    private BigDecimal singleDeliveryFee;

    /**
    * 称重商品类型：0、按件；1、按重量
    */
    private Integer goodsChengType;

    /**
    * 排序
    */
    private Integer orderSort;

    /**
    * 百货豆抵扣比例
    */
    private Integer beanDeductionRatio;

    /**
    * 商品线上售价
    */
    private BigDecimal goodsWebSalePrice;

    /**
     * 批发价
     */
    private BigDecimal wholesalePrice;
    /**
     * 是否设为批发价 0 否 1，是
     */
    private Integer wholesalePriceFlg;
    /**
     * 起批数量
     */
    private BigDecimal wholesaleCount;

    /**
     * 货位
     */
    private BigDecimal goodsPosition;

    /**
    * goods_page
    */
    private Integer goodsPage;

    /**
    * goods_index
    */
    private Integer goodsIndex;

    /**
    * 售价和网购价是否同步：1同步 2不同步
    */
    private Integer samePrice;
    /**
     * 划线价
     */
    private BigDecimal underlinedPrice;
    public Integer getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Integer goodsId) {
        this.goodsId = goodsId;
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public Long getGoodsKindUnique() {
        return goodsKindUnique;
    }

    public void setGoodsKindUnique(Long goodsKindUnique) {
        this.goodsKindUnique = goodsKindUnique;
    }

    public String getGoodsBrand() {
        return goodsBrand;
    }

    public void setGoodsBrand(String goodsBrand) {
        this.goodsBrand = goodsBrand;
    }

    public String getGoodsPromotion() {
        return goodsPromotion;
    }

    public void setGoodsPromotion(String goodsPromotion) {
        this.goodsPromotion = goodsPromotion;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsAlias() {
        return goodsAlias;
    }

    public void setGoodsAlias(String goodsAlias) {
        this.goodsAlias = goodsAlias;
    }

    public BigDecimal getGoodsInPrice() {
        return goodsInPrice;
    }

    public void setGoodsInPrice(BigDecimal goodsInPrice) {
        this.goodsInPrice = goodsInPrice;
    }

    public BigDecimal getGoodsSalePrice() {
        return goodsSalePrice;
    }

    public void setGoodsSalePrice(BigDecimal goodsSalePrice) {
        this.goodsSalePrice = goodsSalePrice;
    }

    public Integer getGoodsLife() {
        return goodsLife;
    }

    public void setGoodsLife(Integer goodsLife) {
        this.goodsLife = goodsLife;
    }

    public Integer getGoodsPoints() {
        return goodsPoints;
    }

    public void setGoodsPoints(Integer goodsPoints) {
        this.goodsPoints = goodsPoints;
    }

    public String getGoodsAddress() {
        return goodsAddress;
    }

    public void setGoodsAddress(String goodsAddress) {
        this.goodsAddress = goodsAddress;
    }

    public BigDecimal getGoodsDiscount() {
        return goodsDiscount;
    }

    public void setGoodsDiscount(BigDecimal goodsDiscount) {
        this.goodsDiscount = goodsDiscount;
    }

    public BigDecimal getGoodsContain() {
        return goodsContain;
    }

    public void setGoodsContain(BigDecimal goodsContain) {
        this.goodsContain = goodsContain;
    }

    public String getGoodsPicturepath() {
        return goodsPicturepath;
    }

    public void setGoodsPicturepath(String goodsPicturepath) {
        this.goodsPicturepath = goodsPicturepath;
    }

    public String getGoodsRemarks() {
        return goodsRemarks;
    }

    public void setGoodsRemarks(String goodsRemarks) {
        this.goodsRemarks = goodsRemarks;
    }

    public Long getForeignKey() {
        return foreignKey;
    }

    public void setForeignKey(Long foreignKey) {
        this.foreignKey = foreignKey;
    }

    public BigDecimal getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(BigDecimal goodsCount) {
        this.goodsCount = goodsCount;
    }

    public BigDecimal getGoodsSold() {
        return goodsSold;
    }

    public void setGoodsSold(BigDecimal goodsSold) {
        this.goodsSold = goodsSold;
    }

    public Integer getGoodsHits() {
        return goodsHits;
    }

    public void setGoodsHits(Integer goodsHits) {
        this.goodsHits = goodsHits;
    }

    public String getGoodsStandard() {
        return goodsStandard;
    }

    public void setGoodsStandard(String goodsStandard) {
        this.goodsStandard = goodsStandard;
    }

    public String getGoodsUnit() {
        return goodsUnit;
    }

    public void setGoodsUnit(String goodsUnit) {
        this.goodsUnit = goodsUnit;
    }

    public String getGoodsCusPrice() {
        return goodsCusPrice;
    }

    public void setGoodsCusPrice(String goodsCusPrice) {
        this.goodsCusPrice = goodsCusPrice;
    }

    public String getDefaultSupplierUnique() {
        return defaultSupplierUnique;
    }

    public void setDefaultSupplierUnique(String defaultSupplierUnique) {
        this.defaultSupplierUnique = defaultSupplierUnique;
    }

    public Integer getSametype() {
        return sametype;
    }

    public void setSametype(Integer sametype) {
        this.sametype = sametype;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getShelfState() {
        return shelfState;
    }

    public void setShelfState(Integer shelfState) {
        this.shelfState = shelfState;
    }

    public Integer getPcShelfState() {
        return pcShelfState;
    }

    public void setPcShelfState(Integer pcShelfState) {
        this.pcShelfState = pcShelfState;
    }

    public Integer getChildId() {
        return childId;
    }

    public void setChildId(Integer childId) {
        this.childId = childId;
    }

    public String getSupGoodsBarcode() {
        return supGoodsBarcode;
    }

    public void setSupGoodsBarcode(String supGoodsBarcode) {
        this.supGoodsBarcode = supGoodsBarcode;
    }

    public Integer getPromotionCount() {
        return promotionCount;
    }

    public void setPromotionCount(Integer promotionCount) {
        this.promotionCount = promotionCount;
    }

    public Long getBindingUnique() {
        return bindingUnique;
    }

    public void setBindingUnique(Long bindingUnique) {
        this.bindingUnique = bindingUnique;
    }

    public Integer getOutStockRemindType() {
        return outStockRemindType;
    }

    public void setOutStockRemindType(Integer outStockRemindType) {
        this.outStockRemindType = outStockRemindType;
    }

    public Integer getOutStockWaringCount() {
        return outStockWaringCount;
    }

    public void setOutStockWaringCount(Integer outStockWaringCount) {
        this.outStockWaringCount = outStockWaringCount;
    }

    public Integer getUnsalableCount() {
        return unsalableCount;
    }

    public void setUnsalableCount(Integer unsalableCount) {
        this.unsalableCount = unsalableCount;
    }

    public Integer getOutStockCount() {
        return outStockCount;
    }

    public void setOutStockCount(Integer outStockCount) {
        this.outStockCount = outStockCount;
    }

    public Integer getStayStockCount() {
        return stayStockCount;
    }

    public void setStayStockCount(Integer stayStockCount) {
        this.stayStockCount = stayStockCount;
    }

    public String getSameTime() {
        return sameTime;
    }

    public void setSameTime(String sameTime) {
        this.sameTime = sameTime;
    }

    public Integer getGoodsPointsType() {
        return goodsPointsType;
    }

    public void setGoodsPointsType(Integer goodsPointsType) {
        this.goodsPointsType = goodsPointsType;
    }

    public BigDecimal getGoodsPointsVal() {
        return goodsPointsVal;
    }

    public void setGoodsPointsVal(BigDecimal goodsPointsVal) {
        this.goodsPointsVal = goodsPointsVal;
    }

    public Integer getGoodsCommissionType() {
        return goodsCommissionType;
    }

    public void setGoodsCommissionType(Integer goodsCommissionType) {
        this.goodsCommissionType = goodsCommissionType;
    }

    public BigDecimal getGoodsCommissionVal() {
        return goodsCommissionVal;
    }

    public void setGoodsCommissionVal(BigDecimal goodsCommissionVal) {
        this.goodsCommissionVal = goodsCommissionVal;
    }

    public Integer getStockWarningStatus() {
        return stockWarningStatus;
    }

    public void setStockWarningStatus(Integer stockWarningStatus) {
        this.stockWarningStatus = stockWarningStatus;
    }

    public Integer getSynchronousPlatform() {
        return synchronousPlatform;
    }

    public void setSynchronousPlatform(Integer synchronousPlatform) {
        this.synchronousPlatform = synchronousPlatform;
    }

    public BigDecimal getGoodsPointsUnit() {
        return goodsPointsUnit;
    }

    public void setGoodsPointsUnit(BigDecimal goodsPointsUnit) {
        this.goodsPointsUnit = goodsPointsUnit;
    }

    public BigDecimal getGoodsPointsUnitVal() {
        return goodsPointsUnitVal;
    }

    public void setGoodsPointsUnitVal(BigDecimal goodsPointsUnitVal) {
        this.goodsPointsUnitVal = goodsPointsUnitVal;
    }

    public BigDecimal getGoodsCommissionUnit() {
        return goodsCommissionUnit;
    }

    public void setGoodsCommissionUnit(BigDecimal goodsCommissionUnit) {
        this.goodsCommissionUnit = goodsCommissionUnit;
    }

    public BigDecimal getGoodsCommissionUnitVal() {
        return goodsCommissionUnitVal;
    }

    public void setGoodsCommissionUnitVal(BigDecimal goodsCommissionUnitVal) {
        this.goodsCommissionUnitVal = goodsCommissionUnitVal;
    }

    public String getSelfEncoding() {
        return selfEncoding;
    }

    public void setSelfEncoding(String selfEncoding) {
        this.selfEncoding = selfEncoding;
    }

    public BigDecimal getGoodsWeight() {
        return goodsWeight;
    }

    public void setGoodsWeight(BigDecimal goodsWeight) {
        this.goodsWeight = goodsWeight;
    }

    public Integer getIsSpecial() {
        return isSpecial;
    }

    public void setIsSpecial(Integer isSpecial) {
        this.isSpecial = isSpecial;
    }

    public Integer getIsOneDelivery() {
        return isOneDelivery;
    }

    public void setIsOneDelivery(Integer isOneDelivery) {
        this.isOneDelivery = isOneDelivery;
    }

    public BigDecimal getSingleDeliveryFee() {
        return singleDeliveryFee;
    }

    public void setSingleDeliveryFee(BigDecimal singleDeliveryFee) {
        this.singleDeliveryFee = singleDeliveryFee;
    }

    public Integer getGoodsChengType() {
        return goodsChengType;
    }

    public void setGoodsChengType(Integer goodsChengType) {
        this.goodsChengType = goodsChengType;
    }

    public Integer getOrderSort() {
        return orderSort;
    }

    public void setOrderSort(Integer orderSort) {
        this.orderSort = orderSort;
    }

    public Integer getBeanDeductionRatio() {
        return beanDeductionRatio;
    }

    public void setBeanDeductionRatio(Integer beanDeductionRatio) {
        this.beanDeductionRatio = beanDeductionRatio;
    }

    public BigDecimal getGoodsWebSalePrice() {
        return goodsWebSalePrice;
    }

    public void setGoodsWebSalePrice(BigDecimal goodsWebSalePrice) {
        this.goodsWebSalePrice = goodsWebSalePrice;
    }

    public Integer getGoodsPage() {
        return goodsPage;
    }

    public void setGoodsPage(Integer goodsPage) {
        this.goodsPage = goodsPage;
    }

    public Integer getGoodsIndex() {
        return goodsIndex;
    }

    public void setGoodsIndex(Integer goodsIndex) {
        this.goodsIndex = goodsIndex;
    }

    public Integer getSamePrice() {
        return samePrice;
    }

    public void setSamePrice(Integer samePrice) {
        this.samePrice = samePrice;
    }

    public BigDecimal getWholesalePrice() {
        return wholesalePrice;
    }

    public void setWholesalePrice(BigDecimal wholesalePrice) {
        this.wholesalePrice = wholesalePrice;
    }

    public Integer getWholesalePriceFlg() {
        return wholesalePriceFlg;
    }

    public void setWholesalePriceFlg(Integer wholesalePriceFlg) {
        this.wholesalePriceFlg = wholesalePriceFlg;
    }

    public BigDecimal getWholesaleCount() {
        return wholesaleCount;
    }

    public void setWholesaleCount(BigDecimal wholesaleCount) {
        this.wholesaleCount = wholesaleCount;
    }

    public BigDecimal getGoodsPosition() {
        return goodsPosition;
    }

    public void setGoodsPosition(BigDecimal goodsPosition) {
        this.goodsPosition = goodsPosition;
    }

    public BigDecimal getUnderlinedPrice() {
        return underlinedPrice;
    }

    public void setUnderlinedPrice(BigDecimal underlinedPrice) {
        this.underlinedPrice = underlinedPrice;
    }

    @Override
    public String toString() {
        return "GoodsEntity{" +
                "goodsId=" + goodsId +
                ", shopUnique=" + shopUnique +
                ", goodsBarcode='" + goodsBarcode + '\'' +
                ", goodsKindUnique=" + goodsKindUnique +
                ", goodsBrand='" + goodsBrand + '\'' +
                ", goodsPromotion='" + goodsPromotion + '\'' +
                ", goodsName='" + goodsName + '\'' +
                ", goodsAlias='" + goodsAlias + '\'' +
                ", goodsInPrice=" + goodsInPrice +
                ", goodsSalePrice=" + goodsSalePrice +
                ", goodsLife=" + goodsLife +
                ", goodsPoints=" + goodsPoints +
                ", goodsAddress='" + goodsAddress + '\'' +
                ", goodsDiscount=" + goodsDiscount +
                ", goodsContain=" + goodsContain +
                ", goodsPicturepath='" + goodsPicturepath + '\'' +
                ", goodsRemarks='" + goodsRemarks + '\'' +
                ", foreignKey=" + foreignKey +
                ", goodsCount=" + goodsCount +
                ", goodsSold=" + goodsSold +
                ", goodsHits=" + goodsHits +
                ", goodsStandard='" + goodsStandard + '\'' +
                ", goodsUnit='" + goodsUnit + '\'' +
                ", goodsCusPrice='" + goodsCusPrice + '\'' +
                ", defaultSupplierUnique='" + defaultSupplierUnique + '\'' +
                ", sametype=" + sametype +
                ", updateTime=" + updateTime +
                ", shelfState=" + shelfState +
                ", pcShelfState=" + pcShelfState +
                ", childId=" + childId +
                ", supGoodsBarcode='" + supGoodsBarcode + '\'' +
                ", promotionCount=" + promotionCount +
                ", bindingUnique=" + bindingUnique +
                ", outStockRemindType=" + outStockRemindType +
                ", outStockWaringCount=" + outStockWaringCount +
                ", unsalableCount=" + unsalableCount +
                ", outStockCount=" + outStockCount +
                ", stayStockCount=" + stayStockCount +
                ", sameTime='" + sameTime + '\'' +
                ", goodsPointsType=" + goodsPointsType +
                ", goodsPointsVal=" + goodsPointsVal +
                ", goodsCommissionType=" + goodsCommissionType +
                ", goodsCommissionVal=" + goodsCommissionVal +
                ", stockWarningStatus=" + stockWarningStatus +
                ", synchronousPlatform=" + synchronousPlatform +
                ", goodsPointsUnit=" + goodsPointsUnit +
                ", goodsPointsUnitVal=" + goodsPointsUnitVal +
                ", goodsCommissionUnit=" + goodsCommissionUnit +
                ", goodsCommissionUnitVal=" + goodsCommissionUnitVal +
                ", selfEncoding='" + selfEncoding + '\'' +
                ", goodsWeight=" + goodsWeight +
                ", isSpecial=" + isSpecial +
                ", isOneDelivery=" + isOneDelivery +
                ", singleDeliveryFee=" + singleDeliveryFee +
                ", goodsChengType=" + goodsChengType +
                ", orderSort=" + orderSort +
                ", beanDeductionRatio=" + beanDeductionRatio +
                ", goodsWebSalePrice=" + goodsWebSalePrice +
                ", wholesalePrice=" + wholesalePrice +
                ", wholesalePriceFlg=" + wholesalePriceFlg +
                ", wholesaleCount=" + wholesaleCount +
                ", goodsPosition=" + goodsPosition +
                ", goodsPage=" + goodsPage +
                ", goodsIndex=" + goodsIndex +
                ", samePrice=" + samePrice +
                '}';
    }
}