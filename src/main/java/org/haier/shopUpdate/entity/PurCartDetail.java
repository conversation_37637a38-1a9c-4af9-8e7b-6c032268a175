package org.haier.shopUpdate.entity;

public class PurCartDetail{
	//商品条码
	private String goodsBarcode;
	//商品名称
	private String goodsName;
	//商品单价
	private Double goodsPrice;
	//商品数量
	private Double goodsCount;
	//商品规格
	private String goodsStandard;
	//是否赠品
	private Integer giftType;
	//商品图片路径
	private String goodsPicturePath;
	//商品默认供货商
	private String supplierUnique;
	//商品默认供货商名称
	private String supplierName;
	
	
	public Integer getGiftType() {
		return giftType;
	}
	public void setGiftType(Integer giftType) {
		this.giftType = giftType;
	}
	public String getGoodsBarcode() {
		return goodsBarcode;
	}
	public void setGoodsBarcode(String goodsBarcode) {
		this.goodsBarcode = goodsBarcode;
	}
	public String getGoodsName() {
		return goodsName;
	}
	public void setGoodsName(String goodsName) {
		this.goodsName = goodsName;
	}
	public Double getGoodsPrice() {
		return goodsPrice;
	}
	public void setGoodsPrice(Double goodsPrice) {
		this.goodsPrice = goodsPrice;
	}
	public Double getGoodsCount() {
		return goodsCount;
	}
	public void setGoodsCount(Double goodsCount) {
		this.goodsCount = goodsCount;
	}
	public String getGoodsStandard() {
		return goodsStandard;
	}
	public void setGoodsStandard(String goodsStandard) {
		this.goodsStandard = goodsStandard;
	}
	public String getGoodsPicturePath() {
		return goodsPicturePath;
	}
	public void setGoodsPicturePath(String goodsPicturePath) {
		this.goodsPicturePath = goodsPicturePath;
	}
	public String getSupplierUnique() {
		return supplierUnique;
	}
	public void setSupplierUnique(String supplierUnique) {
		this.supplierUnique = supplierUnique;
	}
	public String getSupplierName() {
		return supplierName;
	}
	public void setSupplierName(String supplierName) {
		this.supplierName = supplierName;
	}
	@Override
	public String toString() {
		return "PurCartDetail [goodsBarcode=" + goodsBarcode + ", goodsName=" + goodsName + ", goodsPrice=" + goodsPrice
				+ ", goodsCount=" + goodsCount + ", goodsStandard=" + goodsStandard + ", goodsPicturePath="
				+ goodsPicturePath + ", supplierUnique=" + supplierUnique + ", supplierName=" + supplierName + "]";
	}
	
}
