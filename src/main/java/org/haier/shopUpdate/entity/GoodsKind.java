package org.haier.shopUpdate.entity;

import java.util.List;

/**
 * 商品分类信息
 * <AUTHOR>
 *
 */
public class GoodsKind {

	private Integer sourceType;//设备类型;1、PC收银机；2、手机；3、网页；4、其他
	private Integer goodsKindId;//分类ID
	private String shopUnique;//店铺编号
	private String goodsKindUnique;//分类编号
	private String goodsKindParunique;//父级分类编号；无则为0
	private String goodsKindName;//分类名称
	private Integer validType;//是否有效；1、有效；2、无效
	private Integer kindType;//分类类型；1、系统分类；2、自定义分类
	private Integer editType;//可编辑状态；1、不可编辑；2、可编辑
	private String oldKindUnique;//删除商品分类时，传入要删除的商品分类
	private String kindUnique;//分类编号
	private String kindIconId;//分类图标ID
	private List<Long> kindUniques;//分类编号集合
	public Integer getSourceType() {
		return sourceType;
	}
	public void setSourceType(Integer sourceType) {
		this.sourceType = sourceType;
	}
	public Integer getGoodsKindId() {
		return goodsKindId;
	}
	public void setGoodsKindId(Integer goodsKindId) {
		this.goodsKindId = goodsKindId;
	}
	public String getShopUnique() {
		return shopUnique;
	}
	public void setShopUnique(String shopUnique) {
		this.shopUnique = shopUnique;
	}
	public String getGoodsKindUnique() {
		return goodsKindUnique;
	}
	public void setGoodsKindUnique(String goodsKindUnique) {
		this.goodsKindUnique = goodsKindUnique;
	}
	public String getGoodsKindParunique() {
		return goodsKindParunique;
	}
	public void setGoodsKindParunique(String goodsKindParunique) {
		this.goodsKindParunique = goodsKindParunique;
	}
	public String getGoodsKindName() {
		return goodsKindName;
	}
	public void setGoodsKindName(String goodsKindName) {
		this.goodsKindName = goodsKindName;
	}
	public Integer getValidType() {
		return validType;
	}
	public void setValidType(Integer validType) {
		this.validType = validType;
	}
	public Integer getKindType() {
		return kindType;
	}
	public void setKindType(Integer kindType) {
		this.kindType = kindType;
	}
	public Integer getEditType() {
		return editType;
	}
	public void setEditType(Integer editType) {
		this.editType = editType;
	}
	public String getOldKindUnique() {
		return oldKindUnique;
	}
	public void setOldKindUnique(String oldKindUnique) {
		this.oldKindUnique = oldKindUnique;
	}
	public String getKindUnique() {
		return kindUnique;
	}
	public void setKindUnique(String kindUnique) {
		this.kindUnique = kindUnique;
	}
	public String getKindIconId() {
		return kindIconId;
	}
	public void setKindIconId(String kindIconId) {
		this.kindIconId = kindIconId;
	}
	@Override
	public String toString() {
		return "GoodsKind [goodsKindId=" + goodsKindId + ", shopUnique=" + shopUnique + ", goodsKindUnique="
				+ goodsKindUnique + ", goodsKindParunique=" + goodsKindParunique + ", goodsKindName=" + goodsKindName
				+ ", validType=" + validType + ", kindIconId=" + kindIconId + ", kindType=" + kindType + ", editType=" + editType + ", oldKindUnique="
				+ oldKindUnique + "]";
	}

    public List<Long> getKindUniques() {
        return kindUniques;
    }

    public void setKindUniques(List<Long> kindUniques) {
        this.kindUniques = kindUniques;
    }
}
