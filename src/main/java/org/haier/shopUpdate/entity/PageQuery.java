package org.haier.shopUpdate.entity;

/**
 * 常用分页查询
 * <AUTHOR>
 *
 */
public class PageQuery {
	private String shopUnique;
	private String startTime;
	private String endTime;
	private Integer pageSize;
	private Integer pageNum;
	private String managerUnique;
	private Integer handleStatus;
	private Integer type;
	public String getShopUnique() {
		return shopUnique;
	}
	public void setShopUnique(String shopUnique) {
		this.shopUnique = shopUnique;
	}
	public String getStartTime() {
		return startTime;
	}
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	public Integer getPageSize() {
		return pageSize;
	}
	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}
	public Integer getPageNum() {
		return pageNum;
	}
	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}
	public Integer getStartNum() {
		return (pageNum-1)*pageSize;
	}
	public String getManagerUnique() {
		return managerUnique;
	}
	public void setManagerUnique(String managerUnique) {
		this.managerUnique = managerUnique;
	}
	public Integer getHandleStatus() {
		return handleStatus;
	}
	public void setHandleStatus(Integer handleStatus) {
		this.handleStatus = handleStatus;
	}
	public Integer getType() {
		return type;
	}
	public void setType(Integer type) {
		this.type = type;
	}
}
