package org.haier.shopUpdate.entity;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @ClassName GoodsWholesaleInfo
 * <AUTHOR>
 * @Date 2025/5/19 17:01
 */

public class GoodsWholesaleInfo implements Serializable {
    /**
     * id
     */
    private Long id;
    /**
     * 批发价
     */
    private BigDecimal wholesalePrice;
    /**
     * 起批数量
     */
    private BigDecimal wholesaleCount;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public BigDecimal getWholesalePrice() {
        return wholesalePrice;
    }

    public void setWholesalePrice(BigDecimal wholesalePrice) {
        this.wholesalePrice = wholesalePrice;
    }

    public BigDecimal getWholesaleCount() {
        return wholesaleCount;
    }

    public void setWholesaleCount(BigDecimal wholesaleCount) {
        this.wholesaleCount = wholesaleCount;
    }
}
