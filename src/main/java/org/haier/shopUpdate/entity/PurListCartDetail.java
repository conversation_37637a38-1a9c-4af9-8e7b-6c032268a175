package org.haier.shopUpdate.entity;

import java.util.List;

public class PurListCartDetail {
	//商品条码
	private String goodsBarcode; 
	//商品名称
	private String goodsName;
	//供货商名称
	private String supplierName;
	//供货商编号
	private Long supplierUnique;
	//商品单价
	private Double goodsPrice;
	//商品数量
	private Double goodsCount;
	//商品规格
	private String goodsStandard;
	//购物车商品全选状态
	private Integer checkAll=1;
	//选中
	private Integer checked;
	//赠品或商品
	private Integer giftType;
	//商品图片
	private String goodsPicturePath;
	//赠品的信息
	private List<String> listPro;
	//赠品数量
	private Integer detailLong;
	//赠品详情
	private List<PurListCartPresent> detail;
	public String getGoodsPicturePath() {
		return goodsPicturePath;
	}
	public void setGoodsPicturePath(String goodsPicturePath) {
		this.goodsPicturePath = goodsPicturePath;
	}
	public Integer getCheckAll() {
		return checkAll;
	}
	public void setCheckAll(Integer checkAll) {
		this.checkAll = checkAll;
	}
	public String getGoodsBarcode() {
		return goodsBarcode;
	}
	public void setGoodsBarcode(String goodsBarcode) {
		this.goodsBarcode = goodsBarcode;
	}
	public String getGoodsName() {
		return goodsName;
	}
	public void setGoodsName(String goodsName) {
		this.goodsName = goodsName;
	}
	public Double getGoodsPrice() {
		return goodsPrice;
	}
	public String getSupplierName() {
		return supplierName;
	}
	public void setSupplierName(String supplierName) {
		this.supplierName = supplierName;
	}
	public Long getSupplierUnique() {
		return supplierUnique;
	}
	public void setSupplierUnique(Long supplierUnique) {
		this.supplierUnique = supplierUnique;
	}
	public void setGoodsPrice(Double goodsPrice) {
		this.goodsPrice = goodsPrice;
	}
	public Double getGoodsCount() {
		return goodsCount;
	}
	public void setGoodsCount(Double goodsCount) {
		this.goodsCount = goodsCount;
	}
	public String getGoodsStandard() {
		return goodsStandard;
	}
	public void setGoodsStandard(String goodsStandard) {
		this.goodsStandard = goodsStandard;
	}
	public Integer getChecked() {
		return checked;
	}
	public void setChecked(Integer checked) {
		this.checked = checked;
	}
	public Integer getGiftType() {
		return giftType;
	}
	public void setGiftType(Integer giftType) {
		this.giftType = giftType;
	}
	public List<PurListCartPresent> getDetail() {
		return detail;
	}
	public void setDetail(List<PurListCartPresent> detail) {
		this.detail = detail;
	}
	public List<String> getListPro() {
		return listPro;
	}
	public void setListPro(List<String> listPro) {
		this.listPro = listPro;
	}
	public Integer getDetailLong() {
		return detailLong;
	}
	public void setDetailLong(Integer detailLong) {
		this.detailLong = detailLong;
	}
	@Override
	public String toString() {
		return "PurListCartDetail [goodsBarcode=" + goodsBarcode + ", goodsName=" + goodsName + ", supplierName="
				+ supplierName + ", supplierUnique=" + supplierUnique + ", goodsPrice=" + goodsPrice + ", goodsCount="
				+ goodsCount + ", goodsStandard=" + goodsStandard + ", checked=" + checked + ", giftType=" + giftType
				+ ", goodsPicturePath=" + goodsPicturePath + ", detail=" + detail + "]";
	}
	
}
