package org.haier.shopUpdate.entity;

public class BeansGetRule {
	
	private Integer beansGetId;//ID
	private Integer beansAgreement;//开通状态，1、开通；0、未开通
	private String shopUnique;//店铺编号
	private String ruleTime;//创建时间
	private Integer mmPer;//免密支付，赠送比率
	private Integer fmmPer;//非免密支付，赠送比率
	private Integer count;//抵扣规则，抵扣最大比率
	public Integer getBeansGetId() {
		return beansGetId;
	}
	public void setBeansGetId(Integer beansGetId) {
		this.beansGetId = beansGetId;
	}
	public String getShopUnique() {
		return shopUnique;
	}
	public void setShopUnique(String shopUnique) {
		this.shopUnique = shopUnique;
	}
	public String getRuleTime() {
		return ruleTime;
	}
	public void setRuleTime(String ruleTime) {
		this.ruleTime = ruleTime;
	}
	public Integer getMmPer() {
		return mmPer;
	}
	public void setMmPer(Integer mmPer) {
		this.mmPer = mmPer;
	}
	public Integer getFmmPer() {
		return fmmPer;
	}
	public void setFmmPer(Integer fmmPer) {
		this.fmmPer = fmmPer;
	}
	public Integer getBeansAgreement() {
		return beansAgreement;
	}
	public void setBeansAgreement(Integer beansAgreement) {
		this.beansAgreement = beansAgreement;
	}
	public Integer getCount() {
		return count;
	}
	public void setCount(Integer count) {
		this.count = count;
	}
	@Override
	public String toString() {
		return "BeansGetRule [beansGetId=" + beansGetId + ", beansAgreement=" + beansAgreement + ", shopUnique="
				+ shopUnique + ", ruleTime=" + ruleTime + ", mmPer=" + mmPer + ", fmmPer=" + fmmPer + ", count=" + count
				+ "]";
	}
}
