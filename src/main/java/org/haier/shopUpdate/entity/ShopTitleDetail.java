package org.haier.shopUpdate.entity;

public class ShopTitleDetail {
	private String id;//Id
	private String titleName;//模块名称
	private String titleImg;//模块图片
	private Integer titleSort;//排序序号
	private Integer modularNum;//指向的模块编号
	private String addTime;//功能添加的时间
	private Integer newTitle;//是否新增功能
	
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getTitleName() {
		return titleName;
	}
	public void setTitleName(String titleName) {
		this.titleName = titleName;
	}
	public String getTitleImg() {
		return titleImg;
	}
	public void setTitleImg(String titleImg) {
		this.titleImg = titleImg;
	}
	public Integer getTitleSort() {
		return titleSort;
	}
	public void setTitleSort(Integer titleSort) {
		this.titleSort = titleSort;
	}
	public Integer getModularNum() {
		return modularNum;
	}
	public void setModularNum(Integer modularNum) {
		this.modularNum = modularNum;
	}
	public String getAddTime() {
		return addTime;
	}
	public void setAddTime(String addTime) {
		this.addTime = addTime;
	}
	public Integer getNewTitle() {
		return newTitle;
	}
	public void setNewTitle(Integer newTitle) {
		this.newTitle = newTitle;
	}
	@Override
	public String toString() {
		return "ShopTitleDetail [titleName=" + titleName + ", titleImg=" + titleImg + ", titleSort=" + titleSort
				+ ", modularNum=" + modularNum + ", addTime=" + addTime + ", newTitle=" + newTitle + "]";
	}
}
