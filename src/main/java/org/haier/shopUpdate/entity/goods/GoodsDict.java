package org.haier.shopUpdate.entity.goods;

import java.math.BigDecimal;

public class GoodsDict {
    private String barcode;//商品条码
    private String name;//商品名称
    private String alias;//商品昵称（拼音码）
    private Long kind;//商品分类编号
    private String standard;//商品规格
    private String unit;//商品单位
    private String brand;//商品品牌
    private String pictureName;//图片保存路径
    private Integer base;//是否标准库中的商品;1、是；0、否；
    private Integer shopUnique;//用于关联商品分类时关联公共部分，不用填
    private String address;//商品产地
    private String remarks;//备注信息
    private BigDecimal inprice;//进价
    private BigDecimal saleprice;//售价
    private String foreignKey;//外键，关联商品信息
    private Integer contain;//包含最小规格的商品数量
    private Integer finalSure;//是否已最终确认：0、未确认；1、已确认，不可再次修改；

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public Long getKind() {
        return kind;
    }

    public void setKind(Long kind) {
        this.kind = kind;
    }

    public String getStandard() {
        return standard;
    }

    public void setStandard(String standard) {
        this.standard = standard;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getPictureName() {
        return pictureName;
    }

    public void setPictureName(String pictureName) {
        this.pictureName = pictureName;
    }

    public Integer getBase() {
        return base;
    }

    public void setBase(Integer base) {
        this.base = base;
    }

    public Integer getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Integer shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public BigDecimal getInprice() {
        return inprice;
    }

    public void setInprice(BigDecimal inprice) {
        this.inprice = inprice;
    }

    public BigDecimal getSaleprice() {
        return saleprice;
    }

    public void setSaleprice(BigDecimal saleprice) {
        this.saleprice = saleprice;
    }

    public String getForeignKey() {
        return foreignKey;
    }

    public void setForeignKey(String foreignKey) {
        this.foreignKey = foreignKey;
    }

    public Integer getContain() {
        return contain;
    }

    public void setContain(Integer contain) {
        this.contain = contain;
    }

    public Integer getFinalSure() {
        return finalSure;
    }

    public void setFinalSure(Integer finalSure) {
        this.finalSure = finalSure;
    }
}
