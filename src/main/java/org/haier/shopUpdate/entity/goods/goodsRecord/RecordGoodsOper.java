package org.haier.shopUpdate.entity.goods.goodsRecord;

import javax.validation.constraints.NotNull;
import java.util.List;

public class RecordGoodsOper {
    /**
     * 记录ID
     */
    private Long id;
    /**
     * 商品ID
     */
    private Long goodsId;
    /**
     * 商品条码
     */
    @NotNull
    private String goodsBarcode;
    /**
     * 店铺编号
     */
    @NotNull
    private Long shopUnique;
    /**
     * 操作类型：
     * 1、新增；2、修改；3、删除；
     */
    @NotNull
    private Integer operType;
    /**
     * 设备类型
     * 1、手机APP；2、收银机软件；3、网页客户端；4、小程序客户端；5、供货商APP；6、供货商后台；7、其他（具体可参考sys_dict_data数据库中：device_source）
     */
    @NotNull
    private Integer deviceSource;
    /**
     * 设备信息；
     * 所有设备提供用户信息，再额外提供如下信息
     * 1、手机APP提供手机型号；
     * 2、收银机软件提供版本号和macId
     * 3、网页客户端提供浏览器信息和IP地址信息
     * 4、小程序客户端提供用户信息
     * 5、供货商APP提供供货商信息
     * 6、供货商提供供货商信息
     * 7、其他新增时，需要额外提供
     */
    @NotNull
    private String deviceSourceMsg;
    /**
     * 操作人用户ID
     * 1、店铺员工为staffId
     * 2、小程序用户为cusUnique
     * 3、供货商为供货商员工ID
     * 4、
     */
    @NotNull
    private String userId;
    /**
     * 操作人名称
     * 1、店铺员工为staffName;
     * 2、小程序用户为小程序ID
     * 3、供货商为供货商员工staffName
     * 4、其他具体根据实际情况调整
     */
    @NotNull
    private String userName;
    /**
     * 操作人类型：
     * 1、收银机员工
     * 2、客户
     * 3、供货商
     */
    @NotNull
    private Integer userType;
    /**
     * 记录创建时间
     */
    private String createTtime;
    /**
     * 商品修改由何种操作引起
     * 1、商品修改，删除，新增
     * 2、订单销售
     * 3、退款、退货
     * 4、商品出入库
     * 5、商品盘库
     * 6、网页采购
     * 7、采购退货
     * 8、商品调拨
     * 9、商品上下架
     * 10、商品进价、售价 调价
     * 11、小程序商品上下架
     * 12、商品分类转移
     * 13、供货商城确认收货
     */
    @NotNull
    private Integer operSource;

    /**
     * 对应的单号
     * 1、商品修改，暂无记录
     * 2、销售订单编号
     * 3、退款订单编号，非销售订单号
     * 4、商品出入库记录编号
     * 5、盘点单号
     * 6、采购订单号
     * 7、采购退款单号
     * 8、调拨单号
     * 9、商品上下架暂无记录
     * 10、调价单号
     * 11、小程序商品上下架暂无记录
     * 12、分类转移暂无单号
     * 13、供货商采购单号
     */
    private String orderNo;

    /**
     * 修改字段详情，后端自己比较生产，不需要上传
     */
    private List<RecordGoodsOperDetail> detailList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Long goodsId) {
        this.goodsId = goodsId;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public Integer getOperType() {
        return operType;
    }

    public void setOperType(Integer operType) {
        this.operType = operType;
    }

    public Integer getDeviceSource() {
        return deviceSource;
    }

    public void setDeviceSource(Integer deviceSource) {
        this.deviceSource = deviceSource;
    }

    public String getDeviceSourceMsg() {
        return deviceSourceMsg;
    }

    public void setDeviceSourceMsg(String deviceSourceMsg) {
        this.deviceSourceMsg = deviceSourceMsg;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    public String getCreateTtime() {
        return createTtime;
    }

    public void setCreateTtime(String createTtime) {
        this.createTtime = createTtime;
    }

    public Integer getOperSource() {
        return operSource;
    }

    public void setOperSource(Integer operSource) {
        this.operSource = operSource;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public List<RecordGoodsOperDetail> getDetailList() {
        return detailList;
    }

    public void setDetailList(List<RecordGoodsOperDetail> detailList) {
        this.detailList = detailList;
    }
}
