package org.haier.shopUpdate.entity.goods.goodsRecord;

import org.jfree.chart.plot.PlotRenderingInfo;
public class GoodsOperParam {
    //设备类型：1、手机APP;2、收银机软件；3、商家后台网页；4、一刻钟到家小程序；5、其他
    private Integer deviceSource;
    //操作源信息：1、APP手机型号；2、收银机MACID；3、浏览器信息；4、手机型号信息
    private String devicesourcemsg;
    //客户类型：1、B端店铺员工；2、C端客户；3、系统管理员；4、其他
    private Integer userType;
    //用户名称
    private String userName;
    //用户ID信息
    private Integer userId;

    public Integer getDeviceSource() {
        return deviceSource;
    }

    public void setDeviceSource(Integer deviceSource) {
        this.deviceSource = deviceSource;
    }

    public String getDevicesourcemsg() {
        return devicesourcemsg;
    }

    public void setDevicesourcemsg(String devicesourcemsg) {
        this.devicesourcemsg = devicesourcemsg;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    @Override
    public String toString() {
        return "GoodsOperParam{" +
                "deviceSource=" + deviceSource +
                ", devicesourcemsg='" + devicesourcemsg + '\'' +
                ", userType=" + userType +
                ", userName='" + userName + '\'' +
                ", userId=" + userId +
                '}';
    }
}