package org.haier.shopUpdate.entity.goods;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * 仓库调拨单详情
 */
public class AllocationShopDetailEntity implements Serializable {

    /**
     * 调拨单详情id
     */
    private Integer purchaseListDetailId;
    /**
     * 调拨单唯一标识符
     */
    private Long purchaseListUnique;
    /**
     * 商品名称（供货商）
     */
    private String goodsName;
    /**
     * 商品条形码
     */
    private String goodsBarcode;
    /**
     * 订购数量
     */
    private BigDecimal purchaseListDetailCount;
    /**
     * 采购价格
     */
    private BigDecimal purchaseListDetailPrice;


    public Integer getPurchaseListDetailId() {
        return purchaseListDetailId;
    }

    public void setPurchaseListDetailId(Integer purchaseListDetailId) {
        this.purchaseListDetailId = purchaseListDetailId;
    }

    public Long getPurchaseListUnique() {
        return purchaseListUnique;
    }

    public void setPurchaseListUnique(Long purchaseListUnique) {
        this.purchaseListUnique = purchaseListUnique;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public BigDecimal getPurchaseListDetailCount() {
        return purchaseListDetailCount;
    }

    public void setPurchaseListDetailCount(BigDecimal purchaseListDetailCount) {
        this.purchaseListDetailCount = purchaseListDetailCount;
    }

    public BigDecimal getPurchaseListDetailPrice() {
        return purchaseListDetailPrice;
    }

    public void setPurchaseListDetailPrice(BigDecimal purchaseListDetailPrice) {
        this.purchaseListDetailPrice = purchaseListDetailPrice;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof AllocationShopDetailEntity)) return false;
        AllocationShopDetailEntity that = (AllocationShopDetailEntity) o;
        return Objects.equals(getPurchaseListDetailId(), that.getPurchaseListDetailId()) && Objects.equals(getPurchaseListUnique(), that.getPurchaseListUnique()) && Objects.equals(getGoodsName(), that.getGoodsName()) && Objects.equals(getGoodsBarcode(), that.getGoodsBarcode()) && Objects.equals(getPurchaseListDetailCount(), that.getPurchaseListDetailCount()) && Objects.equals(getPurchaseListDetailPrice(), that.getPurchaseListDetailPrice());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getPurchaseListDetailId(), getPurchaseListUnique(), getGoodsName(), getGoodsBarcode(), getPurchaseListDetailCount(), getPurchaseListDetailPrice());
    }
}