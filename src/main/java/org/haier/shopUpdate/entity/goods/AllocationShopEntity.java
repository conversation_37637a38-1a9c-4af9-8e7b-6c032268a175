package org.haier.shopUpdate.entity.goods;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * 连锁店调拨单
 */
public class AllocationShopEntity implements Serializable {

    /**
     * 调拨单id
     */
    private Integer purchaseListId;
    /**
     * 进货订单唯一标识符
     */
    private Long purchaseListUnique;
    /**
     * 订单备注
     */
    private String purchaseListRemark;
    /**
     * 入库日期
     */
    private Date purchaseListDate;
    /**
     * 调入仓库ID
     */
    private Long storehouseInId;
    /**
     * 调出仓库ID
     */
    private Long storehouseOutId;
    /**
     * 操作人
     */
    private Integer userId;
    /**
     * @see org.haier.shopUpdate.enums.AllocationShopStatus
     */
    private Integer allocationStatus;

    private Integer recipientsUserId;
    private String recipientsUserIdName;
    private Date recipientsTime;
    private String userName;


    public Integer getPurchaseListId() {
        return purchaseListId;
    }

    public void setPurchaseListId(Integer purchaseListId) {
        this.purchaseListId = purchaseListId;
    }

    public Long getPurchaseListUnique() {
        return purchaseListUnique;
    }

    public void setPurchaseListUnique(Long purchaseListUnique) {
        this.purchaseListUnique = purchaseListUnique;
    }

    public String getPurchaseListRemark() {
        return purchaseListRemark;
    }

    public void setPurchaseListRemark(String purchaseListRemark) {
        this.purchaseListRemark = purchaseListRemark;
    }

    public Date getPurchaseListDate() {
        return purchaseListDate;
    }

    public void setPurchaseListDate(Date purchaseListDate) {
        this.purchaseListDate = purchaseListDate;
    }

    public Long getStorehouseInId() {
        return storehouseInId;
    }

    public void setStorehouseInId(Long storehouseInId) {
        this.storehouseInId = storehouseInId;
    }

    public Long getStorehouseOutId() {
        return storehouseOutId;
    }

    public void setStorehouseOutId(Long storehouseOutId) {
        this.storehouseOutId = storehouseOutId;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Integer getAllocationStatus() {
        return allocationStatus;
    }

    public void setAllocationStatus(Integer allocationStatus) {
        this.allocationStatus = allocationStatus;
    }

    public Integer getRecipientsUserId() {
        return recipientsUserId;
    }

    public void setRecipientsUserId(Integer recipientsUserId) {
        this.recipientsUserId = recipientsUserId;
    }

    public String getRecipientsUserIdName() {
        return recipientsUserIdName;
    }

    public void setRecipientsUserIdName(String recipientsUserIdName) {
        this.recipientsUserIdName = recipientsUserIdName;
    }

    public Date getRecipientsTime() {
        return recipientsTime;
    }

    public void setRecipientsTime(Date recipientsTime) {
        this.recipientsTime = recipientsTime;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof AllocationShopEntity)) return false;
        AllocationShopEntity that = (AllocationShopEntity) o;
        return Objects.equals(getPurchaseListId(), that.getPurchaseListId()) && Objects.equals(getPurchaseListUnique(), that.getPurchaseListUnique()) && Objects.equals(getPurchaseListRemark(), that.getPurchaseListRemark()) && Objects.equals(getPurchaseListDate(), that.getPurchaseListDate()) && Objects.equals(getStorehouseInId(), that.getStorehouseInId()) && Objects.equals(getStorehouseOutId(), that.getStorehouseOutId()) && Objects.equals(getUserId(), that.getUserId()) && Objects.equals(getAllocationStatus(), that.getAllocationStatus()) && Objects.equals(getRecipientsUserId(), that.getRecipientsUserId()) && Objects.equals(getRecipientsUserIdName(), that.getRecipientsUserIdName()) && Objects.equals(getRecipientsTime(), that.getRecipientsTime()) && Objects.equals(getUserName(), that.getUserName());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getPurchaseListId(), getPurchaseListUnique(), getPurchaseListRemark(), getPurchaseListDate(), getStorehouseInId(), getStorehouseOutId(), getUserId(), getAllocationStatus(), getRecipientsUserId(), getRecipientsUserIdName(), getRecipientsTime(), getUserName());
    }
}