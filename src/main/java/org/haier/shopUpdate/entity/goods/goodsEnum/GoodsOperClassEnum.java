package org.haier.shopUpdate.entity.goods.goodsEnum;

public enum GoodsOperClassEnum {
    GOODS_IN_PRICE("商品进价", "goodsInPrice", 1),
    GOODS_SALE_PRICE("商品售价", "goodsSalePrice", 2),
    GOODS_CUS_PRICE("商品会员价" , "goodsCusPrice", 3),
    GOODS_WEB_PRICE("商品网购价", "goodsWebSalePrice", 4),
    GOODS_COUNT("商品库存", "goodsCount", 5),
    GOODS_KIND_UNIQUE("商品分类" , "goodsKindUnique" ,6),
    GOODS_PICTUREPATH("商品图片", "goodsPicturepath", 7),
    GOODS_STANDARD("商品规格", "goodsStandard", 8),
    GOODS_UNIT("商品单位", "goodsUnit", 9),
    GOODS_LIFE("商品保质期" , "goodsLife" , 10),
    GOODS_POINTS("商品积分配置", "goodsPoints" ,11),
    GOODS_ADDRESS("商品产地", "goodsAddress", 12),
    GOODS_CONTAIN("商品换算比例", "goodsContain", 13),
    FOREIGN_KEY("关联的商品信息", "foreignKey", 14),
    DEFAULT_SUPPLIER_UNIQUE("默认供应商信息", "defaultSupplierUnique", 15),
    PC_SHELF_STATE("商品上下架", "pcShelfState", 16),
    SHELF_STATE("线上商品商品上下架", "shelfState", 17),
    BINDING_UNIQUE("商品捆绑销售信息", "bindingUnique", 18),
    GOODS_CHENG_TYPE("商品称重类型", "goodsChengType", 19),
    GOODS_POINT_TYPE("积分计算方法" , "goodsPointsType", 20),
    GOODS_COMMISSION_TYPE("提成计算方法", "goodsCommissionType", 21),
    IS_SPECIAL("是否单独配送商品", "isSpecial", 22),

    ;

    GoodsOperClassEnum(String label, String name, Integer value){
        this.label = label;
        this.name = name;
        this.value = value;
    }

    private String label;
    private String name;
    private Integer value;

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    /**
     * 根据name获取对应的value值
     * @param name
     * @return
     */
    public static Integer getEnumValue(String name){
        GoodsOperClassEnum[] enums = GoodsOperClassEnum.values();
        for(GoodsOperClassEnum g : enums){
            if(g.getName().equals(name)){
                return g.getValue();
            }
        }
        return null;
    }
}
