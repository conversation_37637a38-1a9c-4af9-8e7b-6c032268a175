package org.haier.shopUpdate.entity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description 盘点筐重量
 */
public class InventoryTaskBucketWeightEntity {
    /**
    * id
    */
    private Long id;

    /**
    * 店铺编号
    */
    private Long shopUnique;

    /**
    * 筐重量
    */
    private BigDecimal bucketWeight;

    /**
    * 修改人
    */
    private Integer updateUser;

    /**
    * 修改时间
    */
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public BigDecimal getBucketWeight() {
        return bucketWeight;
    }

    public void setBucketWeight(BigDecimal bucketWeight) {
        this.bucketWeight = bucketWeight;
    }

    public Integer getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Integer updateUser) {
        this.updateUser = updateUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}