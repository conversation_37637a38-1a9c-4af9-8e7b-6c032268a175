package org.haier.shopUpdate.entity;

public class CusConsumptionRecord {
	//消费或充值记录;
	private Integer recId;
	//消费时间
	private String recDate;
	//消费金额
	private Double recMoney;
	//本次消费获得的积分
	private Integer salePoints;
	//操作员姓名
	private String staffName;
	//操作类型
	private String consumptionType;
	//操作订单编号
	private String saleListUnique;
	public Integer getRecId() {
		return recId;
	}
	public void setRecId(Integer recId) {
		this.recId = recId;
	}
	public String getRecDate() {
		return recDate;
	}
	public void setRecDate(String recDate) {
		this.recDate = recDate;
	}
	public Double getRecMoney() {
		return recMoney;
	}
	public void setRecMoney(Double recMoney) {
		this.recMoney = recMoney;
	}
	public Integer getSalePoints() {
		return salePoints;
	}
	public void setSalePoints(Integer salePoints) {
		this.salePoints = salePoints;
	}
	public String getStaffName() {
		return staffName;
	}
	public void setStaffName(String staffName) {
		this.staffName = staffName;
	}
	public String getConsumptionType() {
		return consumptionType;
	}
	public void setConsumptionType(String consumptionType) {
		this.consumptionType = consumptionType;
	}
	public String getSaleListUnique() {
		return saleListUnique;
	}
	public void setSaleListUnique(String saleListUnique) {
		this.saleListUnique = saleListUnique;
	}
}
