package org.haier.shopUpdate.entity;

import java.util.List;

public class PurCartGoods {
	//供货商编号
	private String supplierUnique;
	//供货商名称
	private String supplierName;
	//订单编号
	private Long purListUnique;
	//商品详情
	private List<PurCartDetail> listDetail;
	//供货商商品总数量
	private Double purListSum;
	//供货商商品总计恩
	private Double purListTotal;
	
	public String getSupplierUnique() {
		return supplierUnique;
	}
	public void setSupplierUnique(String supplierUnique) {
		this.supplierUnique = supplierUnique;
	}
	public String getSupplierName() {
		return supplierName;
	}
	public void setSupplierName(String supplierName) {
		this.supplierName = supplierName;
	}
	public List<PurCartDetail> getListDetail() {
		return listDetail;
	}
	public void setListDetail(List<PurCartDetail> listDetail) {
		this.listDetail = listDetail;
	}
	public Long getPurListUnique() {
		return purListUnique;
	}
	public void setPurListUnique(Long purListUnique) {
		this.purListUnique = purListUnique;
	}
	public Double getPurListSum() {
		return purListSum;
	}
	public void setPurListSum(Double purListSum) {
		this.purListSum = purListSum;
	}
	public Double getPurListTotal() {
		return purListTotal;
	}
	public void setPurListTotal(Double purListTotal) {
		this.purListTotal = purListTotal;
	}
	@Override
	public String toString() {
		return "PurCartGoods [supplierUnique=" + supplierUnique + ", supplierName=" + supplierName + ", purListUnique="
				+ purListUnique + ", listDetail=" + listDetail + ", purListSum=" + purListSum + ", purListTotal="
				+ purListTotal + "]";
	}
	
}
