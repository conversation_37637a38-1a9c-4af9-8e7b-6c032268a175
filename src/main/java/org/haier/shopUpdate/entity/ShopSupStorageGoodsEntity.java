package org.haier.shopUpdate.entity;

import java.math.BigDecimal;
import java.sql.Timestamp;

public class ShopSupStorageGoodsEntity {
    private int sssdId;
    private Long shopUnique;
    private String goodsBarcode;
    private Long billId;
    private Long billDetailId;
    private BigDecimal stayStockCount;
    private BigDecimal goodsInPrice;
    private BigDecimal goodsSalePrice;
    private BigDecimal goodsWebSalePrice;
    private String goodsCusPrice;
    private BigDecimal goodsInPriceOld;
    private BigDecimal goodsSalePriceOld;
    private BigDecimal goodsWebSalePriceOld;
    private String goodsCusPriceOld;
    private Long createId;
    private String createBy;
    private Timestamp createTime;
    private Long modifyId;
    private String modifyBy;
    private Timestamp modifyTime;
    private Integer delFlag;

    public int getSssdId() {
        return sssdId;
    }

    public void setSssdId(int sssdId) {
        this.sssdId = sssdId;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public Long getBillId() {
        return billId;
    }

    public void setBillId(Long billId) {
        this.billId = billId;
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public Long getBillDetailId() {
        return billDetailId;
    }

    public void setBillDetailId(Long billDetailId) {
        this.billDetailId = billDetailId;
    }

    public BigDecimal getStayStockCount() {
        return stayStockCount;
    }

    public void setStayStockCount(BigDecimal stayStockCount) {
        this.stayStockCount = stayStockCount;
    }

    public BigDecimal getGoodsInPrice() {
        return goodsInPrice;
    }

    public void setGoodsInPrice(BigDecimal goodsInPrice) {
        this.goodsInPrice = goodsInPrice;
    }

    public BigDecimal getGoodsSalePrice() {
        return goodsSalePrice;
    }

    public void setGoodsSalePrice(BigDecimal goodsSalePrice) {
        this.goodsSalePrice = goodsSalePrice;
    }

    public BigDecimal getGoodsWebSalePrice() {
        return goodsWebSalePrice;
    }

    public void setGoodsWebSalePrice(BigDecimal goodsWebSalePrice) {
        this.goodsWebSalePrice = goodsWebSalePrice;
    }

    public String getGoodsCusPrice() {
        return goodsCusPrice;
    }

    public void setGoodsCusPrice(String goodsCusPrice) {
        this.goodsCusPrice = goodsCusPrice;
    }

    public BigDecimal getGoodsInPriceOld() {
        return goodsInPriceOld;
    }

    public void setGoodsInPriceOld(BigDecimal goodsInPriceOld) {
        this.goodsInPriceOld = goodsInPriceOld;
    }

    public BigDecimal getGoodsSalePriceOld() {
        return goodsSalePriceOld;
    }

    public void setGoodsSalePriceOld(BigDecimal goodsSalePriceOld) {
        this.goodsSalePriceOld = goodsSalePriceOld;
    }

    public BigDecimal getGoodsWebSalePriceOld() {
        return goodsWebSalePriceOld;
    }

    public void setGoodsWebSalePriceOld(BigDecimal goodsWebSalePriceOld) {
        this.goodsWebSalePriceOld = goodsWebSalePriceOld;
    }

    public String getGoodsCusPriceOld() {
        return goodsCusPriceOld;
    }

    public void setGoodsCusPriceOld(String goodsCusPriceOld) {
        this.goodsCusPriceOld = goodsCusPriceOld;
    }

    public Long getCreateId() {
        return createId;
    }

    public void setCreateId(Long createId) {
        this.createId = createId;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Long getModifyId() {
        return modifyId;
    }

    public void setModifyId(Long modifyId) {
        this.modifyId = modifyId;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public Timestamp getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Timestamp modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ShopSupStorageGoodsEntity that = (ShopSupStorageGoodsEntity) o;

        if (sssdId != that.sssdId) return false;
        if (shopUnique != that.shopUnique) return false;
        if (billDetailId != that.billDetailId) return false;
        if (goodsBarcode != null ? !goodsBarcode.equals(that.goodsBarcode) : that.goodsBarcode != null) return false;
        if (billId != null ? !billId.equals(that.billId) : that.billId != null) return false;
        if (stayStockCount != null ? !stayStockCount.equals(that.stayStockCount) : that.stayStockCount != null)
            return false;
        if (goodsInPrice != null ? !goodsInPrice.equals(that.goodsInPrice) : that.goodsInPrice != null) return false;
        if (goodsSalePrice != null ? !goodsSalePrice.equals(that.goodsSalePrice) : that.goodsSalePrice != null)
            return false;
        if (goodsWebSalePrice != null ? !goodsWebSalePrice.equals(that.goodsWebSalePrice) : that.goodsWebSalePrice != null)
            return false;
        if (goodsCusPrice != null ? !goodsCusPrice.equals(that.goodsCusPrice) : that.goodsCusPrice != null)
            return false;
        if (goodsSalePriceOld != null ? !goodsSalePriceOld.equals(that.goodsSalePriceOld) : that.goodsSalePriceOld != null)
            return false;
        if (goodsWebSalePriceOld != null ? !goodsWebSalePriceOld.equals(that.goodsWebSalePriceOld) : that.goodsWebSalePriceOld != null)
            return false;
        if (goodsCusPriceOld != null ? !goodsCusPriceOld.equals(that.goodsCusPriceOld) : that.goodsCusPriceOld != null)
            return false;
        if (createId != null ? !createId.equals(that.createId) : that.createId != null) return false;
        if (createBy != null ? !createBy.equals(that.createBy) : that.createBy != null) return false;
        if (createTime != null ? !createTime.equals(that.createTime) : that.createTime != null) return false;
        if (modifyId != null ? !modifyId.equals(that.modifyId) : that.modifyId != null) return false;
        if (modifyBy != null ? !modifyBy.equals(that.modifyBy) : that.modifyBy != null) return false;
        if (modifyTime != null ? !modifyTime.equals(that.modifyTime) : that.modifyTime != null) return false;
        if (delFlag != null ? !delFlag.equals(that.delFlag) : that.delFlag != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = sssdId;
        result = 31 * result + (shopUnique != null ? shopUnique.hashCode() : 0);
        result = 31 * result + (goodsBarcode != null ? goodsBarcode.hashCode() : 0);
        result = 31 * result + (billId != null ? billId.hashCode() : 0);
        result = 31 * result + (int) (billDetailId ^ (billDetailId >>> 32));
        result = 31 * result + (stayStockCount != null ? stayStockCount.hashCode() : 0);
        result = 31 * result + (goodsInPrice != null ? goodsInPrice.hashCode() : 0);
        result = 31 * result + (goodsSalePrice != null ? goodsSalePrice.hashCode() : 0);
        result = 31 * result + (goodsWebSalePrice != null ? goodsWebSalePrice.hashCode() : 0);
        result = 31 * result + (goodsCusPrice != null ? goodsCusPrice.hashCode() : 0);
        result = 31 * result + (goodsSalePriceOld != null ? goodsSalePriceOld.hashCode() : 0);
        result = 31 * result + (goodsWebSalePriceOld != null ? goodsWebSalePriceOld.hashCode() : 0);
        result = 31 * result + (goodsCusPriceOld != null ? goodsCusPriceOld.hashCode() : 0);
        result = 31 * result + (createId != null ? createId.hashCode() : 0);
        result = 31 * result + (createBy != null ? createBy.hashCode() : 0);
        result = 31 * result + (createTime != null ? createTime.hashCode() : 0);
        result = 31 * result + (modifyId != null ? modifyId.hashCode() : 0);
        result = 31 * result + (modifyBy != null ? modifyBy.hashCode() : 0);
        result = 31 * result + (modifyTime != null ? modifyTime.hashCode() : 0);
        result = 31 * result + (delFlag != null ? delFlag.hashCode() : 0);
        return result;
    }
}
