package org.haier.shopUpdate.entity;


import org.haier.shopUpdate.validator.AddGroup;
import org.haier.shopUpdate.validator.DeleteGroup;
import org.haier.shopUpdate.validator.SearchGroup;
import org.haier.shopUpdate.validator.UpdateGroup;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.sql.Date;

public class GoodsPositionEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "ID不能为空",groups = {UpdateGroup.class, DeleteGroup.class})
    private Long id;

    @NotNull(message = "店铺id不能为空",groups = {AddGroup.class, SearchGroup.class,UpdateGroup.class})
    private Long shopUnique;
    /**
     * 父级id
     */
    @NotNull(message = "parentId不能为空",groups = {AddGroup.class,UpdateGroup.class})
    private Long parentId;
    /**
     * 货位名称
     */
    @NotNull(message = "货位名称不能为空",groups = {AddGroup.class,UpdateGroup.class})
    private String positionName;
    /**
     * 货位全名称
     */
    private String completePositionName;
    /**
     * 级别
     */
    private Integer level=0;    /**
     * 级别
     */
    private Date updateTime;    /**
     * 级别
     */
    private Date createTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getPositionName() {
        return positionName;
    }

    public void setPositionName(String positionName) {
        this.positionName = positionName;
    }

    public String getCompletePositionName() {
        return completePositionName;
    }

    public void setCompletePositionName(String completePositionName) {
        this.completePositionName = completePositionName;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
