package org.haier.shopUpdate.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * @description 聚合码详情
 */
public class ShopAggregationCodeEntity implements Serializable {

    /**
    * id
    */
    private Long id;

    /**
    * 店铺编号
    */
    private Long shopUnique;

    /**
    * 营业执照
    */
    private String license;

    /**
    * 手持营业执照
    */
    private String licenseHand;

    /**
    * 法人身份证人像面
    */
    private String legalIdCardPortrait;

    /**
    * 法人身份证国徽面
    */
    private String legalIdCardEmblem;

    /**
    * 法人手持身份证
    */
    private String legalIdCardHand;

    /**
    * 法人身份证号
    */
    private String legalIdCardCode;

    /**
    * 法人姓名
    */
    private String legalName;

    /**
    * 银行卡正面(有字的一面)
    */
    private String bankCardFront;

    /**
    * 银行卡背面
    */
    private String bankCardBack;

    /**
    * 银行卡号
    */
    private String bankCode;

    /**
    * 开户行
    */
    private String openBank;

    /**
    * 门头照
    */
    private String shopDoorhead;

    /**
    * 收银台照片
    */
    private String shopCashier;

    /**
    * 店内照
    */
    private String shopInside;

    /**
    * 营业执照商户名称
    */
    private String licenseMerchantName;

    /**
    * 营业执照号
    */
    private String licenseCode;

    /**
    * 营业执照地址
    */
    private String licenseAddress;

    /**
    * 持卡人身份证人像面
    */
    private String cardholderIdCardPortrait;

    /**
    * 持卡人身份证国徽面
    */
    private String cardholderIdCardEmblem;

    /**
    * 持卡人身份证号
    */
    private String cardholderIdCardCode;

    /**
    * 持卡人姓名
    */
    private String cardholderName;

    /**
    * 合利宝授权书
    */
    private String helibaoAuth;

    /**
    * 瑞银信授权书
    */
    private String ruiyinxinAuth;

    /**
    * 合利宝审核状态
    */
    private Integer helibaoAuditStatus;

    /**
    * 瑞银信审核状态
    */
    private Integer ruiyinxinAuditStatus;

    /**
    * 合利宝拒绝原因
    */
    private String helibaoRefuseReason;

    /**
    * 瑞银信拒绝原因
    */
    private String ruiyinxinRefuseReason;

    /**
    * 聚合支付拒绝原因
    */
    private String aggregateRefuseReason;

    /**
    * 申请人类型0未知1法人申请2非法人申请
    */
    private Integer aggregateApplyType;

    /**
     * 聚合码数据类型：1草稿2非草稿
     */
    private Integer aggregateCodeType;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 修改时间
    */
    private Date modifyTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getLicense() {
        return license;
    }

    public void setLicense(String license) {
        this.license = license;
    }

    public String getLicenseHand() {
        return licenseHand;
    }

    public void setLicenseHand(String licenseHand) {
        this.licenseHand = licenseHand;
    }

    public String getLegalIdCardPortrait() {
        return legalIdCardPortrait;
    }

    public void setLegalIdCardPortrait(String legalIdCardPortrait) {
        this.legalIdCardPortrait = legalIdCardPortrait;
    }

    public String getLegalIdCardEmblem() {
        return legalIdCardEmblem;
    }

    public void setLegalIdCardEmblem(String legalIdCardEmblem) {
        this.legalIdCardEmblem = legalIdCardEmblem;
    }

    public String getLegalIdCardHand() {
        return legalIdCardHand;
    }

    public void setLegalIdCardHand(String legalIdCardHand) {
        this.legalIdCardHand = legalIdCardHand;
    }

    public String getLegalIdCardCode() {
        return legalIdCardCode;
    }

    public void setLegalIdCardCode(String legalIdCardCode) {
        this.legalIdCardCode = legalIdCardCode;
    }

    public String getLegalName() {
        return legalName;
    }

    public void setLegalName(String legalName) {
        this.legalName = legalName;
    }

    public String getBankCardFront() {
        return bankCardFront;
    }

    public void setBankCardFront(String bankCardFront) {
        this.bankCardFront = bankCardFront;
    }

    public String getBankCardBack() {
        return bankCardBack;
    }

    public void setBankCardBack(String bankCardBack) {
        this.bankCardBack = bankCardBack;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getOpenBank() {
        return openBank;
    }

    public void setOpenBank(String openBank) {
        this.openBank = openBank;
    }

    public String getShopDoorhead() {
        return shopDoorhead;
    }

    public void setShopDoorhead(String shopDoorhead) {
        this.shopDoorhead = shopDoorhead;
    }

    public String getShopCashier() {
        return shopCashier;
    }

    public void setShopCashier(String shopCashier) {
        this.shopCashier = shopCashier;
    }

    public String getShopInside() {
        return shopInside;
    }

    public void setShopInside(String shopInside) {
        this.shopInside = shopInside;
    }

    public String getLicenseMerchantName() {
        return licenseMerchantName;
    }

    public void setLicenseMerchantName(String licenseMerchantName) {
        this.licenseMerchantName = licenseMerchantName;
    }

    public String getLicenseCode() {
        return licenseCode;
    }

    public void setLicenseCode(String licenseCode) {
        this.licenseCode = licenseCode;
    }

    public String getLicenseAddress() {
        return licenseAddress;
    }

    public void setLicenseAddress(String licenseAddress) {
        this.licenseAddress = licenseAddress;
    }

    public String getCardholderIdCardPortrait() {
        return cardholderIdCardPortrait;
    }

    public void setCardholderIdCardPortrait(String cardholderIdCardPortrait) {
        this.cardholderIdCardPortrait = cardholderIdCardPortrait;
    }

    public String getCardholderIdCardEmblem() {
        return cardholderIdCardEmblem;
    }

    public void setCardholderIdCardEmblem(String cardholderIdCardEmblem) {
        this.cardholderIdCardEmblem = cardholderIdCardEmblem;
    }

    public String getCardholderIdCardCode() {
        return cardholderIdCardCode;
    }

    public void setCardholderIdCardCode(String cardholderIdCardCode) {
        this.cardholderIdCardCode = cardholderIdCardCode;
    }

    public String getCardholderName() {
        return cardholderName;
    }

    public void setCardholderName(String cardholderName) {
        this.cardholderName = cardholderName;
    }

    public String getHelibaoAuth() {
        return helibaoAuth;
    }

    public void setHelibaoAuth(String helibaoAuth) {
        this.helibaoAuth = helibaoAuth;
    }

    public String getRuiyinxinAuth() {
        return ruiyinxinAuth;
    }

    public void setRuiyinxinAuth(String ruiyinxinAuth) {
        this.ruiyinxinAuth = ruiyinxinAuth;
    }

    public Integer getHelibaoAuditStatus() {
        return helibaoAuditStatus;
    }

    public void setHelibaoAuditStatus(Integer helibaoAuditStatus) {
        this.helibaoAuditStatus = helibaoAuditStatus;
    }

    public Integer getRuiyinxinAuditStatus() {
        return ruiyinxinAuditStatus;
    }

    public void setRuiyinxinAuditStatus(Integer ruiyinxinAuditStatus) {
        this.ruiyinxinAuditStatus = ruiyinxinAuditStatus;
    }

    public String getHelibaoRefuseReason() {
        return helibaoRefuseReason;
    }

    public void setHelibaoRefuseReason(String helibaoRefuseReason) {
        this.helibaoRefuseReason = helibaoRefuseReason;
    }

    public String getRuiyinxinRefuseReason() {
        return ruiyinxinRefuseReason;
    }

    public void setRuiyinxinRefuseReason(String ruiyinxinRefuseReason) {
        this.ruiyinxinRefuseReason = ruiyinxinRefuseReason;
    }

    public String getAggregateRefuseReason() {
        return aggregateRefuseReason;
    }

    public void setAggregateRefuseReason(String aggregateRefuseReason) {
        this.aggregateRefuseReason = aggregateRefuseReason;
    }

    public Integer getAggregateApplyType() {
        return aggregateApplyType;
    }

    public void setAggregateApplyType(Integer aggregateApplyType) {
        this.aggregateApplyType = aggregateApplyType;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Integer getAggregateCodeType() {
        return aggregateCodeType;
    }

    public void setAggregateCodeType(Integer aggregateCodeType) {
        this.aggregateCodeType = aggregateCodeType;
    }
}