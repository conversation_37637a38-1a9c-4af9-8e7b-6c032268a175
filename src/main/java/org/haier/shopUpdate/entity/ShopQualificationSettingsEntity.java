package org.haier.shopUpdate.entity;

/**
 * @description 店铺资质信息相关设置-资质表sx_shop_loan
 */
public class ShopQualificationSettingsEntity {

    /**
    * id
    */
    private Long id;

    /**
    * 聚合码非法人引导图片
    */
    private String notLegalGuideImage;

    /**
    * 聚合码申请开通聚合码首页图片
    */
    private String aggregateIndexImage;

    /**
    * 聚合码合利宝授权书下载地址
    */
    private String helibaoAuthBookUrl;

    /**
    * 聚合码瑞银信授权书下载地址
    */
    private String ruiyinxinAuthBookUrl;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNotLegalGuideImage() {
        return notLegalGuideImage;
    }

    public void setNotLegalGuideImage(String notLegalGuideImage) {
        this.notLegalGuideImage = notLegalGuideImage;
    }

    public String getAggregateIndexImage() {
        return aggregateIndexImage;
    }

    public void setAggregateIndexImage(String aggregateIndexImage) {
        this.aggregateIndexImage = aggregateIndexImage;
    }

    public String getHelibaoAuthBookUrl() {
        return helibaoAuthBookUrl;
    }

    public void setHelibaoAuthBookUrl(String helibaoAuthBookUrl) {
        this.helibaoAuthBookUrl = helibaoAuthBookUrl;
    }

    public String getRuiyinxinAuthBookUrl() {
        return ruiyinxinAuthBookUrl;
    }

    public void setRuiyinxinAuthBookUrl(String ruiyinxinAuthBookUrl) {
        this.ruiyinxinAuthBookUrl = ruiyinxinAuthBookUrl;
    }
}