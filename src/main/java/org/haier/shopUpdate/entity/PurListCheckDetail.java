package org.haier.shopUpdate.entity;

public class PurListCheckDetail {
	private Integer purchase_list_detail_id;
	private String goods_name;
	private String goods_barcode;
	private Double purchase_list_detail_price;
	private Double purchase_list_detail_count;
	private String goods_picturepath;
	private Integer giftType=1;
	public Integer getPurchase_list_detail_id() {
		return purchase_list_detail_id;
	}
	public void setPurchase_list_detail_id(Integer purchase_list_detail_id) {
		this.purchase_list_detail_id = purchase_list_detail_id;
	}
	public String getGoods_name() {
		return goods_name;
	}
	public void setGoods_name(String goods_name) {
		this.goods_name = goods_name;
	}
	public String getGoods_barcode() {
		return goods_barcode;
	}
	public void setGoods_barcode(String goods_barcode) {
		this.goods_barcode = goods_barcode;
	}
	public Double getPurchase_list_detail_price() {
		return purchase_list_detail_price;
	}
	public void setPurchase_list_detail_price(Double purchase_list_detail_price) {
		this.purchase_list_detail_price = purchase_list_detail_price;
	}
	public Double getPurchase_list_detail_count() {
		return purchase_list_detail_count;
	}
	public void setPurchase_list_detail_count(Double purchase_list_detail_count) {
		this.purchase_list_detail_count = purchase_list_detail_count;
	}
	public String getGoods_picturepath() {
		return goods_picturepath;
	}
	public void setGoods_picturepath(String goods_picturepath) {
		this.goods_picturepath = goods_picturepath;
	}
	public Integer getGiftType() {
		return giftType;
	}
	public void setGiftType(Integer giftType) {
		this.giftType = giftType;
	}
	@Override
	public String toString() {
		return "PurListCheckDetail [purchase_list_detail_id=" + purchase_list_detail_id + ", goods_name=" + goods_name
				+ ", goods_barcode=" + goods_barcode + ", purchase_list_detail_price=" + purchase_list_detail_price
				+ ", purchase_list_detail_count=" + purchase_list_detail_count + ", goods_picturepath="
				+ goods_picturepath + "]";
	}
	
}
