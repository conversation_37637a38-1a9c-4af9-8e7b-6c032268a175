package org.haier.shopUpdate.entity;
/**
 * 查询商品盘库时 上次盘库到本次盘库的出入库数量 YXL20230201
 * <AUTHOR>
 *
 */
public class InventoryBwDetail {
	
	private String inBoundCount;//入库总
	private String outBoundCount;
	private String inHandCount;//入库-手动
	private String outHandCount;
	private String inPankuCount;//入库-盘库
	private String outPankuCount;
	private String inOrderCount;//入库-订单
	private String outOrderCount;
	public String getInBoundCount() {
		return inBoundCount;
	}
	public void setInBoundCount(String inBoundCount) {
		this.inBoundCount = inBoundCount;
	}
	public String getOutBoundCount() {
		return outBoundCount;
	}
	public void setOutBoundCount(String outBoundCount) {
		this.outBoundCount = outBoundCount;
	}
	public String getInHandCount() {
		return inHandCount;
	}
	public void setInHandCount(String inHandCount) {
		this.inHandCount = inHandCount;
	}
	public String getOutHandCount() {
		return outHandCount;
	}
	public void setOutHandCount(String outHandCount) {
		this.outHandCount = outHandCount;
	}
	public String getInPankuCount() {
		return inPankuCount;
	}
	public void setInPankuCount(String inPankuCount) {
		this.inPankuCount = inPankuCount;
	}
	public String getOutPankuCount() {
		return outPankuCount;
	}
	public void setOutPankuCount(String outPankuCount) {
		this.outPankuCount = outPankuCount;
	}
	public String getInOrderCount() {
		return inOrderCount;
	}
	public void setInOrderCount(String inOrderCount) {
		this.inOrderCount = inOrderCount;
	}
	public String getOutOrderCount() {
		return outOrderCount;
	}
	public void setOutOrderCount(String outOrderCount) {
		this.outOrderCount = outOrderCount;
	}

}
