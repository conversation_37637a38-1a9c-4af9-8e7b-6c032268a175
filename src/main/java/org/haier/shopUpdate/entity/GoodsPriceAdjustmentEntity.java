package org.haier.shopUpdate.entity;

import java.math.BigDecimal;
import java.util.Date;

public class GoodsPriceAdjustmentEntity {
    private Long id;
    private String shopUnique;
    private String goodsBarcode;
    private BigDecimal goodsSalePrice;
    private BigDecimal goodsWebSalePrice;
    private BigDecimal goodsCusPrice;
    private Date createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(String shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public BigDecimal getGoodsSalePrice() {
        return goodsSalePrice;
    }

    public void setGoodsSalePrice(BigDecimal goodsSalePrice) {
        this.goodsSalePrice = goodsSalePrice;
    }

    public BigDecimal getGoodsWebSalePrice() {
        return goodsWebSalePrice;
    }

    public void setGoodsWebSalePrice(BigDecimal goodsWebSalePrice) {
        this.goodsWebSalePrice = goodsWebSalePrice;
    }

    public BigDecimal getGoodsCusPrice() {
        return goodsCusPrice;
    }

    public void setGoodsCusPrice(BigDecimal goodsCusPrice) {
        this.goodsCusPrice = goodsCusPrice;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
