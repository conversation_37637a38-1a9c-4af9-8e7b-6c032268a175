package org.haier.shopUpdate.entity;

import java.io.Serializable;

/**
 * 语音指令权限表
 */
public class SpeechCmdPermissionEntity implements Serializable {
    private Long id;
    //对应的app类型，见SpeechAppTypeEnum
    private String appType;
    //对应的命令类型，见表speech_cmd
    private String cmdId;
    //对应在各个系统中的权限，百货商家端见shop_title
    private String modularNum;
    //是否有效1、有效；0、失效
    private Integer validType;
    //创建时间
    private String createTime;
    //更新时间
    private String updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAppType() {
        return appType;
    }

    public void setAppType(String appType) {
        this.appType = appType;
    }

    public String getCmdId() {
        return cmdId;
    }

    public void setCmdId(String cmdId) {
        this.cmdId = cmdId;
    }

    public String getModularNum() {
        return modularNum;
    }

    public void setModularNum(String modularNum) {
        this.modularNum = modularNum;
    }

    public Integer getValidType() {
        return validType;
    }

    public void setValidType(Integer validType) {
        this.validType = validType;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }
}
