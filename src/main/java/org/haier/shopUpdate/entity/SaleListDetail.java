package org.haier.shopUpdate.entity;

public class SaleListDetail {
	//订单详情编号
	private Long saleListDetailId;
	//商品条码
	private String goodsBarcode;
	//商品名称
	private String goodsName;
	//商品图片名称
	private String goodsPicturepath;
	//订单商品数量
	private Double saleListDetailCount;
	//订单商品单价
	private Double saleListDetailPrice;
	//订单金额小计
	private Double subTotal;
	//单位
	private String goods_unit;
	
	private Double goods_purprice;
	
	private String goodsChengType;
	
	private String goodsType;
	
	
	
	public String getGoodsType() {
		return goodsType;
	}
	public void setGoodsType(String goodsType) {
		this.goodsType = goodsType;
	}
	public String getGoodsChengType() {
		return goodsChengType;
	}
	public void setGoodsChengType(String goodsChengType) {
		this.goodsChengType = goodsChengType;
	}
	public String getGoods_unit() {
		return goods_unit;
	}
	public void setGoods_unit(String goods_unit) {
		this.goods_unit = goods_unit;
	}
	public Long getSaleListDetailId() {
		return saleListDetailId;
	}
	public void setSaleListDetailId(Long saleListDetailId) {
		this.saleListDetailId = saleListDetailId;
	}
	public String getGoodsBarcode() {
		return goodsBarcode;
	}
	public void setGoodsBarcode(String goodsBarcode) {
		this.goodsBarcode = goodsBarcode;
	}
	public String getGoodsName() {
		return goodsName;
	}
	public void setGoodsName(String goodsName) {
		this.goodsName = goodsName;
	}
	public String getGoodsPicturepath() {
		return goodsPicturepath;
	}
	public void setGoodsPicturepath(String goodsPicturepath) {
		this.goodsPicturepath = goodsPicturepath;
	}
	public Double getSaleListDetailCount() {
		return saleListDetailCount;
	}
	public void setSaleListDetailCount(Double saleListDetailCount) {
		this.saleListDetailCount = saleListDetailCount;
	}
	public Double getSaleListDetailPrice() {
		return saleListDetailPrice;
	}
	public void setSaleListDetailPrice(Double saleListDetailPrice) {
		this.saleListDetailPrice = saleListDetailPrice;
	}
	public Double getSubTotal() {
		return subTotal;
	}
	public void setSubTotal(Double subTotal) {
		this.subTotal = subTotal;
	}
	public Double getGoods_purprice() {
		return goods_purprice;
	}
	public void setGoods_purprice(Double goods_purprice) {
		this.goods_purprice = goods_purprice;
	}
	
}
