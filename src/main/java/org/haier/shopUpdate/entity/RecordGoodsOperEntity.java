package org.haier.shopUpdate.entity;

import java.util.List;

/**
 * @description 商品修改记录信息
 */
public class RecordGoodsOperEntity {
    private Long id;
    private Long goodsId;
    private String goodsBarcode;
    private Long shopUnique;
    private String operType;
    private String deviceSource;
    private String deviceSourceMsg;
    private Long userId;
    private String userName;
    private String userType;
    private String createTime;
    private String operSource;
    private String goodsOperClass;

    private List<RecordGoodsOperDetailEntity> recordDetail;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Long goodsId) {
        this.goodsId = goodsId;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getDeviceSourceMsg() {
        return deviceSourceMsg;
    }

    public void setDeviceSourceMsg(String deviceSourceMsg) {
        this.deviceSourceMsg = deviceSourceMsg;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getGoodsOperClass() {
        return goodsOperClass;
    }

    public void setGoodsOperClass(String goodsOperClass) {
        this.goodsOperClass = goodsOperClass;
    }

    public String getOperType() {
        return operType;
    }

    public void setOperType(String operType) {
        this.operType = operType;
    }

    public String getDeviceSource() {
        return deviceSource;
    }

    public void setDeviceSource(String deviceSource) {
        this.deviceSource = deviceSource;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getOperSource() {
        return operSource;
    }

    public void setOperSource(String operSource) {
        this.operSource = operSource;
    }

    public List<RecordGoodsOperDetailEntity> getRecordDetail() {
        return recordDetail;
    }

    public void setRecordDetail(List<RecordGoodsOperDetailEntity> recordDetail) {
        this.recordDetail = recordDetail;
    }
}