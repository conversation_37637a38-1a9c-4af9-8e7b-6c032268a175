package org.haier.shopUpdate.entity;

import java.util.List;

public class Evaluate {
	//订单评论详情
	private Long detailId;
	//订单评论者
	private String cusName;
	//订单评论内容
	private String evaluateContent;
	//订单评论者头像
	private String cusProtrait;
	//评论日期
	private String evaluateDate;
	//评论的图片
	private List<String> listDetail;
	//评论编号
	private Integer evaluateId;
	//评论员类型
	private Integer userType;
	//评论详情FOR ANDRIOD
	private List<ImageForEvaluate> listDetails;
	
	public Long getDetailId() {
		return detailId;
	}
	public void setDetailId(Long detailId) {
		this.detailId = detailId;
	}
	public String getCusName() {
		return cusName;
	}
	public void setCusName(String cusName) {
		this.cusName = cusName;
	}
	public String getEvaluateContent() {
		return evaluateContent;
	}
	public void setEvaluateContent(String evaluateContent) {
		this.evaluateContent = evaluateContent;
	}
	public String getCusProtrait() {
		return cusProtrait;
	}
	public void setCusProtrait(String cusProtrait) {
		this.cusProtrait = cusProtrait;
	}
	public String getEvaluateDate() {
		return evaluateDate;
	}
	public void setEvaluateDate(String evaluateDate) {
		this.evaluateDate = evaluateDate;
	}
	public List<String> getListDetail() {
		return listDetail;
	}
	public void setListDetail(List<String> listDetail) {
		this.listDetail = listDetail;
	}
	public Integer getEvaluateId() {
		return evaluateId;
	}
	public void setEvaluateId(Integer evaluateId) {
		this.evaluateId = evaluateId;
	}
	public Integer getUserType() {
		return userType;
	}
	public void setUserType(Integer userType) {
		this.userType = userType;
	}
	public List<ImageForEvaluate> getListDetails() {
		return listDetails;
	}
	public void setListDetails(List<ImageForEvaluate> listDetails) {
		this.listDetails = listDetails;
	}
}
