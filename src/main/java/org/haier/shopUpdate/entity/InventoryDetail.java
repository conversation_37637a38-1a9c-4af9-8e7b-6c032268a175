package org.haier.shopUpdate.entity;

//盘点订单详情
public class InventoryDetail {
	//详情编号
	private Integer  id;
	//商品条码
	private String goodsBarcode;
	//商品名称
	private String goodsName;
	//商品进价
	private  Double goodsInPrice;
	//盘点前库存
	private Double goodsCount;
	//盘点差值
	private Double differenceCount;
	//盘点数量
	private Double inventoryCount;
	//盘点盈亏数
	private Double invenCount;
	//盈亏额
	private Double profitLoss;
	//盘点类型
	private Integer stockType;
	//新增的数量
	private Double addCount;
	//商品图片
	private String goodsPicturePath;
	//盘后数
	private Double afterInventoryCount;
	
	
	public Double getAfterInventoryCount() {
		return afterInventoryCount;
	}
	public void setAfterInventoryCount(Double afterInventoryCount) {
		this.afterInventoryCount = afterInventoryCount;
	}
	public String getGoodsPicturePath() {
		return goodsPicturePath;
	}
	public void setGoodsPicturePath(String goodsPicturePath) {
		this.goodsPicturePath = goodsPicturePath;
	}
	public Double getDifferenceCount() {
		return differenceCount;
	}
	public void setDifferenceCount(Double differenceCount) {
		this.differenceCount = differenceCount;
	}
	public Double getGoodsInPrice() {
		return goodsInPrice;
	}
	public void setGoodsInPrice(Double goodsInPrice) {
		this.goodsInPrice = goodsInPrice;
	}
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getGoodsBarcode() {
		return goodsBarcode;
	}
	public void setGoodsBarcode(String goodsBarcode) {
		this.goodsBarcode = goodsBarcode;
	}
	public String getGoodsName() {
		return goodsName;
	}
	public void setGoodsName(String goodsName) {
		this.goodsName = goodsName;
	}
	public Double getGoodsCount() {
		return goodsCount;
	}
	public void setGoodsCount(Double goodsCount) {
		this.goodsCount = goodsCount;
	}
	
	public Double getInventoryCount() {
		return inventoryCount;
	}
	public void setInventoryCount(Double inventoryCount) {
		this.inventoryCount = inventoryCount;
	}
	public Double getInvenCount() {
		return invenCount;
	}
	public void setInvenCount(Double invenCount) {
		this.invenCount = invenCount;
	}
	public Double getProfitLoss() {
		return profitLoss;
	}
	public void setProfitLoss(Double profitLoss) {
		this.profitLoss = profitLoss;
	}
	public Integer getStockType() {
		return stockType;
	}
	public void setStockType(Integer stockType) {
		this.stockType = stockType;
	}
	public Double getAddCount() {
		return addCount;
	}
	public void setAddCount(Double addCount) {
		this.addCount = addCount;
	}
}
