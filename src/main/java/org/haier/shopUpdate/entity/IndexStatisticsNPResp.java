package org.haier.shopUpdate.entity;

public class IndexStatisticsNPResp {

    private Double monthSale; //月销售额

    private Double monthLoan; //月欠款总额

    private Double loanTotal; //欠款总额

    private Double todaySale; //今日销售金额

    private Integer todaySaleCount; //今日销售订单数

    private Double todayLoan; //今日欠款

    private Integer todayLoanCount; //今日欠款订单数

    private Double todayIncome; //今日收款

    private Integer todayIncomeCount; //今日收款单数

    private Double todayAgentMoney; //今日代卖收益

    public Double getMonthSale() {
        return monthSale;
    }

    public void setMonthSale(Double monthSale) {
        this.monthSale = monthSale;
    }

    public Double getMonthLoan() {
        return monthLoan;
    }

    public void setMonthLoan(Double monthLoan) {
        this.monthLoan = monthLoan;
    }

    public Double getLoanTotal() {
        return loanTotal;
    }

    public void setLoanTotal(Double loanTotal) {
        this.loanTotal = loanTotal;
    }

    public Double getTodaySale() {
        return todaySale;
    }

    public void setTodaySale(Double todaySale) {
        this.todaySale = todaySale;
    }

    public Integer getTodaySaleCount() {
        return todaySaleCount;
    }

    public void setTodaySaleCount(Integer todaySaleCount) {
        this.todaySaleCount = todaySaleCount;
    }

    public Double getTodayLoan() {
        return todayLoan;
    }

    public void setTodayLoan(Double todayLoan) {
        this.todayLoan = todayLoan;
    }

    public Integer getTodayLoanCount() {
        return todayLoanCount;
    }

    public void setTodayLoanCount(Integer todayLoanCount) {
        this.todayLoanCount = todayLoanCount;
    }

    public Double getTodayIncome() {
        return todayIncome;
    }

    public void setTodayIncome(Double todayIncome) {
        this.todayIncome = todayIncome;
    }

    public Integer getTodayIncomeCount() {
        return todayIncomeCount;
    }

    public void setTodayIncomeCount(Integer todayIncomeCount) {
        this.todayIncomeCount = todayIncomeCount;
    }

    public Double getTodayAgentMoney() {
        return todayAgentMoney;
    }

    public void setTodayAgentMoney(Double todayAgentMoney) {
        this.todayAgentMoney = todayAgentMoney;
    }
}
