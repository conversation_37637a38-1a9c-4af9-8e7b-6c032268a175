package org.haier.shopUpdate.entity;

/**
 * 采购订单详情
 * <AUTHOR>
 */
public class PurListDetail {
	//进货订单详情ID
	private Integer purDetailId;
	//订单编号
	private Long purListUnique;
	//商铺采购主订单编号
	private Long purListParUnique;
	//商品名称（供货商）
	private String goodsName;
	//商品条码
	private String goodsBarcode;
	//商品售价
	private Double goodsSalePrice;
	//商品数量
	private Integer purDetailCount;
	//商品采购价格
	private Double purDetailPrice;
	//商品供货商编号
	private Long supplierUnique;
	public Integer getPurDetailId() {
		return purDetailId;
	}
	public void setPurDetailId(Integer purDetailId) {
		this.purDetailId = purDetailId;
	}
	public Long getPurListUnique() {
		return purListUnique;
	}
	public void setPurListUnique(Long purListUnique) {
		this.purListUnique = purListUnique;
	}
	public Long getPurListParUnique() {
		return purListParUnique;
	}
	public void setPurListParUnique(Long purListParUnique) {
		this.purListParUnique = purListParUnique;
	}
	public String getGoodsName() {
		return goodsName;
	}
	public void setGoodsName(String goodsName) {
		this.goodsName = goodsName;
	}
	public String getGoodsBarcode() {
		return goodsBarcode;
	}
	public void setGoodsBarcode(String goodsBarcode) {
		this.goodsBarcode = goodsBarcode;
	}
	public Double getGoodsSalePrice() {
		return goodsSalePrice;
	}
	public void setGoodsSalePrice(Double goodsSalePrice) {
		this.goodsSalePrice = goodsSalePrice;
	}
	public Integer getPurDetailCount() {
		return purDetailCount;
	}
	public void setPurDetailCount(Integer purDetailCount) {
		this.purDetailCount = purDetailCount;
	}
	public Double getPurDetailPrice() {
		return purDetailPrice;
	}
	public void setPurDetailPrice(Double purDetailPrice) {
		this.purDetailPrice = purDetailPrice;
	}
	public Long getSupplierUnique() {
		return supplierUnique;
	}
	public void setSupplierUnique(Long supplierUnique) {
		this.supplierUnique = supplierUnique;
	}
}
