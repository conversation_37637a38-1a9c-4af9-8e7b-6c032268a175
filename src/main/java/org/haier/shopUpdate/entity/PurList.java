package org.haier.shopUpdate.entity;

import java.util.List;

/**
 * 进货订单
 * <AUTHOR>
 */
public class PurList {
	//订单ID
	private Integer  purListId;
	//订单编号
	private Long purListUnique;
	//店铺编号
	private Long shopUnique;
	//订单商品数量
	private Integer purListSum;
	//订单总金额
	private Double purListTotal;
	//订单状态（1：普通订单；2：购物车订单）
	private String purListStatus;
	//订单备注
	private String purListRemark;
	//订单主订单编号
	private Long purListParUnique;
	//供应商编号
	private Long supplierUnique;
	//物流单号
	private Long logisticsUnique;
	//订单处理状态
	private Integer receiptStatus;
	//订单处理状态
	private String recStatusCode;
	//支付方式
	private Integer payMethodCode;
	//支付方式
	private String payMethod;
	//支付状态
	private Integer payStatusCode;
	//支付状态
	private String payStatus;
	//下单时间
	private String purListDate;
	//接单时间
	private String purListRedate;
	//订单发货时间
	private String logisticsDate;
	//订单完成时间
	private String receiptDate;
	//支付时间
	private String payDate;
	//订单详情
	private List<PurListDetail> listDetail;
	
	public Integer getPurListId() {
		return purListId;
	}
	public void setPurListId(Integer purListId) {
		this.purListId = purListId;
	}
	public Long getPurListUnique() {
		return purListUnique;
	}
	public void setPurListUnique(Long purListUnique) {
		this.purListUnique = purListUnique;
	}
	public Long getShopUnique() {
		return shopUnique;
	}
	public void setShopUnique(Long shopUnique) {
		this.shopUnique = shopUnique;
	}
	public Integer getPurListSum() {
		return purListSum;
	}
	public void setPurListSum(Integer purListSum) {
		this.purListSum = purListSum;
	}
	public Double getPurListTotal() {
		return purListTotal;
	}
	public void setPurListTotal(Double purListTotal) {
		this.purListTotal = purListTotal;
	}
	public String getPurListStatus() {
		return purListStatus;
	}
	public void setPurListStatus(String purListStatus) {
		this.purListStatus = purListStatus;
	}
	public String getPurListRemark() {
		return purListRemark;
	}
	public void setPurListRemark(String purListRemark) {
		this.purListRemark = purListRemark;
	}
	public Long getPurListParUnique() {
		return purListParUnique;
	}
	public void setPurListParUnique(Long purListParUnique) {
		this.purListParUnique = purListParUnique;
	}
	public Long getSupplierUnique() {
		return supplierUnique;
	}
	public void setSupplierUnique(Long supplierUnique) {
		this.supplierUnique = supplierUnique;
	}
	public Long getLogisticsUnique() {
		return logisticsUnique;
	}
	public void setLogisticsUnique(Long logisticsUnique) {
		this.logisticsUnique = logisticsUnique;
	}
	public Integer getReceiptStatus() {
		return receiptStatus;
	}
	public void setReceiptStatus(Integer receiptStatus) {
		this.receiptStatus = receiptStatus;
	}
	
	public String getRecStatusCode() {
		return recStatusCode;
	}
	public void setRecStatusCode(String recStatusCode) {
		this.recStatusCode = recStatusCode;
	}
	public String getPayMethod() {
		return payMethod;
	}
	public void setPayMethod(String payMethod) {
		this.payMethod = payMethod;
	}
	public String getPurListDate() {
		return purListDate;
	}
	public void setPurListDate(String purListDate) {
		this.purListDate = purListDate;
	}
	public String getPurListRedate() {
		return purListRedate;
	}
	public void setPurListRedate(String purListRedate) {
		this.purListRedate = purListRedate;
	}
	public String getReceiptDate() {
		return receiptDate;
	}
	public void setReceiptDate(String receiptDate) {
		this.receiptDate = receiptDate;
	}
	public String getPayDate() {
		return payDate;
	}
	public void setPayDate(String payDate) {
		this.payDate = payDate;
	}
	public List<PurListDetail> getListDetail() {
		return listDetail;
	}
	public void setListDetail(List<PurListDetail> listDetail) {
		this.listDetail = listDetail;
	}
	public Integer getPayMethodCode() {
		return payMethodCode;
	}
	public void setPayMethodCode(Integer payMethodCode) {
		this.payMethodCode = payMethodCode;
	}
	public Integer getPayStatusCode() {
		return payStatusCode;
	}
	public void setPayStatusCode(Integer payStatusCode) {
		this.payStatusCode = payStatusCode;
	}
	public String getPayStatus() {
		return payStatus;
	}
	public void setPayStatus(String payStatus) {
		this.payStatus = payStatus;
	}
	public String getLogisticsDate() {
		return logisticsDate;
	}
	public void setLogisticsDate(String logisticsDate) {
		this.logisticsDate = logisticsDate;
	}
}
