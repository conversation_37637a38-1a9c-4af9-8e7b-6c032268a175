package org.haier.shopUpdate.entity;

import java.io.Serializable;

public class BaseEntity implements Serializable {
    //ID
    private Long id;
    //创建时间
    private String createTime;
    //更新时间
    private String updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }
}
