package org.haier.shopUpdate.entity;

import lombok.Data;
import org.haier.shopUpdate.params.goods.UpdateGoodsParam;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class GoodsPacking {
	private Long goodsId;
	//商品编号
	private String goodsBarcode;
	//商品名称
	private String goodsName;
	//商品规格
	private String goodsStandard;
	//商品包装
	private String goodsUnit;
	//商品进价
	private Double goodsInPrice;
	//商品售价
	private Double goodsSalePrice;
	
	private Double goodsWebSalePrice;//网购价
	//商品规格关联关键字
	private String foreign_key;
	//包含销售商品最小单位数量
	private Integer containCount;
	//包含销售商品最小单位数量
	private Double goodsContainCount;
	//商品促销状态
	private Double goodsPromotion;
	 //商品折扣
	private Double goodsDiscount;
	//商品图片路径
	private String goodsPicturepath;
	//商品会员价
	private Double goodsCusPrice;
	//商品上架，下架状态
	private Integer shelfState;
	//收银机上下架状态
	private Integer pcShelfState;
	//满赠的起始数量
	private Double countPresent=0.0;
	//赠品的名称
	private String pgoodsName;
	//赠品的数量
	private Double pgoodsCount;
	//赠品的规格
	private String pgoodsUnit;
	//0:没有库存预警 1:有库存预警
	private Integer stock_warning_status;
	//低于库存量
	private Integer out_stock_waring_count;
	//高于库存量
	private Integer unsalable_count;
	//商品本地存在情况，若存在多个商品，当且仅当多个商品同时存在时，主商品信息设置为存在
	private Integer tableType;

	// 上一次进货价
	private Double stockPrice;
	private BigDecimal minSaleCount;
	private BigDecimal goodStockPrice;
	/**
	 * 是否设为批发价 0 否 1，是
	 */
	private Integer wholesalePriceFlg;

	/**
	 * 货位
	 */
	private String goodsPosition;
	// 货位名称
	private String completePositionName;
	/**
	 * 划线价
	 */
	private BigDecimal underlinedPrice;
	/**
	 * 批发价列表
	 */
	private List<GoodsWholesaleInfo> wholesaleList;
	
	public String getPgoodsUnit() {
		return pgoodsUnit;
	}
	public void setPgoodsUnit(String pgoodsUnit) {
		this.pgoodsUnit = pgoodsUnit;
	}
	public Long getGoodsId() {
		return goodsId;
	}
	public void setGoodsId(Long goodsId) {
		this.goodsId = goodsId;
	}
	public String getGoodsBarcode() {
		return goodsBarcode;
	}
	public void setGoodsBarcode(String goodsBarcode) {
		this.goodsBarcode = goodsBarcode;
	}
	public String getGoodsName() {
		return goodsName;
	}
	public void setGoodsName(String goodsName) {
		this.goodsName = goodsName;
	}
	public String getGoodsStandard() {
		return goodsStandard;
	}
	public void setGoodsStandard(String goodsStandard) {
		this.goodsStandard = goodsStandard;
	}
	public String getGoodsUnit() {
		return goodsUnit;
	}
	public void setGoodsUnit(String goodsUnit) {
		this.goodsUnit = goodsUnit;
	}
	public Double getGoodsInPrice() {
		return goodsInPrice;
	}
	public void setGoodsInPrice(Double goodsInPrice) {
		this.goodsInPrice = goodsInPrice;
	}
	public Double getGoodsSalePrice() {
		return goodsSalePrice;
	}
	public void setGoodsSalePrice(Double goodsSalePrice) {
		this.goodsSalePrice = goodsSalePrice;
	}
	public String getForeign_key() {
		return foreign_key;
	}
	public void setForeign_key(String foreign_key) {
		this.foreign_key = foreign_key;
	}
	public Integer getContainCount() {
		return containCount;
	}
	public void setContainCount(Integer containCount) {
		this.containCount = containCount;
	}
	
	public Double getGoodsContainCount() {
		return goodsContainCount;
	}
	public void setGoodsContainCount(Double goodsContainCount) {
		this.goodsContainCount = goodsContainCount;
	}
	public Double getGoodsPromotion() {
		return goodsPromotion;
	}
	public void setGoodsPromotion(Double goodsPromotion) {
		this.goodsPromotion = goodsPromotion;
	}
	public Double getGoodsDiscount() {
		return goodsDiscount;
	}
	public void setGoodsDiscount(Double goodsDiscount) {
		this.goodsDiscount = goodsDiscount;
	}
	public String getGoodsPicturepath() {
		return goodsPicturepath;
	}
	public void setGoodsPicturepath(String goodsPicturepath) {
		this.goodsPicturepath = goodsPicturepath;
	}
	public Double getGoodsCusPrice() {
		return goodsCusPrice;
	}
	public void setGoodsCusPrice(Double goodsCusPrice) {
		this.goodsCusPrice = goodsCusPrice;
	}
	public Integer getShelfState() {
		return shelfState;
	}
	public void setShelfState(Integer shelfState) {
		this.shelfState = shelfState;
	}
	public Double getCountPresent() {
		return countPresent;
	}
	public void setCountPresent(Double countPresent) {
		this.countPresent = countPresent;
	}
	public String getPgoodsName() {
		return pgoodsName;
	}
	public void setPgoodsName(String pgoodsName) {
		this.pgoodsName = pgoodsName;
	}
	public Double getPgoodsCount() {
		return pgoodsCount;
	}
	public void setPgoodsCount(Double pgoodsCount) {
		this.pgoodsCount = pgoodsCount;
	}
	public Integer getStock_warning_status() {
		return stock_warning_status;
	}
	public void setStock_warning_status(Integer stock_warning_status) {
		this.stock_warning_status = stock_warning_status;
	}
	public Integer getOut_stock_waring_count() {
		return out_stock_waring_count;
	}
	public void setOut_stock_waring_count(Integer out_stock_waring_count) {
		this.out_stock_waring_count = out_stock_waring_count;
	}
	public Integer getUnsalable_count() {
		return unsalable_count;
	}
	public void setUnsalable_count(Integer unsalable_count) {
		this.unsalable_count = unsalable_count;
	}
	public Integer getTableType() {
		return tableType;
	}
	public void setTableType(Integer tableType) {
		this.tableType = tableType;
	}
	public Double getGoodsWebSalePrice() {
		return goodsWebSalePrice;
	}
	public void setGoodsWebSalePrice(Double goodsWebSalePrice) {
		this.goodsWebSalePrice = goodsWebSalePrice;
	}
	public Integer getPcShelfState() {
		return pcShelfState;
	}
	public void setPcShelfState(Integer pcShelfState) {
		this.pcShelfState = pcShelfState;
	}

	public Double getStockPrice() {
		return stockPrice;
	}

	public void setStockPrice(Double stockPrice) {
		this.stockPrice = stockPrice;
	}

	public BigDecimal getMinSaleCount() {
		return minSaleCount;
	}

	public void setMinSaleCount(BigDecimal minSaleCount) {
		this.minSaleCount = minSaleCount;
	}

	public BigDecimal getGoodStockPrice() {
		return goodStockPrice;
	}

	public void setGoodStockPrice(BigDecimal goodStockPrice) {
		this.goodStockPrice = goodStockPrice;
	}

	public Integer getWholesalePriceFlg() {
		return wholesalePriceFlg;
	}

	public void setWholesalePriceFlg(Integer wholesalePriceFlg) {
		this.wholesalePriceFlg = wholesalePriceFlg;
	}

	public String getGoodsPosition() {
		return goodsPosition;
	}

	public void setGoodsPosition(String goodsPosition) {
		this.goodsPosition = goodsPosition;
	}

	public String getCompletePositionName() {
		return completePositionName;
	}

	public void setCompletePositionName(String completePositionName) {
		this.completePositionName = completePositionName;
	}

	public BigDecimal getUnderlinedPrice() {
		return underlinedPrice;
	}

	public void setUnderlinedPrice(BigDecimal underlinedPrice) {
		this.underlinedPrice = underlinedPrice;
	}

	public List<GoodsWholesaleInfo> getWholesaleList() {
		return wholesaleList;
	}

	public void setWholesaleList(List<GoodsWholesaleInfo> wholesaleList) {
		this.wholesaleList = wholesaleList;
	}

	@Override
	public String toString() {
		return "GoodsPacking [goodsId=" + goodsId + ", goodsBarcode=" + goodsBarcode + ", goodsName=" + goodsName
				+ ", goodsStandard=" + goodsStandard + ", goodsUnit=" + goodsUnit + ", goodsInPrice=" + goodsInPrice
				+ ", goodsSalePrice=" + goodsSalePrice + ", foreign_key=" + foreign_key + ", containCount="
				+ containCount + ", goodsPromotion=" + goodsPromotion + ", goodsDiscount=" + goodsDiscount
				+ ", goodsPicturepath=" + goodsPicturepath + ", goodsCusPrice=" + goodsCusPrice + ", shelfState="
				+ shelfState + ", countPresent=" + countPresent + ", pgoodsName=" + pgoodsName + ", pgoodsCount="
				+ pgoodsCount + ", pgoodsUnit=" + pgoodsUnit + ", stock_warning_status=" + stock_warning_status
				+ ", out_stock_waring_count=" + out_stock_waring_count + ", unsalable_count=" + unsalable_count
				+ ", tableType=" + tableType+ ", stockPrice=" + stockPrice + "]";
	}
}
