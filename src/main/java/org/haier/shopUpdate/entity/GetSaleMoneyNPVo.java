package org.haier.shopUpdate.entity;

public class GetSaleMoneyNPVo {

    private String dateStart; //开始时间

    private String dateEnd; //结束时间

    private Long shopUnique; //商店唯一标识符

    private Integer saleListState; //付款状态

    public String getDateStart() {
        return dateStart;
    }

    public void setDateStart(String dateStart) {
        this.dateStart = dateStart;
    }

    public String getDateEnd() {
        return dateEnd;
    }

    public void setDateEnd(String dateEnd) {
        this.dateEnd = dateEnd;
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public Integer getSaleListState() {
        return saleListState;
    }

    public void setSaleListState(Integer saleListState) {
        this.saleListState = saleListState;
    }
}
