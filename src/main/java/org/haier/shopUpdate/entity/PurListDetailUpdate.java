package org.haier.shopUpdate.entity;

import java.util.List;

public class PurListDetailUpdate {
	private Integer purchase_list_id;
	private String supplier_name;
	private String purchase_list_unique;
	private String shop_unique;
	private Integer purchase_list_sum;
	private Double purchase_list_total;
	private String purchase_list_remark;
	private String purchase_list_parunique;
	private String supplier_unique;
	private String logistics_unique;
	private String receipt_status;
	private String pay_method;
	private String paystatus;
	private String purchase_list_date;
	private String purchase_list_redate;
	private String receipt_datetime;
	private String pay_datetime;
	private Double purchase_list_saleTotal;
	private String supplier_phone;
	private String logistics_endtime;
	private String supplier_address;
	private List<PurListDetails> listDetail;
	public String getShop_unique() {
		return shop_unique;
	}
	public void setShop_unique(String shop_unique) {
		this.shop_unique = shop_unique;
	}
	public String getPurchase_list_parunique() {
		return purchase_list_parunique;
	}
	
	public String getSupplier_name() {
		return supplier_name;
	}
	public void setSupplier_name(String supplier_name) {
		this.supplier_name = supplier_name;
	}
	public void setPurchase_list_parunique(String purchase_list_parunique) {
		this.purchase_list_parunique = purchase_list_parunique;
	}
	public String getSupplier_unique() {
		return supplier_unique;
	}
	public void setSupplier_unique(String supplier_unique) {
		this.supplier_unique = supplier_unique;
	}
	
	public Integer getPurchase_list_id() {
		return purchase_list_id;
	}
	public void setPurchase_list_id(Integer purchase_list_id) {
		this.purchase_list_id = purchase_list_id;
	}
	public String getPurchase_list_unique() {
		return purchase_list_unique;
	}
	public void setPurchase_list_unique(String purchase_list_unique) {
		this.purchase_list_unique = purchase_list_unique;
	}
	public Integer getPurchase_list_sum() {
		return purchase_list_sum;
	}
	public void setPurchase_list_sum(Integer purchase_list_sum) {
		this.purchase_list_sum = purchase_list_sum;
	}
	public Double getPurchase_list_total() {
		return purchase_list_total;
	}
	public void setPurchase_list_total(Double purchase_list_total) {
		this.purchase_list_total = purchase_list_total;
	}
	public String getPurchase_list_remark() {
		return purchase_list_remark;
	}
	public void setPurchase_list_remark(String purchase_list_remark) {
		this.purchase_list_remark = purchase_list_remark;
	}
	public String getLogistics_unique() {
		return logistics_unique;
	}
	public void setLogistics_unique(String logistics_unique) {
		this.logistics_unique = logistics_unique;
	}
	public String getReceipt_status() {
		return receipt_status;
	}
	public void setReceipt_status(String receipt_status) {
		this.receipt_status = receipt_status;
	}
	public String getPay_method() {
		return pay_method;
	}
	public void setPay_method(String pay_method) {
		this.pay_method = pay_method;
	}
	public String getPaystatus() {
		return paystatus;
	}
	public void setPaystatus(String paystatus) {
		this.paystatus = paystatus;
	}
	public String getPurchase_list_date() {
		return purchase_list_date;
	}
	public void setPurchase_list_date(String purchase_list_date) {
		this.purchase_list_date = purchase_list_date;
	}
	public String getPurchase_list_redate() {
		return purchase_list_redate;
	}
	public void setPurchase_list_redate(String purchase_list_redate) {
		this.purchase_list_redate = purchase_list_redate;
	}
	public String getReceipt_datetime() {
		return receipt_datetime;
	}
	public void setReceipt_datetime(String receipt_datetime) {
		this.receipt_datetime = receipt_datetime;
	}
	public String getPay_datetime() {
		return pay_datetime;
	}
	public void setPay_datetime(String pay_datetime) {
		this.pay_datetime = pay_datetime;
	}
	public Double getPurchase_list_saleTotal() {
		return purchase_list_saleTotal;
	}
	public void setPurchase_list_saleTotal(Double purchase_list_saleTotal) {
		this.purchase_list_saleTotal = purchase_list_saleTotal;
	}
	public List<PurListDetails> getListDetail() {
		return listDetail;
	}
	public void setListDetail(List<PurListDetails> listDetail) {
		this.listDetail = listDetail;
	}
	public String getSupplier_phone() {
		return supplier_phone;
	}
	public void setSupplier_phone(String supplier_phone) {
		this.supplier_phone = supplier_phone;
	}
	public String getLogistics_endtime() {
		return logistics_endtime;
	}
	public void setLogistics_endtime(String logistics_endtime) {
		this.logistics_endtime = logistics_endtime;
	}
	public String getSupplier_address() {
		return supplier_address;
	}
	public void setSupplier_address(String supplier_address) {
		this.supplier_address = supplier_address;
	}
	
}
