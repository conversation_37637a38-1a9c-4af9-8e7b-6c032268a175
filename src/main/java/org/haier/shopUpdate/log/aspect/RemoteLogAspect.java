package org.haier.shopUpdate.log.aspect;

import cc.buyhoo.common.dingdingtalk.params.MarkdownMessageParams;
import cc.buyhoo.common.dingdingtalk.result.OApiRobotSendResponseResult;
import cc.buyhoo.common.dingdingtalk.utils.DingTalkUtils;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.digest.HMac;
import cn.hutool.crypto.digest.HmacAlgorithm;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.aspectj.lang.reflect.MethodSignature;
import org.haier.shopUpdate.log.annotation.RemoteLog;
import org.haier.shopUpdate.log.enums.BusinessStatus;
import org.haier.shopUpdate.log.event.OperLogEvent;
import org.haier.shopUpdate.log.util.IpUtils;
import org.haier.shopUpdate.project.ProjectConfig;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.validation.BindingResult;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.*;


/**
 * @Description 操作日志记录处理
 * @ClassName RemoteLogAspect
 * <AUTHOR>
 * @Date 2023/11/29 13:49
 **/
@Aspect
@Component
public class RemoteLogAspect {
    @Resource
    private ProjectConfig projectConfig;
    /**
     * 排除敏感属性字段
     */
    public static final String[] EXCLUDE_PROPERTIES = {"password", "oldPassword", "newPassword", "confirmPassword"};
    @Resource
    private final ApplicationEventPublisher publisher;
    private org.apache.log4j.Logger logger = org.apache.log4j.Logger.getLogger(RemoteLogAspect.class);

    public RemoteLogAspect() {
        super();
        publisher = null;
    }

    public RemoteLogAspect(ApplicationEventPublisher publisher) {
        this.publisher = publisher;
    }

    @Pointcut("@annotation(org.haier.shopUpdate.log.annotation.RemoteLog) * execution(* org.haier.shopUpdate.controller..*(..))")
    public void logPointCut() {
    }

    @Around("logPointCut()")
    public Object doAround(final ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        final Object jsonResult;
        final long startTime = DateUtil.current();
        final HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        final Map<String,Object> map = new HashMap<>();
        map.put("ip", IpUtils.getIpAddr(request));
        map.put("requestURI",request.getRequestURI());
        map.put("method",request.getMethod());
        map.put("headerMap",ServletUtil.getHeaderMap(request));
        map.put("paramsMap",ServletUtil.getParamMap(request));
        try {
            jsonResult = proceedingJoinPoint.proceed();
            final long endTime = DateUtil.current();
            ThreadUtil.execute(new Runnable() {
                @Override
                public void run() {
                    Method method = ((MethodSignature) proceedingJoinPoint.getSignature()).getMethod();
                    RemoteLog remoteLog = method.getAnnotation(RemoteLog.class);
                    handleLog(proceedingJoinPoint, remoteLog, null, jsonResult, map, endTime - startTime);
                }
            });
            return jsonResult;
        } catch (final Throwable t) {
            ThreadUtil.execute(new Runnable() {
                @Override
                public void run() {
                    long endTime = DateUtil.current();
                    Method method = ((MethodSignature) proceedingJoinPoint.getSignature()).getMethod();
                    RemoteLog remoteLog = method.getAnnotation(RemoteLog.class);
                    if (t instanceof Exception) {
                        handleLog(proceedingJoinPoint, remoteLog, (Exception) t, null, map, endTime - startTime);// 如果t已经是Exception或其子类，直接强制类型转换并抛出
                    } else {
                        handleLog(proceedingJoinPoint, remoteLog, new Exception("An error occurred", t), null, map, endTime - startTime);// 否则，创建一个新的Exception并传入t作为原因
                    }
                }
            });
            throw t;
        }
    }


    protected void handleLog(final JoinPoint joinPoint, RemoteLog controllerLog, final Exception e, Object jsonResult, Map<String,Object> requestMap,
                             Long costTime) {
        // *========数据库日志=========*//
        OperLogEvent operLog = new OperLogEvent(this);
        operLog.setOperTime(DateUtil.date());
        operLog.setProjectCode("shopupdate");
        operLog.setStatus(BusinessStatus.SUCCESS.ordinal());

        operLog.setOperIp(requestMap.get("ip") != null?String.valueOf(requestMap.get("ip")):"");
        operLog.setOperUrl(StringUtils.substring(requestMap.get("requestURI") != null?String.valueOf(requestMap.get("requestURI")):"", 0, 255));

        if (e != null) {
            operLog.setStatus(BusinessStatus.FAIL.ordinal());
            operLog.setErrorMsg(StringUtils.substring(e.getMessage(), 0, 2000));
        }
        // 设置方法名称
        String className = joinPoint.getTarget().getClass().getName();
        String methodName = joinPoint.getSignature().getName();
        operLog.setMethod(className + "." + methodName + "()");
        // 设置请求方式
        operLog.setRequestMethod(requestMap.get("method") != null?String.valueOf(requestMap.get("method")):"");
        // 处理设置注解上的参数
        try {
            getControllerMethodDescription(joinPoint, controllerLog, operLog, jsonResult, requestMap);
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
        // 设置消耗时间
        operLog.setCostTime(costTime);

        // 是否需要发送钉钉机器人告警信息
        if (controllerLog.isSendDingDingTalk() && e != null) {
            sendDingDingTalk(operLog, controllerLog);
        }

        //保存数据库
        publisher.publishEvent(operLog);


    }

    private void sendDingDingTalk(OperLogEvent operLog, RemoteLog controllerLog) {
        try {
            String timeStamp = DateUtil.formatDateTime(new Date());
            HMac hmac = SecureUtil.hmac(HmacAlgorithm.HmacMD5, projectConfig.getDingDingTalkKey());
            MarkdownMessageParams markdownMessageParams = new MarkdownMessageParams();
            markdownMessageParams.setTitle("接口异常告警通知");
            markdownMessageParams.setText("- 接口出错： " /*+ "@" + DingTalkMobileEnum.ZMS.getCode()*/+ "\n1. 运行环境：" + projectConfig.getProjectActive() + "\n1. 项目名称：" + operLog.getProjectCode() + "\n2. 接口URL：" + operLog.getOperUrl()
                    + "\n3. 模块标题：" + operLog.getTitle() + "\n4. 方法名称：" + operLog.getMethod() + "\n5. 请求方式:" + operLog.getRequestMethod()
                    + "\n6. 业务类型：" + operLog.getBusinessTypeDesc() + "\n7. 操作类别：" + operLog.getOperatorTypeDesc()
                    + "\n8. 操作人员:" + operLog.getOperName() + "\n9. 主机地址:" + operLog.getOperIp() + "\n10. 操作地点：" + operLog.getOperLocation()
                    + "\n11. 请求头信息：" + operLog.getOperHeader() + "\n12. 请求参数：" + operLog.getOperParam() + "\n13. 返回参数：" + operLog.getJsonResult()
                    + "\n14. 错误信息：" + operLog.getErrorMsg() + "\n15. 操作时间：" + DateUtil.formatDateTime(new Date()) + "\n16. 耗时：" + operLog.getCostTime() + "毫秒");
//            markdownMessageParams.setAtUserIds(Collections.singletonList(DingTalkUserIdEnum.ZMS.getCode()));
//            markdownMessageParams.setAtMobiles(Arrays.asList(DingTalkMobileEnum.ZMS.getCode()));
            if (controllerLog.isSendDingDingTalkAtAll()) {
                markdownMessageParams.setIsAtAll(true);
            }
            markdownMessageParams.setTimeStamp(timeStamp);
            markdownMessageParams.setSigh(hmac.digestHex(timeStamp));
            OApiRobotSendResponseResult response = DingTalkUtils.sendMarkdown(markdownMessageParams);
            System.out.println(JSONUtil.toJsonStr(response));
        } catch (Exception ex) {
            logger.error("发送钉钉失败", ex);
        }
    }
    /**
     * 获取注解中对方法的描述信息 用于Controller层注解
     *
     * @param log     日志
     * @param operLog 操作日志
     * @throws Exception
     */
    public void getControllerMethodDescription(JoinPoint joinPoint, RemoteLog log, OperLogEvent operLog, Object jsonResult, Map<String,Object> requestMap) throws Exception {
        // 设置action动作
        operLog.setBusinessType(log.businessType().ordinal());
        // 设置标题
        operLog.setTitle(log.title());
        // 设置操作人类别
        operLog.setOperatorType(log.operatorType().ordinal());
        // 是否需要保存header参数和值
        if (log.isSaveRequestHeader()) {
            // 获取参数的信息，传入到数据库中。
            setHeaderValue(joinPoint, operLog, log.excludeParamNames(), requestMap);
        }
        // 是否需要保存request，参数和值
        if (log.isSaveRequestData()) {
            // 获取参数的信息，传入到数据库中。
            setRequestValue(joinPoint, operLog, log.excludeParamNames(), requestMap);
        }
        // 是否需要保存response，参数和值
        if (log.isSaveResponseData() && ObjectUtil.isNotNull(jsonResult)) {
            operLog.setJsonResult(StringUtils.substring(JSONUtil.toJsonStr(jsonResult), 0, 2000));
        }
    }

    private void setHeaderValue(JoinPoint joinPoint, OperLogEvent operLog, String[] excludeParamNames, Map<String,Object> requestMap) throws Exception {
        Map<String, String> headerMap = requestMap.get("headerMap") != null? (Map<String, String>) requestMap.get("headerMap") : Collections.<String, String>emptyMap();
        if (ObjectUtil.isNotNull(headerMap)) {
            String params = JSONUtil.toJsonStr(headerMap);
            operLog.setOperHeader(StringUtils.substring(params, 0, 1000));
            if (StrUtil.isBlank(operLog.getShopUnique()) && (headerMap.containsKey("shop_unique") || headerMap.containsValue("shopUnique"))) {
                String shopUnique = headerMap.get("shop_unique");
                if (StrUtil.isBlank(shopUnique)) {
                    shopUnique = headerMap.get("shopUnique");
                }
                operLog.setShopUnique(shopUnique);
            }
        }
    }

    /**
     * 获取请求的参数，放到log中
     *
     * @param operLog 操作日志
     * @throws Exception 异常
     */
    private void setRequestValue(JoinPoint joinPoint, OperLogEvent operLog, String[] excludeParamNames, Map<String,Object> requestMap) throws Exception {
        Map<String, String> paramsMap = requestMap.get("paramsMap") != null? (Map<String, String>) requestMap.get("paramsMap") : Collections.<String, String>emptyMap();
        String requestMethod = operLog.getRequestMethod();
        if (HttpMethod.PUT.name().equals(requestMethod) || HttpMethod.POST.name().equals(requestMethod)) {
            StringBuffer stringBuffer = new StringBuffer();
            if (ObjectUtil.isNotNull(paramsMap) && paramsMap.size() > 0) {
                if (StrUtil.isBlank(operLog.getShopUnique()) && (paramsMap.containsKey("shop_unique") || paramsMap.containsKey("shopUnique"))) {
                    String shopUnique = paramsMap.get("shop_unique");
                    if (StrUtil.isBlank(shopUnique)) {
                        shopUnique = paramsMap.get("shopUnique");
                    }
                    operLog.setShopUnique(shopUnique);
                }
                stringBuffer.append("request:").append(JSONUtil.toJsonStr(paramsMap));
            }
            if (ObjectUtil.isNotNull(joinPoint.getArgs())) {
                String params = argsArrayToString(joinPoint.getArgs(), excludeParamNames);
                if (StrUtil.isNotBlank(params)) {
                    if (stringBuffer.length() > 0) {
                        stringBuffer.append(", ");
                    }
                    stringBuffer.append("body:").append(params);
                    if (StrUtil.isBlank(operLog.getShopUnique()) && JSONUtil.isTypeJSONObject(params)) {
                        JSONObject paramJson = JSONUtil.parseObj(params);
                        if (paramJson.containsKey("shop_unique")) {
                            operLog.setShopUnique(paramJson.getStr("shop_unique"));
                        } else if (paramJson.containsKey("shopUnique")) {
                            operLog.setShopUnique(paramJson.getStr("shopUnique"));
                        }
                    }
                }
            }
            operLog.setOperParam(StringUtils.substring(stringBuffer.toString(), 0, 2000));
        } else {
            operLog.setOperParam(StringUtils.substring(JSONUtil.toJsonStr(paramsMap), 0, 2000));
        }
    }

    /**
     * 参数拼装
     */
    private String argsArrayToString(Object[] paramsArray, String[] excludeParamNames) {
        StringJoiner params = new StringJoiner(" ");
        if (ArrayUtil.isEmpty(paramsArray)) {
            return params.toString();
        }
        for (Object o : paramsArray) {
            Map paramsMap = BeanUtil.beanToMap(o);
            if (MapUtil.isNotEmpty(paramsMap)) {
                if (ObjectUtil.isNotNull(o) && !isFilterObject(o)) {
                    String str = JSONUtil.toJsonStr(o);
                    Dict dict = JSONUtil.toBean(str, Dict.class);
                    if (MapUtil.isNotEmpty(dict)) {
                        str = JSONUtil.toJsonStr(dict);
                    }
                    params.add(str);
                }
            }
        }
        return params.toString();
    }
    /**
     * 判断是否需要过滤的对象。
     *
     * @param o 对象信息。
     * @return 如果是需要过滤的对象，则返回true；否则返回false。
     */
    @SuppressWarnings("rawtypes")
    public boolean isFilterObject(final Object o) {
        Class<?> clazz = o.getClass();
        if (clazz.isArray()) {
            return clazz.getComponentType().isAssignableFrom(MultipartFile.class);
        } else if (Collection.class.isAssignableFrom(clazz)) {
            Collection collection = (Collection) o;
            for (Object value : collection) {
                return value instanceof MultipartFile;
            }
        } else if (Map.class.isAssignableFrom(clazz)) {
            Map map = (Map) o;
            for (Object value : map.values()) {
                return value instanceof MultipartFile;
            }
        }
        return o instanceof MultipartFile || o instanceof HttpServletRequest || o instanceof HttpServletResponse
                || o instanceof BindingResult;
    }
}
