package org.haier.shopUpdate.log.event;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import org.haier.shopUpdate.project.ProjectConfig;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Description
 * @ClassName LogEventListener
 * <AUTHOR>
 * @Date 2023/11/30 17:00
 **/
@Component
public class RemoteLogEventListener implements ApplicationListener<OperLogEvent> {

    @Resource
    private ProjectConfig projectConfig;

    /**
     * 保存系统日志记录
     */
    @Async
    @Override
    public void onApplicationEvent(OperLogEvent operLogEvent) {
        operLogEvent.setEnv(projectConfig.getProjectActive());
        String response = HttpUtil.post(projectConfig.getRometeLogUrl(), JSONUtil.toJsonStr(operLogEvent));
        System.out.println("保存日志结果："+response);
    }
}
