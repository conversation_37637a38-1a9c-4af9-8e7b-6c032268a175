package org.haier.shopUpdate.log.event;

import org.springframework.context.ApplicationEvent;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description
 * @ClassName OperLogEvent
 * <AUTHOR>
 * @Date 2023/11/30 10:25
 **/
public class OperLogEvent extends ApplicationEvent implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 日志主键
     */
    private String projectCode;
    private String env;

    /**
     * 操作模块
     */
    private String title;

    /**
     * 业务类型（0其它 1新增 2修改 3删除）
     */
    private Integer businessType;

    /**
     *业务类型（0其它 1新增 2修改 3删除）
     */
    private String businessTypeDesc;
    /**
     * 业务类型数组
     */
    private Integer[] businessTypes;

    private List<String> businessTypesDesc;
    /**
     * 请求方法
     */
    private String method;

    /**
     * 请求方式
     */
    private String requestMethod;

    /**
     * 操作类别（0其它 1后台用户 2手机端用户）
     */
    private Integer operatorType;

    /**
     * 操作类别（0其它 1后台用户 2手机端用户）
     */
    private String operatorTypeDesc;
    /**
     * 操作人员
     */
    private String operName;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 请求url
     */
    private String operUrl;

    /**
     * 操作地址
     */
    private String operIp;

    /**
     * 操作地点
     */
    private String operLocation;
    /**
     * 请求头信息
     */
    private String operHeader;

    /**
     * 请求参数
     */
    private String operParam;

    /**
     * 返回参数
     */
    private String jsonResult;

    /**
     * 操作状态（0正常 1异常）
     */
    private Integer status;

    /**
     * 店铺编码
     */
    private String shopUnique;


    /**
     * 错误消息
     */
    private String errorMsg;

    /**
     * 操作时间
     */
    private Date operTime;

    /**
     * 消耗时间
     */
    private Long costTime;

    public OperLogEvent(Object source) {
        super(source);
    }

    public String getProjectCode() {
        return projectCode;
    }

    public String getEnv() {
        return env;
    }

    public void setEnv(String env) {
        this.env = env;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public Integer[] getBusinessTypes() {
        return businessTypes;
    }

    public void setBusinessTypes(Integer[] businessTypes) {
        this.businessTypes = businessTypes;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getRequestMethod() {
        return requestMethod;
    }

    public void setRequestMethod(String requestMethod) {
        this.requestMethod = requestMethod;
    }

    public Integer getOperatorType() {
        return operatorType;
    }

    public void setOperatorType(Integer operatorType) {
        this.operatorType = operatorType;
    }

    public String getOperName() {
        return operName;
    }

    public void setOperName(String operName) {
        this.operName = operName;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getOperUrl() {
        return operUrl;
    }

    public void setOperUrl(String operUrl) {
        this.operUrl = operUrl;
    }

    public String getOperIp() {
        return operIp;
    }

    public void setOperIp(String operIp) {
        this.operIp = operIp;
    }

    public String getOperLocation() {
        return operLocation;
    }

    public void setOperLocation(String operLocation) {
        this.operLocation = operLocation;
    }

    public String getOperParam() {
        return operParam;
    }

    public void setOperParam(String operParam) {
        this.operParam = operParam;
    }

    public String getJsonResult() {
        return jsonResult;
    }

    public void setJsonResult(String jsonResult) {
        this.jsonResult = jsonResult;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public Date getOperTime() {
        return operTime;
    }

    public void setOperTime(Date operTime) {
        this.operTime = operTime;
    }

    public Long getCostTime() {
        return costTime;
    }

    public void setCostTime(Long costTime) {
        this.costTime = costTime;
    }

    public String getOperHeader() {
        return operHeader;
    }

    public void setOperHeader(String operHeader) {
        this.operHeader = operHeader;
    }

    public String getBusinessTypeDesc() {
        switch (businessType) {
            case 1:
                businessTypeDesc = "新增";
                break;
            case 2:
                businessTypeDesc = "修改";
                break;
            case 3:
                businessTypeDesc = "删除";
                break;
            case 4:
                businessTypeDesc = "授权";
                break;
            case 5:
                businessTypeDesc = "导出";
                break;
            case 6:
                businessTypeDesc = "导入";
                break;
            case 7:
                businessTypeDesc = "强退";
                break;
            case 8:
                businessTypeDesc = "生成代码";
                break;
            case 9:
                businessTypeDesc = "清空数据";
                break;
            case 10:
                businessTypeDesc = "支付";
                break;
            case 11:
                businessTypeDesc = "充值";
                break;
            case 0:
                businessTypeDesc = "其他";
                break;
        }
        return businessTypeDesc;
    }

    public void setBusinessTypeDesc(String businessTypeDesc) {
        this.businessTypeDesc = businessTypeDesc;
    }

    public List<String> getBusinessTypesDesc() {
        return businessTypesDesc;
    }

    public void setBusinessTypesDesc(List<String> businessTypesDesc) {
        this.businessTypesDesc = businessTypesDesc;
    }

    public String getOperatorTypeDesc() {
        switch (operatorType) {
            case 1:
                operatorTypeDesc = "后台用户";
                break;
            case 2:
                operatorTypeDesc = "手机端用户";
                break;
            case 3:
                operatorTypeDesc = "收银机用户";
                break;
            case 4:
                operatorTypeDesc = "小程序用户";
                break;
            case 0:
                businessTypeDesc = "其他";
                break;
        }
        return operatorTypeDesc;
    }

    public void setOperatorTypeDesc(String operatorTypeDesc) {
        this.operatorTypeDesc = operatorTypeDesc;
    }

    public String getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(String shopUnique) {
        this.shopUnique = shopUnique;
    }
}
