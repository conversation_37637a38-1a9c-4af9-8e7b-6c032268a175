package org.haier.shopUpdate.log.util;

import cc.buyhoo.common.dingdingtalk.params.MarkdownMessageParams;
import cc.buyhoo.common.dingdingtalk.result.OApiRobotSendResponseResult;
import cc.buyhoo.common.dingdingtalk.utils.DingTalkUtils;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.digest.HMac;
import cn.hutool.crypto.digest.HmacAlgorithm;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.haier.shopUpdate.log.enums.OperatorType;
import org.haier.shopUpdate.log.event.OperLogEvent;
import org.haier.shopUpdate.project.ProjectConfig;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.Map;

@Component
@Slf4j
public class SendDingDingTalkUtils {
    @Resource
    private ProjectConfig projectConfig;

    public void sendDingDingTalkMsg(final HttpServletRequest request, final String errorMsg, final Exception e, final String body, final String title, final Integer businessType) {
        try{
            ThreadUtil.execute(new Runnable() {
                public void run() {
                    OperLogEvent operLog = new OperLogEvent(this);
                    operLog.setOperParam(body);
                    operLog.setBusinessType(businessType);
                    operLog.setTitle(title);
                    operLog.setErrorMsg(errorMsg+"异常：" + org.apache.commons.lang3.StringUtils.substring(e.getMessage(), 0, 2000));
                    operLog.setProjectCode("shopupdate");
                    operLog.setEnv(SpringUtil.getActiveProfile());
                    operLog.setOperatorType(OperatorType.OTHER.ordinal());
                    operLog.setOperIp(ServletUtil.getClientIP(request));
                    operLog.setOperUrl(StringUtils.substring(request.getRequestURI(), 0, 255));
                    operLog.setRequestMethod(request.getMethod());
                    Map<String, String> headerMap = ServletUtil.getHeaderMap(request);
                    if (ObjectUtil.isNotNull(headerMap)) {
                        String params = JSONUtil.toJsonStr(headerMap);
                        operLog.setOperHeader(StringUtils.substring(params, 0, 1000));
                    }
                    sendDingDingTalk(operLog);
                }
            });

        }catch (Exception e1){
            log.error("发送钉钉失败",e1);
        }
    }
    public void sendDingDingTalk(OperLogEvent operLog){
        try {
            String timeStamp = DateUtil.formatDateTime(new Date());
            HMac hmac = SecureUtil.hmac(HmacAlgorithm.HmacMD5, projectConfig.getDingDingTalkKey());
            MarkdownMessageParams markdownMessageParams = new MarkdownMessageParams();
            markdownMessageParams.setTitle("接口异常告警通知");
            markdownMessageParams.setText("- 接口出错： " /*+ "@" + DingTalkMobileEnum.ZMS.getCode()*/ + "\n1. 运行环境：" + projectConfig.getProjectActive()
                    + "\n2. 项目名称：" + operLog.getProjectCode() + "\n3. 接口URL：" + operLog.getOperUrl()
                    + "\n4. 模块标题：" + operLog.getTitle() + "\n6. 请求方式：" + operLog.getRequestMethod()
                    + "\n7. 业务类型：" + operLog.getBusinessTypeDesc() + "\n8. 操作类别：" + operLog.getOperatorTypeDesc()
                    + "\n9. 操作人员：" + operLog.getOperName() + "\n10. 主机地址：" + operLog.getOperIp() + "\n11. 操作地点：" + operLog.getOperLocation()
                    + "\n12. 请求头信息：" + operLog.getOperHeader() + "\n13. 请求参数：" + operLog.getOperParam() + "\n14. 返回参数：" + operLog.getJsonResult()
                    + "\n15. 错误信息：" + operLog.getErrorMsg() + "\n16. 操作时间：" + DateUtil.formatDateTime(new Date()));
//            markdownMessageParams.setAtUserIds(Collections.singletonList(DingTalkUserIdEnum.ZMS.getCode()));
//            markdownMessageParams.setAtMobiles(Arrays.asList(DingTalkMobileEnum.ZMS.getCode()));
//            markdownMessageParams.setIsAtAll(true);
            markdownMessageParams.setTimeStamp(timeStamp);
            markdownMessageParams.setSigh(hmac.digestHex(timeStamp));
            System.out.println(markdownMessageParams.getText());
            OApiRobotSendResponseResult response = DingTalkUtils.sendMarkdown(markdownMessageParams);
            System.out.println(JSONUtil.toJsonStr(response));
        }catch (Exception e1){
            log.error("发送钉钉失败",e1);
        }
    }
}
