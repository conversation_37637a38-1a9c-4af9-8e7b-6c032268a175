package org.haier.shopUpdate.log.enums;

/**
 * @Description 业务操作类型
 * @ClassName BusinessType
 * <AUTHOR>
 * @Date 2023/11/29 13:48
 **/
public enum BusinessType {
    /**
     * 其它
     */
    OTHER,

    /**
     * 新增
     */
    INSERT,

    /**
     * 修改
     */
    UPDATE,

    /**
     * 删除
     */
    DELETE,

    /**
     * 授权
     */
    GRANT,

    /**
     * 导出
     */
    EXPORT,

    /**
     * 导入
     */
    IMPORT,

    /**
     * 强退
     */
    FORCE,

    /**
     * 生成代码
     */
    GENCODE,

    /**
     * 清空数据
     */
    CLEAN,
    /**
     * 支付
     */
    PAY,
    /**
     * 充值
     */
    RECHARGE
}
