package org.haier.shopUpdate.upload.impl;


import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import org.haier.shopUpdate.exception.OssException;
import org.haier.shopUpdate.oss.OssClient;
import org.haier.shopUpdate.oss.OssFactory;
import org.haier.shopUpdate.oss.UploadResult;
import org.haier.shopUpdate.upload.FileUploadService;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;


/**
 * 通知公告Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-18
 */
@Service
public class FileUploadServiceImpl implements FileUploadService {
    @Override
    public UploadResult upload(MultipartFile file) throws Exception {
        OssClient storage = OssFactory.getInstance();
        String originalfileName = file.getOriginalFilename();
        if (StrUtil.isEmpty(originalfileName)||!StrUtil.contains(originalfileName,'.')){
            throw new OssException("文件格式不对");
        }
        String suffix = StrUtil.sub(originalfileName, originalfileName.lastIndexOf("."), originalfileName.length());
        UploadResult uploadResult;
        try {
            byte[] fileBytes = file.getBytes();
            uploadResult = storage.uploadSuffix(fileBytes, suffix, file.getContentType());
        } catch (IOException e) {
            throw new OssException("文件格式不对");
        }
        return uploadResult;
    }

    @Override
    public UploadResult uploadFileByPath(InputStream is, String path, String fileType) throws Exception {
        OssClient storage = OssFactory.getInstance();
        UploadResult uploadResult;
        uploadResult = storage.upload(is, path, fileType);
        return uploadResult;
    }
}
