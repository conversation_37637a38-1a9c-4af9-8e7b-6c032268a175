package org.haier.shopUpdate.upload;


import org.haier.shopUpdate.oss.UploadResult;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.InputStream;

/**
 * 通知公告Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-18
 */
public interface FileUploadService
{
    /**
     * 文件上传
     * @param file
     * @return
     */
    UploadResult upload(MultipartFile file) throws Exception;

    /**
     * 文件上传
     * @param is
     * @param path 路径
     * @param fileType 文件类型
     * @return
     */
    UploadResult uploadFileByPath(InputStream is, String path, String fileType) throws Exception;

}
