package org.haier.shopUpdate.result.getAggregatePayStatus;

public class GetAuditStatusResult {

    private Integer aggregateAuditStatus; //聚合码申请状态，0待申请，1审核中，2审核成功，3审核失败

    private String aggregateRefuseReason; //aggregatePayStatus=3返回，审核失败原因

    private String aggregatePayImage; //aggregatePayStatus=2返回，聚合码

    private String notLegalGuideImage; //非法人引导页

    private String aggregateIndexImage; //申请开通聚合码首页

    private String helibaoAuthBookUrl; //聚合码合利宝授权书下载地址

    private String ruiyinxinAuthBookUrl; //聚合码瑞银信授权书下载地址

    private Integer aggregateApplyType; //申请人类型0未知1法人申请2非法人申请

    public Integer getAggregateAuditStatus() {
        return aggregateAuditStatus;
    }

    public void setAggregateAuditStatus(Integer aggregateAuditStatus) {
        this.aggregateAuditStatus = aggregateAuditStatus;
    }

    public String getAggregateRefuseReason() {
        return aggregateRefuseReason;
    }

    public void setAggregateRefuseReason(String aggregateRefuseReason) {
        this.aggregateRefuseReason = aggregateRefuseReason;
    }

    public String getAggregatePayImage() {
        return aggregatePayImage;
    }

    public void setAggregatePayImage(String aggregatePayImage) {
        this.aggregatePayImage = aggregatePayImage;
    }

    public String getNotLegalGuideImage() {
        return notLegalGuideImage;
    }

    public void setNotLegalGuideImage(String notLegalGuideImage) {
        this.notLegalGuideImage = notLegalGuideImage;
    }

    public String getAggregateIndexImage() {
        return aggregateIndexImage;
    }

    public void setAggregateIndexImage(String aggregateIndexImage) {
        this.aggregateIndexImage = aggregateIndexImage;
    }

    public String getHelibaoAuthBookUrl() {
        return helibaoAuthBookUrl;
    }

    public void setHelibaoAuthBookUrl(String helibaoAuthBookUrl) {
        this.helibaoAuthBookUrl = helibaoAuthBookUrl;
    }

    public String getRuiyinxinAuthBookUrl() {
        return ruiyinxinAuthBookUrl;
    }

    public void setRuiyinxinAuthBookUrl(String ruiyinxinAuthBookUrl) {
        this.ruiyinxinAuthBookUrl = ruiyinxinAuthBookUrl;
    }

    public Integer getAggregateApplyType() {
        return aggregateApplyType;
    }

    public void setAggregateApplyType(Integer aggregateApplyType) {
        this.aggregateApplyType = aggregateApplyType;
    }
}
