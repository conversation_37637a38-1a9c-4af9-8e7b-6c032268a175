package org.haier.shopUpdate.result.goods.allocation;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * 仓库调拨单详情
 */
public class AllocationShopDetailInfo implements Serializable {

    /**
     * 调拨单详情id
     */
    private Integer purchaseListDetailId;
    /**
     * 调拨单唯一标识符
     */
    private Long purchaseListUnique;
    /**
     * 商品名称（供货商）
     */
    private String goodsName;
    /**
     * 商品条形码
     */
    private String goodsBarcode;
    /**
     * 订购数量
     */
    private BigDecimal purchaseListDetailCount;
    /**
     * 进价
     */
    private BigDecimal goodsInPrice;
    /**
     * 售价
     */
    private BigDecimal goodsSalePrice;
    /**
     * 规格
     */
    private String goodsSpec;
    private String goodsUnit;

    private String goodsPicturePath;

    public Integer getPurchaseListDetailId() {
        return purchaseListDetailId;
    }

    public void setPurchaseListDetailId(Integer purchaseListDetailId) {
        this.purchaseListDetailId = purchaseListDetailId;
    }

    public Long getPurchaseListUnique() {
        return purchaseListUnique;
    }

    public void setPurchaseListUnique(Long purchaseListUnique) {
        this.purchaseListUnique = purchaseListUnique;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public BigDecimal getPurchaseListDetailCount() {
        return purchaseListDetailCount;
    }

    public void setPurchaseListDetailCount(BigDecimal purchaseListDetailCount) {
        this.purchaseListDetailCount = purchaseListDetailCount;
    }

    public BigDecimal getGoodsInPrice() {
        return goodsInPrice;
    }

    public void setGoodsInPrice(BigDecimal goodsInPrice) {
        this.goodsInPrice = goodsInPrice;
    }

    public BigDecimal getGoodsSalePrice() {
        return goodsSalePrice;
    }

    public void setGoodsSalePrice(BigDecimal goodsSalePrice) {
        this.goodsSalePrice = goodsSalePrice;
    }

    public String getGoodsSpec() {
        return goodsSpec;
    }

    public void setGoodsSpec(String goodsSpec) {
        this.goodsSpec = goodsSpec;
    }

    public String getGoodsUnit() {
        return goodsUnit;
    }

    public void setGoodsUnit(String goodsUnit) {
        this.goodsUnit = goodsUnit;
    }

    public String getGoodsPicturePath() {
        return goodsPicturePath;
    }

    public void setGoodsPicturePath(String goodsPicturePath) {
        this.goodsPicturePath = goodsPicturePath;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof AllocationShopDetailInfo)) return false;
        AllocationShopDetailInfo that = (AllocationShopDetailInfo) o;
        return Objects.equals(getPurchaseListDetailId(), that.getPurchaseListDetailId()) && Objects.equals(getPurchaseListUnique(), that.getPurchaseListUnique()) && Objects.equals(getGoodsName(), that.getGoodsName()) && Objects.equals(getGoodsBarcode(), that.getGoodsBarcode()) && Objects.equals(getPurchaseListDetailCount(), that.getPurchaseListDetailCount()) && Objects.equals(getGoodsInPrice(), that.getGoodsInPrice()) && Objects.equals(getGoodsSalePrice(), that.getGoodsSalePrice()) && Objects.equals(getGoodsSpec(), that.getGoodsSpec()) && Objects.equals(getGoodsUnit(), that.getGoodsUnit()) && Objects.equals(getGoodsPicturePath(), that.getGoodsPicturePath());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getPurchaseListDetailId(), getPurchaseListUnique(), getGoodsName(), getGoodsBarcode(), getPurchaseListDetailCount(), getGoodsInPrice(), getGoodsSalePrice(), getGoodsSpec(), getGoodsUnit(), getGoodsPicturePath());
    }
}