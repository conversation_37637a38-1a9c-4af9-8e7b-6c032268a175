package org.haier.shopUpdate.result.goods.allocation;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 商品最近入库价查询结果
 */
public class QueryGoodsLastInPriceResult implements Serializable {
    private String goodsBarcode;
    private BigDecimal stockPrice;

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public BigDecimal getStockPrice() {
        return stockPrice;
    }

    public void setStockPrice(BigDecimal stockPrice) {
        this.stockPrice = stockPrice;
    }
}
