package org.haier.shopUpdate.result.goods.allocation;

import java.util.Date;
import java.util.List;

public class AllocationShopInfo {
    /**
     * 调拨单id
     */
    private Integer purchaseListId;
    /**
     * 进货订单唯一标识符
     */
    private Long purchaseListUnique;
    /**
     * 订单备注
     */
    private String purchaseListRemark;
    /**
     * 入库日期
     */
    private Date purchaseListDate;
    /**
     * 调入仓库ID
     */
    private Long storehouseInId;
    /**
     * 调入仓库名称
     */
    private String storehouseInName;
    /**
     * 调出仓库ID
     */
    private Long storehouseOutId;
    /**
     * 调出仓库名称
     */
    private String storehouseOutName;
    /**
     * 操作人
     */
    private Integer userId;
    /**
     * @see org.haier.shopUpdate.enums.AllocationShopStatus
     */
    private Integer allocationStatus;
    private String allocationStatusName;
    private Integer recipientsUserId;
    private String recipientsUserIdName;
    private Date recipientsTime;
    private String userName;

    /**
     * 商品列表
     */
    private List<AllocationShopDetailInfo> detailInfoList;

    public Integer getPurchaseListId() {
        return purchaseListId;
    }

    public void setPurchaseListId(Integer purchaseListId) {
        this.purchaseListId = purchaseListId;
    }

    public Long getPurchaseListUnique() {
        return purchaseListUnique;
    }

    public void setPurchaseListUnique(Long purchaseListUnique) {
        this.purchaseListUnique = purchaseListUnique;
    }

    public String getPurchaseListRemark() {
        return purchaseListRemark;
    }

    public void setPurchaseListRemark(String purchaseListRemark) {
        this.purchaseListRemark = purchaseListRemark;
    }

    public Date getPurchaseListDate() {
        return purchaseListDate;
    }

    public void setPurchaseListDate(Date purchaseListDate) {
        this.purchaseListDate = purchaseListDate;
    }

    public Long getStorehouseInId() {
        return storehouseInId;
    }

    public void setStorehouseInId(Long storehouseInId) {
        this.storehouseInId = storehouseInId;
    }

    public Long getStorehouseOutId() {
        return storehouseOutId;
    }

    public void setStorehouseOutId(Long storehouseOutId) {
        this.storehouseOutId = storehouseOutId;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Integer getAllocationStatus() {
        return allocationStatus;
    }

    public void setAllocationStatus(Integer allocationStatus) {
        this.allocationStatus = allocationStatus;
    }

    public List<AllocationShopDetailInfo> getDetailInfoList() {
        return detailInfoList;
    }

    public void setDetailInfoList(List<AllocationShopDetailInfo> detailInfoList) {
        this.detailInfoList = detailInfoList;
    }

    public Integer getRecipientsUserId() {
        return recipientsUserId;
    }

    public void setRecipientsUserId(Integer recipientsUserId) {
        this.recipientsUserId = recipientsUserId;
    }

    public String getRecipientsUserIdName() {
        return recipientsUserIdName;
    }

    public void setRecipientsUserIdName(String recipientsUserIdName) {
        this.recipientsUserIdName = recipientsUserIdName;
    }

    public Date getRecipientsTime() {
        return recipientsTime;
    }

    public void setRecipientsTime(Date recipientsTime) {
        this.recipientsTime = recipientsTime;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getStorehouseInName() {
        return storehouseInName;
    }

    public void setStorehouseInName(String storehouseInName) {
        this.storehouseInName = storehouseInName;
    }

    public String getStorehouseOutName() {
        return storehouseOutName;
    }

    public void setStorehouseOutName(String storehouseOutName) {
        this.storehouseOutName = storehouseOutName;
    }

    public String getAllocationStatusName() {
        return allocationStatusName;
    }

    public void setAllocationStatusName(String allocationStatusName) {
        this.allocationStatusName = allocationStatusName;
    }
}
