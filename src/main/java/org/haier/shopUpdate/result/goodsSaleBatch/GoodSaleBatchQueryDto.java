package org.haier.shopUpdate.result.goodsSaleBatch;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName GoodSaleBatchQueryResult
 * <AUTHOR>
 * @Date 2024/4/28 14:08
 */

public class GoodSaleBatchQueryDto implements Serializable {
    private Long goodsSaleBatchId;
    private Long shopUnique;
    private String goodsBarcode;
    private String batchUnique;
    private String stockListUnique;
    private BigDecimal goodsOutCount;
    private BigDecimal goodsInPrice;
    private BigDecimal goodsOutPrice;
    private Long goodsBatchId;
    private BigDecimal goodsCount;
    private BigDecimal goodsInCount;
    private Long createId;
    private Date createTime;
    private Long updateId;
    private Date updateTime;

    public Long getGoodsSaleBatchId() {
        return goodsSaleBatchId;
    }

    public void setGoodsSaleBatchId(Long goodsSaleBatchId) {
        this.goodsSaleBatchId = goodsSaleBatchId;
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public String getBatchUnique() {
        return batchUnique;
    }

    public void setBatchUnique(String batchUnique) {
        this.batchUnique = batchUnique;
    }

    public String getStockListUnique() {
        return stockListUnique;
    }

    public void setStockListUnique(String stockListUnique) {
        this.stockListUnique = stockListUnique;
    }

    public BigDecimal getGoodsOutCount() {
        return goodsOutCount;
    }

    public void setGoodsOutCount(BigDecimal goodsOutCount) {
        this.goodsOutCount = goodsOutCount;
    }

    public BigDecimal getGoodsInPrice() {
        return goodsInPrice;
    }

    public void setGoodsInPrice(BigDecimal goodsInPrice) {
        this.goodsInPrice = goodsInPrice;
    }

    public BigDecimal getGoodsOutPrice() {
        return goodsOutPrice;
    }

    public void setGoodsOutPrice(BigDecimal goodsOutPrice) {
        this.goodsOutPrice = goodsOutPrice;
    }

    public Long getCreateId() {
        return createId;
    }

    public void setCreateId(Long createId) {
        this.createId = createId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateId() {
        return updateId;
    }

    public void setUpdateId(Long updateId) {
        this.updateId = updateId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getGoodsBatchId() {
        return goodsBatchId;
    }

    public void setGoodsBatchId(Long goodsBatchId) {
        this.goodsBatchId = goodsBatchId;
    }

    public BigDecimal getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(BigDecimal goodsCount) {
        this.goodsCount = goodsCount;
    }

    public BigDecimal getGoodsInCount() {
        return goodsInCount;
    }

    public void setGoodsInCount(BigDecimal goodsInCount) {
        this.goodsInCount = goodsInCount;
    }
}
