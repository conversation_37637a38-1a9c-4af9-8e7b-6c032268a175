package org.haier.shopUpdate.result.statistics;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: 王恩龙
 * @create: 2024-05-20 10:10
 * @Description:
 */
public class QuerySaleListByPayMethodEntity implements Serializable {
    public static final long serialVersionUID = 1L;
    //支付方式
    private Integer payMethod;
    //支付方式名称
    private String payMethodName;
    //支付或退款金额
    private BigDecimal payMoney;
    //创建时间
    private String saleListDatetime;
    //订单编号
    private String saleListUnique;
    //订单类型：1、消费订单；2、退款订单
    private Integer orderType;
    //是否组合支付；0、非组合支付；1、组合支付；
    private Integer isZh;
    //店铺类型:1、普通超市；12、餐饮；
    private Integer shopType;

    public Integer getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(Integer payMethod) {
        this.payMethod = payMethod;
    }

    public String getPayMethodName() {
        return payMethodName;
    }

    public void setPayMethodName(String payMethodName) {
        this.payMethodName = payMethodName;
    }

    public BigDecimal getPayMoney() {
        return payMoney;
    }

    public void setPayMoney(BigDecimal payMoney) {
        this.payMoney = payMoney;
    }

    public String getSaleListDatetime() {
        return saleListDatetime;
    }

    public void setSaleListDatetime(String saleListDatetime) {
        this.saleListDatetime= saleListDatetime;
    }

    public String getSaleListUnique() {
        return saleListUnique;
    }

    public void setSaleListUnique(String saleListUnique) {
        this.saleListUnique = saleListUnique;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public Integer getIsZh() {
        return isZh;
    }

    public void setIsZh(Integer isZh) {
        this.isZh = isZh;
    }

    public Integer getShopType() {
        return shopType;
    }

    public void setShopType(Integer shopType) {
        this.shopType = shopType;
    }
}
