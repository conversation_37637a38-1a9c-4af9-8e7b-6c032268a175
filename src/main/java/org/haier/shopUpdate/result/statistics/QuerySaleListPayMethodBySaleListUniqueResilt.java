package org.haier.shopUpdate.result.statistics;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: 王恩龙
 * @create: 2024-05-21 11:47
 * @Description:
 */
public class QuerySaleListPayMethodBySaleListUniqueResilt implements Serializable {
    private static final long serialVersionUID = 1L;
    private String payMethodName;
    private String payMethodIcon;
    private Integer payMethod;
    private BigDecimal payMoney;

    public String getPayMethodName() {
        return payMethodName;
    }

    public void setPayMethodName(String payMethodName) {
        this.payMethodName = payMethodName;
    }

    public Integer getPayMethod() {
        return payMethod;
    }

    public String getPayMethodIcon() {
        return payMethodIcon;
    }

    public void setPayMethodIcon(String payMethodIcon) {
        this.payMethodIcon = payMethodIcon;
    }

    public void setPayMethod(Integer payMethod) {
        this.payMethod = payMethod;
    }

    public BigDecimal getPayMoney() {
        return payMoney;
    }

    public void setPayMoney(BigDecimal payMoney) {
        this.payMoney = payMoney;
    }
}
