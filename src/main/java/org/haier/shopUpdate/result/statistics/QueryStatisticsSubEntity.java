package org.haier.shopUpdate.result.statistics;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: 王恩龙
 * @create: 2024-05-17 15:54
 * @Description:
 */
public class QueryStatisticsSubEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    //支付方式
    private Integer payMethod;
    //支付方式名称
    private String payMethodName;
    //支付方式图标
    private String payMethodIcon;
    //收款金额（实收金额）
    private BigDecimal payMoney;
    //订单数量
    private Integer orderCount;
    //订单类型：0、线下订单；1、线上订单；
    private Integer saleType;

    public Integer getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(Integer payMethod) {
        this.payMethod = payMethod;
    }

    public String getPayMethodName() {
        return payMethodName;
    }

    public void setPayMethodName(String payMethodName) {
        this.payMethodName = payMethodName;
    }

    public String getPayMethodIcon() {
        return payMethodIcon;
    }

    public void setPayMethodIcon(String payMethodIcon) {
        this.payMethodIcon = payMethodIcon;
    }

    public BigDecimal getPayMoney() {
        return payMoney;
    }

    public void setPayMoney(BigDecimal payMoney) {
        this.payMoney = payMoney;
    }

    public Integer getOrderCount() {
        return orderCount;
    }

    public void setOrderCount(Integer orderCount) {
        this.orderCount = orderCount;
    }

    public Integer getSaleType() {
        return saleType;
    }

    public void setSaleType(Integer saleType) {
        this.saleType = saleType;
    }
}
