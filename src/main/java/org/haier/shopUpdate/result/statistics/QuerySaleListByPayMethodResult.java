package org.haier.shopUpdate.result.statistics;

import java.io.Serializable;
import java.util.List;

/**
 * @author: 王恩龙
 * @create: 2024-05-20 15:13
 * @Description:
 */
public class QuerySaleListByPayMethodResult implements Serializable {
    private QueryStatisticsStatis queryStatisticsStatis;
    private List<QuerySaleListByPayMethodEntity> list;
    private Integer orderCount;

    public QueryStatisticsStatis getQueryStatisticsStatis() {
        return queryStatisticsStatis;
    }

    public void setQueryStatisticsStatis(QueryStatisticsStatis queryStatisticsStatis) {
        this.queryStatisticsStatis = queryStatisticsStatis;
    }

    public List<QuerySaleListByPayMethodEntity> getList() {
        return list;
    }

    public void setList(List<QuerySaleListByPayMethodEntity> list) {
        this.list = list;
    }

    public Integer getOrderCount() {
        return orderCount;
    }

    public void setOrderCount(Integer orderCount) {
        this.orderCount = orderCount;
    }
}
