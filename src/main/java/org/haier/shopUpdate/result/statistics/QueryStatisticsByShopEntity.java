package org.haier.shopUpdate.result.statistics;

import java.io.Serializable;
import java.util.List;

/**
 * @author: 王恩龙
 * @create: 2024-05-17 15:52
 * @Description:
 */
public class QueryStatisticsByShopEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    //本方式的收入统计
    private QueryStatisticsStatis queryStatisticsStatis;
    //本方式的收入明细
    private List<QueryStatisticsSubEntity> list;

    public org.haier.shopUpdate.result.statistics.QueryStatisticsStatis getQueryStatisticsStatis() {
        return queryStatisticsStatis;
    }

    public void setQueryStatisticsStatis(org.haier.shopUpdate.result.statistics.QueryStatisticsStatis queryStatisticsStatis) {
        this.queryStatisticsStatis = queryStatisticsStatis;
    }

    public List<QueryStatisticsSubEntity> getList() {
        return list;
    }

    public void setList(List<QueryStatisticsSubEntity> list) {
        this.list = list;
    }
}
