package org.haier.shopUpdate.result.statistics;

import java.io.Serializable;

/**
 * @author: 王恩龙
 * @create: 2024-05-17 15:42
 * @Description:
 */
public class QueryStatisticsByShopResult implements Serializable {
    private static final long serialVersionUID = 1L;
    private QueryStatisticsStatis queryStatisticsStatis;

    //线下数据统计
    private QueryStatisticsByShopEntity unlineStatis;
    //线上数据统计
    private QueryStatisticsByShopEntity onlineStatis;

    public QueryStatisticsStatis getQueryStatisticsStatis() {
        return queryStatisticsStatis;
    }

    public void setQueryStatisticsStatis(QueryStatisticsStatis queryStatisticsStatis) {
        this.queryStatisticsStatis = queryStatisticsStatis;
    }

    public QueryStatisticsByShopEntity getUnlineStatis() {
        return unlineStatis;
    }

    public void setUnlineStatis(QueryStatisticsByShopEntity unlineStatis) {
        this.unlineStatis = unlineStatis;
    }

    public QueryStatisticsByShopEntity getOnlineStatis() {
        return onlineStatis;
    }

    public void setOnlineStatis(QueryStatisticsByShopEntity onlineStatis) {
        this.onlineStatis = onlineStatis;
    }
}
