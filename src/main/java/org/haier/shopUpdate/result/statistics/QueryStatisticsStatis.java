package org.haier.shopUpdate.result.statistics;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 统计的数据之和
 * @author: 王恩龙
 * @create: 2024-05-17 15:43
 * @Description:
 */
public class QueryStatisticsStatis implements Serializable {
    private static final long serialVersionUID = 1L;
    //总实收
    private BigDecimal saleListTotal;
    //总营业额
    private BigDecimal saleListTotalMoney;
    //退款金额
    private BigDecimal saleListRefundMoney;
    //订单总数量
    private Integer saleListCount;

    public BigDecimal getSaleListTotal() {
        return saleListTotal;
    }

    public void setSaleListTotal(BigDecimal saleListTotal) {
        this.saleListTotal = saleListTotal;
    }

    public BigDecimal getSaleListTotalMoney() {
        return saleListTotalMoney;
    }

    public void setSaleListTotalMoney(BigDecimal saleListTotalMoney) {
        this.saleListTotalMoney = saleListTotalMoney;
    }

    public BigDecimal getSaleListRefundMoney() {
        return saleListRefundMoney;
    }

    public void setSaleListRefundMoney(BigDecimal saleListRefundMoney) {
        this.saleListRefundMoney = saleListRefundMoney;
    }

    public Integer getSaleListCount() {
        return saleListCount;
    }

    public void setSaleListCount(Integer saleListCount) {
        this.saleListCount = saleListCount;
    }
}
