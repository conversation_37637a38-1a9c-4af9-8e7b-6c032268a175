package org.haier.shopUpdate.result.shopSupplier;

import java.math.BigDecimal;
import java.util.List;

public class ShopSupplierBillResult {
    /**
     *供货商编号
     */
    private String supplierUnique;
    /**
     *供货商名称
     */
    private String supplierName;
    /**
     *联系方式
     */
    private String supplierPhone;
    /**
     * 购销单ID
     */
    private Long id;
    /**
     * 状态
     */
    private Integer status;
    /**
     *商品种类
     */
    private Integer goodsCategory;
    /**
     *采购金额
     */
    private BigDecimal totalPrice;
    /**
     *待结金额
     */
    private BigDecimal outstandingAmount;
    /**
     * 商品明细
     */
    private List<ShopSupplierBillDetailResult> goodsList;

    public String getSupplierUnique() {
        return supplierUnique;
    }

    public void setSupplierUnique(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getSupplierPhone() {
        return supplierPhone;
    }

    public void setSupplierPhone(String supplierPhone) {
        this.supplierPhone = supplierPhone;
    }

    public Integer getGoodsCategory() {
        return goodsCategory;
    }

    public void setGoodsCategory(Integer goodsCategory) {
        this.goodsCategory = goodsCategory;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public BigDecimal getOutstandingAmount() {
        return outstandingAmount;
    }

    public void setOutstandingAmount(BigDecimal outstandingAmount) {
        this.outstandingAmount = outstandingAmount;
    }

    public List<ShopSupplierBillDetailResult> getGoodsList() {
        return goodsList;
    }

    public void setGoodsList(List<ShopSupplierBillDetailResult> goodsList) {
        this.goodsList = goodsList;
    }
}
