package org.haier.shopUpdate.result.shopSupplier;

import java.math.BigDecimal;

public class ShopSupplierBillDetailResult {
    /**
     * 商品条码
     */
    private String goodsBarcode;
    /**
     * 商品名称
     */
    private String goodsName;
    /**
     *商品图片路径
     */
    private String goodsPicturepath;
    /**
     *商品采购数量
     */
    private BigDecimal purchaseGoodsCount;

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsPicturepath() {
        return goodsPicturepath;
    }

    public void setGoodsPicturepath(String goodsPicturepath) {
        this.goodsPicturepath = goodsPicturepath;
    }

    public BigDecimal getPurchaseGoodsCount() {
        return purchaseGoodsCount;
    }

    public void setPurchaseGoodsCount(BigDecimal purchaseGoodsCount) {
        this.purchaseGoodsCount = purchaseGoodsCount;
    }
}
