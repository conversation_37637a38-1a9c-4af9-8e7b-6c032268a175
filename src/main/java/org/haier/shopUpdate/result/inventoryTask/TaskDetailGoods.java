package org.haier.shopUpdate.result.inventoryTask;

import java.math.BigDecimal;

public class TaskDetailGoods {

    private Long taskDetailId; //任务详情编号

    private String goodsBarcode; //商品条码

    private Integer goodsId; //商品编号

    private String goodsName; //商品名称

    private BigDecimal inventoryCount; //盘点数

    private String staffName; //操作人

    private String remarks; //备注

    private BigDecimal bucketWeight; //筐重量

    private String goodsPicturepath; //商品图片

    /**
     * 单位
     */
    private String goodsUnit;
    /**
     * 称重商品类型：0、按件；1、按重量
     */
    private Integer goodsChengType;
    /**
     * 货位
     */
    private String goodsPosition;
    // 货位名称
    private String completePositionName;

    public String getGoodsUnit() {
        return goodsUnit;
    }

    public void setGoodsUnit(String goodsUnit) {
        this.goodsUnit = goodsUnit;
    }

    public Integer getGoodsChengType() {
        return goodsChengType;
    }

    public void setGoodsChengType(Integer goodsChengType) {
        this.goodsChengType = goodsChengType;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public Integer getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Integer goodsId) {
        this.goodsId = goodsId;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public BigDecimal getInventoryCount() {
        return inventoryCount;
    }

    public void setInventoryCount(BigDecimal inventoryCount) {
        this.inventoryCount = inventoryCount;
    }

    public String getStaffName() {
        return staffName;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public BigDecimal getBucketWeight() {
        return bucketWeight;
    }

    public void setBucketWeight(BigDecimal bucketWeight) {
        this.bucketWeight = bucketWeight;
    }

    public Long getTaskDetailId() {
        return taskDetailId;
    }

    public void setTaskDetailId(Long taskDetailId) {
        this.taskDetailId = taskDetailId;
    }

    public String getGoodsPicturepath() {
        return goodsPicturepath;
    }

    public void setGoodsPicturepath(String goodsPicturepath) {
        this.goodsPicturepath = goodsPicturepath;
    }

    public String getGoodsPosition() {
        return goodsPosition;
    }

    public void setGoodsPosition(String goodsPosition) {
        this.goodsPosition = goodsPosition;
    }

    public String getCompletePositionName() {
        return completePositionName;
    }

    public void setCompletePositionName(String completePositionName) {
        this.completePositionName = completePositionName;
    }
}
