package org.haier.shopUpdate.result.inventoryTask;

import java.math.BigDecimal;
import java.util.List;

public class InventoryGoodsRecordResult {

    private String goodsName; //商品名称

    private String goodsPicturepath; //商品图片

    private BigDecimal goodsCount; //商品库存

    private String goodsUnit; //商品单位

    private List<InventoryGoodsRecordInventory> inventoryList; //盘点列表

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsPicturepath() {
        return goodsPicturepath;
    }

    public void setGoodsPicturepath(String goodsPicturepath) {
        this.goodsPicturepath = goodsPicturepath;
    }

    public BigDecimal getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(BigDecimal goodsCount) {
        this.goodsCount = goodsCount;
    }

    public List<InventoryGoodsRecordInventory> getInventoryList() {
        return inventoryList;
    }

    public void setInventoryList(List<InventoryGoodsRecordInventory> inventoryList) {
        this.inventoryList = inventoryList;
    }

    public String getGoodsUnit() {
        return goodsUnit;
    }

    public void setGoodsUnit(String goodsUnit) {
        this.goodsUnit = goodsUnit;
    }
}
