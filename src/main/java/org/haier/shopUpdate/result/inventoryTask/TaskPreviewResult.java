package org.haier.shopUpdate.result.inventoryTask;

import java.util.List;

public class TaskPreviewResult {

    private String taskName; //任务名称

    private String createUser; //创建人

    private String createTime; //创建时间

    private String taskNo; //创建单号

    private Integer taskStatus; //盘点单状态1待提交2已盘点

    private List<TaskPreviewTaskDetailList> taskDetailList;

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getTaskNo() {
        return taskNo;
    }

    public void setTaskNo(String taskNo) {
        this.taskNo = taskNo;
    }

    public List<TaskPreviewTaskDetailList> getTaskDetailList() {
        return taskDetailList;
    }

    public void setTaskDetailList(List<TaskPreviewTaskDetailList> taskDetailList) {
        this.taskDetailList = taskDetailList;
    }

    public Integer getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(Integer taskStatus) {
        this.taskStatus = taskStatus;
    }
}
