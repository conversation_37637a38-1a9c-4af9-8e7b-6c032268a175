package org.haier.shopUpdate.result.inventoryTask;

import java.math.BigDecimal;

public class InventoryGoodsRecordInventory {

    private Long taskId; //盘点任务id

    private String taskName; //盘点单名称

    private BigDecimal preStock; //盘点前库存

    private BigDecimal inventoryCount; //盘点数

    private BigDecimal diffCount; //盈亏数

    private BigDecimal diffMoney; //盈亏金额

    private BigDecimal goodsInPrice; //商品进价

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public BigDecimal getPreStock() {
        return preStock;
    }

    public void setPreStock(BigDecimal preStock) {
        this.preStock = preStock;
    }

    public BigDecimal getInventoryCount() {
        return inventoryCount;
    }

    public void setInventoryCount(BigDecimal inventoryCount) {
        this.inventoryCount = inventoryCount;
    }

    public BigDecimal getDiffCount() {
        return diffCount;
    }

    public void setDiffCount(BigDecimal diffCount) {
        this.diffCount = diffCount;
    }

    public BigDecimal getDiffMoney() {
        return diffMoney;
    }

    public void setDiffMoney(BigDecimal diffMoney) {
        this.diffMoney = diffMoney;
    }

    public BigDecimal getGoodsInPrice() {
        return goodsInPrice;
    }

    public void setGoodsInPrice(BigDecimal goodsInPrice) {
        this.goodsInPrice = goodsInPrice;
    }
}
