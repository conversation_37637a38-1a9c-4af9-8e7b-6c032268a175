package org.haier.shopUpdate.result.inventoryTask;

import java.math.BigDecimal;

public class TaskGoodsDetailGoodsList {

    private BigDecimal inventoryCount; //盘点数

    private String staffName; //操作员工

    private String remarks; //备注

    private String goodsPosition;//货位
    // 货位名称
    private String completePositionName;

    public BigDecimal getInventoryCount() {
        return inventoryCount;
    }

    public void setInventoryCount(BigDecimal inventoryCount) {
        this.inventoryCount = inventoryCount;
    }

    public String getStaffName() {
        return staffName;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getGoodsPosition() {
        return goodsPosition;
    }

    public void setGoodsPosition(String goodsPosition) {
        this.goodsPosition = goodsPosition;
    }

    public String getCompletePositionName() {
        return completePositionName;
    }

    public void setCompletePositionName(String completePositionName) {
        this.completePositionName = completePositionName;
    }
}
