package org.haier.shopUpdate.result.inventoryTask;

import java.util.List;

public class TaskListTask {

    private Long taskId; //任务id

    private String taskName; //任务名称

    private String createTime; //创建时间

    private Integer taskStatus; //盘点任务状态:1待提交2已盘点

    private String createUser; //创建人

    private String finishTime; //完成时间

    private Integer kindTotal; //种类

    private List<TaskListGoodsList> goodsList; //商品信息

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public Integer getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(Integer taskStatus) {
        this.taskStatus = taskStatus;
    }

    public List<TaskListGoodsList> getGoodsList() {
        return goodsList;
    }

    public void setGoodsList(List<TaskListGoodsList> goodsList) {
        this.goodsList = goodsList;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(String finishTime) {
        this.finishTime = finishTime;
    }

    public Integer getKindTotal() {
        return kindTotal;
    }

    public void setKindTotal(Integer kindTotal) {
        this.kindTotal = kindTotal;
    }
}
