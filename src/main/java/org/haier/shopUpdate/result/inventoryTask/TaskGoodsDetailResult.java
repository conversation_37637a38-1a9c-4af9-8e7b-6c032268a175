package org.haier.shopUpdate.result.inventoryTask;

import java.math.BigDecimal;
import java.util.List;

public class TaskGoodsDetailResult {

    private String goodsName; //商品名称

    private String goodsBarcode; //商品条码

    private String goodsPicturepath; //商品图片

    private String taskName; //盘点任务名称

    private String taskNo; //盘点单号

    private BigDecimal preStock; //盘点前库存

    private BigDecimal inventoryCount; //盘点数

    private BigDecimal diffCount; //盈亏数

    private BigDecimal diffMoney; //盈亏金额

    private BigDecimal goodsInPrice; //商品进价

    private List<TaskGoodsDetailGoodsList> goodsList; //商品列表

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public String getGoodsPicturepath() {
        return goodsPicturepath;
    }

    public void setGoodsPicturepath(String goodsPicturepath) {
        this.goodsPicturepath = goodsPicturepath;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getTaskNo() {
        return taskNo;
    }

    public void setTaskNo(String taskNo) {
        this.taskNo = taskNo;
    }

    public BigDecimal getPreStock() {
        return preStock;
    }

    public void setPreStock(BigDecimal preStock) {
        this.preStock = preStock;
    }

    public BigDecimal getInventoryCount() {
        return inventoryCount;
    }

    public void setInventoryCount(BigDecimal inventoryCount) {
        this.inventoryCount = inventoryCount;
    }

    public BigDecimal getDiffCount() {
        return diffCount;
    }

    public void setDiffCount(BigDecimal diffCount) {
        this.diffCount = diffCount;
    }

    public BigDecimal getDiffMoney() {
        return diffMoney;
    }

    public void setDiffMoney(BigDecimal diffMoney) {
        this.diffMoney = diffMoney;
    }

    public BigDecimal getGoodsInPrice() {
        return goodsInPrice;
    }

    public void setGoodsInPrice(BigDecimal goodsInPrice) {
        this.goodsInPrice = goodsInPrice;
    }

    public List<TaskGoodsDetailGoodsList> getGoodsList() {
        return goodsList;
    }

    public void setGoodsList(List<TaskGoodsDetailGoodsList> goodsList) {
        this.goodsList = goodsList;
    }

}
