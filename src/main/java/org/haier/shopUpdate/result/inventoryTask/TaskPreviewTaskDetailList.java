package org.haier.shopUpdate.result.inventoryTask;

import java.math.BigDecimal;

public class TaskPreviewTaskDetailList {

    private String goodsName; //商品名称

    private String goodsBarcode; //商品条码

    private BigDecimal preStock; //盘点前库存

    private BigDecimal inventoryCount; //盘点数

    private BigDecimal goodsInPrice; //成本/进货价

    private BigDecimal diffCount; //盈亏数

    private BigDecimal diffMoney; //盈亏金额

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public BigDecimal getPreStock() {
        return preStock;
    }

    public void setPreStock(BigDecimal preStock) {
        this.preStock = preStock;
    }

    public BigDecimal getInventoryCount() {
        return inventoryCount;
    }

    public void setInventoryCount(BigDecimal inventoryCount) {
        this.inventoryCount = inventoryCount;
    }

    public BigDecimal getGoodsInPrice() {
        return goodsInPrice;
    }

    public void setGoodsInPrice(BigDecimal goodsInPrice) {
        this.goodsInPrice = goodsInPrice;
    }

    public BigDecimal getDiffCount() {
        return diffCount;
    }

    public void setDiffCount(BigDecimal diffCount) {
        this.diffCount = diffCount;
    }

    public BigDecimal getDiffMoney() {
        return diffMoney;
    }

    public void setDiffMoney(BigDecimal diffMoney) {
        this.diffMoney = diffMoney;
    }
}
