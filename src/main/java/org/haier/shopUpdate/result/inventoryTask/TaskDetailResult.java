package org.haier.shopUpdate.result.inventoryTask;

import java.math.BigDecimal;
import java.util.List;

public class TaskDetailResult {

    private String taskName; //盘点任务名称

    private String createUser; //创建人

    private String createTime; //创建时间

    private String taskNo; //盘点单号
    private Integer kindTotal;

    private List<TaskDetailGoods> taskDetailList;

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getTaskNo() {
        return taskNo;
    }

    public void setTaskNo(String taskNo) {
        this.taskNo = taskNo;
    }

    public List<TaskDetailGoods> getTaskDetailList() {
        return taskDetailList;
    }

    public void setTaskDetailList(List<TaskDetailGoods> taskDetailList) {
        this.taskDetailList = taskDetailList;
    }

    public Integer getKindTotal() {
        return kindTotal;
    }

    public void setKindTotal(Integer kindTotal) {
        this.kindTotal = kindTotal;
    }
}
