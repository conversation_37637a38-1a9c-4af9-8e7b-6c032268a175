package org.haier.shopUpdate.result.shopSupBill;

import java.math.BigDecimal;
import java.util.List;

public class QueryShopSupBillResult {
    private Long id;
    private String billNo;
    private String createTime;
    private String supplierUnique;
    private String supplierName;
    private String supplierPhone;
    private Integer status;
    private BigDecimal totalPrice;
    private Integer goodsCategory;
    private List<GoodsListResult> goodsList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getSupplierUnique() {
        return supplierUnique;
    }

    public void setSupplierUnique(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getSupplierPhone() {
        return supplierPhone;
    }

    public void setSupplierPhone(String supplierPhone) {
        this.supplierPhone = supplierPhone;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public Integer getGoodsCategory() {
        return goodsCategory;
    }

    public void setGoodsCategory(Integer goodsCategory) {
        this.goodsCategory = goodsCategory;
    }

    public List<GoodsListResult> getGoodsList() {
        return goodsList;
    }

    public void setGoodsList(List<GoodsListResult> goodsList) {
        this.goodsList = goodsList;
    }
}
