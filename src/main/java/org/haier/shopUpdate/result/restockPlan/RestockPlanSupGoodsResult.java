package org.haier.shopUpdate.result.restockPlan;

import java.math.BigDecimal;

public class RestockPlanSupGoodsResult {
    private Long shopRestockplanGoodsId;
    private String goodsPicturepath;
    private String goodsName;
    private String goodsBarcode;
    private BigDecimal goodsCount;
    private BigDecimal goodsInPrice;
    private String goodsUnit;
    private BigDecimal goodsTotal;

    public Long getShopRestockplanGoodsId() {
        return shopRestockplanGoodsId;
    }

    public void setShopRestockplanGoodsId(Long shopRestockplanGoodsId) {
        this.shopRestockplanGoodsId = shopRestockplanGoodsId;
    }

    public String getGoodsPicturepath() {
        return goodsPicturepath;
    }

    public void setGoodsPicturepath(String goodsPicturepath) {
        this.goodsPicturepath = goodsPicturepath;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public BigDecimal getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(BigDecimal goodsCount) {
        this.goodsCount = goodsCount;
    }

    public BigDecimal getGoodsInPrice() {
        return goodsInPrice;
    }

    public void setGoodsInPrice(BigDecimal goodsInPrice) {
        this.goodsInPrice = goodsInPrice;
    }

    public String getGoodsUnit() {
        return goodsUnit;
    }

    public void setGoodsUnit(String goodsUnit) {
        this.goodsUnit = goodsUnit;
    }

    public BigDecimal getGoodsTotal() {
        return goodsTotal;
    }

    public void setGoodsTotal(BigDecimal goodsTotal) {
        this.goodsTotal = goodsTotal;
    }
}
