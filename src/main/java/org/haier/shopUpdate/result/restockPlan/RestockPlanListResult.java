package org.haier.shopUpdate.result.restockPlan;

import org.haier.shopUpdate.entity.RestockPlanEntity;

import java.math.BigDecimal;
import java.util.List;

public class RestockPlanListResult extends RestockPlanEntity {

    private BigDecimal goodsCount;
    private List<RestockPlanGoodsResult> goodsList;

    public BigDecimal getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(BigDecimal goodsCount) {
        this.goodsCount = goodsCount;
    }

    public List<RestockPlanGoodsResult> getGoodsList() {
        return goodsList;
    }

    public void setGoodsList(List<RestockPlanGoodsResult> goodsList) {
        this.goodsList = goodsList;
    }
}
