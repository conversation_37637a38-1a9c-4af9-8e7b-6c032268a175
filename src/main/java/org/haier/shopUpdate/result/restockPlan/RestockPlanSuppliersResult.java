package org.haier.shopUpdate.result.restockPlan;

import org.haier.shopUpdate.entity.RestockPlanSupplierEntity;

import java.math.BigDecimal;
import java.util.List;

public class RestockPlanSuppliersResult extends RestockPlanSupplierEntity {

    private String supplierExamineId;
    private String supplierName;
    private Integer purchaseType;
    private Integer enableStatus;
    private String supplierPhone;
    private String orderNo;
    private BigDecimal goodsTotal;
    private Integer goodsCounts;

    public String getSupplierExamineId() {
        return supplierExamineId;
    }

    public void setSupplierExamineId(String supplierExamineId) {
        this.supplierExamineId = supplierExamineId;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public Integer getPurchaseType() {
        return purchaseType;
    }

    public void setPurchaseType(Integer purchaseType) {
        this.purchaseType = purchaseType;
    }

    public Integer getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    public String getSupplierPhone() {
        return supplierPhone;
    }

    public void setSupplierPhone(String supplierPhone) {
        this.supplierPhone = supplierPhone;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public BigDecimal getGoodsTotal() {
        return goodsTotal;
    }

    public void setGoodsTotal(BigDecimal goodsTotal) {
        this.goodsTotal = goodsTotal;
    }

    public Integer getGoodsCounts() {
        return goodsCounts;
    }

    public void setGoodsCounts(Integer goodsCounts) {
        this.goodsCounts = goodsCounts;
    }

    private List<RestockPlanGoodsResult> goodsList;

    public List<RestockPlanGoodsResult> getGoodsList() {
        return goodsList;
    }

    public void setGoodsList(List<RestockPlanGoodsResult> goodsList) {
        this.goodsList = goodsList;
    }
}
