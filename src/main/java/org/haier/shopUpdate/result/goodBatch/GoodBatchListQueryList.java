package org.haier.shopUpdate.result.goodBatch;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName GoodBatchListQueryList
 * <AUTHOR>
 * @Date 2024/4/28 14:08
 */

public class GoodBatchListQueryList implements Serializable {
    private List<GoodBatchListQueryDto> list;

    public List<GoodBatchListQueryDto> getList() {
        return list;
    }

    public void setList(List<GoodBatchListQueryDto> list) {
        this.list = list;
    }
}
