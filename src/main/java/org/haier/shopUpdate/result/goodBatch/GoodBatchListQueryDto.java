package org.haier.shopUpdate.result.goodBatch;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName GoodBatchListQueryDto
 * <AUTHOR>
 * @Date 2024/4/28 14:08
 */

public class GoodBatchListQueryDto implements Serializable {
    private Long goodsBatchId;
    /**
     * 批次唯一标识
     */
    private String batchUnique;
    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date goodsProd;
    /**
     * 到期日
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date goodsExp;
    /**
     * 剩余数量
     */
    private BigDecimal goodsCount;
    /**
     * 入库数量
     */
    private BigDecimal goodsInCount;
    /**
     * 出库数量
     */
    private BigDecimal outStockCount;
    /**
     * 入库时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 入库单价
     */
    private BigDecimal goodsInPrice;

    public Long getGoodsBatchId() {
        return goodsBatchId;
    }

    public void setGoodsBatchId(Long goodsBatchId) {
        this.goodsBatchId = goodsBatchId;
    }

    public String getBatchUnique() {
        return batchUnique;
    }

    public void setBatchUnique(String batchUnique) {
        this.batchUnique = batchUnique;
    }

    public Date getGoodsProd() {
        return goodsProd;
    }

    public void setGoodsProd(Date goodsProd) {
        this.goodsProd = goodsProd;
    }

    public Date getGoodsExp() {
        return goodsExp;
    }

    public void setGoodsExp(Date goodsExp) {
        this.goodsExp = goodsExp;
    }

    public BigDecimal getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(BigDecimal goodsCount) {
        this.goodsCount = goodsCount;
    }

    public BigDecimal getGoodsInCount() {
        return goodsInCount;
    }

    public void setGoodsInCount(BigDecimal goodsInCount) {
        this.goodsInCount = goodsInCount;
    }

    public BigDecimal getOutStockCount() {
        return outStockCount;
    }

    public void setOutStockCount(BigDecimal outStockCount) {
        this.outStockCount = outStockCount;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public BigDecimal getGoodsInPrice() {
        return goodsInPrice;
    }

    public void setGoodsInPrice(BigDecimal goodsInPrice) {
        this.goodsInPrice = goodsInPrice;
    }
}
