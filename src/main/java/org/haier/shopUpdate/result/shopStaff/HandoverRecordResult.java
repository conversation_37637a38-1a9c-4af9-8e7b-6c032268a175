package org.haier.shopUpdate.result.shopStaff;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 交接班返回值
 * @ClassName HandoverRecordResult
 * <AUTHOR>
 * @Date 2023/8/23 15:46
 **/
public class HandoverRecordResult implements Serializable {

    /**
     * 收银员ID
     */
    private Long sale_list_cashier;
    /**
     * 收银员姓名
     */
    private String staff_name;
    /**
     * 支付总订单数量
     */
    private Long orderCount;
    /**
     * 支付总金额
     */
    private BigDecimal sumMoney;
    /**
     * 充值总金额
     */
    private BigDecimal rechargeSum;
    /**
     * 充值订单
     */
    private Long rechargeCount;
    /**
     * 登录时间
     */
    private String login_datetime;
    /**
     * 退出时间
     */
    private String sign_out_datetime;
    /**
     * 第一笔订单时间
     */
    private String start_datetime;
    /**
     * 最后一笔订单时间
     */
    private String end_datetime;

    /**
     * 支付方式列表
     */
    private List<PayMethodDetail> detail;

    /**
     * 充值方式列表
     */
    private List<PayMethodDetail> rechargeDetail;

    /**
     * 支付类型
     */
    public static class PayMethodDetail {

        /**
         * 支付类型
         */
        private Integer recharge_method;
        /**
         * 支付名称
         */
        private String name;
        /**
         * 支付金额
         */
        private BigDecimal sale_list_actually_received;
        /**
         * 退款金额
         */
        private BigDecimal sale_list_return_money;
        /**
         * 订单数量
         */
        private Long orderNum;
        /**
         * 百分比
         */
        private String accounting;

        public PayMethodDetail() {
        }

        public Integer getRecharge_method() {
            return recharge_method;
        }

        public void setRecharge_method(Integer recharge_method) {
            this.recharge_method = recharge_method;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public BigDecimal getSale_list_actually_received() {
            return sale_list_actually_received;
        }

        public void setSale_list_actually_received(BigDecimal sale_list_actually_received) {
            this.sale_list_actually_received = sale_list_actually_received;
        }

        public Long getOrderNum() {
            return orderNum;
        }

        public void setOrderNum(Long orderNum) {
            this.orderNum = orderNum;
        }

        public String getAccounting() {
            return accounting;
        }

        public void setAccounting(String accounting) {
            this.accounting = accounting;
        }

        public BigDecimal getSale_list_return_money() {
            return sale_list_return_money;
        }

        public void setSale_list_return_money(BigDecimal sale_list_return_money) {
            this.sale_list_return_money = sale_list_return_money;
        }
    }

    public HandoverRecordResult() {
    }

    public Long getSale_list_cashier() {
        return sale_list_cashier;
    }

    public void setSale_list_cashier(Long sale_list_cashier) {
        this.sale_list_cashier = sale_list_cashier;
    }

    public String getStaff_name() {
        return staff_name;
    }

    public void setStaff_name(String staff_name) {
        this.staff_name = staff_name;
    }

    public Long getOrderCount() {
        return orderCount;
    }

    public void setOrderCount(Long orderCount) {
        this.orderCount = orderCount;
    }

    public BigDecimal getSumMoney() {
        return sumMoney;
    }

    public void setSumMoney(BigDecimal sumMoney) {
        this.sumMoney = sumMoney;
    }

    public BigDecimal getRechargeSum() {
        return rechargeSum;
    }

    public void setRechargeSum(BigDecimal rechargeSum) {
        this.rechargeSum = rechargeSum;
    }

    public Long getRechargeCount() {
        return rechargeCount;
    }

    public void setRechargeCount(Long rechargeCount) {
        this.rechargeCount = rechargeCount;
    }

    public String getLogin_datetime() {
        return login_datetime;
    }

    public void setLogin_datetime(String login_datetime) {
        this.login_datetime = login_datetime;
    }

    public String getSign_out_datetime() {
        return sign_out_datetime;
    }

    public void setSign_out_datetime(String sign_out_datetime) {
        this.sign_out_datetime = sign_out_datetime;
    }

    public String getStart_datetime() {
        return start_datetime;
    }

    public void setStart_datetime(String start_datetime) {
        this.start_datetime = start_datetime;
    }

    public String getEnd_datetime() {
        return end_datetime;
    }

    public void setEnd_datetime(String end_datetime) {
        this.end_datetime = end_datetime;
    }

    public List<PayMethodDetail> getDetail() {
        return detail;
    }

    public void setDetail(List<PayMethodDetail> detail) {
        this.detail = detail;
    }

    public List<PayMethodDetail> getRechargeDetail() {
        return rechargeDetail;
    }

    public void setRechargeDetail(List<PayMethodDetail> rechargeDetail) {
        this.rechargeDetail = rechargeDetail;
    }
}
