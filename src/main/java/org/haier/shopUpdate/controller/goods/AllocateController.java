package org.haier.shopUpdate.controller.goods;

import org.apache.commons.beanutils.BeanUtils;
import org.haier.shopUpdate.dao.dojo.QueryGoodsInfoDo;
import org.haier.shopUpdate.dto.AddGoodsDto;
import org.haier.shopUpdate.dto.AddGoodsStockDto;
import org.haier.shopUpdate.entity.goods.AllocationShopEntity;
import org.haier.shopUpdate.entity.goods.goodsRecord.GoodsOperParam;
import org.haier.shopUpdate.enums.AllocationShopStatus;
import org.haier.shopUpdate.enums.StockOriginEnums;
import org.haier.shopUpdate.enums.StockResourceEnums;
import org.haier.shopUpdate.enums.StockTypeEnums;
import org.haier.shopUpdate.params.goods.allocation.AddAllocateShopsParams;
import org.haier.shopUpdate.params.goods.allocation.FinishAllocateParams;
import org.haier.shopUpdate.params.goods.allocation.QueryAllocationParams;
import org.haier.shopUpdate.result.goods.allocation.AllocationShopDetailInfo;
import org.haier.shopUpdate.result.goods.allocation.AllocationShopInfo;
import org.haier.shopUpdate.service.GoodsService;
import org.haier.shopUpdate.service.StockService;
import org.haier.shopUpdate.service.goods.AllocateService;
import org.haier.shopUpdate.util.ShopsResult;
import org.haier.shopUpdate.util.common.HeaderUtil;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/goods/allocate")
public class AllocateController {

    @Resource
    private AllocateService allocateService;

    @Resource
    private StockService stockService;

    @Resource
    private GoodsService goodsService;
    /**
     * 列表查询
     * @param params
     * @return
     * @throws InvocationTargetException
     * @throws IllegalAccessException
     */
    @ResponseBody
    @RequestMapping(value = "/queryList.do")
    public ShopsResult queryList(QueryAllocationParams params) throws InvocationTargetException, IllegalAccessException {
        if (null == params.getPageIndex()) {
            params.setPageIndex(1);
        }
        if (null == params.getPageSize()) {
            params.setPageSize(15);
        }
        params.setPageIndex((params.getPageIndex() - 1) * params.getPageSize());

        return allocateService.queryList(params);
    }

    /**
     * 新增调拨
     *
     * @return
     */
    @ResponseBody
    @RequestMapping("/addAllocate.do")
    public ShopsResult addAllocate(@RequestBody AddAllocateShopsParams params) {
        return allocateService.addAllocate(params);
    }

    /**
     * 调拨 确认调拨
     *
     * @param id
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/confirmAllocate.do")
    public ShopsResult confirmAllocate(Integer id) {
        allocateService.updateAllocationStatus(id, AllocationShopStatus.WAIT_SHIPMENTS);
        return ShopsResult.ok();
    }

    /**
     * 调拨 确认发货
     *
     * @param id
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/confirmShipment.do")
    public ShopsResult confirmShipment(Integer id, HttpServletRequest request) throws InvocationTargetException, IllegalAccessException {
        allocateService.updateAllocationStatus(id, AllocationShopStatus.WAIT_RECEIVED);
        // 查询调拨产品
        AllocationShopInfo allocationShopInfo = allocateService.queryOneAndDetailById(id);
        List<AllocationShopDetailInfo> detailInfoList = allocationShopInfo.getDetailInfoList();

        for (AllocationShopDetailInfo detailInfo : detailInfoList) {
            // 库存 出库
            AddGoodsStockDto dto = new AddGoodsStockDto();
            dto.setShopUnique(allocationShopInfo.getStorehouseInId());
            dto.setGoodsBarcode(detailInfo.getGoodsBarcode());
            dto.setChangeCount(detailInfo.getPurchaseListDetailCount());

            dto.setStockType(StockTypeEnums.STOCK_OUT);
            dto.setStockPrice(detailInfo.getGoodsInPrice());
            dto.setStockOrigin(StockOriginEnums.APP);
            dto.setStockResource(StockResourceEnums.ALLOCATE);

            dto.setStaffId(String.valueOf(allocationShopInfo.getRecipientsUserId()));

            GoodsOperParam goodsOperParam = HeaderUtil.getHeadParam(request);
            stockService.addGoodsStock(dto, goodsOperParam);
        }
        return ShopsResult.ok();
    }

    /**
     * 调拨 确认收货
     *
     * @param params
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/finishAllocate.do")
    public ShopsResult finishAllocate(@RequestBody FinishAllocateParams params, HttpServletRequest request) throws InvocationTargetException, IllegalAccessException {
        // 查询调拨产品
        AllocationShopInfo allocationShopInfo = allocateService.queryOneAndDetailById(params.getId());
        GoodsOperParam goodsOperParam = HeaderUtil.getHeadParam(request);
        if (AllocationShopStatus.FINISH.getStatus().equals(allocationShopInfo.getAllocationStatus())){
            return ShopsResult.fail("调拨单已完成无法收货");
        }
        List<AllocationShopDetailInfo> detailInfoList = allocationShopInfo.getDetailInfoList();
        List<String> goodsBarcodeList = new ArrayList<>();
        for (AllocationShopDetailInfo detailInfo : detailInfoList) {
            goodsBarcodeList.add(detailInfo.getGoodsBarcode());
        }
        // 查询当前店铺 商品
        Map<String, QueryGoodsInfoDo> inStoreGoods =
                goodsService.queryListByShopUniqueAndBarcode(allocationShopInfo.getStorehouseInId(), goodsBarcodeList);
        // 查询 调出方商品信息
        Map<String, QueryGoodsInfoDo> outStoreGoods =
                goodsService.queryListByShopUniqueAndBarcode(allocationShopInfo.getStorehouseOutId(), goodsBarcodeList);
        // 出库
        for (AllocationShopDetailInfo detailInfo : detailInfoList) {
            // 库存 出库
            AddGoodsStockDto dto = new AddGoodsStockDto();
            dto.setShopUnique(allocationShopInfo.getStorehouseOutId());
            dto.setGoodsBarcode(detailInfo.getGoodsBarcode());
            dto.setChangeCount(detailInfo.getPurchaseListDetailCount());

            dto.setStockType(StockTypeEnums.STOCK_OUT);
            dto.setStockPrice(detailInfo.getGoodsInPrice());
            dto.setStockOrigin(StockOriginEnums.APP);
            goodsOperParam.setDeviceSource(StockOriginEnums.APP.getCode());
            dto.setStockResource(StockResourceEnums.ALLOCATE);

            dto.setStaffId(String.valueOf(params.getRecipientsUserId()));



            stockService.addGoodsStock(dto, goodsOperParam);
        }


        // 入库
        for (AllocationShopDetailInfo detailInfo : detailInfoList) {
            QueryGoodsInfoDo inStoreGoodsInfo = inStoreGoods.get(detailInfo.getGoodsBarcode());
            QueryGoodsInfoDo outStoreGoodsInfo = outStoreGoods.get(detailInfo.getGoodsBarcode());
            if (null == inStoreGoodsInfo){
                AddGoodsDto dto = new AddGoodsDto();
                BeanUtils.copyProperties(dto,outStoreGoodsInfo);
                dto.setShopsUnique(allocationShopInfo.getStorehouseInId());
                dto.setGoodsCount(BigDecimal.ZERO);
                dto.setGoodsKindUnique("98001");
                // 商品新增
                goodsService.addGoodsInfo(dto);
            }
            // 库存 添加
            AddGoodsStockDto dto = new AddGoodsStockDto();
            dto.setShopUnique(allocationShopInfo.getStorehouseInId());
            dto.setGoodsBarcode(outStoreGoodsInfo.getGoodsBarcode());
            dto.setStockType(StockTypeEnums.STOCK_IN);
            dto.setStockPrice(outStoreGoodsInfo.getGoodsInPrice());
            dto.setChangeCount(detailInfo.getPurchaseListDetailCount());
            dto.setStockOrigin(StockOriginEnums.APP);
            goodsOperParam.setDeviceSource(StockOriginEnums.APP.getCode());
            dto.setStockResource(StockResourceEnums.ALLOCATE);

            stockService.addGoodsStock(dto, goodsOperParam);
        }
        // 更新调拨状态
        allocateService.recipientsAllocation(params);
        return ShopsResult.ok();
    }

    /**
     * 调拨 取消
     *
     * @param id
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/quashAllocate.do")
    public ShopsResult quashAllocate(Integer id) {
        AllocationShopEntity allocationShopEntity = allocateService.queryOneById(id);
        // 仅待调拨可撤销
        if (AllocationShopStatus.WAIT_SHIPMENTS.getStatus().equals(allocationShopEntity.getAllocationStatus())) {
            return allocateService.updateAllocationStatus(id, AllocationShopStatus.QUASH);
        }
        return ShopsResult.fail("调拨单状态已变更无法撤销");
    }
}
