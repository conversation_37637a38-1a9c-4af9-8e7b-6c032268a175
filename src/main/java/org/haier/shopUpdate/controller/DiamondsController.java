package org.haier.shopUpdate.controller;

import com.alipay.api.AlipayApiException;
import com.alipay.api.internal.util.AlipaySignature;
import lombok.extern.slf4j.Slf4j;
import org.apache.log4j.Logger;
import org.haier.shopUpdate.entity.BeansExchange;
import org.haier.shopUpdate.entity.BeansGetRule;
import org.haier.shopUpdate.entity.PageQuery;
import org.haier.shopUpdate.entity.ShopCard;
import org.haier.shopUpdate.service.DiamondsService;
import org.haier.shopUpdate.util.AlipayPayUtil;
import org.haier.shopUpdate.util.MUtil;
import org.haier.shopUpdate.util.ShopsResult;
import org.haier.shopUpdate.util.XMLUtils;
import org.haier.shopUpdate.util.common.CommonResult;
import org.jdom.JDOMException;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
@Controller
@RequestMapping("/diamonds")
public class DiamondsController {
	@Resource
	private DiamondsService diamondsService;
	private static Logger log=Logger.getLogger(DiamondsController.class);

	/**
	 * 周期时间段内钻石的营收统计及余额显示
	 * @param map
	 * @return
	 */
	@RequestMapping("/statisticsShopsDiamondsByTime.do")
	@ResponseBody
	public ShopsResult statisticsShopsDiamondsByTime(
			String shopUnique,
			String managerUnique,
			String startTime,
			String endTime
			,HttpServletRequest request
			){

		Map<String,Object> map=new HashMap<>();
		map.put("shopUnique", shopUnique);
		map.put("managerUnique", managerUnique);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		if(shopUnique==null&&managerUnique==null){
			return new ShopsResult(2,"请输入至少一个店铺相关信息");
		}
		return diamondsService.statisticsShopsDiamondsByTime(map);
	}



	/**
	 * 获取店铺的银行卡列表信息
	 * @param shopCard
	 * @return
	 */
	@RequestMapping(value = "/getShopCardList.do" , method = RequestMethod.POST)
	@ResponseBody
	public CommonResult getShopCardList(ShopCard shopCard,HttpServletRequest request){

		return diamondsService.getShopCardList(shopCard);
	}


	/**
	 * 删除银行卡信息
	 * @param shopCard
	 * @return
	 */
	@RequestMapping(value ="/removeShopCard.do", method = RequestMethod.POST)
	@ResponseBody
	public CommonResult removeShopCard(ShopCard shopCard,HttpServletRequest request){

		return diamondsService.removeShopCard(shopCard);
	}

	/**
	 * 添加或保存银行卡信息
	 * @param card
	 * @return
	 */
	@RequestMapping("/addNewShopCard.do")
	@ResponseBody
	public CommonResult addNewShopCard(ShopCard card,HttpServletRequest request){

		return diamondsService.addNewShopCard(card);
	}


	@RequestMapping(value ="/getBankListMsg.do", method = RequestMethod.POST)
	@ResponseBody
	public ShopsResult getBankListMsg(HttpServletRequest request){

		return diamondsService.getBankListMsg();
	}

	/**
	 * 获取店铺的当前钻石数量
	 * @return
	 */
	@RequestMapping("/getShopBeansAndRule.do")
	@ResponseBody
	public ShopsResult getShopBeansAndRule(ShopCard card,HttpServletRequest request){

		return diamondsService.getShopBeansAndRule(card);
	}

	/**
	 * 百货豆提现检测
	 * 1、提现次数限制
	 * 2、提现余额限制
	 * 3、提现规则验证（手续费是否足够）
	 * 4、提现
	 * @param beansExchange
	 * @return
	 */
	@RequestMapping("/addNewCashRecord.do")
	@ResponseBody
	public ShopsResult addNewCashRecord(BeansExchange beansExchange,HttpServletRequest request){

		return diamondsService.addNewCashRecord(beansExchange);
	}

	@RequestMapping("/addNewCahsExchangeNew.do")
	@ResponseBody
	public ShopsResult addNewCahsExchangeNew(
//			String shopUnique,
//			Integer beansOldCount,
//			Integer exchangeType,
//			Integer receiveCount,
//			Double payMoney,
//			Integer serviceCharge,
//			Integer cardId,
			HttpServletRequest request
			){

//		Map<String,String[]> params=request.getParameterMap();
		String shopUnique=request.getParameter("shopUnique");
		BeansExchange beansExchange=new BeansExchange();
		beansExchange.setShopUnique(shopUnique);
		beansExchange.setBeansOldCount(Integer.parseInt(request.getParameter("beansOldCount")));
		beansExchange.setExchangeType(Integer.parseInt(request.getParameter("exchangeType")));
		beansExchange.setReceiveCount(Integer.parseInt(request.getParameter("receiverCount")));
		beansExchange.setPayMoney(Double.parseDouble(request.getParameter("payMoney")));
		beansExchange.setServiceCharge(Integer.parseInt(request.getParameter("serviceCharge")));
		beansExchange.setCardId(Integer.parseInt(request.getParameter("cardId")));
		return new ShopsResult(1,"chengg !");
//		return diamondsService.addNewCashRecord(beansExchange);
	}

	/**
	 * 获取银行卡详情
	 * @param card
	 * @return
	 */
	@RequestMapping("/getCardDetail.do")
	@ResponseBody
	public ShopsResult getCardDetail(ShopCard card,HttpServletRequest request){

		return diamondsService.getCardDetail(card);
	}

	/**
	 * 查询店铺的规则设置信息
	 * @param card
	 * @return
	 */
	@RequestMapping("/getDiamondsRule.do")
	@ResponseBody
	public ShopsResult getDiamondsRule(ShopCard card,HttpServletRequest request){

		return diamondsService.getDiamondsRule(card);
	}


	/**
	 * 修改店铺的规则信息
	 * @param beansGetRule
	 * @return
	 */
	@RequestMapping("/addNewGetRule.do")
	@ResponseBody
	public ShopsResult addNewGetRule(BeansGetRule beansGetRule,HttpServletRequest request){

		return diamondsService.addNewGetRule(beansGetRule);
	}

	/**
	 *
	 * @param map
	 * @return
	 */
	@RequestMapping("/beansBuyRecordTotal.do")
	@ResponseBody
	public ShopsResult beansBuyRecordTotal(PageQuery pageQuery,HttpServletRequest request){

		return diamondsService.beansBuyRecordTotal(pageQuery);
	}

	/**
	 * 取现记录列表
	 * @param pageQuery
	 * @return
	 */
	@RequestMapping("/beansBuyRecordList.do")
	@ResponseBody
	public ShopsResult beansBuyRecordList(PageQuery pageQuery,HttpServletRequest request){

		return diamondsService.beansBuyRecordList(pageQuery);
	}

	/**
	 * 抵扣记录信息统计
	 * @param pageQuery
	 * @return
	 */
	@RequestMapping("/dikouTotal.do")
	@ResponseBody
	public ShopsResult dikouTotal(PageQuery pageQuery,HttpServletRequest request){

		return diamondsService.dikouTotal(pageQuery);
	}

	/**
	 * 抵扣信息分页查询
	 * @param pageQuery
	 * @return
	 */
	@RequestMapping("/dikouList.do")
	@ResponseBody
	public ShopsResult dikouList(PageQuery pageQuery,HttpServletRequest request){

		return diamondsService.dikouList(pageQuery);
	}

	/**
	 * 免密赠送商家百货豆统计
	 * @param pageQuery
	 * @return
	 */
	@RequestMapping("/mmGiveBeansTotal.do")
	@ResponseBody
	public ShopsResult mmGiveBeansTotal(PageQuery pageQuery,HttpServletRequest request){

		return diamondsService.mmGiveBeansTotal(pageQuery);
	}


	/**
	 * 免密赠送分页查询
	 * @param pageQuery
	 * @return
	 */
	@RequestMapping("/mmGiveBeansList.do")
	@ResponseBody
	public ShopsResult mmGiveBeansList(PageQuery pageQuery,HttpServletRequest request){

		return diamondsService.mmGiveBeansList(pageQuery);
	}
	/**
	 * 提现记录统计
	 * @param pageQuery
	 * @return
	 */
	@RequestMapping("/takeCashTotal.do")
	@ResponseBody
	public ShopsResult takeCashTotal(PageQuery pageQuery,HttpServletRequest request){

		return diamondsService.takeCashTotal(pageQuery);
	}

	/**
	 * 提现分页查询
	 * @param pageQuery
	 * @return
	 */
	@RequestMapping("/takeCashList.do")
	@ResponseBody
	public ShopsResult takeCashList(PageQuery pageQuery,HttpServletRequest request){

		return diamondsService.takeCashList(pageQuery);
	}

	/**
	 * 赠送信息统计
	 * @param pageQuery
	 * @return
	 */
	@RequestMapping("/giveTotal.do")
	@ResponseBody
	public ShopsResult giveTotal(PageQuery pageQuery,HttpServletRequest request){

		return diamondsService.giveTotal(pageQuery);
	}

	/**
	 * 钻石赠送记录分页查询
	 * @param pageQuery
	 * @return
	 */
	@RequestMapping("/giveList.do")
	@ResponseBody
	public ShopsResult giveList(PageQuery pageQuery,HttpServletRequest request){

		return diamondsService.giveList(pageQuery);
	}
	/**
	 * 钻石购买规则查询
	 * @return
	 */
	@RequestMapping("/beansBuyRule.do")
	@ResponseBody
	public ShopsResult beansBuyRule(HttpServletRequest request){

		return diamondsService.beansBuyRule();
	}

	/**
	 * 充值
	 * @param beansExchange
	 * @return
	 */
	@RequestMapping("/diamondsBuyNew.do")
	@ResponseBody
	public ShopsResult diamondsBuyNew(BeansExchange beansExchange,HttpServletRequest request){

		return diamondsService.diamondsBuyNew(beansExchange);
	}
	//支付宝回调函数
	@RequestMapping("/aliPayCallBack.do")
	public String aliPayCallBack(HttpServletRequest request,HttpServletResponse response){
		try {
			//获取支付宝POST过来反馈信息
			Map<String,String> params = new HashMap<String,String>();
			Map<String, String[]> requestParams = request.getParameterMap();
			for (Iterator<String> iter = requestParams.keySet().iterator(); iter.hasNext();) {
			    String name = (String) iter.next();
			    String[] values = (String[]) requestParams.get(name);
			    String valueStr = "";
			    for (int i = 0; i < values.length; i++) {
			        valueStr = (i == values.length - 1) ? valueStr + values[i]
			                    : valueStr + values[i] + ",";
			  	}
			   System.out.println("支付宝接口回调返回信息："+valueStr);
			    //乱码解决，这段代码在出现乱码时使用。
//				valueStr = new String(valueStr.getBytes("ISO-8859-1"), "utf-8");
				params.put(name, valueStr);
			}
			System.out.println(params);
			boolean flag = AlipaySignature.rsaCheckV1(params, AlipayPayUtil.ALIPAY_PUBLIC_KEY, "utf-8","RSA2");//验签操作
			if(flag){
				//业务逻辑，修改订单的状态

				BeansExchange beansExchange=new BeansExchange();
				beansExchange.setOrderId(params.get("out_trade_no").toString());
				//交易状态
				String trade_status = params.get("trade_status");
		        if(trade_status.equals("TRADE_FINISHED") || trade_status.equals("TRADE_SUCCESS")){
		        	beansExchange.setReceiveState(1);//成功
	            }else if(trade_status.equals("TRADE_CLOSED")){
	            	beansExchange.setReceiveState(2);//失败
	            }
		        //更新订单支付状态
		        flag=diamondsService.modifyBeansExchangeRecord(beansExchange);
		        //将充值的金额添加到商户中
		        if(flag){
		        	//修改成功，查询订单信息，并修改相应店铺钻石数量
		        	Map<String,Object> r=diamondsService.queryExchangeMsgByOrderId(beansExchange);
		        	//验证数量

		        	//更改库存
		        	diamondsService.modifyShopDiamonds(r);
		        }
			}else{
				reponse(response,"failure");
            	System.out.println("通知数据校验失败");
			}
		} catch (AlipayApiException e) {
			reponse(response,"failure");
			System.out.println("AlipayApiException异常错误，验签操作发生错误");
			log.error("AlipayApiException异常错误，验签操作发生错误",e);
		}catch (Exception e) {
			reponse(response,"failure");
			System.out.println("UnsupportedEncodingException异常错误,获取返回的参数发生错误");
			log.error("UnsupportedEncodingException异常错误,获取返回的参数发生错误",e);
		}
		return null;
	}
	/**
	 * 支付回调返回
	 * @param response
	 * @param recode
	 */
	public void reponse(HttpServletResponse response, String message){
	   response.setCharacterEncoding("UTF-8");
       response.setContentType("text/plain");
       PrintWriter out = null;
       try
       {
           out = response.getWriter();
       }
       catch(IOException e)
       {
           log.error("IOException异常错误，获取response.getWriter()失败");
       }
       out.write(message);
       out.close();
	}

	@RequestMapping("/weixinPayCallBack.do")
	public void weixinPayCallBack(HttpServletRequest request,HttpServletResponse response){


		try{
    		request.setCharacterEncoding("UTF-8");//设置请求格式
    		BufferedReader reader = request.getReader();// 获得 http body 内容
            StringBuffer buffer = new StringBuffer();
            String xmlString = null;
            String string;
            while ((string = reader.readLine()) != null) {
                buffer.append(string);
            }
            xmlString = buffer.toString();
            if(log.isInfoEnabled()){
            	log.info(xmlString);
            }
            if(log.isDebugEnabled()){
            	log.debug(xmlString);
            }
            reader.close();
            @SuppressWarnings("rawtypes")
			Map map = XMLUtils.doXMLParse(xmlString);
            String return_code = MUtil.strObject(map.get("return_code"));//返回状态码
            if(log.isInfoEnabled()){
            	log.info(return_code);
            }
            if(log.isDebugEnabled()){
            	log.debug(return_code);
            }
            if(return_code != null && return_code.equals("SUCCESS")){
            	//回调成功，更新数据
        		BeansExchange beansExchange=new BeansExchange();
				beansExchange.setOrderId(MUtil.strObject(map.get("out_trade_no")));
				beansExchange.setReceiveState(1);
				System.out.println(beansExchange);
				//更新订单支付状态
				boolean flag = diamondsService.modifyBeansExchangeRecord(beansExchange);
		        //更新后，将钻石添加
		        if(flag){
		        	//修改成功，查询订单信息，并修改相应店铺钻石数量
		        	Map<String,Object> r=diamondsService.queryExchangeMsgByOrderId(beansExchange);
		        	//验证数量

		        	//更改库存
		        	diamondsService.modifyShopDiamonds(r);
		        }

            	reponseWeiXin(response,"SUCCESS","OK");
            }
    	}catch(JDOMException e){
    		reponseWeiXin(response,"FAIL","返回参数解析过程发生错误");
    		log.error("JDOMException异常错误，返回参数解析过程发生错误",e);
    	}catch(Exception e){
    		reponseWeiXin(response,"FAIL","请求参数时发生错误");
    		log.error("Exception异常错误，请求参数时发生错误",e);
    	}
	}

	/**
	 * 微信回调返回
	 * @param response
	 * @param recode
	 */
	public void reponseWeiXin(HttpServletResponse response, String recode, String message){
		String msg = "<xml><return_code><![CDATA["+ recode +"]]></return_code><return_msg><![CDATA["+ message +"]]></return_msg></xml>";
    	response.setCharacterEncoding("UTF-8");
        response.setContentType("text/plain");
        PrintWriter out = null;
        try
        {
            out = response.getWriter();
        }
        catch(IOException e)
        {
            log.error("微信回调返回异常",e);
        }
        out.write(msg);
        out.close();
	}

	/**
	 *
	 * @param type
	 * @return
	 */
	@RequestMapping("/queryRulrForDiamonds.do")
	@ResponseBody
	public ShopsResult queryRulrForDiamonds(Integer type,
			@RequestParam(value="validType",defaultValue="1")Integer validType,HttpServletRequest request){

		Map<String,Object> map=new HashMap<>();
		map.put("type", type);
		map.put("validType", validType);
		return diamondsService.queryRulrForDiamonds(map);
	}
}
