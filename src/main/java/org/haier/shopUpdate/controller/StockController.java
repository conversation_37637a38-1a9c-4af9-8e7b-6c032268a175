package org.haier.shopUpdate.controller;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

//import org.apache.log4j.Logger;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import org.haier.shopUpdate.entity.goods.goodsRecord.GoodsOperParam;
import org.haier.shopUpdate.log.annotation.RemoteLog;
import org.haier.shopUpdate.log.enums.BusinessType;
import org.haier.shopUpdate.service.StockService;
import org.haier.shopUpdate.util.ShopsResult;
import org.haier.shopUpdate.util.TimeUtil;
import org.haier.shopUpdate.util.common.CommonResult;
//import org.haier.shopUpdate.util.ShopsUtil;
import org.haier.shopUpdate.util.common.HeaderUtil;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/stock")
public class StockController {
	@Resource
	private StockService stockService;
//	private static Logger log=Logger.getLogger(StockController.class);
	/**
	 * 添加商品出入库记录，并修改相应商品库存
	 * @param shopUnique
	 * @param goodsBarcode
	 * @param goodsCount
	 * @param stockType 出入口类型：1、出库；2、入库
	 * @param stockOrigin 
	 * @return
	 */
	@RequestMapping("/newStockRecord.do")
	@ResponseBody
	@RemoteLog(title="出入库记录", businessType = BusinessType.INSERT,isSendDingDingTalk = true)
	public ShopsResult  newStockRecord(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			@RequestParam(value="goodsBarcode",required=true)String goodsBarcode,
			@RequestParam(value="goodsCount",required=true)Double goodsCount,
			@RequestParam(value="stockType",required=true)Integer stockType,
			@RequestParam(value="stockPrice",defaultValue="0.0")Double stockPrice,
			@RequestParam(value="stockTotalPrice",required = false)Double stockTotalPrice,
			@RequestParam(value="stockOrigin",defaultValue="1")Integer stockOrigin,
			@RequestParam(value="goodsProd",required = false, defaultValue="")String goodsProd,
			@RequestParam(value="goodsExp",required = false,defaultValue="")String goodsExp,
			@RequestParam(value="goodBatchMessage", required = false) String goodBatchMessage,
			@RequestParam(value="stockRemarks", required = false) String stockRemarks,
			@RequestParam(value="stockPicture", required = false) String stockPicture,
			@RequestParam(value = "reason",defaultValue = "")String reason

			,HttpServletRequest request
			){
		
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("sourceGoodsBarcode", goodsBarcode);
		map.put("goodsCount", goodsCount);
		map.put("stockType", stockType);
		map.put("stockTime", new Timestamp(new Date().getTime()));
		map.put("stockPrice", stockPrice);
		map.put("stockTotalPrice", stockTotalPrice);
		map.put("stockOrigin", stockOrigin);
		map.put("staffId", request.getHeader("userId"));
		map.put("reason", reason);
		map.put("list_unique", System.currentTimeMillis());
		map.put("stockRemarks", stockRemarks);
		map.put("stockPicture", stockPicture);

		/*if (ObjectUtil.equals(1, stockType)) {
			if (ObjectUtil.isEmpty(goodsProd)) {
				ShopsResult sr = new ShopsResult(2, "生产日期不能为空");
				return sr;
			}
			if (ObjectUtil.isEmpty(goodsExp)) {
				ShopsResult sr = new ShopsResult(2, "到期日不能为空");
				return sr;
			}
		}*/

		if (ObjectUtil.isNotEmpty(goodsProd)) {
			map.put("goodsProd", !goodsProd.contains(" ")?goodsProd + " 00:00:00":goodsProd);
		}
		if (ObjectUtil.isNotEmpty(goodsExp)) {
			map.put("goodsExp", !goodsExp.contains(" ")?goodsExp + " 00:00:00":goodsExp);
		}
		if (ObjectUtil.isNotEmpty(goodsProd) && ObjectUtil.isNotEmpty(goodsExp)) {
			map.put("goodsLife", DateUtil.between(DateUtil.parse(String.valueOf(map.get("goodsProd")), "yyyy-MM-dd HH:mm:ss"),
					DateUtil.parse(String.valueOf(map.get("goodsExp")), "yyyy-MM-dd HH:mm:ss"), DateUnit.DAY));
		}
		map.put("goodBatchMessage", goodBatchMessage);

		GoodsOperParam headerMap = HeaderUtil.getHeadParam(request);

		return stockService.newStockRecord(map , headerMap , stockType);
	}
	/**
	 * 批量出入库
	 */
	@RequestMapping("/addBatchStockRecord.do")
	@ResponseBody
	public ShopsResult  addBatchStockRecord(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			@RequestParam(value="stockType",required=true)Integer stockType,
			@RequestParam(value="supplier_unique",required=true)String supplier_unique,
			Integer staffId,
			String list_unique,
			String goods_stock_list,
			String stock_kind,
			@RequestParam(value="stockOrigin",defaultValue="1")Integer stockOrigin
			){
		
		return stockService.addBatchStockRecord(shopUnique,stockType,supplier_unique,staffId,list_unique,goods_stock_list,stockOrigin,stock_kind);
	}
	
	/**
	 * 出入库记录查询
	 * @param shopUnique 店铺编号
	 * @param pageNum 查询页码（从1开始）
	 * @param pageSize 单页查询数量（默认20）
	 * @param goodsMessage 查询的商品信息（可为名称，条码，商品首字母缩写）
	 * @param startTime 开始查询日期
	 * @param endTime 截至查询日期
	 * @param order 排序依据
	 * @param orderType 生降序
	 * @param stockResource 出入库类型：（1手动入库；2订单出入库）
	 * @param stockType 出入库类型（1：入库，2：出库）
	 * @return
	 */
	@RequestMapping("/queryShopStockRecord.do")
	@ResponseBody
	public ShopsResult queryShopStockRecord(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			@RequestParam(value="pageNum",defaultValue="1")Integer pageNum,
			@RequestParam(value="pageSize",defaultValue="20")Integer pageSize,
			String goodsMessage,
			String startTime,
			String endTime,
			@RequestParam(value="order",defaultValue="4")Integer order,
			@RequestParam(value="orderType",defaultValue="2")Integer orderType,
			Integer stockResource,
			Integer stockType
			,HttpServletRequest request
			){
		
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("pageSize", pageSize);
		map.put("startNum", (pageNum-1)*pageSize);
		map.put("startTime", startTime);
		map.put("endTime",  TimeUtil.fullTimeString(endTime));
		if(goodsMessage!=null && !goodsMessage.equals("")){
			map.put("goodsMessage", "%"+goodsMessage+"%");
		}
		map.put("stockResource", stockResource);
		map.put("stockType", stockType);
		if(order==1){
			map.put("order", "goods_name");
		}else if(order==2){
			map.put("order", "goods_barcode");
		}else if(order==3){
			map.put("order", "goods_count");
		}else if(order==4){
			map.put("order", "stockTime");
		}else if(order==5){
			map.put("order", "list_unique");
		}
		
		if(orderType==1){
			map.put("orderType", "ASC");
		}else if (orderType==2) {
			map.put("orderType", "DESC");
		}
//		System.out.println("出入库历史查询"+map);
		return stockService.queryShopStockRecord(map);
	}
	/**
	 * 出入库记录查询2.0
	 * @return
	 */
	@RequestMapping("/queryShopStockRecordList.do")
	@ResponseBody
	public ShopsResult queryShopStockRecordList(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			@RequestParam(value="pageNum",defaultValue="1")Integer pageNum,
			@RequestParam(value="pageSize",defaultValue="20")Integer pageSize,
			String goodsMessage,
			String startTime,
			String endTime,
			@RequestParam(value="order",defaultValue="4")Integer order,
			@RequestParam(value="orderType",defaultValue="2")Integer orderType,
			Integer stockResource,
			Integer stockType,
			String stock_kind,
			String supplier_name,
			String audit_status,
			HttpServletRequest request
			){
		
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("pageSize", pageSize);
		map.put("startNum", (pageNum-1)*pageSize);
		if(startTime!=null&&!"".equals(startTime)){
			map.put("startTime", startTime);
		}
		if(endTime!=null&&!"".equals(endTime)){
			map.put("endTime", endTime);
		}
		if(goodsMessage!=null && !goodsMessage.equals("")){
			map.put("goodsMessage", "%"+goodsMessage+"%");
		}
		map.put("stockResource", stockResource);
		map.put("stockType", stockType);
		if(order==1){
			map.put("order", "goodsName");
		}else if(order==2){
			map.put("order", "goodsBarcode");
		}else if(order==3){
			map.put("order", "goodsCount");
		}else if(order==4){
			map.put("order", "stock_time");
		}else if(order==5){
			map.put("order", "listUnique");
		}
		if(orderType==1){
			map.put("orderType", "ASC");
		}else if (orderType==2) {
			map.put("orderType", "DESC");
		}
		if(stock_kind!=null&&!"".equals(stock_kind)){
			map.put("stock_kind", stock_kind);
		}
		if(supplier_name!=null&&!"".equals(supplier_name)){
			map.put("supplier_name","%"+ supplier_name+"%");
		}
		if(audit_status!=null&&!"".equals(audit_status)&&!"-1".equals(audit_status)){
			map.put("audit_status",audit_status);
		}
		return stockService.queryShopStockRecordList(map);
	}
	/**
	 * 出入库记录查询详情2.0
	 * @return
	 */
	@RequestMapping("/queryShopStockRecordDetail.do")
	@ResponseBody
	public ShopsResult queryShopStockRecordDetail(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			String list_unique
			){
		
		return stockService.queryShopStockRecordDetail(shopUnique,list_unique);
	}
	/**
	 * 查询商品上次出入库记录
	 * @return
	 */
	@RequestMapping("/queryGoodsStockLast.do")
	@ResponseBody
	public ShopsResult queryGoodsStockLast(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			String goods_barcode,
			String stock_type
			){
		
		return stockService.queryGoodsStockLast(shopUnique,goods_barcode,stock_type);
	}
	/**
	 * 审核
	 * @param list_unique 单号，订单号/出入库单号/退款单号等
	 * @param shop_unique 店铺编号
	 * @param audit_status 审核状态
	 * @param stock_type_code 出入库类型
	 * @return
	 */
	@RequestMapping("/addAuditStock.do")
	@ResponseBody
	public ShopsResult addAuditStock(
			String list_unique,
			String shop_unique,
			String audit_status,
			String stock_type_code
			){
		return stockService.addAuditStock(list_unique,shop_unique,audit_status,stock_type_code);
	}
	
	/**
	 * 修改入库单
	 * @return
	 */
	@RequestMapping("/editIntoStock.do")
	@ResponseBody
	public ShopsResult editIntoStock(
			String shop_unique,
			String detailJson,
			String list_unique,
			String startTime,
			String user_id,
			String supplier_unique,
			String stock_kind
		){
		return stockService.editIntoStock(shop_unique,detailJson,list_unique,startTime,user_id,supplier_unique,stock_kind);
	}
	/**
	 * 查询单个商品出入库记录
	 * @return
	 */
	@RequestMapping("/qeryGoodsStockLog.do")
	@ResponseBody
	public ShopsResult qeryGoodsStockLog(
			String shop_unique,
			String goods_barcode,
			String stock_type
			){
		return stockService.qeryGoodsStockLog(shop_unique,goods_barcode,stock_type);
	}
	
	/**
	 * 出入库原因
	 */
	@RequestMapping(value ="/reason.do", method = RequestMethod.POST)
	@ResponseBody
	public CommonResult  reason(
			HttpServletRequest request
			){
		
		Map<String,Object> map=new HashMap<String, Object>();
	
		map.put("list_unique", System.currentTimeMillis());
		List<String> list = new ArrayList<String>();
		list.add("sys_inbound_reason");
		list.add("sys_outbound_reason");
		map.put("list", list);
		return stockService.reason(map);
	}
	
	@RequestMapping("/querySystemDict.do")
	@ResponseBody
	public CommonResult querySystemDict(String code) {
		return stockService.querySystemDict(code);
	}
}
