package org.haier.shopUpdate.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.haier.shopUpdate.redism.RedisCache;
import org.haier.shopUpdate.service.PurService;
//import org.apache.log4j.Logger;
import org.haier.shopUpdate.service.TestService;
import org.haier.shopUpdate.util.ParamTest;
import org.haier.shopUpdate.util.ShopsResult;
//import org.haier.shopUpdate.util.ShopsUtil;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;


@Controller
@RequestMapping("/test")
public class TestController {
	@Resource
	private  TestService testService;
//	private static Logger log=Logger.getLogger(TestController.class);
	
	@Resource
	private PurService purService;
	
	//参数格式校验
	@RequestMapping("/testParam.do")
	@ResponseBody
	public ShopsResult testParam(@Valid ParamTest param,BindingResult bindingResult) {
		ShopsResult sr = new ShopsResult(1,"成功!");
		String msg = "";
		  if (bindingResult.hasErrors()) {
		    List<FieldError> errors = bindingResult.getFieldErrors();
		    for(FieldError e : errors) {
		    	msg += e.getField() + e.getDefaultMessage() + ";";
		    }
		  }
		  sr.setMsg(msg);
		return sr;
	}
	
	@RequestMapping("/test.do")
	@ResponseBody
	public ShopsResult test(HttpServletRequest request){
		
		return  testService.test();
	}
	
	@RequestMapping("/testRedis.do")
	@ResponseBody
	public ShopsResult testRedis() {
		ShopsResult sr = new ShopsResult();
		RedisCache rc = new RedisCache();
//		rc.putObject("1615278900568o2KiUjnWzmaCtB3igbliG4iyu_tE", "2");
		System.out.println(rc.getSize());
		return sr;
	}
	
	@RequestMapping("/sellListTask.do")
	@ResponseBody
	public ShopsResult sellListTask() {
		purService.sellListTask();
		return new ShopsResult(1, "成功！");
	}
	
	/**
	 * 更新店铺的注册时间
	 * @return
	 */
	@RequestMapping("/modifyShopTime.do")
	@ResponseBody
	public ShopsResult modifyShopTime(HttpServletRequest request){
		
		return testService.modifyShopTime();
	}

	/**
	 * @return
	 */
	@RequestMapping("/modifyGoodsCount.do")
	@ResponseBody
	public ShopsResult modifyGoodsCount(HttpServletRequest request){
		
		return testService.modifyGoodsCount();
	}
	
	@RequestMapping("/modifyShopLong.do")
	@ResponseBody
	public ShopsResult modifyShopLong(HttpServletRequest request){
		
		return testService.modifyShopLong();
	}
	
	@RequestMapping("/goodsKind.do")
	@ResponseBody
	public ShopsResult goodsKind(HttpServletRequest request){
		
		return testService.goodsKind();
	}
	
	@RequestMapping("/addGoods.do")
	@ResponseBody
	public ShopsResult addGoods(
			Integer pageNum,
			@RequestParam(value="pageSize",defaultValue="20")Integer pageSize
			,HttpServletRequest request
			){
		
		Map<String,Object> map=new HashMap<>();
		map.put("startNum", (pageNum-1)*pageSize);
		map.put("pageSize", pageSize);
		return testService.addGoods(map);
	}
	
	@RequestMapping("/querySaleListUnique.do")
	@ResponseBody
	public  ShopsResult querySaleListUnique(HttpServletRequest request){
		
		return testService.querySaleListUnique();
	}
	
	@RequestMapping("aaa.do")
	@ResponseBody
	public ShopsResult aaa(HttpServletRequest request){
		
		return new ShopsResult(1, "");
	}
	
	
	/**
	 * 将goods_new中的商品数量添加到goods中
	 * @param shopUnique
	 * @return
	 */
	@RequestMapping("/modifyShopGoods.do")
	@ResponseBody
	public ShopsResult modifyShopGoods(String shopUnique,HttpServletRequest request){
		
		return testService.modifyShopGoods(shopUnique);
	}
	
	/**
	 * 修改商品图片大小
	 * @param path：指定修改的路径或文件
	 * @param size：修改后图片的宽度
	 * 本方法仅针对.png;.jpeg;.jpg有效
	 * @return
	 */
	@RequestMapping("/modifyGoodsSize.do")
	@ResponseBody
	public ShopsResult modifyGoodsSize(String path,Integer size,Integer type) {
		return testService.modifyGoodsSize(path, size ,type);
	}

}
