package org.haier.shopUpdate.controller;

import lombok.extern.slf4j.Slf4j;
import org.haier.shopUpdate.entity.SpeechCmdEntity;
import org.haier.shopUpdate.entity.SpeechListEntity;
import org.haier.shopUpdate.params.speech.QueryCommonSpeechListParams;
import org.haier.shopUpdate.service.SpeechService;
import org.haier.shopUpdate.params.speech.AddNewSpeechEntityParams;
import org.haier.shopUpdate.util.NewResultObject;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 语音命令
 */
@Slf4j
@Controller
@RequestMapping("/speech")
public class SpeechController {
    @Resource
    private SpeechService speechService;

    /**
     * 新增语音命令记录
     * @param addNewSpeechEntityParams
     * @param request
     * @return
     */
    @RequestMapping("/addNewSpeechEntity.do")
    @ResponseBody
    public NewResultObject<SpeechListEntity> addNewSpeechEntity(@RequestPart AddNewSpeechEntityParams addNewSpeechEntityParams) {
        try {
            return speechService.addNewSpeechEntity(addNewSpeechEntityParams, null);
        } catch (Exception e) {
            log.error("新增语音命令记录失败", e);
            return NewResultObject.fail("系统异常");
        }
    }

    /**
     * 新增语音命令记录（上传文件版本）
     * @param appType
     * @param shopUnique
     * @param speechTextPhone
     * @param file
     * @return
     */
    @RequestMapping(value = "/v1/addNewSpeechEntity.do",method = RequestMethod.POST)
    @ResponseBody
    public NewResultObject<SpeechListEntity> addNewSpeechEntity(@RequestParam(value = "appType", required = true) String appType,
                                                                @RequestParam(value = "shopUnique", required = true) Long shopUnique,
                                                                @RequestParam(value = "staffId", required = true)Long staffId,
                                                                String speechTextPhone,
                                                                HttpServletRequest request) {
        try {
            AddNewSpeechEntityParams addNewSpeechEntityParams = new AddNewSpeechEntityParams();
            addNewSpeechEntityParams.setAppType(appType);
            addNewSpeechEntityParams.setSpeechTextPhone(speechTextPhone);
            addNewSpeechEntityParams.setShopUnique(shopUnique);

            return speechService.addNewSpeechEntity(addNewSpeechEntityParams, request);
        } catch (Exception e) {
            log.error("新增语音命令记录失败", e);
            return NewResultObject.fail("系统异常");
        }

    }

    /**
     * 查询常用语音命令列表
     * @return
     */
    @RequestMapping("/queryCommonSpeechList.do")
    @ResponseBody
    public NewResultObject<List<SpeechCmdEntity>> queryCommonSpeechList(@RequestBody @Validated QueryCommonSpeechListParams queryCommonSpeechListParams) {
        return speechService.queryCommonSpeechList(queryCommonSpeechListParams);
    }
}
