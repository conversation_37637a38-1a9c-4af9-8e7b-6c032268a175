package org.haier.shopUpdate.controller;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import org.haier.shopUpdate.entity.PalmResult;
import org.haier.shopUpdate.service.PurService;
import org.haier.shopUpdate.util.NoteResult;
import org.haier.shopUpdate.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/purList")
public class PurListController {
	@Resource
	private PurService purService;
	
	/**
	 * 修改购物车商品数量  
	 * @param shopUnique
	 * @param goodsBarcode
	 * @param goodsCount
	 * @param supplierUnique
	 * @return
	 */
	@RequestMapping("/modifyCartGoods.do")
	@ResponseBody
	public ShopsResult modifyCartGoods(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			@RequestParam(value="goodsBarcode",required=true)String goodsBarcode,
			@RequestParam(value="detailCount",required=true)Double detailCount,
			@RequestParam(value="supplierUnique",required=true)String supplierUnique){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("goodsBarcode", goodsBarcode);
		map.put("detailCount", detailCount);
		map.put("supplierUnique", supplierUnique);
		return purService.modifyCartGoods(map);
	}
	
	/**
	 * 修改购物车商品数量
	 * @param shopUnique
	 * @param goodsBarcode
	 * @param detailCount
	 * @param supplierUnique
	 * @return
	 */
	@RequestMapping("/modifyCartGoodsNew.do")
	@ResponseBody
	public ShopsResult modifyCartGoodsNew(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			@RequestParam(value="goodsBarcode",required=true)String goodsBarcode,
			@RequestParam(value="detailCount",required=true)Double detailCount,
			@RequestParam(value="supplierUnique",required=true)String supplierUnique,
			String giftMessage,
			String goodsGiftMessage
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("goodsBarcode", goodsBarcode);
		map.put("detailCount", detailCount);
		map.put("supplierUnique", supplierUnique);
		return purService.modifyCartGoodsNew(map,giftMessage,goodsGiftMessage);
	}
	/**
	 * 购物车商品数量查询
	 * @param map
	 * @return
	 */
	@RequestMapping("/cartGoodsCount.do")
	@ResponseBody
	public ShopsResult cartGoodsCount(String shopUnique){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		return purService.cartGoodsCount(map);
	}
	
	/**
	 * 
	 * 直接修改商品购物车数量
	 * @param map
	 * @return
	 */
	@RequestMapping("/directlyModifyCartGoods.do")
	@ResponseBody
	public ShopsResult directlyModifyCartGoods(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			@RequestParam(value="goodsBarcode",required=true)String goodsBarcode,
			@RequestParam(value="detailCount",required=true)Double detailCount,
			@RequestParam(value="supplierUnique",required=true)String supplierUnique){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("goodsBarcode", goodsBarcode);
		map.put("detailCount", detailCount);
		map.put("supplierUnique", supplierUnique);
		return purService.directlyModifyCartGoods(map);
	}
	
	
	@RequestMapping("/directlyModifyCartGoodsNew.do")
	@ResponseBody
	public ShopsResult directlyModifyCartGoodsNew(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			@RequestParam(value="goodsBarcode",required=true)String goodsBarcode,
			@RequestParam(value="detailCount",required=true)Double detailCount,
			@RequestParam(value="supplierUnique",required=true)String supplierUnique,
			String giftMessage,
			String goodsGiftMessage
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("goodsBarcode", goodsBarcode);
		map.put("detailCount", detailCount);
		map.put("supplierUnique", supplierUnique);
		return purService.directlyModifyCartGoodsNew(map,giftMessage,goodsGiftMessage);
	}
	/**
	 * 库存预警--统计剩余库存商品的预期销售时间（库存量/月均销量），根据商户选定的时间，比较并获取相应的产品信息
	 * @param shopUnique 店铺ID
	 * @param days  选定的天数
	 * @param orderType 排序方式 Asc|Desc
	 * @return
	 * 
	 */
	@RequestMapping("/countSurplusGoods.do")
	@ResponseBody
	public ShopsResult countSurplusGoods(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			@RequestParam(value="days",required=true)Long days,
			@RequestParam(value="orderType",required=true)String orderType,
			@RequestParam(value="pageNum",defaultValue="1")Integer pageNum,
			@RequestParam(value="pgeSize",defaultValue="20")Integer pageSize
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("days", days);
		map.put("orderType", orderType);
		map.put("startNum", (pageNum-1)*pageSize);
		map.put("pageSize", pageSize);
		ShopsResult sr= purService.countSurplusGoods(map);
		return sr;
	}
	/**
	 * 商品批量采购
	 * @param shopUnique   店铺编号
	 * @param goodsBarcode 商品条码
	 * @param detailCount   商品数量
	 * @param supplierUnique 供货商编码
	 * @return
	 * 
	 */
	@RequestMapping("/batchModifyCartGoods.do")
	@ResponseBody
	public ShopsResult batchModifyCartGoods(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			@RequestParam(value="goodsBarcode",required=true)String goodsBarcode,
			@RequestParam(value="detailCount",required=true)String detailCount,
			@RequestParam(value="supplierUnique",required=true)String supplierUnique){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("goodsBarcode", goodsBarcode);
		map.put("detailCount", detailCount);
		map.put("supplierUnique", supplierUnique);
		return purService.batchModifyCartGoods(map);
	}
	/**
	 * 滞销商品
	 * @param shopUnique 店铺ID
	 * @param orderType 排序方式 Asc|Desc
	 * @return
	 * 
	 */
	@RequestMapping("/unsalableGoods.do")
	@ResponseBody
	public ShopsResult unsalableGoods(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			@RequestParam(value="orderType",required=true)String orderType,
			@RequestParam(value="pageNum",defaultValue="1")Integer pageNum,
			@RequestParam(value="pageSize",defaultValue="20")Integer pageSize
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("orderType", orderType);
		map.put("startNum", pageSize*(pageNum-1));
		map.put("pageSize", pageSize);
		ShopsResult sr= purService.unsalableGoods(map);
//		System.out.println(sr.getData().toString());
		return sr;
	}
	/**
	 * 各大类库存成本统计
	 * @param shopUnique 店铺ID
	 * @return
	 * 
	 */
	@RequestMapping("/bigKindCountCost.do")
	@ResponseBody
	public ShopsResult bigKindCountCost(
			@RequestParam(value="shopUnique",required=true)Long shopUnique){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		ShopsResult sr= purService.bigKindCountCost(map);
//		System.out.println(sr.getData().toString());
		return sr;
	}
	/**
	 * 各大类库存占比统计
	 * @param shopUnique 店铺ID
	 * @return
	 * 
	 */
	@RequestMapping("/bigKindCountProportion.do")
	@ResponseBody
	public ShopsResult bigKindCountProportion(
			@RequestParam(value="shopUnique",required=true)Long shopUnique){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		ShopsResult sr= purService.bigKindCountProportion(map);
//		System.out.println(sr.getData().toString());
		return sr;
	}
	/**
	 * 各小类库存成本统计
	 * @param shopUnique 店铺ID
	 * @return
	 * 
	 */
	@RequestMapping("/smallKindCountCost.do")
	@ResponseBody
	public ShopsResult smallKindCountCost(
			@RequestParam(value="shopUnique",required=true)Long shopUnique){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		ShopsResult sr= purService.smallKindCountCost(map);
//		System.out.println(sr.getData().toString());
		return sr;
	}
	/**
	 * 各小类库存占比统计
	 * @param shopUnique 店铺ID
	 * @return
	 * 
	 */
	@RequestMapping("/smallKindCountProportion.do")
	@ResponseBody
	public ShopsResult smallKindCountProportion(
			@RequestParam(value="shopUnique",required=true)Long shopUnique){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		ShopsResult sr= purService.smallKindCountProportion(map);
//		System.out.println(sr.getData().toString());
		return sr;
	}
	
	/**
	 * 购物车商品信息查询
	 * @param map
	 * @return
	 */
	@RequestMapping("/purCartGoodsSearch.do")
	@ResponseBody
	public ShopsResult purCartGoodsSearch(String shopUnique){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique",shopUnique);
		return purService.purCartGoodsSearch(map);
	}
	
	/**
	 * 购物车商品信息查询（新版，自动查询供货商是否有满赠设置）；
	 * @param shopUnique
	 * @return
	 */
	@RequestMapping("/purCartGoodsSearchNew.do")
	@ResponseBody
	public ShopsResult purCartGoodsSearchNew(String shopUnique){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique",shopUnique);
		return purService.purCartGoodsSearchNew(map);
	}
	/**
	 * 查询供货商订单
	 * @param maps
	 * @return
	 */
	@RequestMapping("/paySupOrder.do")
	@ResponseBody
	public PalmResult paySupOrder(
			Long shopUnique,
			Long supplierUnique,
			@RequestParam(value="paystatus",defaultValue="1")Integer paystatus,
			Long days,
			@RequestParam(value="pages",defaultValue="1")int pages,
			@RequestParam(value="perpage",defaultValue="20")int perpage){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique",shopUnique);
		if(supplierUnique==0){
			supplierUnique=null;
		}
		map.put("supplierUnique",supplierUnique);
		map.put("paystatus",paystatus);
		map.put("days",days);
		map.put("pages", (pages-1)*perpage);
		map.put("perpage",perpage);
		return purService.paySupOrder(map);
	}
	/**
	 * 批量确认支付接口
	 * @param maps
	 * @return
	 */
	@RequestMapping("/payOrders.do")
	@ResponseBody
	public PalmResult payOrders(String purchaseListUniques){
		
		return purService.payOrders(purchaseListUniques);
	}
	/**
	 * 采购订单查询
	 * @param shop_unique
	 * @param receipt_status
	 * @param pageNum
	 * @param pageSize
	 * @return
	 */
	@RequestMapping("/queryPurList.do")
	@ResponseBody
	public PalmResult queryPurList(
			@RequestParam(value="shop_unique",required=true)String shop_unique,
			Integer receipt_status,
			@RequestParam(value="pageNum",defaultValue="1")Integer pageIndex,
			@RequestParam(value="pageSize",defaultValue="20")Integer pageSize,
			@RequestParam(value="supplierUnique",defaultValue="0")Long supplierUnique,
			String purMessage
			){
		return purService.queryPurList(shop_unique, receipt_status, pageIndex, pageSize,supplierUnique,purMessage);
	}
	
	@RequestMapping("/subCartGoods.do")
	@ResponseBody
	/**
	 * 订单确认后，提交订单
	 * @param shop_unique
	 * @param barcodes
	 * @param remarks
	 * @return
	 */
	public NoteResult subCartGoods(HttpServletRequest request,String shop_unique,String[] goodsBarcodes,String remarks){
		return  purService.subCartGoods(request,shop_unique,goodsBarcodes,remarks);
	}
	
	@RequestMapping("/subCartGoodsNew.do")
	@ResponseBody
	/**
	 * 订单确认后，提交订单（新）
	 * 主要修改赠品信息的价格
	 * @param request
	 * @param shop_unique
	 * @param goodsBarcodes
	 * @param remarks
	 * @return
	 */
	public NoteResult subCartGoodsNew(HttpServletRequest request,String shop_unique,String[] goodsBarcodes,String remarks){
		return  purService.subCartGoodsNew(request,shop_unique,goodsBarcodes,remarks);
	}
	/**
	 * 采购订单详情查询
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryPurListDetail.do")
	@ResponseBody
	public ShopsResult queryPurListDetail(Long shopUnique,Long purListUnique){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("purListUnique", purListUnique);
		return purService.queryPurListDetail(map);
	}
	
	/**
	 * 采购订单详情查询
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryPurListDetailNew.do")
	@ResponseBody
	public ShopsResult queryPurListDetailNew(Long shopUnique,Long purListUnique){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("purListUnique", purListUnique);
		return purService.queryPurListDetailNew(map);
	}
	
	/**
	 * 更新订单处理状态，确认收货
	 * @param shopUnique
	 * @param purListUnique
	 * @param sameType
	 * @return
	 */
	@RequestMapping("/updateReceiptStatus.do")
	@ResponseBody
	public ShopsResult updateReceiptStatus(
			@RequestParam(value="shopUnique",required=true)String shopUnique,
			@RequestParam(value="purListUnique",required=true)String purListUnique,
			@RequestParam(value="sameType",defaultValue="2")Integer sameType,
			@RequestParam(value="receiptStatus",defaultValue="6")Integer receiptStatus){
		return purService.updateReceiptStatus(shopUnique, purListUnique, sameType, receiptStatus);
	}
	
	/**
	 * 采购订单个状态数量查询
	 * @param map
	 * @return
	 */
	@RequestMapping("/shopPurListCount.do")
	@ResponseBody
	public ShopsResult shopPurListCount(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			Long supplierUnique
		){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("supplierUnique", supplierUnique);
		return purService.shopPurListCount(map);
	}
	/**
	 * 删除购物车商品（购物车商品编辑）
	 * @param map
	 * @return
	 */
	@RequestMapping("/purGoodsDelete.do")
	@ResponseBody
	public ShopsResult purGoodsDelete(
			@RequestParam(value="purListUnique",required=true)Long purListUnique,
			@RequestParam(value="goodsBarcodes",required=true)String[] goodsBarcodes){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("purListUnique", purListUnique);
		map.put("array", goodsBarcodes);
		return purService.purGoodsDelete(map);
	}
	/**
	 * 结算
	 * @param shop_unique
	 * @param goodsBarcodes
	 * @param purchase_list_parunique
	 * @return
	 */
	@RequestMapping("/toSubmitCart.do")
	@ResponseBody
	public ShopsResult toSubmitCart(Long purListUnique,String[] goodsBarcodes){
		return purService.toSubmitCart(purListUnique, goodsBarcodes);
	}

	@RequestMapping("/toSubmitCartNew.do")
	@ResponseBody
	public ShopsResult toSubmitCartNew(Long purListUnique,String[] goodsBarcodes){
		return purService.toSubmitCartNew(purListUnique, goodsBarcodes);
	}
	
	/**
	 * 取消订单
	 * @param purListUnique
	 * @param receiptStatus
	 * @return
	 */
	@RequestMapping("/cancelPurList.do")
	@ResponseBody
	public ShopsResult cancelPurList(
			@RequestParam(value="purListUnique",required=true)Long purListUnique,
			@RequestParam(value="receiptStatus",defaultValue="7")Integer  receiptStatus){
		return purService.cancelPurList(purListUnique, receiptStatus);
	}
	/**
	 * 商品购物车详情查询测试
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryBottomGoodsMessage.do")
	@ResponseBody
	public ShopsResult queryBottomGoodsMessage(
			@RequestParam(value="purUnique")Long purUnique
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("purUnique", purUnique);
		return purService.queryBottomGoodsMessage(map);
	}
	
	/**
	 * 更新购物车商品选中状态
	 * @param map 	
	 * @return
	 */
	@RequestMapping("/modifyCartChecked.do")
	@ResponseBody
	public ShopsResult modifyCartChecked(
			Long supplierUnique,
			@RequestParam(value="purListUnique",required=true)Long purListUnique,
			String goodsBarcode,
			@RequestParam(value="checked",defaultValue="2")Integer checked,
			String giftMessage
			){
		Map<String,Object> map=new HashMap<String, Object>();
		if(null!=supplierUnique&&supplierUnique.equals("")){
			supplierUnique=null;
		}
		if(null!=goodsBarcode&&goodsBarcode.equals("")){
			goodsBarcode=null;
		}
		map.put("supplierUnique", supplierUnique);
		map.put("purListUnique", purListUnique);
		map.put("goodsBarcode", goodsBarcode);
		map.put("checked", checked);
		if(null!=goodsBarcode&&supplierUnique!=null){
			System.out.println("aaa");
			return purService.modifyCartChecked(map, giftMessage);
		}else if(goodsBarcode==null&&supplierUnique!=null){
			return purService.modifyCartCheckedSup(map, giftMessage,checked);
		}else{
			return purService.modifyCartCheckedAll(map);
		}
	}
	/**
	 * 测试空字符串
	 * @return
	 */
	@RequestMapping("/test.do")
	@ResponseBody
	public ShopsResult test(
			String aaa
			){
		ShopsResult sr=new ShopsResult();
		System.out.println(aaa==null);
		sr.setData(aaa);
		return sr;
	}
	//热销排行查询
	@RequestMapping("/sellList.do")
	@ResponseBody
	public ShopsResult sellList(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			@RequestParam(value="startDate",required=true)String startDate
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shopUnique);
		map.put("startDate", startDate);
		ShopsResult sr= purService.querySellList(map);
		return sr;
	}
	
}
