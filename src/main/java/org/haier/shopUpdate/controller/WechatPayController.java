package org.haier.shopUpdate.controller;

import java.text.ParseException;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import lombok.extern.slf4j.Slf4j;
import org.haier.shopUpdate.project.ProjectConfig;
import org.haier.shopUpdate.service.LoanMoneyService;
import org.haier.shopUpdate.util.MUtil;
import org.haier.shopUpdate.util.ShopsResult;
import org.haier.shopUpdate.util.XMLUtils;
import org.haier.shopUpdate.util.XmlUtilsYu;
import org.haier.shopUpdate.util.helibao.HelibaoPayConfig;
import org.haier.shopUpdate.util.wechat.WXPay;
import org.haier.shopUpdate.util.wechat.WXPayConfig;
import org.haier.shopUpdate.util.wechat.WechatConfig;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 微信-app支付
 * @author: yuliangliang
 * @date: 2020年6月10日
 */
@Slf4j
@Controller
@RequestMapping("/wechat")
public class WechatPayController {

	@Resource
	private LoanMoneyService loanService;

	 private WXPay wxPay;
	 private WXPayConfig config;

	 public WechatPayController() {
		   try {
		      //初始化微信支付客户端
		      config = new WechatConfig();
		      wxPay = new WXPay(config);
		   } catch (Exception e) {
		      log.error("微信支付初始化失败", e);
		   }
		}

	/**
	 * 生成预支付交易单-微信
	 */
	@RequestMapping("/generateWXOrder.do")
	@ResponseBody
	public ShopsResult generateWXOrder(HttpServletRequest request,int total_fee,String out_trade_no) {
		ShopsResult rs=new ShopsResult();
		try {
			Map<String, String> paramMap = new TreeMap<String,String>();
			paramMap.put("body", "供货商城-app微信支付");
			paramMap.put("out_trade_no", out_trade_no);
			paramMap.put("total_fee", String.valueOf(total_fee));
			paramMap.put("spbill_create_ip", MUtil.getIpAddr2(request));
			//这个链接是专门用于商家端APP供货商城回调的
			paramMap.put("notify_url", ProjectConfig.PROJECT_URL + "/shop/shopping/wxPayCallBack.do");
			paramMap.put("trade_type", "APP");					
			//统一下单
			Map<String, String> resMap =wxPay.unifiedOrder(paramMap);
			rs.setStatus(0);
			rs.setData(resMap);
			rs.setMsg("微信统一下单成功！");
		}catch(Exception e) {
			log.error("微信统一下单失败", e);
			rs.setStatus(1);
			rs.setMsg("微信统一下单失败！");
		}
		return rs;
	}
	/**
	 * 生成预支付交易单-微信还款
	 */
	@RequestMapping("/generateWXOrderLoanMoney.do")
	@ResponseBody
	public ShopsResult generateWXOrderLoanMoney(HttpServletRequest request,int total_fee,String out_trade_no,String shop_unique) {
		ShopsResult rs=new ShopsResult();
		try {
			Map<String, String> paramMap = new TreeMap<String,String>();
			paramMap.put("body", "供货商城-app微信支付");
			paramMap.put("out_trade_no", out_trade_no);
			paramMap.put("total_fee", String.valueOf(total_fee));
			paramMap.put("spbill_create_ip", MUtil.getIpAddr2(request));
			//这个链接是专门用于商家端APP供货商城回调的
			paramMap.put("notify_url", HelibaoPayConfig.LOAN_RETURN_WEIXIN_NOTIFY_URL);
			paramMap.put("trade_type", "APP");
			paramMap.put("device_info", shop_unique == null ? "1536215939565":shop_unique);

			//统一下单
			Map<String, String> resMap =wxPay.unifiedOrder(paramMap);
			rs.setStatus(0);
			rs.setData(resMap);
			rs.setMsg("微信统一下单成功！");
		}catch(Exception e) {
			log.error("微信统一下单失败", e);
			rs.setStatus(1);
			rs.setMsg("微信统一下单失败！");
		}
		return rs;
	}

	@RequestMapping("/testWxPayCallBack.do")
	@ResponseBody
	public synchronized ShopsResult testWxPayCallBack() {
		ShopsResult sr = new ShopsResult(1,"成功");
		loanService.updateReturnMoney("HK20220913112949", null,"31703","1553824518505");
		return sr;
	}
	/**
	 * 赊销提前还款-微信支付回调-单笔订单
	 */
	@RequestMapping(value = "/wxPayCallBackReturMoneyForOrder.do" ,method = RequestMethod.POST)
	@ResponseBody
	public synchronized String wxPayCallBackReturMoneyForOrder(HttpServletRequest request,HttpServletResponse response) throws ParseException{
		String respString = "fail";
		try {
			 String xmlString = XMLUtils.parseRequst(request);
			 System.out.println("还款-微信支付回调===="+xmlString);
			 Map<String,String> map = XmlUtilsYu.toMap(xmlString.getBytes(), "utf-8");
			 if(map.containsKey("sign")){
//                if(!SignUtils.checkParam(map, "yingxiangli123shangjiabaihuoabcd")){
//                    respString = "fail";
//                }else{
                	if ("SUCCESS".equals(map.get("result_code"))) {
                			//支付成功
            			loanService.updateReturnMoney(map.get("out_trade_no"), map.get("out_trade_no"),map.get("total_fee"),map.get("device_info"));
            			respString = "success";
//                	}
                }
            }
		}catch (Exception e) {
		  	log.error("还款-微信支付回调异常", e);
		}
		return respString;
	}
	/**
	 * 赊销提前还款-微信支付回调
	 */
	@RequestMapping(value = "/wxPayCallBackReturMoney.do" ,method = RequestMethod.POST)
	@ResponseBody
	public synchronized String wxPayCallBackReturMoney(HttpServletRequest request,HttpServletResponse response) throws ParseException{
		String respString = "fail";
		try {
			 String xmlString = XMLUtils.parseRequst(request);
			 System.out.println("还款-微信支付回调===="+xmlString);
			 Map<String,String> map = XmlUtilsYu.toMap(xmlString.getBytes(), "utf-8");
			 if(map.containsKey("sign")){
//                if(!SignUtils.checkParam(map, "yingxiangli123shangjiabaihuoabcd")){
//                    respString = "fail";
//                }else{
                	if ("SUCCESS".equals(map.get("result_code"))) {
                			//支付成功
            			loanService.updateReturnMoney(map.get("out_trade_no"), null,map.get("total_fee"),map.get("device_info"));
            			respString = "success";
//                	}
                }
            }
		}catch (Exception e) {
		  	log.error("还款-微信支付回调异常", e);
		}
		return respString;
	}


	/**
	 * 查询订单
	 */
	@RequestMapping("/queryOrderWX.do")
	@ResponseBody
	public ShopsResult queryOrderWX(HttpServletRequest request,String out_trade_no) {
		ShopsResult rs=new ShopsResult();
		try {
			Map<String, String> paramMap = new TreeMap<String,String>();
			paramMap.put("out_trade_no", out_trade_no);
			//统一下单
			Map<String, String> resMap =wxPay.orderQuery(paramMap);
			rs.setStatus(0);
			rs.setData(resMap);
			rs.setMsg("微信-订单查询成功！");
		}catch(Exception e) {
			log.error("微信-订单查询失败！",e);
			rs.setStatus(1);
			rs.setMsg("微信-订单查询失败！");
		}
		return rs;
	}




}
