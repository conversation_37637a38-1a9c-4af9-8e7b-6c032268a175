package org.haier.shopUpdate.controller;

import javax.annotation.Resource;

import org.haier.shopUpdate.service.GoldService;
import org.haier.shopUpdate.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
* @author: 作者:王恩龙
* @version: 2020年7月4日 上午11:33:40
*
*/
@Controller
@RequestMapping("/gold")
public class GoldController {
	@Resource
	private GoldService goldService;
	
	/**
	 * 获取当前店铺的金圈币余额
	 * @param shopUnique
	 * @return
	 */
	@RequestMapping("/queryShopJQB.do")
	@ResponseBody
	public ShopsResult queryShopJQB(String shopUnique) {
		try {
			return goldService.queryShopJQB(shopUnique);
		}catch (Exception e) {
			return new ShopsResult(0,"查询失败");
		}
	}
	
	@RequestMapping("/modifyShopGold.do")
	@ResponseBody
	public ShopsResult modifyShopGold(String shopUnique,String goldGrantId) {
		try {
			return goldService.modifyShopGold(shopUnique, goldGrantId);
		} catch (Exception e) {
			return new ShopsResult(0, "查询失败");
		}
	}
}
