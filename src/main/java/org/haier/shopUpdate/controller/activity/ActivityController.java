package org.haier.shopUpdate.controller.activity;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.haier.shopUpdate.params.ActivityInfoParams;
import org.haier.shopUpdate.params.AddActivityParams;
import org.haier.shopUpdate.params.QueryActivityListParams;
import org.haier.shopUpdate.util.HttpsUtil;
import org.haier.shopUpdate.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Map;

@Controller
@RequestMapping("/activity")
public class ActivityController {
    /**
     * 查询促销列表
     *
     * @param params
     * @return
     */
    @RequestMapping("/queryPromotionList.do")
    @ResponseBody
    public ShopsResult queryPromotionList(QueryActivityListParams params) {
        Map<String, Object> map = JSONObject.parseObject(JSON.toJSONString(params));
        return HttpsUtil.forward("/shop/activity/queryPromotionList", map);
    }

    /**
     * 删除促销活动
     *
     * @param params
     * @return
     */
    @RequestMapping("/deleteActivity.do")
    @ResponseBody
    public ShopsResult deleteActivity(ActivityInfoParams params) {
        Map<String, Object> map = JSONObject.parseObject(JSON.toJSONString(params));
        return HttpsUtil.forward("/shop/activity/deleteActivity", map);
    }

    /**
     * 停用促销活动
     *
     * @param params
     * @return
     */
    @RequestMapping("/updateActivityStatus.do")
    @ResponseBody
    public ShopsResult updateActivityStatus(ActivityInfoParams params) {
        Map<String, Object> map = JSONObject.parseObject(JSON.toJSONString(params));
        return HttpsUtil.forward("/shop/activity/updateActivityStatus", map);
    }

    /**
     * 新增商品折扣
     *
     * @param params
     * @return
     */
    @RequestMapping("/submitSupplierStorageOrder.do")
    @ResponseBody
    public ShopsResult submitSupplierStorageOrder(AddActivityParams params) {
        Map<String, Object> map = JSONObject.parseObject(JSON.toJSONString(params));
        return HttpsUtil.forward("/shop/activity/submitSupplierStorageOrder", map);
    }

    /**
     * 添加商品满赠
     *
     * @param params
     * @return
     */
    @RequestMapping("/submitGoodsGift.do")
    @ResponseBody
    public ShopsResult submitGoodsGift(AddActivityParams params) {
        Map<String, Object> map = JSONObject.parseObject(JSON.toJSONString(params));
        return HttpsUtil.forward("/shop/activity/submitGoodsGift", map);
    }

    /**
     * 添加订单促销
     *
     * @param params
     * @return
     */
    @RequestMapping("/addOrderMarkdown.do")
    @ResponseBody
    public ShopsResult addOrderMarkdown(AddActivityParams params) {
        JSONObject jsonObject = JSONObject.parseObject(params.getDetailJson());
        Map<String, Object> map = JSONObject.parseObject(JSON.toJSONString(params));
        map.putAll(jsonObject);
        map.remove("detailJson");
        return HttpsUtil.forward("/shop/activity/addOrderMarkdown", map);
    }

    /**
     * 添加单品促销
     *
     * @param params
     * @return
     */
    @RequestMapping("/submitSingleGoodsPromotion.do")
    @ResponseBody
    public ShopsResult submitSingleGoodsPromotion(AddActivityParams params) {
        Map<String, Object> map = JSONObject.parseObject(JSON.toJSONString(params));
        return HttpsUtil.forward("/shop/activity/submitSingleGoodsPromotion", map);
    }

    /**
     * 商品折扣查看详情
     *
     * @param params
     * @return
     */
    @RequestMapping("/queryGoodsMarkdownDetail.do")
    @ResponseBody
    public ShopsResult queryGoodsMarkdownDetail(ActivityInfoParams params) {
        Map<String, Object> map = JSONObject.parseObject(JSON.toJSONString(params));
        return HttpsUtil.forward("/shop/activity/queryGoodsMarkdownDetail", map);
    }

    /**
     * 商品满赠查看详情
     *
     * @param params
     * @return
     */
    @RequestMapping("/queryGoodsGiftDetail.do")
    @ResponseBody
    public ShopsResult queryGoodsGiftDetail(ActivityInfoParams params) {
        Map<String, Object> map = JSONObject.parseObject(JSON.toJSONString(params));
        return HttpsUtil.forward("/shop/activity/queryGoodsGiftDetail", map);
    }

    /**
     * 订单促销查看详情
     *
     * @param params
     * @return
     */
    @RequestMapping("/queryOrderMarkdownDetail.do")
    @ResponseBody
    public ShopsResult queryOrderMarkdownDetail(ActivityInfoParams params) {
        Map<String, Object> map = JSONObject.parseObject(JSON.toJSONString(params));
        return HttpsUtil.forward("/shop/activity/queryOrderMarkdownDetail", map);
    }

    /**
     * 单品促销查看详情
     *
     * @param params
     * @return
     */
    @RequestMapping("/querySingleGoodsPromotionDetail.do")
    @ResponseBody
    public ShopsResult querySingleGoodsPromotionDetail(ActivityInfoParams params) {
        Map<String, Object> map = JSONObject.parseObject(JSON.toJSONString(params));
        return HttpsUtil.forward("/shop/activity/querySingleGoodsPromotionDetail", map);
    }

    /**
     * 添加秒杀促销
     *
     * @param params
     * @return
     */
    @RequestMapping("/submitFlashSale.do")
    @ResponseBody
    public ShopsResult submitFlashSale(AddActivityParams params) {
        Map<String, Object> map = JSONObject.parseObject(JSON.toJSONString(params));
        return HttpsUtil.forward("/shop/activity/submitFlashSale", map);
    }

    /**
     * 单品促销查看详情
     *
     * @param params
     * @return
     */
    @RequestMapping("/queryFlashSaleDetail.do")
    @ResponseBody
    public ShopsResult queryFlashSaleDetail(ActivityInfoParams params) {
        Map<String, Object> map = JSONObject.parseObject(JSON.toJSONString(params));
        return HttpsUtil.forward("/shop/activity/queryFlashSaleDetail", map);
    }
}
