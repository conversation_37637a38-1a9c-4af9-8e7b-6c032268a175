package org.haier.shopUpdate.controller;

import java.beans.IntrospectionException;
import java.lang.reflect.InvocationTargetException;
import java.text.ParseException;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Random;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.httpclient.HttpStatus;
import org.apache.log4j.Logger;
import org.haier.shopUpdate.project.ProjectConfig;
import org.haier.shopUpdate.redism.RedisCache;
import org.haier.shopUpdate.service.LoanMoneyService;
import org.haier.shopUpdate.service.WechatService;
import org.haier.shopUpdate.util.AlipayConfig;
import org.haier.shopUpdate.util.AlipayPayUtil;
import org.haier.shopUpdate.util.helibao.HelibaoPayConfig;
import org.haier.shopUpdate.wechat.AuthUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.AlipayTradeAppPayModel;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.request.AlipaySystemOauthTokenRequest;
import com.alipay.api.request.AlipayTradeAppPayRequest;
import com.alipay.api.request.AlipayTradeQueryRequest;
import com.alipay.api.response.AlipaySystemOauthTokenResponse;
import com.alipay.api.response.AlipayTradeAppPayResponse;
import com.alipay.api.response.AlipayTradeQueryResponse;
import com.haier.constant.Constant;
import com.haier.util.Disguiser;
import com.haier.util.HttpClientService;
import com.haier.util.MyBeanUtils;
import com.haier.vo.AppPayPublicCreateOrderVo;
import com.haier.vo.AppPayPublicOrderResponseVo;

/**
 * 支付宝-app支付
 * @author: yuliangliang
 * @date: 2020年6月10日
 */
@Slf4j
@Controller
@RequestMapping("/aliPay")
public class AliPayController {
    private static final Logger logger = Logger.getLogger(AliPayController.class);
	private AlipayClient alipayClient;

	@Autowired
	private RedisCache redis;

	@Resource
	private LoanMoneyService loanService;

	@Resource
	private WechatService wechatService;

	@Resource
	private ProjectConfig projectConfig;
	
	public AliPayController() {
		 try {
		      ////实例化支付宝客户端
			 alipayClient = new DefaultAlipayClient("https://openapi.alipay.com/gateway.do",AlipayPayUtil.APP_ID,AlipayPayUtil.APP_PRIVATE_KEY,"json","UTF-8",AlipayPayUtil.ALIPAY_PUBLIC_KEY,"RSA2");
		   } catch (Exception e) {
		      log.error("支付宝支付初始化失败",e);
		   }
	}

	@RequestMapping("/getUseridCallBack.do")
	public String getUseridCallBack(HttpServletRequest req,HttpServletResponse rsp) throws AlipayApiException, IllegalAccessException, InvocationTargetException, IntrospectionException {
		Enumeration<String> n = req.getParameterNames();
		String auth_code = "2088112075078351";
		String total_fee = null;
		String orderNo = null;
		String type = null;
		String shopUnique = null;
		String goodsMsg = null;
        String mqttId = null;
        String mchId = HelibaoPayConfig.PURMCHID;
        String mchKey = HelibaoPayConfig.PUBMCHKEY;
		if(null != n) {
			while (n.hasMoreElements()) {
				String string = (String) n.nextElement();
				if(string.equals("auth_code")) {
					auth_code = req.getParameter(string);
				}
				if(string.equals("state")) {
					String state = req.getParameter("state");
					 String[] stateArr = state.split(";");
		        	 if(stateArr.length <= 1) {
		        		 //转换为json形式
		        		 JSONObject jo = JSONObject.parseObject(state);
		        		 orderNo = jo.getString("orderNo");
		        		 total_fee = jo.getString("payMoney");
		        		 type = jo.getString("orderType");
		        		 shopUnique = jo.getString("shopUnique");
		        		 goodsMsg = jo.containsKey("goodsMsg") ? jo.getString("goodsMsg") : "";
		        		 mqttId = jo.containsKey("mqttId") ? jo.getString("") : "";
		        	 }else {
		        		 orderNo = stateArr.length >= 1 ? stateArr[0] : null;
		        		 total_fee = stateArr.length >= 2 ? stateArr[1] : null;
		        		 type = stateArr.length >= 3 ? stateArr[2] : null;
		        		 shopUnique = stateArr.length >= 4 ? stateArr[3] : null;
		        	 }
				}
			}
		}

		AlipayClient alipayClient = new DefaultAlipayClient("https://openapi.alipay.com/gateway.do",AlipayPayUtil.APP_ID,AlipayPayUtil.APP_PRIVATE_KEY,"json","GBK",AlipayPayUtil.ALIPAY_PUBLIC_KEY,"RSA2");
		AlipaySystemOauthTokenRequest request = new AlipaySystemOauthTokenRequest();
		request.setGrantType("authorization_code");
		request.setCode(auth_code);

		//根据店铺信息获取店铺名称
     	Map<String,Object> payMap = wechatService.queryShopMsg(shopUnique);
     	req.setAttribute("shopName", payMap.get("shopName"));

		AlipaySystemOauthTokenResponse response = alipayClient.execute(request);
		if(response.isSuccess()){
			String userId = response.getUserId();
			System.out.println("用户ID"+userId);
			//根据不同的客户支付类型，回调不同的路径
			String notifyUrl = null;
			if(null == type || type.equals("") || type.equals("1")) {
				notifyUrl = AuthUtil.AUTHPAYSUCCESSNOTIFYURL;
			}else if(type.equals("2")) {
				notifyUrl = AuthUtil.QRCODEPAYCALLBACK;
	     		mchId = payMap.get("mch_id").toString();
	     		mchKey = payMap.get("mch_key").toString();
			}

			//发起支付请求
			AppPayPublicCreateOrderVo orderVo = new AppPayPublicCreateOrderVo();
			//随机产生新的订单号，防止更换支付方式
			orderVo.setP1_bizType("AppPayPublic");
			orderVo.setP2_orderId(orderNo);
			orderVo.setP3_customerNumber(mchId);
			orderVo.setP4_payType("PUBLIC");
			orderVo.setP5_appid("1");
			orderVo.setP6_deviceInfo("WEB");
			orderVo.setP7_isRaw("1");
			orderVo.setP8_openid(userId);
			orderVo.setP9_orderAmount(total_fee);
			orderVo.setP10_currency("CNY");
			orderVo.setP11_appType("ALIPAY");
			orderVo.setP12_notifyUrl(notifyUrl);
			orderVo.setP14_orderIp("***************");
			orderVo.setP15_goodsName("金圈供货商城");
			orderVo.setP18_desc(shopUnique);

			redis.putObject(AuthUtil.QRCODEPAY + orderNo, shopUnique, 600);

			Map<String, String> map = MyBeanUtils.convertBean(orderVo, new LinkedHashMap());
			 String oriMessage = MyBeanUtils.getSigned(map, new String[]{"P19_subscribeAppId", "P20_subMerchantId" , "P21_goodsTag",
	                 "P22_guid", "timeExpire", "industryRefluxInfo", "foodOrderType", "termInfo"},mchKey);
	         logger.info("签名原文串：" + oriMessage);
	         String sign = Disguiser.disguiseMD5(oriMessage.trim());
	         logger.info("签名串：" + sign);
	         map.put("sign", sign);
	         logger.info("发送参数：" + map);
	         Map<String, Object> resultMap = HttpClientService.getHttpResp(map, Constant.REQUEST_URL);
	         logger.info("响应结果：" + resultMap);
	         if ((Integer) resultMap.get("statusCode") == HttpStatus.SC_OK) {
	        	  String resultMsg = (String) resultMap.get("response");
	              AppPayPublicOrderResponseVo orderResponseVo = JSONObject.parseObject(resultMsg, AppPayPublicOrderResponseVo.class);
	              //建议是加签/验签的固定参数,具体按照文档要求加签.因为新增参数需要历史兼容是排除签名的
	              String[] excludes = {"rt3_retMsg", "rt13_channelRetCode", "subMerchantNo"};
	              String assemblyRespOriSign = MyBeanUtils.getSigned(orderResponseVo, excludes,mchKey);
	              logger.info("组装返回结果签名串：" + assemblyRespOriSign);
	              String responseSign = orderResponseVo.getSign();
	              logger.info("响应签名：" + responseSign);
	              String checkSign = Disguiser.disguiseMD5(assemblyRespOriSign.trim());
	              if (checkSign.equals(responseSign)) {
	                  if ("0000".equals(orderResponseVo.getRt2_retCode())) {
	                      JSONObject jsonObject = JSONObject.parseObject(resultMsg);
	                      req.setAttribute("payMsg", jsonObject.get("rt10_payInfo"));
	                      req.setAttribute("orderNo", jsonObject.getString("rt5_orderId"));
	                      req.setAttribute("orderAmount", total_fee);
	                      req.setAttribute("shopUnique", shopUnique);
	                      req.setAttribute("goodsMsg", goodsMsg);
	                      req.setAttribute("mqttId", mqttId);
	                  } else {
	                  }
	              } else {
	              }
	         }
		} else {
			System.out.println("调用失败");
		}
		req.setAttribute("payStatus", 1);
		if(null == type || type.equals("") || type.equals("1")) {
			return "/WEB-INF/yunPay/payMsg.jsp";
		}else if(type.equals("2")) {
			return "/WEB-INF/pay/payForShop.jsp";
		}else if(type.equals("3")) {

		}

		return null;
	}

	/**
	 * app支付接口
	 */
	@RequestMapping(value = "/aliAppPay.do" )
	@ResponseBody
	public String aliAppPay(String total_amount,String out_trade_no){
		//实例化具体API对应的request类,类名称和接口名称对应,当前调用接口名称：alipay.trade.app.pay
		AlipayTradeAppPayRequest request = new AlipayTradeAppPayRequest();
		//SDK已经封装掉了公共参数，这里只需要传入业务参数。以下方法为sdk的model入参方式(model和biz_content同时存在的情况下取biz_content)。
		AlipayTradeAppPayModel model = new AlipayTradeAppPayModel();
		model.setBody("body");
		model.setSubject("alipay");
		model.setOutTradeNo(out_trade_no);
		model.setTimeoutExpress("30m");
		model.setTotalAmount(total_amount);
		model.setProductCode("QUICK_MSECURITY_PAY");
		request.setBizModel(model);
		request.setNotifyUrl(projectConfig.getProjectUrl() + "/shop/shopping/aliPayCallBack.do");
		try {
		        //这里和普通的接口调用不同，使用的是sdkExecute
		        AlipayTradeAppPayResponse response = alipayClient.sdkExecute(request);
		        System.out.println(response.getBody());//就是orderString 可以直接给客户端请求，无需再做处理。
		        return response.getBody();
		    } catch (AlipayApiException e) {
		        log.error("调用失败",e);
		        return "fail";
		}
	}

	@RequestMapping("aliAppPayLoanMoneyForOrder.do")
	@ResponseBody
	public String aliAppPayLoanMoneyForOrder(String total_amount,String out_trade_no,String shop_unique) {
		//实例化具体API对应的request类,类名称和接口名称对应,当前调用接口名称：alipay.trade.app.pay
		AlipayTradeAppPayRequest request = new AlipayTradeAppPayRequest();
		//SDK已经封装掉了公共参数，这里只需要传入业务参数。以下方法为sdk的model入参方式(model和biz_content同时存在的情况下取biz_content)。
		AlipayTradeAppPayModel model = new AlipayTradeAppPayModel();
		model.setBody("body");
		model.setSubject("alipay");
		model.setOutTradeNo(out_trade_no);
		model.setTimeoutExpress("30m");
		model.setTotalAmount(total_amount);
		model.setProductCode("QUICK_MSECURITY_PAY");
		request.setBizModel(model);
		request.setNotifyUrl(HelibaoPayConfig.LOAN_RETURN_ALIPAY_NOTIFY_URL_FORORDER);
		try {
			//这里和普通的接口调用不同，使用的是sdkExecute
			AlipayTradeAppPayResponse response = alipayClient.sdkExecute(request);
			System.out.println(response.getBody());//就是orderString 可以直接给客户端请求，无需再做处理。

			return response.getBody();
		} catch (AlipayApiException e) {
			log.error("调用失败",e);
			return "fail";
		}
	}

	/**
	 * 赊销还款app支付接口
	 */
	@RequestMapping(value = "/aliAppPayLoanMoney.do" )
	@ResponseBody
	public String aliAppPayLoanMoney(String total_amount,String out_trade_no,String shop_unique){
		//实例化具体API对应的request类,类名称和接口名称对应,当前调用接口名称：alipay.trade.app.pay
		AlipayTradeAppPayRequest request = new AlipayTradeAppPayRequest();
		//SDK已经封装掉了公共参数，这里只需要传入业务参数。以下方法为sdk的model入参方式(model和biz_content同时存在的情况下取biz_content)。
		AlipayTradeAppPayModel model = new AlipayTradeAppPayModel();
		model.setBody("body");
		model.setSubject("alipay");
		model.setOutTradeNo(out_trade_no);
		model.setTimeoutExpress("30m");
		model.setTotalAmount(total_amount);
		model.setProductCode("QUICK_MSECURITY_PAY");
		request.setBizModel(model);
		request.setNotifyUrl(HelibaoPayConfig.LOAN_RETURN_ALIPAY_NOTIFY_URL);
		try {
			//这里和普通的接口调用不同，使用的是sdkExecute
			AlipayTradeAppPayResponse response = alipayClient.sdkExecute(request);
			System.out.println(response.getBody());//就是orderString 可以直接给客户端请求，无需再做处理。

			return response.getBody();
		} catch (AlipayApiException e) {
			log.error("调用失败",e);
			return "fail";
		}
	}

	@RequestMapping(value = "/aliPayCallBackReturnMoneyForOrder.do" ,method = RequestMethod.POST)
	@ResponseBody
	public synchronized String aliPayCallBackReturnMoneyForOrder(HttpServletRequest request,HttpServletResponse response) throws ParseException, Exception{
		Map<String,String> params = new HashMap<String,String>();
		Map<String,String[]> requestParams = request.getParameterMap();
		System.out.println("还款-支付宝支付回调===="+requestParams);
		for (Iterator<String> iter = requestParams.keySet().iterator(); iter.hasNext();) {
			String name = (String) iter.next();
			String[] values = (String[]) requestParams.get(name);
			String valueStr = "";
			for (int i = 0; i < values.length; i++) {
				valueStr = (i == values.length - 1) ? valueStr + values[i]
						: valueStr + values[i] + ",";
			}
			//乱码解决，这段代码在出现乱码时使用
//			valueStr = new String(valueStr.getBytes("ISO-8859-1"), "utf-8");
			System.out.println("返回参数名："+name+",值："+valueStr);
			params.put(name, valueStr);
		}
		boolean signVerified = AlipaySignature.rsaCheckV1(params, AlipayPayUtil.ALIPAY_PUBLIC_KEY, "UTF-8", AlipayConfig.sign_type); //调用SDK验证签名
		System.out.println("签名结果："+signVerified);

		/* 实际验证过程建议商户务必添加以下校验：
		1、需要验证该通知数据中的out_trade_no是否为商户系统中创建的订单号，
		2、判断total_amount是否确实为该订单的实际金额（即商户订单创建时的金额），
		3、校验通知中的seller_id（或者seller_email) 是否为out_trade_no这笔单据的对应的操作方（有的时候，一个商户可能有多个seller_id/seller_email）
		4、验证app_id是否为该商户本身。
		*/
		if(signVerified) {//验证成功
			//商户订单号
			String out_trade_no = new String(request.getParameter("out_trade_no").getBytes("ISO-8859-1"),"UTF-8");

			//支付宝交易号
			//String trade_no = new String(request.getParameter("trade_no").getBytes("ISO-8859-1"),"UTF-8");

			//交易状态
			String trade_status = new String(request.getParameter("trade_status").getBytes("ISO-8859-1"),"UTF-8");
			if(trade_status.equals("TRADE_FINISHED")){
				//判断该笔订单是否在商户网站中已经做过处理
				//如果没有做过处理，根据订单号（out_trade_no）在商户网站的订单系统中查到该笔订单的详细，并执行商户的业务程序
				//如果有做过处理，不执行商户的业务程序
				loanService.updateReturnMoney(out_trade_no, out_trade_no,params.get("receipt_amount"),params.get(""));

				//注意：
				//退款日期超过可退款期限后（如三个月可退款），支付宝系统发送该交易状态通知
			}else if (trade_status.equals("TRADE_SUCCESS")){
				//判断该笔订单是否在商户网站中已经做过处理
				//如果没有做过处理，根据订单号（out_trade_no）在商户网站的订单系统中查到该笔订单的详细，并执行商户的业务程序
				//如果有做过处理，不执行商户的业务程序
				loanService.updateReturnMoney(out_trade_no,out_trade_no ,params.get("receipt_amount"),params.get(""));
				//注意：
				//付款完成后，支付宝系统发送该交易状态通知
			}

			System.out.println("success");
			return "success";
		}else {
			System.out.println("fail");
			return "fail";
		}
	}

	/**
	 * 还款-支付宝支付回调
	 * @throws Exception
	 */
	@RequestMapping(value = "/aliPayCallBackReturnMoney.do" ,method = RequestMethod.POST)
	@ResponseBody
	public synchronized String aliPayCallBackReturnMoney(HttpServletRequest request,HttpServletResponse response) throws ParseException, Exception{
		Map<String,String> params = new HashMap<String,String>();
		Map<String,String[]> requestParams = request.getParameterMap();
		System.out.println("还款-支付宝支付回调===="+requestParams);
		for (Iterator<String> iter = requestParams.keySet().iterator(); iter.hasNext();) {
			String name = (String) iter.next();
			String[] values = (String[]) requestParams.get(name);
			String valueStr = "";
			for (int i = 0; i < values.length; i++) {
				valueStr = (i == values.length - 1) ? valueStr + values[i]
						: valueStr + values[i] + ",";
			}
			//乱码解决，这段代码在出现乱码时使用
//			valueStr = new String(valueStr.getBytes("ISO-8859-1"), "utf-8");
			System.out.println("返回参数名："+name+",值："+valueStr);
			params.put(name, valueStr);
		}
		boolean signVerified = AlipaySignature.rsaCheckV1(params, AlipayPayUtil.ALIPAY_PUBLIC_KEY, "UTF-8", AlipayConfig.sign_type); //调用SDK验证签名
		System.out.println("签名结果："+signVerified);
		//——请在这里编写您的程序（以下代码仅作参考）——

		/* 实际验证过程建议商户务必添加以下校验：
		1、需要验证该通知数据中的out_trade_no是否为商户系统中创建的订单号，
		2、判断total_amount是否确实为该订单的实际金额（即商户订单创建时的金额），
		3、校验通知中的seller_id（或者seller_email) 是否为out_trade_no这笔单据的对应的操作方（有的时候，一个商户可能有多个seller_id/seller_email）
		4、验证app_id是否为该商户本身。
		*/
		if(signVerified) {//验证成功
			//商户订单号
			String out_trade_no = new String(request.getParameter("out_trade_no").getBytes("ISO-8859-1"),"UTF-8");

			//支付宝交易号
			//String trade_no = new String(request.getParameter("trade_no").getBytes("ISO-8859-1"),"UTF-8");

			//交易状态
			String trade_status = new String(request.getParameter("trade_status").getBytes("ISO-8859-1"),"UTF-8");

			if(trade_status.equals("TRADE_FINISHED")){
				//判断该笔订单是否在商户网站中已经做过处理
				//如果没有做过处理，根据订单号（out_trade_no）在商户网站的订单系统中查到该笔订单的详细，并执行商户的业务程序
				//如果有做过处理，不执行商户的业务程序
				loanService.updateReturnMoney(out_trade_no , null ,params.get("receipt_amount"),params.get(""));

				//注意：
				//退款日期超过可退款期限后（如三个月可退款），支付宝系统发送该交易状态通知
			}else if (trade_status.equals("TRADE_SUCCESS")){
				//判断该笔订单是否在商户网站中已经做过处理
				//如果没有做过处理，根据订单号（out_trade_no）在商户网站的订单系统中查到该笔订单的详细，并执行商户的业务程序
				//如果有做过处理，不执行商户的业务程序
				loanService.updateReturnMoney(out_trade_no , null,params.get("receipt_amount"),params.get(""));
				//注意：
				//付款完成后，支付宝系统发送该交易状态通知
			}

			System.out.println("success");
			return "success";
		}else {//验证失败
			System.out.println("fail");
			return "fail";
			//调试用，写文本函数记录程序运行情况是否正常
			//String sWord = AlipaySignature.getSignCheckContentV1(params);
			//AlipayConfig.logResult(sWord);
		}
	}



	/**
	 * 订单查询接口
	 * @throws AlipayApiException
	 */
	@RequestMapping(value = "/alipayQueryOrdrer.do" )
	@ResponseBody
	public String alipayQueryOrdrer(String out_trade_no) throws AlipayApiException{
		AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();
		request.setBizContent("{" +
		"\"out_trade_no\":\""+out_trade_no+"\""+
		"}");
		AlipayTradeQueryResponse response = alipayClient.execute(request);
		if(response.isSuccess()){
		System.out.println("调用成功");
		} else {
		System.out.println("调用失败");
		}
		return response.getBody();
	}
}
