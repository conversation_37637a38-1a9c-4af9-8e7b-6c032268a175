package org.haier.shopUpdate.controller;

import java.sql.Timestamp;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.log4j.Logger;
import org.haier.shopUpdate.enums.SaleListPayMethod;
import org.haier.shopUpdate.params.QuerySaleListByPayMethodParams;
import org.haier.shopUpdate.params.QuerySaleListPayMethodBySaleListUniqueParams;
import org.haier.shopUpdate.params.QueryStatisticsByShopParams;
import org.haier.shopUpdate.result.common.NameAndValue;
import org.haier.shopUpdate.service.StatisticsService;
import org.haier.shopUpdate.util.ShopsResult;
import org.haier.shopUpdate.util.ShopsUtil;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * 支付统计
 */
@Controller
@RequestMapping("/statistics")
public class StatisticsController {
	@Resource
	private StatisticsService statisticsService;
	private static Logger log=Logger.getLogger(StatisticsController.class);


	/**
	 * 查询订单或退款订单的支付详情
	 * @param params
	 * @return
	 */
	@RequestMapping("/querySaleListPayMethodBySaleListUnique.do")
	@ResponseBody
	public ShopsResult querySaleListPayMethodBySaleListUnique(@RequestBody QuerySaleListPayMethodBySaleListUniqueParams params) {
		return statisticsService.querySaleListPayMethodBySaleListUnique(params);
	}

	@RequestMapping("/getPayMethodList.do")
	@ResponseBody
	public ShopsResult getPayMethodList() {
		List<NameAndValue> list = SaleListPayMethod.getList();
		return ShopsResult.ok(list);
	}

	/**
	 * 查询各支付方式下的订单列表
	 * @param params
	 * @return
	 */
	@RequestMapping("/querySaleListByPaymethod.do")
	@ResponseBody
	public ShopsResult querySaleListByPaymethod(@RequestBody QuerySaleListByPayMethodParams params) {

		return statisticsService.querySaleListByPaymethod(params);
	}
	/**
	 * 统计信息
	 * @return
	 */
	@RequestMapping(value = "/queryStatisticsByShop.do",method = RequestMethod.POST)
	@ResponseBody
	public ShopsResult queryStatisticsByShop(@RequestBody QueryStatisticsByShopParams params) {
		System.out.println(params);
		return statisticsService.queryStatisticsByShop(params);
	}

	/**
	 * APP主界面信息统计
	 * @return
	 */
	@RequestMapping("/statisticsShopsMessage.do")
	@ResponseBody
	public ShopsResult statisticsShopsMessage(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			@RequestParam(value="datetime",required=true)String datetime
			,HttpServletRequest request
			){
		ShopsUtil.recordLog(log, request);
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		String startDateTime=datetime+" 00:00:00";
		String endDateTime=datetime+" 23:59:59";
		String pattern="yyyy-MM-dd hh:mm:ss";
		Timestamp startTime=ShopsUtil.getTimestamp(startDateTime,pattern,0);
		if(null==startTime){
			ShopsResult sr=new ShopsResult();
			sr.setStatus(1);
			sr.setMsg("时间格式错误！");
			return sr;
		}
		Calendar startCa=Calendar.getInstance();
		startCa.setTimeInMillis(startTime.getTime());//将当前的开始时间转换为格林威治时间
		Timestamp endTime=ShopsUtil.getTimestamp(endDateTime, pattern,0);
		Timestamp lastDayStartTime=ShopsUtil.getTimestamp(startDateTime, pattern, 1);
		//当日营业额、订单量、利润用时间
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		//当日时间--当日进货额用
		map.put("date", ShopsUtil.getTimestamp(datetime, "yyyy-MM-dd",0));
		map.put("lastDate", ShopsUtil.getNewDays(datetime, "yyyy-MM-dd", 1));
		map.put("lastDayStartTime", lastDayStartTime);
		//本周时间--本周流水用
		startCa.set(Calendar.DAY_OF_WEEK, 2);
		map.put("weekStartTime", new Timestamp(startCa.getTimeInMillis()));//本周开始时间
		startCa.add(Calendar.WEEK_OF_YEAR, -1);
		map.put("lastWeekStartTime", new Timestamp(startCa.getTimeInMillis()));
		
		//本月时间--本月流水用
		startCa.setTimeInMillis(startTime.getTime());//将当前的开始时间转换为格林威治时间
		startCa.set(Calendar.DAY_OF_MONTH, 1);
		map.put("monthStartTime", new Timestamp(startCa.getTimeInMillis()));
		startCa.add(Calendar.MONTH, -1);
		map.put("lastMonthStartTime", new Timestamp(startCa.getTimeInMillis()));
//		System.out.println(map);
		return statisticsService.statisticsShopsMessage(map);
	}

	/**
	 * 流水统计设计图
	 * @param shopUnique
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	@RequestMapping("/turnOverBytime.do")
	@ResponseBody
	public ShopsResult turnOverBytime(
			@RequestParam(value="shopUnique",required=true)String shopUnique,
			@RequestParam(value="startTime",required=true)String startTime,
			@RequestParam(value="endTime",required=true)String endTime,
			@RequestParam(value="showType",defaultValue="1")Integer showType
			,HttpServletRequest request
			){
		ShopsUtil.recordLog(log, request);
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("startTime", ShopsUtil.getTimestamp(startTime+" 00:00:01", "yyyy-MM-dd HH:mm:ss", 0));
		map.put("endTime", ShopsUtil.getTimestamp(endTime+" 23:59:59", "yyyy-MM-dd HH:mm:ss", 0));
		return statisticsService.turnOverBytime(map,showType);
	}
	
	
	/**
	 * 新版：主界面信息统计
	 * @param shopUnique
	 * @return
	 */
	@RequestMapping("/queryStatisticsMessageInMain.do")
	@ResponseBody
	public ShopsResult queryStatisticsMessageInMain(
			@RequestParam(value="shopUnique")String shopUnique,
			@RequestParam(value="datetime")String datetime
			,HttpServletRequest request
			){
		ShopsUtil.recordLog(log, request);
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("datetime", datetime);
		map.put("startTime", datetime);
		map.put("endTime", datetime + " 23:59:59");
		return statisticsService.queryStatisticsMessageInMain(map,datetime);
	}
	
	/**
	 * 新版：统计界面店铺总览
	 * @param shopUnique
	 * @return
	 */
	@RequestMapping("/businessOverview.do")
	@ResponseBody
	public ShopsResult businessOverview(
			@RequestParam(value="shopUnique")Long shopUnique,
			@RequestParam(value="checkType",defaultValue="1")Integer checkType
			,HttpServletRequest request
			){
		ShopsUtil.recordLog(log, request);
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		if(checkType==1){
			map.put("days", "1 DAY");
			map.put("lastDays", "2 DAY");
		}
		if(checkType==2){
			map.put("days", "1 WEEK");
			map.put("lastDays","2 WEEK");
		}
		if(checkType==3){
			map.put("days", "1 MONTH");
			map.put("lastDays", "2 MONTH");
		}
		return statisticsService.businessOverview(map);
	}
	
	/**
	 * 新版：分类销量占比
	 * @param shopUnique
	 * @return
	 */
	@RequestMapping("/querySaleSumByKind.do")
	@ResponseBody
	public ShopsResult querySaleSumByKind(
			@RequestParam(value="shopUnique")Long shopUnique,
			@RequestParam(value="checkType",defaultValue="1") Integer checkType
			,HttpServletRequest request
			){
		ShopsUtil.recordLog(log, request);
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		if(checkType==1){
			map.put("days", "1 DAY");
		}
		if(checkType==2){
			map.put("days", "1 WEEK");
		}
		if(checkType==3){
			map.put("days", "1 MONTH");
		}
		return statisticsService.querySaleSumByKind(map);
	}

	/**
	 * 新版：营业额走势
	 * @param shopUnique 店铺编号
	 * @param checkType 1-今日 2-本周 3-本月
	 * @param request
	 * @return
	 */
	@RequestMapping("/querySaleSumTrend.do")
	@ResponseBody
	public ShopsResult querySaleSumTrend(String shopUnique,Integer checkType,HttpServletRequest request){
		ShopsUtil.recordLog(log, request);
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		if(checkType==2){
			map.put("days", "1 WEEK");
			map.put("lastDays", "2 WEEK");
		}
		if(checkType==3){
			map.put("days","1 MONTH");
			map.put("lastDays", "2 MONTH");
		}
		return statisticsService.querySaleSumTrend(map, checkType);
	}
	
	/**
	 * 新版：热销商品TOP5
	 * @param shopUnique
	 * @param checkType
	 * @return
	 */
	@RequestMapping("/queryPreSaleFiveGoods.do")
	@ResponseBody
	public ShopsResult queryPreSaleFiveGoods(
			@RequestParam(value="shopUnique")Long shopUnique,
			@RequestParam(value="checkType",defaultValue="1")Integer checkType
			,HttpServletRequest request
			){
		ShopsUtil.recordLog(log, request);
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		if(checkType==1){
			map.put("days", "1 DAY");
			map.put("lastDays", "2 DAY");
		}
		if(checkType==2){
			map.put("days", "1 WEEK");
			map.put("lastDays", "2 WEEK");
		}
		if(checkType==3){
			map.put("days", "1 MONTH");
			map.put("lastDays", "2 MONTH");
		}
		return statisticsService.queryPreSaleFiveGoods(map);
	}
}
