package org.haier.shopUpdate.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.tools.ant.taskdefs.condition.Http;
import org.haier.shopUpdate.dao.StockDao;
import org.haier.shopUpdate.entity.ShopStock;
import org.haier.shopUpdate.entity.electronic.DeleteGoodsParams;
import org.haier.shopUpdate.log.annotation.RemoteLog;
import org.haier.shopUpdate.log.enums.BusinessType;
import org.haier.shopUpdate.params.QueryPurchaseOrderGoodsParams;
import org.haier.shopUpdate.params.UpdateReplenishmentGoodsParams;
import org.haier.shopUpdate.params.goods.QueryPromotionActivityListParams;
import org.haier.shopUpdate.redism.RedisCache;
import org.haier.shopUpdate.service.BHService;
import org.haier.shopUpdate.service.GoodsService;
import org.haier.shopUpdate.util.ChineseCharToEn;
import org.haier.shopUpdate.util.ShopsResult;
import org.haier.shopUpdate.util.mqtt.MqttxUtil;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.sql.Timestamp;
import java.util.*;
@Slf4j
@Controller
@RequestMapping("/goods")
public class GoodsController {
    @Resource
    private GoodsService goodsService;
    @Resource
    private StockDao shopStockMapper;
    @Resource
    private BHService bhService;


    /**
     * 查询店铺的促销商品列表
     * @param params
     * @return
     */
    @RequestMapping("/queryPromotionActivityList.do")
    @ResponseBody
    public ShopsResult queryPromotionActivityList(@RequestBody QueryPromotionActivityListParams params) {
        return goodsService.queryPromotionActivityList(params);
    }
    /**
     * 1、添加或更新商品信息
     * @param shopUnique
     * @param goodsBarcode
     * @param goodsName
     * @param goodsPrice
     * @param kindUnique
     * @param goodsChengType
     * @return
     */
    @RequestMapping("/addNewGoodsNP.do")
    @ResponseBody
    public ShopsResult addNewGoodsNP(String shopUnique,String goodsBarcode,String goodsName,String goodsPrice,String kindUnique,String goodsChengType) {
    	return goodsService.addNewGoodsNP(shopUnique, goodsBarcode, goodsName, goodsPrice, kindUnique, goodsChengType);
    }
    /**
	 *
	 * @param shopUnique 店铺编号
	 * @param pages 页码
	 * @param pageSize 单页查询数量
	 * @param classUnique 大分类编号
	 * @param supplierUnique 供货商编号
	 * @param goodsMsg 商品输入框信息
	 * @param goodsType
	 * @return
	 */
    @RequestMapping("/queryFarmGoodsList.do")
    @ResponseBody
	public ShopsResult queryFarmGoodsList(String shopUnique,Integer pages,Integer pageSize,String classUnique,String supplierUnique,String goodsMsg,Integer goodsType ) {
		return goodsService.queryFarmGoodsList(shopUnique, pages, pageSize, classUnique, supplierUnique, goodsMsg,goodsType);
	}
    /**
     * 根据条码，查询对应店铺的图片信息
     *
     * @param goodsMsg
     * @return
     */
    @RequestMapping("/searchGoodsImg.do")
    @ResponseBody
    public ShopsResult searchGoodsImg(String goodsMsg, String shopUnique, HttpServletRequest request) {
        ShopsResult sr = goodsService.queryOneByParam(shopUnique, goodsMsg);
        return sr;
    }

    /**
     * 商品查询
     *
     * @param shopUnique
     * @param pageIndex
     * @param pageSize
     * @param days
     * @param kindUnique
     * @param goodsMessage
     * @param orderType
     * @return
     */
    @RequestMapping("/queryGoodsMessage.do")
    @ResponseBody
    public ShopsResult queryGoodsMessage(
            String shopUnique,
            @RequestParam(value = "pageIndex", defaultValue = "1") Integer pageIndex,
            @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize,
            @RequestParam(value = "days", defaultValue = "30") Integer days,
            String kindUnique,
            String goodsMessage,
            String groupUnique,
            String threeUnique,
            @RequestParam(value = "order", defaultValue = "1") Integer order,
            @RequestParam(value = "orderType", defaultValue = "2") Integer orderType
            , HttpServletRequest request
    ) {

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shopUnique", shopUnique);
        map.put("startNum", (pageIndex - 1) * pageSize);
        map.put("pageSize", pageSize);
        map.put("days", days);
        Calendar time = Calendar.getInstance();
        Timestamp endTime = new Timestamp(time.getTimeInMillis());
        map.put("endTime", endTime);
        time.add(Calendar.MONTH, -1);
        Timestamp startTime = new Timestamp(time.getTimeInMillis());
        map.put("startTime", startTime);
        map.put("kindUnique", kindUnique);
        map.put("threeUnique", threeUnique);
        map.put("groupUnique", groupUnique);
        map.put("pageIndex", pageIndex);
        if (null != goodsMessage && !goodsMessage.equals("")) {
            map.put("goodsMessage", "%" + goodsMessage + "%");
        }
        if (order == 4) {
            map.put("order", "goodsCount");
        } else if (order == 3) {
            map.put("order", "goodsSalePrice");
        } else if (order == 2) {
            map.put("order", "saleCount");
        } else if (order == 1) {
            map.put("order", "goodsId");
        }

        if (orderType == 1) {
            map.put("orderType", "ASC");
        } else {
            map.put("orderType", "DESC");
        }
        return goodsService.queryGoodsMessage(map);
    }

    /**
     * 商品查询-收银分类
     *
     * @param shopUnique
     * @param pageIndex
     * @param pageSize
     * @param days
     * @param kindUnique
     * @param goodsMessage
     * @param orderType
     * @return
     */
    @RequestMapping("/queryGoodsMessageInvented.do")
    @ResponseBody
    public ShopsResult queryGoodsMessageInvented(
            String shopUnique,
            @RequestParam(value = "pageIndex", defaultValue = "1") Integer pageIndex,
            @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize,
            @RequestParam(value = "days", defaultValue = "30") Integer days,
            String kindUnique,
            String goodsMessage,
            String groupUnique,
            @RequestParam(value = "order", defaultValue = "1") Integer order,
            @RequestParam(value = "orderType", defaultValue = "2") Integer orderType
            , HttpServletRequest request
    ) {

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shopUnique", shopUnique);
        map.put("startNum", (pageIndex - 1) * pageSize);
        map.put("pageSize", pageSize);
        map.put("days", days);
        Calendar time = Calendar.getInstance();
        Timestamp endTime = new Timestamp(time.getTimeInMillis());
        map.put("endTime", endTime);
        time.add(Calendar.MONTH, -1);
        Timestamp startTime = new Timestamp(time.getTimeInMillis());
        map.put("startTime", startTime);
        map.put("kindUnique", kindUnique);
        map.put("groupUnique", groupUnique);
        map.put("pageIndex", pageIndex);
        if (null != goodsMessage && !goodsMessage.equals("")) {
            map.put("goodsMessage", "%" + goodsMessage + "%");
        }
        if (order == 4) {
            map.put("order", "goodsCount");
        } else if (order == 3) {
            map.put("order", "goodsSalePrice");
        } else if (order == 2) {
            map.put("order", "saleCount");
        } else if (order == 1) {
            map.put("order", "goodsId");
        }

        if (orderType == 1) {
            map.put("orderType", "ASC");
        } else {
            map.put("orderType", "DESC");
        }
        return goodsService.queryGoodsMessageInvented(map);
    }

    /**
     * 删除店铺商品
     *
     * @param shopUnique   店铺编号
     * @param goodsBarcode 商品条码
     * @return
     */
    @RequestMapping("/deleteShopsGoods.do")
    @ResponseBody
    @RemoteLog(title = "删除店铺商品", businessType = BusinessType.DELETE, isSendDingDingTalk = true)
    public ShopsResult deleteShopsGoods(
            @RequestParam(value = "shopUnique", required = true) String shopUnique,
            @RequestParam(value = "goodsBarcode", required = true) String goodsBarcode, HttpServletRequest request) {

        if (null == goodsBarcode || goodsBarcode.trim().equals("")) {
            return ShopsResult.fail("商品条形码 为空");
        }
        List<String> goodsBarcodes = new ArrayList<>();
        Map<String, Object> map;
        ShopsResult sr = new ShopsResult();
        List<Map<String, Object>> returnList = new ArrayList<>();
        if (goodsBarcode.contains(",")){
            String[] goodsBarcodeArr = goodsBarcode.split(",");
            for (String simpleGoodsBarcode : goodsBarcodeArr) {
                goodsBarcodes.add(simpleGoodsBarcode);
                ShopStock queryParam = new ShopStock();
                queryParam.setShopUnique(Long.parseLong(shopUnique));
                queryParam.setGoodsBarcode(simpleGoodsBarcode);
                List<ShopStock> shopStockList = shopStockMapper.findList(queryParam);
                if (ObjectUtil.isNotEmpty(shopStockList)) {
                    return sr.fail("该商品已产生库存记录，不允许删除！");
                }

                map = new HashMap<>(2);
                map.put("shopUnique", shopUnique);
                map.put("goodsBarcode", simpleGoodsBarcode.trim());
                sr = goodsService.deleteShopsGoods(map);
                returnList.add(map);
                if (sr.getStatus().equals(ShopsResult.FAIL)){
                    break;
                }
            }
        }else {
            ShopStock queryParam = new ShopStock();
            queryParam.setShopUnique(Long.parseLong(shopUnique));
            queryParam.setGoodsBarcode(goodsBarcode.trim());
            List<ShopStock> shopStockList = shopStockMapper.findList(queryParam);
            if (ObjectUtil.isNotEmpty(shopStockList)) {
                return sr.fail("该商品已产生库存记录，不允许删除！");
            }

            map = new HashMap<>(2);
            map.put("shopUnique", shopUnique);
            map.put("goodsBarcode", goodsBarcode.trim());
            returnList.add(map);
            sr = goodsService.deleteShopsGoods(map);
        }
        //mqtt 同步商品 ----------start 20230703
        try {
            RedisCache rc = new RedisCache("");
            Object mac = rc.getObject("topic_" + shopUnique);
            if (mac != null) {
                List<String> macIdList = (List<String>) mac;
                //2 MQTT 发送消息
                for (String macid : macIdList) {
                    Map<String, Object> data = new HashMap<String, Object>();
                    data.put("ctrl", "msg_goods_delete");
                    data.put("ID", macid);
                    data.put("status", 200);
                    data.put("data", returnList);
                    data.put("count", 1);
                    MqttxUtil.sendMapMsg(data, macid);
                }
            }

            //同步商品删除信息到电子价签
            DeleteGoodsParams deleteGoodsParams = new DeleteGoodsParams();
            deleteGoodsParams.setShopUnique(shopUnique);
            deleteGoodsParams.setGoodsBarcodes(goodsBarcodes);
            bhService.deleteGoods(deleteGoodsParams);
        } catch (Exception e) {
            log.error("MQTT发送消息失败", e);
        }
        //mqtt 同步商品 ----------end 20230703
        return sr;
    }


    /**
     * 更新商品信息
     *
     * @param shopUnique     店铺编号
     * @param goodsBarcode   商品条码
     * @param kindUnique     商品分类
     * @param goodsBrand     商品品牌
     * @param goodsPromotion 商品促销状态
     * @param goodsName      商品名称
     * @param goodsInPrice   商品进价
     * @param goodsSalePrice 商品售价
     * @param goodsAddress   商品产地
     * @param goodsDiscount  商品折扣
     * @param goodsRemarks   商品备注
     * @param goodsCount     商品库存数量
     * @param goodsStandard  商品规格
     * @param goodsUnit      计价单位
     * @param goodsCusPrice  会员价
     * @param sameType       同步类型
     * @param shelfState     上架状态
     * @return
     */
    @RequestMapping("/modifyGoods.do")
    @ResponseBody
    public ShopsResult modifyGoods(
            @RequestParam(value = "shopUnique", required = true) Long shopUnique,
            @RequestParam(value = "goodsBarcode", required = true) String goodsBarcode,
            String kindUnique,
            String goodsBrand,
            Integer goodsPromotion,
            String goodsName,
            Double goodsInPrice,
            Double goodsSalePrice,
            String goodsAddress,
            Double goodsDiscount,
            String goodsRemarks,
            Double goodsCount,
            String goodsStandard,
            String goodsUnit,
            Double goodsCusPrice,
            Integer sameType,
            Integer shelfState,
            Integer pcShelfState,
            HttpServletRequest request
    ) {

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shopUnique", shopUnique);
        map.put("goodsBarcode", goodsBarcode.trim());
        map.put("kindUnique", kindUnique);
        map.put("goodsBrand", goodsBrand);
        map.put("goodsPromotion", goodsPromotion);
        if (goodsName != null) {
            map.put("goodsName", ChineseCharToEn.replaceLetter(goodsName));
        }
        map.put("goodsInPrice", goodsInPrice);
        map.put("goodsSalePrice", goodsSalePrice);
        map.put("goodsAddress", goodsAddress);
        map.put("goodsDiscount", goodsDiscount);
        map.put("goodsRemarks", goodsRemarks);
        map.put("goodsCount", goodsCount);
        map.put("goodsStandard", goodsStandard);
        map.put("goodsUnit", goodsUnit);
        map.put("goodsCusPrice", goodsCusPrice);
//		map.put("sameType", sameType);
        map.put("shelfState", shelfState);
        map.put("pcShelfState", pcShelfState);
        map.put("sameType", 2);
        return goodsService.modifyGoods(map, request);

    }

    @RequestMapping("/goodsShelfState.do")
    @ResponseBody
    public ShopsResult goodsShelfState(
            @RequestParam(value = "shopUnique", required = true) Long shopUnique,
            @RequestParam(value = "goodsBarcode", required = true) String goodsBarcode,
            Integer shelfState,
            Integer pcShelfState,
            HttpServletRequest request
    ) {
        String staffId = request.getHeader("userId");
        ShopsResult shopsResult = new ShopsResult();
        Map<String, Object> map;
        if (goodsBarcode.contains(",")) {
            String[] goodsBarcodeArr = goodsBarcode.trim().split(",");
            for (String simpleGoodsBarcode : goodsBarcodeArr) {
                map = new HashMap<>();
                map.put("shopUnique", shopUnique);
                map.put("goodsBarcode", simpleGoodsBarcode);
                map.put("shelfState", shelfState);
                map.put("pcShelfState", pcShelfState);
                map.put("staffId", staffId);
                shopsResult = goodsService.modifyGoodsInfoTrx(map);
                if (Objects.equals(shopsResult.getStatus(), ShopsResult.FAIL)) {
                    break;
                }
            }
        } else {
            map = new HashMap<>();
            map.put("shopUnique", shopUnique);
            map.put("goodsBarcode", goodsBarcode);
            map.put("shelfState", shelfState);
            map.put("pcShelfState", pcShelfState);
            map.put("staffId", staffId);
            shopsResult = goodsService.modifyGoodsInfoTrx(map);
        }
        return shopsResult;
    }

    @RequestMapping("/goodsKindTransfer.do")
    @ResponseBody
    public ShopsResult goodsKindTransfer(
            @RequestParam(value = "shopUnique", required = true) Long shopUnique,
            @RequestParam(value = "goodsBarcode", required = true) String goodsBarcode,
            String kindUnique,
            HttpServletRequest request
    ) {
        ShopsResult shopsResult = new ShopsResult();
        Map<String, Object> map;
        String staffId = request.getHeader("userId");
        if (goodsBarcode.contains(",")) {
            String[] goodsBarcodeArr = goodsBarcode.trim().split(",");
            for (String simpleGoodsBarcode : goodsBarcodeArr) {
                map = new HashMap<>();
                map.put("shopUnique", shopUnique);
                map.put("goodsBarcode", simpleGoodsBarcode);
                map.put("kindUnique", kindUnique);
                map.put("staffId", staffId);
                shopsResult = goodsService.modifyGoodsInfoTrx(map);
                if (Objects.equals(shopsResult.getStatus(), ShopsResult.FAIL)) {
                    break;
                }
            }
        } else {
            map = new HashMap<>();
            map.put("shopUnique", shopUnique);
            map.put("goodsBarcode", goodsBarcode);
            map.put("kindUnique", kindUnique);
            map.put("staffId", staffId);
            shopsResult = goodsService.modifyGoodsInfoTrx(map);
        }
        return shopsResult;
    }

    /**
     * 商品详情查询
     *
     * @param shopUnique
     * @return
     */
    @RequestMapping("/goodsDetail.do")
    @ResponseBody
    public ShopsResult goodsDetail(
            @RequestParam(value = "shopUnique", required = true) String shopUnique,
            @RequestParam(value = "goodsBarcode", required = true) String goodsBarcode
            , HttpServletRequest request) {

        Map<String, String> map = new HashMap<String, String>();
        map.put("goodsBarcode", goodsBarcode.trim());
        map.put("shopUnique", shopUnique);
        return goodsService.goodsDetail(map);
    }


    /**
     * 查询商品供货商详情
     *
     * @param shopUnique
     * @return
     */
    @RequestMapping("/goodsSupplierQuery.do")
    @ResponseBody
    public ShopsResult goodsSupplierQuery(
            @RequestParam(value = "shopUnique", required = true) String shopUnique,
            @RequestParam(value = "goodsBarcode", required = true) String goodsBarcode
            , HttpServletRequest request
    ) {

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shopUnique", shopUnique);
        map.put("goodsBarcode", goodsBarcode.trim());
        return goodsService.goodsSupplierQuery(map);
    }

    /**
     * 查询商品供货商详情
     *
     * @param shopUnique
     * @return
     */
    @RequestMapping("/goodsSupplierQueryNew.do")
    @ResponseBody
    public ShopsResult goodsSupplierQueryNew(
            @RequestParam(value = "shopUnique", required = true) String shopUnique,
            @RequestParam(value = "goodsBarcode", required = true) String goodsBarcode
            , HttpServletRequest request) {

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shopUnique", shopUnique);
        map.put("goodsBarcode", goodsBarcode.trim());
        return goodsService.goodsSupplierQueryNew(map);
    }

    /**
     * 更新商品信息
     *
     * @param shopUnique             店铺编号
     * @param foreignKey             包装外键关键词
     * @param kindUnique             商品分类
     * @param goodsBarcode           基础商品信息商品条码
     * @param goodsCount             商品库存量（以最小单位计算）
     * @param goodsBrand             商品品牌
     * @param supplierUnique         供货商编号
     * @param supGoodsBarcode        选择的进货商品条码
     * @param goodsMessage           商品信息数组
     * @param request                图片存放
     * @return
     * @throws Exception
     */
    @RequestMapping("/updateGoodsMessage.do")
    @ResponseBody
    public ShopsResult updateListGoodsMessage(
            @RequestParam(value = "shopUnique", required = true) Long shopUnique,
            @RequestParam(value = "foreignKey", required = true) Long foreignKey,
            @RequestParam(value = "kindUnique", required = true) String kindUnique,
            @RequestParam(value = "goodsCount", required = true) Double goodsCount,
            @RequestParam(value = "goodsBarcode", required = true) String goodsBarcode,
            @RequestParam(value = "goodsChengType", defaultValue = "0") Integer goodsChengType,
            String goodsPicturePath,
            String goodsBrand,
            String supplierUnique,
            String supGoodsBarcode,
            @RequestParam(value = "sameType", defaultValue = "2") Integer sameType,
            @RequestParam(value = "goodsMessage", required = true) String goodsMessage,
            HttpServletRequest request,
            @RequestParam(value = "tableType", defaultValue = "2") Integer tableType
    ) throws Exception {

        Map<String, Object> bmap = new HashMap<String, Object>();
//		System.out.println(goodsBarcode+"112");
        bmap.put("shopUnique", shopUnique);
        bmap.put("goodsBrand", goodsBrand);
        bmap.put("foreignKey", foreignKey);
        bmap.put("supplierUnique", supplierUnique);
        bmap.put("kindUnique", kindUnique);
        bmap.put("supGoodsBarcode", supGoodsBarcode);
        bmap.put("sameType", sameType);
        bmap.put("goodsChengType", goodsChengType);
        if (goodsBarcode.startsWith("00")) {
            goodsBarcode = goodsBarcode.substring(1);
        }
        System.out.println("updateGoodsMessage:::::::::APP商品更新" + shopUnique + "::::::::::" + goodsBarcode + ":::::::::::" + kindUnique + "::::::" + goodsMessage);
        return goodsService.updateListGoodsMessage(bmap, goodsMessage, request, goodsCount, goodsBarcode.trim(),
                shopUnique, foreignKey, sameType, supplierUnique, goodsPicturePath, tableType, goodsChengType);
    }

    /**
     * 商品基本信息查询
     * 1：获取
     *
     * @param shopUnique
     * @return
     */
    @RequestMapping("/searchBaseGoods.do")
    @ResponseBody
    public ShopsResult searchBaseGoods(
            @RequestParam(value = "shopUnique") Long shopUnique,
            @RequestParam(value = "goodsBarcode") String goodsBarcode
            , HttpServletRequest request
            , Integer type
    ) {

        Map<String, Object> map = new HashMap<String, Object>();
        goodsBarcode = goodsBarcode.trim();
        if (goodsBarcode.startsWith("0") && goodsBarcode.length() > 1) {
            goodsBarcode = removeZero(goodsBarcode);
        }
        map.put("goodsBarcode", goodsBarcode.trim());
        map.put("shopUnique", shopUnique);

        return goodsService.searchBaseGoods(map, goodsBarcode.trim(), type);
    }


    public static String removeZero(String goodsBarcode) {
        goodsBarcode = goodsBarcode.substring(1);
        if (goodsBarcode.startsWith("0") && goodsBarcode.length() > 1) {
            goodsBarcode = removeZero(goodsBarcode);
        }
        return goodsBarcode;
    }

    /**
     * 添加新商品???
     *
     * @param shopUnique
     * @param foreignKey
     * @param kindUnique
     * @param goodsCount
     * @param goodsBarcode
     * @param goodsBrand
     * @param supplierUnique
     * @param supGoodsBarcode
     * @param goodsMessage
     * @param request
     * @return
     */
    @RequestMapping("/addNewGoods.do")
    @ResponseBody
    public ShopsResult addNewGoods(
            @RequestParam(value = "shopUnique", required = true) Long shopUnique,
            @RequestParam(value = "foreignKey", required = true) Long foreignKey,
            @RequestParam(value = "kindUnique", required = true) String kindUnique,
            @RequestParam(value = "goodsCount", required = true, defaultValue = "0") Double goodsCount,
            @RequestParam(value = "goodsBarcode", required = true) String goodsBarcode,
            String goodsPicturePath,
            String goodsBrand,
            String supplierUnique,
            String supGoodsBarcode,
            @RequestParam(value = "sameType", defaultValue = "2") Integer sameType,
            @RequestParam(value = "goodsMessage", required = true) String goodsMessage,
            HttpServletRequest request,
            @RequestParam(value = "tableType", defaultValue = "1") Integer tableType,
            @RequestParam(value = "goodsChengType", defaultValue = "1") Integer goodsChengType
    ) {

        supplierUnique = null;
//		goodsPicturePath =null;
        supGoodsBarcode = null;
        Map<String, Object> bmap = new HashMap<String, Object>();
        bmap.put("shopUnique", shopUnique);
        bmap.put("goodsBrand", goodsBrand);
        bmap.put("sameType", sameType);
        bmap.put("supplierUnique", supplierUnique);
        bmap.put("kindUnique", kindUnique);
//		bmap.put("supGoodsBarcode", supGoodsBarcode);
        if (null == foreignKey || foreignKey.intValue() == 0 || foreignKey.intValue() == 1) {
            foreignKey = null;
        }
        if (null == kindUnique || kindUnique.equals("")) {
            kindUnique = "1700006";
            bmap.put("kindUnique", kindUnique);
        }
        System.out.println("商品信息更新：：：" + bmap);
        if (goodsBarcode.startsWith("00")) {
            goodsBarcode = goodsBarcode.substring(1);
        }
        try {
            return goodsService.updateListGoodsMessage(bmap, goodsMessage.trim(), request, goodsCount, supGoodsBarcode, shopUnique, foreignKey, sameType, supplierUnique, goodsPicturePath, tableType, goodsChengType);
        } catch (Exception e) {
            log.error("商品信息更新失败", e);
            return new ShopsResult(0, "保存失败");
        }
    }


    /**
     * 商品出入库记录查询
     *
     * @param shopUnique
     * @return
     */
    @RequestMapping("/stockRecord.do")
    @ResponseBody
    public ShopsResult stockRecord(
            @RequestParam(value = "shopUnique") Long shopUnique,
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize,
            String goodsMessage,
            Integer stockType,
            Integer stockResource
            , String startTime
            , String endTime
            , HttpServletRequest request
    ) {

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("startNum", (pageNum - 1) * pageSize);
        map.put("pageSize", pageSize);
        map.put("shopUnique", shopUnique);
        map.put("startTime", startTime);
        map.put("endTime", endTime);
        if (stockType != null && stockType != -1) {
            map.put("stockType", stockType);
        }
        if (stockResource != null && stockResource != -1) {
            map.put("stockResource", stockResource);
        }
        if (goodsMessage != null && goodsMessage != "") {
            map.put("goodsMessage", "%" + goodsMessage + "%");
        }
        return goodsService.stockRecord(map);
    }

    /**
     * 商品销量排行
     *
     * @param shopUnique
     * @return
     */
    @RequestMapping("/goodsSaleStatistics.do")
    @ResponseBody
    public ShopsResult goodsSaleStatistics(
            @RequestParam(value = "shopUnique") Long shopUnique,
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize,
            String goodsMessage,
            Integer dayType,
            String parUnique,
            String goodsKindUnique,
            String threeUnique,
            @RequestParam(value = "orderType", defaultValue = "1") Integer orderType,
            String startTime,
            String endTime
            , HttpServletRequest request
    ) {

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shopUnique", shopUnique);
        map.put("startNum", (pageNum - 1) * pageSize);
        map.put("pageSize", pageSize);
        map.put("goodsKindUnique", goodsKindUnique);
        map.put("goodsThirdUnique", threeUnique);
        if (null != goodsMessage && !goodsMessage.equals("")) {
            map.put("goodsMessage", "%" + goodsMessage + "%");
        }
        Calendar startCalendar = Calendar.getInstance();
        Calendar endCalendar = Calendar.getInstance();
        startCalendar.set(Calendar.HOUR_OF_DAY, 0);
        startCalendar.set(Calendar.MINUTE, 0);
        startCalendar.set(Calendar.SECOND, 0);
        endCalendar.set(Calendar.HOUR_OF_DAY, 0);
        endCalendar.set(Calendar.MINUTE, 0);
        endCalendar.set(Calendar.SECOND, 0);
        if (null == dayType) {
            map.put("startTime", startTime);
            map.put("endTime", endTime);
        } else {
            if (dayType == 2) {
                startCalendar.add(Calendar.DATE, -1);
                endCalendar.add(Calendar.DATE, -1);
            } else if (dayType == 3) {
                startCalendar.add(Calendar.DATE, -2);
                endCalendar.add(Calendar.DATE, -2);
            } else if (dayType == 4) {
                startCalendar.add(Calendar.DATE, -6);
            } else if (dayType == 5) {
                startCalendar.add(Calendar.MONTH, -1);
            }
            map.put("startTime", new Timestamp(startCalendar.getTimeInMillis()));
            map.put("endTime", new Timestamp(endCalendar.getTimeInMillis()));

            System.out.println(map.get("startTime"));
            System.out.println(map.get("endTime"));
        }
        if (null == parUnique || parUnique.equals("-1")) {
            parUnique = null;
        }
        map.put("parUnique", parUnique);
        if (orderType == 1) {
            map.put("order", "sum");
            map.put("orderType", "DESC");
        }
        if (orderType == 2) {
            map.put("order", "sum");
            map.put("orderType", "ASC");
        }
        if (orderType == 3) {
            map.put("order", "count");
            map.put("orderType", "DESC");
        }
        if (orderType == 4) {
            map.put("order", "count");
            map.put("orderType", "ASC");
        }
        return goodsService.goodsSaleStatistics(map);
    }

    /**
     * 输入商品后六位，查询商品信息
     *
     * @param shopUnique
     * @return
     */
    @RequestMapping("/queryBaseGoodsMessageByCode.do")
    @ResponseBody
    public ShopsResult queryBaseGoodsMessageByCode(
            @RequestParam(value = "shopUnique", required = true) String shopUnique,
            @RequestParam(value = "goodsBarcode", required = true) String goodsBarcode,
            @RequestParam(value = "kindType", defaultValue = "1") Integer kindType
            , HttpServletRequest request) {

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shopUnique", shopUnique);
        map.put("goodsBarcode", "%" + goodsBarcode.trim() + "%");
        return goodsService.queryBaseGoodsMessageByCode(map, kindType);
    }

    /**
     * 查询补货商品及分类
     * @param request
     * @param params
     * @return
     */
    @RequestMapping("/queryPurchaseOrderGoods.do")
    @ResponseBody
    public ShopsResult queryPurchaseOrderGoods(HttpServletRequest request, QueryPurchaseOrderGoodsParams params) {

        return goodsService.queryPurchaseOrderGoods(params);
    }

    /**
     * 修改补货商品 价格
     * @param request
     * @param params
     * @return
     */
    @RequestMapping("/updateReplenishmentGoods.do")
    @ResponseBody
    public ShopsResult updateReplenishmentGoods(HttpServletRequest request, UpdateReplenishmentGoodsParams params) {

        return goodsService.updateReplenishmentGoodsService(params);
    }
    @RequestMapping("/getIp2.do")
    @ResponseBody
    public static String getIp2(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    /**
     * 创建新的条码
     * @return
     */
    @RequestMapping("/queryGoodsBarcodeSameForeignkey.do")
    @ResponseBody
    public ShopsResult queryGoodsBarcodeSameForeignkey(
            @RequestParam(value="shopUnique")String shopUnique){
        try {
            return goodsService.queryGoodsBarcodeSameForeignkey(shopUnique);
        } catch (Exception e) {
            log.error("创建新的条码失败", e);
            return new ShopsResult(0, "系统错误");
        }
    }
}
