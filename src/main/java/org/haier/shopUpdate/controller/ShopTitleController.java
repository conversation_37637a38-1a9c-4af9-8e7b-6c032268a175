package org.haier.shopUpdate.controller;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

//import org.apache.log4j.Logger;
import org.haier.shopUpdate.entity.ShopTitle;
import org.haier.shopUpdate.service.ShopTitleService;
import org.haier.shopUpdate.util.ShopsResult;
import org.haier.shopUpdate.util.common.CommonResult;
//import org.haier.shopUpdate.util.ShopsUtil;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import net.sf.json.JSONArray;

@Controller
@RequestMapping("/shopTitle")
public class ShopTitleController {
	
	@Resource
	private ShopTitleService stService;
//	private static Logger log=Logger.getLogger(ShopFunctionController.class);
	
	/**
	 * 查询商家端APP主界面的模块信息
	 * @param shopTitle
	 * @return
	 */
	@RequestMapping("/queryMainPageTitle.do")
	@ResponseBody
	public ShopsResult queryMainPageTitle(ShopTitle shopTitle,HttpServletRequest request){
		
		return stService.queryMainPageTitle(shopTitle);
	}
	
	/**
	 * YXL20230201
	 * 20230204
	   * 查询商家端APP主界面的模块信息
	 * @param shopTitle
	 * @return
	 */
    @RequestMapping(value = "/queryMainPageTitle/v2.do", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult queryMainPageTitle_v2(ShopTitle shopTitle,HttpServletRequest request){
		
		return stService.queryMainPageTitle_v2(shopTitle);
	}

	/**
	 * 更新店铺模块的排序信息
	 * @param list
	 * @return
	 */
	@RequestMapping("/modifyTitle.do")
	@ResponseBody
	public ShopsResult modifyTitle(String listMsg,HttpServletRequest request){
		
		JSONArray ja=JSONArray.fromObject(listMsg);
		List<ShopTitle> list=new ArrayList<ShopTitle>();
		for(int i=0;i<ja.size();i++){
			ShopTitle st=new ShopTitle();
			st.setId(ja.getJSONObject(i).getInt("id"));
			st.setShowType(ja.getJSONObject(i).getInt("showType"));
			list.add(st);
		}
		return stService.modifyTitle(list);
	}
}
