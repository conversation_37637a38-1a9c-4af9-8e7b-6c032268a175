package org.haier.shopUpdate.controller;

import org.haier.shopUpdate.entity.ShopQuickPayConfig;
import org.haier.shopUpdate.params.shopQuickPay.ConfigListParams;
import org.haier.shopUpdate.service.ShopQuickPayService;
import org.haier.shopUpdate.util.ShopsResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 宁宇快餐快捷支付/快捷支付配置
 * @Description: 商户快捷支付配置
 * @Version: 1.0
 */
@RequestMapping("/shopQuickPay")
@Controller
public class ShopQuickPayConfigController {

    @Autowired
    private ShopQuickPayService shopQuickPayService;

    /**
     * 查询配置列表
     * @param configListParams
     * @return
     */
    @RequestMapping("/configList.do")
    @ResponseBody
    public ShopsResult configList(@RequestBody ConfigListParams configListParams) {

        return shopQuickPayService.configList(configListParams);
    }

    /**
     * 添加配置
     * @param shopQuickPayConfig
     * @return
     */
    @RequestMapping("/addConfig.do")
    @ResponseBody
    public ShopsResult addConfig(@RequestBody ShopQuickPayConfig shopQuickPayConfig) {
        return shopQuickPayService.addConfig(shopQuickPayConfig);
    }

    /**
     * 修改配置
     * @param shopQuickPayConfig
     * @return
     */
    @RequestMapping("/updateConfig.do")
    @ResponseBody
    public ShopsResult updateConfig(@RequestBody ShopQuickPayConfig shopQuickPayConfig) {
        return shopQuickPayService.updateConfig(shopQuickPayConfig);
    }

    /**
     * 删除配置
     * @param shopQuickPayConfig
     * @return
     */
    @RequestMapping("/deleteConfig.do")
    @ResponseBody
    public ShopsResult deleteConfig(@RequestBody ShopQuickPayConfig shopQuickPayConfig) {
        return shopQuickPayService.deleteConfig(shopQuickPayConfig);
    }
}
