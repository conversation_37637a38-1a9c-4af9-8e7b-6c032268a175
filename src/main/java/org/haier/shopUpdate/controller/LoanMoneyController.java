package org.haier.shopUpdate.controller;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import lombok.extern.slf4j.Slf4j;
import cn.hutool.core.util.StrUtil;
import org.haier.shopUpdate.oss.UploadResult;
import org.haier.shopUpdate.project.ProjectConfig;
import org.haier.shopUpdate.redism.RedisCache;
import org.haier.shopUpdate.service.LoanMoneyService;
import org.haier.shopUpdate.upload.FileUploadService;
import org.haier.shopUpdate.util.ShopsResult;
import org.haier.shopUpdate.util.ShopsUtil;
import org.haier.shopUpdate.util.helibao.HelibaoPayConfig;
import org.haier.shopUpdate.util.helibao.HttpUtil;
import org.haier.shopUpdate.util.helibao.XMLUtil4jdom;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import com.haier.util.HelibaoPayUtil;
import com.haier.util.bean.ReturnVo;
import com.haier.vo.AppCreateOrderResponseVo;
import com.haier.vo.AppCreateOrderVo;



@Slf4j
@Controller
@RequestMapping("/loanMoney")
public class LoanMoneyController {
	@Resource
	private LoanMoneyService loanService;

	@Resource
	private RedisCache redisCache;

	@Resource
	private FileUploadService fileUploadService;
	
	/**
	 * 查询订单还款列表详情
	 * @param shop_unique 店铺编号
	 * @param order_no 订单编号
	 * @param page 页码
	 * @param page_size 单页查询数量
	 * @return
	 */
	@RequestMapping("/queryOrderFenqiList.do")
	@ResponseBody
	public ShopsResult queryOrderFenqiList(String shop_unique,String order_no,Integer page,Integer page_size) {
		return loanService.queryOrderFenqiList(shop_unique, order_no, page, page_size);
	}
	/**
	 *
	 * @param request 上传文件的请求，文件必须上传，且文件名为file
	 * @param yasuo 1、默认不压缩；2、对文件进行压缩
	 * @param file_width 如果压缩文件，可以设置压缩后的文件宽度，默认为400
	 * @param file_height 如果压缩文件，可以设置压缩后的文件高度，默认为400
	 * @return
	 */
	@RequestMapping("/uploadFile.do")
	@ResponseBody
	public ShopsResult uploadFile(HttpServletRequest request,Integer yasuo, Integer file_width,Integer file_height) {
		ShopsResult sr = new ShopsResult(1, "上传成功!");

		MultipartFile file=ShopsUtil.testMulRequest(request, "file");
		if(null == file || file.isEmpty()) {
			sr.setStatus(0);
			sr.setMsg("请上传文件");
		}else {
	        try {
				UploadResult ur = fileUploadService.upload(file);
	        	Map<String,Object> map = new HashMap<String,Object>();
	        	map.put("url", ur.getUrl());
	        	sr.setData(map);
	        }catch (Exception e) {
	        	log.error("上传文件失败",e);
	        	sr.setStatus(0);
	        	sr.setMsg("服务器内部异常");
			}
		}
		return sr;
	}

	/**
	 * 获取文件上传到文件服务器的保存路径
	 * 1、获取缓存的信息，如果存在，使用缓存的信息，如果不存在，根据当前日期生成缓存信息
	 * 2、由于文件服务器只能生产已存在目录的下一级目录，不能创建多级目录，这个坑要提前处理
	 * 3、文件名称由UUID随机生成，防止重复
	 *
	 */
	public String getFileSavePath() {
		String filePathSure = null;
		Object filePath = redisCache.getObject("filePath");
		if(null == filePath) {
			//生成新的文件保存路径
			Date date = new Date();

			SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
			filePathSure = sdf.format(date);

			//计算当前日期剩余缓存秒数，缓存到redis中备用
			Integer time = getRemainSecondsOneDay(date);

			redisCache.putObject("filePath", "/" + filePathSure, time);
		}else {
			filePathSure = filePath.toString();
		}

		return filePathSure;
	}
    /**
     * 获取当天剩余的秒数
     * @param currentDate
     * @return
     */
    public static Integer getRemainSecondsOneDay(Date currentDate) {
        LocalDateTime midnight = LocalDateTime.ofInstant(currentDate.toInstant(),
                ZoneId.systemDefault()).plusDays(1).withHour(0).withMinute(0)
                .withSecond(0).withNano(0);

        LocalDateTime currentDateTime = LocalDateTime.ofInstant(currentDate.toInstant(),
                ZoneId.systemDefault());
        long seconds = ChronoUnit.SECONDS.between(currentDateTime, midnight);
        return (int) seconds;
    }
	/**
	 * 提交店铺赊销申请信息
	 * @param shop_unique 店铺编号
	 * @param business_image 营业执照照片路径
	 * @param business_image_hand 手持营业执照照片路径
	 * @param ID_front_image 身份证正面照片
	 * @param ID_back_image 身份证反面照片（人脸面）
	 * @param ID_back_image_hand 手持身份证人脸面照片
	 * @param bank_front_image 银行卡正面照
	 * @param bank_back_image 银行卡反面照
	 * @param shop_doorhead 门头照
	 * @param shop_cashier 店内收银台照片
	 * @param loan_sign 签名信息
	 * @return
	 */
	@RequestMapping("/addOpenLoanNew.do")
	@ResponseBody
	public ShopsResult addOpenLoanNew(@RequestParam(value="shop_unique",required=true)String shop_unique,
			String business_image,
			String business_image_hand,
			String ID_front_image,
			String ID_back_image,
			String ID_back_image_hand,
			String bank_front_image,
			String bank_back_image,
			String shop_doorhead,
			String shop_cashier,
			String loan_sign,
			String shop_inside
			) {


		return loanService.addOpenLoanNew(shop_unique, business_image, business_image_hand, ID_front_image, ID_back_image,
				ID_back_image_hand, bank_front_image, bank_back_image, shop_doorhead, shop_cashier, loan_sign, shop_inside);
	}

	@RequestMapping("/generateCodeHLBRetrunMoney.do")
	@ResponseBody
	public ShopsResult generateCodeHLBRetrunMoney(@RequestParam(value="orderNo",required=false)String orderNo,
			@RequestParam(value="payType",required=true)String payType,
			@RequestParam(value="orderAmount",required=true)String orderAmount,
			@RequestParam(value="principal_money",required=true)String principal_money,
			@RequestParam(value="shop_unique",required=true)String shop_unique) {

		ShopsResult sr = new ShopsResult(1, "下单成功！");

		if(null == orderNo || orderNo.equals("")) {
			orderNo = "HK_"+System.currentTimeMillis();
		}

		Map<String,Object> params=new HashMap<>();
		params.put("sale_list_unique", orderNo);
		params.put("pay_type", payType);
		params.put("return_money", orderAmount);
		params.put("type", 2);
		params.put("pay_status", 1);
		params.put("shop_unique", shop_unique);
		params.put("principal_money", principal_money);

		loanService.saveReturnMoney(params);

		String codeUrl = "";
		if(payType.equals("2")) {
			try {
				codeUrl= weixinPay( orderAmount, orderNo,HelibaoPayConfig.RETURN_MONEY_NOTIFY_URL);
				sr.setStatus(1);
			}catch (Exception e) {

			}
		}else{
			AppCreateOrderVo vo = new AppCreateOrderVo();
			//随机产生新的订单号，防止更换支付方式


			vo.setP2_orderId(orderNo);
			vo.setP3_customerNumber(HelibaoPayConfig.MCHID);
			vo.setP4_payType("SCAN");
//			vo.setP5_orderAmount("0.02");
			vo.setP5_orderAmount(orderAmount);
			vo.setP7_authcode("1");
			String realPayType = null;
			if(payType.equals("1")) {
				realPayType = "ALIPAY";
			}else if(payType.equals("2")) {
				realPayType = "WXPAY";
			}else {
				realPayType = "UNIONPAY";
			}
			vo.setP8_appType(realPayType);
			vo.setP9_notifyUrl(HelibaoPayConfig.HELIBAONOTIFYURL_RETURN_MONEY);
			vo.setP10_successToUrl("");
			vo.setP11_orderIp("***********");
			vo.setP12_goodsName("金圈平台");

			vo.setSign(HelibaoPayConfig.MCHKEY);
			ReturnVo rs = HelibaoPayUtil.swipe(vo, HelibaoPayConfig.MCHKEY, null);
			System.out.println("轻轻揭过"+rs);
			if(rs.getType() != null && rs.getType().equals("1")) {
				//下单成功
				AppCreateOrderResponseVo  appvo = (AppCreateOrderResponseVo)rs.getData();
				codeUrl = appvo.getRt8_qrcode();
				sr.setStatus(1);
			}
		}
		sr.setObject(orderNo);
		if(StrUtil.isNotBlank(codeUrl)) {
			sr.setData(codeUrl);
		}
		return sr;
	}


	public String weixinPay(String payMoeny,String out_trade_no,String url) throws Exception {
        // 账号信息
        String appid = HelibaoPayConfig.APP_ID;  // appid
        String mch_id = HelibaoPayConfig.MCH_ID; // 商业号
        String key = HelibaoPayConfig.API_KEY; // key


        String currTime = HelibaoPayConfig.getCurrTime();
        String strTime = currTime.substring(8, currTime.length());
        String strRandom = HelibaoPayConfig.buildRandom(4) + "";
        String nonce_str = strTime + strRandom;
        // 回调接口
        String notify_url = url;
        String trade_type = "NATIVE";


        SortedMap<Object,Object> packageParams = new TreeMap<Object,Object>();
        packageParams.put("appid", appid);
        packageParams.put("mch_id", mch_id);
        packageParams.put("nonce_str", nonce_str);
        packageParams.put("body", "金圈百货");  //（调整为自己的名称）
        packageParams.put("out_trade_no", out_trade_no);
        String finalmoney= String.format("%.2f", Double.valueOf(payMoeny));//微信请求金额单位为分
		finalmoney = finalmoney.replace(".", "");
		int total_fee= Integer.parseInt(finalmoney);
        packageParams.put("total_fee", total_fee+""); //价格的单位为分
        packageParams.put("spbill_create_ip", "127.0.0.1");
        packageParams.put("notify_url", notify_url);
        packageParams.put("trade_type", trade_type);

        String sign = HelibaoPayConfig.createSign("UTF-8", packageParams,key);
        packageParams.put("sign", sign);

        String requestXML = HelibaoPayConfig.getRequestXml(packageParams);
        System.out.println(requestXML);

        String resXml = HttpUtil.postData(HelibaoPayConfig.UFDODER_URL, requestXML);
        System.out.println(resXml);

        @SuppressWarnings("rawtypes")
		Map map = XMLUtil4jdom.doXMLParse(resXml);
        String urlCode = (String) map.get("code_url");
        System.out.println(map.toString());
        System.out.println(urlCode);
        return urlCode;
	}

	@RequestMapping("/openLoanManagerPage.do")
	@ResponseBody
	public ShopsResult openLoanManagerPage(String shop_unique){
		ShopsResult result=new ShopsResult(1,"");
		Map<String,Object> map=loanService.queryIsOpenLoan(shop_unique);
		if(map==null){
			//未提交审核资料
			map=new HashMap<>();
			map.put("audit_status", -1);
		}else if("4".equals(map.get("audit_status").toString())){
			//审核不通过重新上传

		}else if("2".equals(map.get("audit_status").toString())||"3".equals(map.get("audit_status").toString())){
			//审核通过
			//查询提前还款的金额
//			Map<String,Object> advanceMoney=loanService.queryAdvanceMoney(shop_unique);
//			map.put("advance_money", advanceMoney.get("advance_money"));
			//查询赊销还款规则
			Map<String,Object> sxSetMap = loanService.querySxPolicySet();
			Map<String,Object> loanMap = loanService.queryShopLoanMsg(shop_unique);

			System.out.println("当前店铺赊销信息为" + loanMap);

			BigDecimal breach_rate = new BigDecimal(sxSetMap.get("breach_rate").toString());
			BigDecimal advance_money = null;
			if(null != loanMap && !loanMap.isEmpty()) {
				advance_money = new BigDecimal(loanMap.get("advance_money").toString());
				map.put("principal_money", advance_money);
				map.put("not_return_money", loanMap.get("not_return_money"));
				map.put("list_count", loanMap.get("list_count"));
			}else {
				advance_money = new BigDecimal(0);
				map.put("principal_money", 0);
				map.put("not_return_money", 0);
				map.put("list_count", 0);
			}

			advance_money = advance_money.add(advance_money.multiply(breach_rate).setScale(2,BigDecimal.ROUND_HALF_UP)).setScale(2,BigDecimal.ROUND_HALF_UP);
			//查询借款利率
			Map<String,Object> rate=loanService.queryLoanMoneyRate(shop_unique);
			map.put("rate", rate.get("rate"));
			map.put("advance_money", advance_money);
		}
		map.put("agreement_explain", ProjectConfig.FILE_URL + "/common/publicImage/shexiao/shexiao.png");
		map.put("agreement_url", ProjectConfig.FILE_URL + "/common/publicImage/credit.html");
		result.setData(map);
		return result;
	}

	 @RequestMapping("/addOpenLoan.do")
	 @ResponseBody
	 public ShopsResult  addOpenLoan(
				HttpServletRequest request,
				@RequestParam(value="shop_unique",required=true)String shop_unique,
				String id

				) throws Exception{
			Map<String,Object> map=new HashMap<String, Object>();
			map.put("shop_unique", shop_unique);
			map.put("id", id);

		return loanService.addOpenLoan(map, request);
	}


	 @RequestMapping("/toPayTypeSelect.do")
	 public String toPayTypeSelect(String shop_unique , Double actual_amt,HttpServletRequest request,HttpServletResponse response) {
			request.setAttribute("orderNo", "HK_"+System.nanoTime());
			request.setAttribute("orderAmount", actual_amt);
			return "/WEB-INF/supplierShopping/payTypeSelect.jsp";
	 }

	 @RequestMapping("/queryOrderStatus.do")
	 @ResponseBody
	 public ShopsResult  queryOrderStatus(
				String out_trade_no){
			Map<String,Object> map=new HashMap<String, Object>();
			map.put("sale_list_unique", out_trade_no);

		return loanService.queryOrderStatus(map);
	}



	 /**
	 * 查询还款明细列表
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryLoanReturnList.do")
	@ResponseBody
	public ShopsResult queryLoanReturnList(
			@RequestParam(value="shopMessage",defaultValue="")String shopMessage,
			@RequestParam(value="startTime",defaultValue="")String startTime,
			@RequestParam(value="endTime",defaultValue="")String endTime,
			@RequestParam(value="managerUnique")Long managerUnique,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "15") int pageSize
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("managerUnique", managerUnique);
		map.put("endTime", endTime);
		map.put("startTime", startTime);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		if(shopMessage!=null&&!"".equals(shopMessage)){
			map.put("shopMessage", "%"+shopMessage+"%");
		}
		System.out.println(map.toString());
		return loanService.queryLoanReturnList(map);
	}

	/**
	 * 查询借款明细列表
	 * @param shopMessage 店铺信息
	 * @param startTime 开始查询时间
	 * @param endTime 结束查询时间
	 * @param managerUnique 店铺编号
	 * @param page 页码
	 * @param pageSize 单页查询数量
	 * @param loan_status 还款状态：1、未还清；2、已还清
	 * @return
	 */
	@RequestMapping("/queryLoanList.do")
	@ResponseBody
	public ShopsResult queryLoanList(
			@RequestParam(value="shopMessage",defaultValue="")String shopMessage,
			@RequestParam(value="startTime",defaultValue="")String startTime,
			@RequestParam(value="endTime",defaultValue="")String endTime,
			@RequestParam(value="managerUnique")Long managerUnique,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "15") int pageSize,
			Integer loan_status
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("managerUnique", managerUnique);
		map.put("endTime", endTime);
		map.put("startTime", startTime);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		map.put("loan_status", loan_status);
		if(shopMessage!=null&&!"".equals(shopMessage)){
			map.put("shopMessage", "%"+shopMessage+"%");
		}
		System.out.println(map.toString());
		return loanService.queryLoanList(map);
	}

}
