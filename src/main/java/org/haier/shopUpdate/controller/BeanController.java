package org.haier.shopUpdate.controller;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import lombok.extern.slf4j.Slf4j;
import org.apache.log4j.Logger;
import org.haier.shopUpdate.entity.BeansExchange;
import org.haier.shopUpdate.service.DiamondsService;
import org.haier.shopUpdate.util.common.BeanResult;
import org.haier.shopUpdate.util.common.CommonResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 百货豆
 * <AUTHOR>
 *
 */
@Slf4j
@Controller
@RequestMapping("/bean")
public class BeanController {

	@Resource
	private DiamondsService diamondsService;


	private static Logger log=Logger.getLogger(BeanController.class);



	/**
	 * 百货豆首页（查询豆的数量、支出情况）
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryBeanList.do")
	@ResponseBody
	public BeanResult queryBeanList(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			@RequestParam(value="page",defaultValue="1")Integer page,
			@RequestParam(value = "pageSize", defaultValue = "20") int pageSize,
			@RequestParam(value = "beanType", defaultValue = "1") int beanType,
			HttpServletRequest request
			){


		try {
			Map<String,Object> map=new HashMap<>();
			map.put("shopUnique", shopUnique);
			map.put("startNum", (page-1)*pageSize);
			map.put("pageSize", pageSize);
			map.put("beanType", beanType);
			return diamondsService.queryBeanList(map);
		}catch (Exception e) {
			log.error("百货豆首页数据查询异常:",e);
			return new BeanResult(0, "系统错误！");
		}

	}


	/**
	 * 2 提现按钮（返回可提现金额、提现说明）
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryBeanMoney.do")
	@ResponseBody
	public CommonResult queryBeanMoney(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			HttpServletRequest request
			){
		try {
			Map<String,Object> map=new HashMap<>();
			map.put("shopUnique", shopUnique);
			return diamondsService.queryBeanMoney(map);
		}catch (Exception e) {
			log.error("百货豆提现按钮数据查询异常:",e);
			return new CommonResult(0, "系统错误！");
		}

	}
	/**
	 * 3 提现
	 * @param map
	 * @return
	 */
	@RequestMapping("/addBeanMoney.do")
	@ResponseBody
	public CommonResult addBeanMoney(
			@RequestParam(value="shopUnique",required=true)String shopUnique,
			@RequestParam(value="payMoney",required=true)Double payMoney,
			@RequestParam(value="cardId",required=true)Integer cardId,

			HttpServletRequest request
			){
		try {

			BeansExchange beansExchange=new BeansExchange();
			beansExchange.setShopUnique(shopUnique);
			beansExchange.setPayMoney(payMoney);
			if(cardId==null)
			{
				return new CommonResult(0, "参数异常！");
			}
			beansExchange.setCardId(cardId);
			return diamondsService.addBeanMoney(beansExchange);
		}catch (Exception e) {
			log.error("百货豆提现数据查询异常:",e);
			return new CommonResult(0, "系统错误！");
		}

	}

}
