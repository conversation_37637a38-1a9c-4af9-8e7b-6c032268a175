package org.haier.shopUpdate.controller;

import org.haier.shopUpdate.entity.Inventory;
import org.haier.shopUpdate.service.InventoryService;
import org.haier.shopUpdate.util.ShopsResult;
import org.haier.shopUpdate.util.common.CommonResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Controller
@RequestMapping("/inventory")
public class InventoryController {
    @Resource
    private InventoryService invenService;
//	private static Logger log=Logger.getLogger(InventoryController.class);


    /**
     * 各状态的订单数量查询
     *
     * @param map
     * @return
     */
    @RequestMapping("/queryInvertoryCountByType.do")
    @ResponseBody
    public ShopsResult queryInvertoryCountByType(
            @RequestParam(value = "shopUnique", required = true) Long shopUnique
            , HttpServletRequest request
    ) {

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shopUnique", shopUnique);
        return invenService.queryInvertoryCountByType(map);
    }

    /**
     * 盘点列表的信息查询
     *
     * @param map
     * @return
     * @throws ParseException
     */
    @RequestMapping("queryInventoryList.do")
    @ResponseBody
    public ShopsResult queryInventoryList(
            @RequestParam(value = "shopUnique", required = true) Long shopUnique,
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize,
            String invenMessage,
            @RequestParam(value = "inventoryType", defaultValue = "0") Integer inventoryType,
            @RequestParam(value = "order", defaultValue = "3") Integer order,
            @RequestParam(value = "orderType", defaultValue = "1") Integer orderType,
            String startTime,
            String endTime
            , HttpServletRequest request
    ) throws ParseException {

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shopUnique", shopUnique);
        map.put("startNum", (pageNum - 1) * pageSize);
        map.put("pageSize", pageSize);
        if (invenMessage != null && !invenMessage.equals("")) {
            if (invenMessage.startsWith("00")) {
                invenMessage = invenMessage.substring(1);
            }
            map.put("invenMessage", "%" + invenMessage.trim() + "%");
        }
        if (null != startTime && !startTime.equals("")) {
            map.put("startTime", startTime);
        }
        if (null != endTime && !endTime.equals("")) {
            if (endTime.length() < 12) {
                map.put("endTime", endTime + " 23:59:59");
            } else {
                map.put("endTime", endTime);
            }

        }
        if (inventoryType != 0) {
            map.put("inventoryType", inventoryType);
        }
        if (order == null) {
        } else if (order == 1) {
            map.put("order", "id");
        } else if (order == 2) {
            map.put("order", "staffId");
        } else if (order == 3) {
            map.put("order", "startTime");
        } else if (order == 4) {
            map.put("order", "endTime");
        } else if (order == 5) {
            map.put("order", "inventoryCount");
        } else if (order == 6) {
            map.put("order", "inventoryAmount");
        }
        if (null == orderType) {
        } else if (orderType == 1) {
            map.put("orderType", "DESC");
        } else if (orderType == 2) {
            map.put("orderType", "ASC");
        }
        return invenService.queryInventoryList(map);
    }

    public static Date transferString2Date(String s) {
        Date date = new Date();
        try {
            date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(s);
        } catch (ParseException e) {
            //LOGGER.error("时间转换错误, string = {}", s, e);
        }
        return date;
    }

    /**
     * 盘点详情
     *
     * @param map
     * @return
     */
    @RequestMapping("/queryInventoryDetail.do")
    @ResponseBody
    public ShopsResult queryInventoryDetail(
            @RequestParam(value = "invenId", required = true) Integer invenId
            , HttpServletRequest request) {

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("invenId", invenId);
        return invenService.queryInventoryDetail(map);
    }

    /**
     * 创建新的盘点草稿
     *
     * @param inven
     * @return
     */
    @RequestMapping("/createNewInvenDraft.do")
    @ResponseBody
    public ShopsResult createNewInvenDraft(
            @RequestParam(value = "shopUnique", required = true) Long shopUnique,
            @RequestParam(value = "staffId") Integer staffId
            , HttpServletRequest request
    ) {

        Inventory inven = new Inventory();
        inven.setStaffId(staffId);
        inven.setShopUnique(shopUnique);
        return invenService.createNewInvenDraft(inven);
    }

    /**
     * 扫码查询商品库存信息
     * 收银机获取最小规格商品信息
     *
     * @param shopUnique
     * @return
     */
    @RequestMapping("/queryGoodsByBarcode.do")
    @ResponseBody
    public CommonResult queryGoodsByBarcode(
            @RequestParam(value = "shopUnique", required = true) Long shopUnique,
            @RequestParam(value = "goodsBarcode", required = true) String goodsBarcode
            , HttpServletRequest request
    ) {

        Map<String, Object> map = new HashMap<String, Object>();
        goodsBarcode = goodsBarcode.trim();
        if (goodsBarcode.startsWith("00")) {
            goodsBarcode = goodsBarcode.substring(1);
        }
        map.put("shopUnique", shopUnique);
        map.put("goodsBarcode", goodsBarcode);
        return invenService.queryGoodsByBarcode(map);
    }

    /**
     * 扫码查询商品库存信息
     * 当前商品码商品
     * @param shopUnique
     * @param goodsBarcode
     * @return
     */
    @RequestMapping("/queryGoodsByBarcodeCurrent.do")
    @ResponseBody
    public CommonResult queryGoodsByBarcodeCurrent(
            @RequestParam(value = "shopUnique", required = true) Long shopUnique,
            @RequestParam(value = "goodsBarcode", required = true) String goodsBarcode
            , HttpServletRequest request
    ) {
        goodsBarcode = goodsBarcode.trim();
        if (goodsBarcode.startsWith("00")) {
            goodsBarcode = goodsBarcode.substring(1);
        }
        return invenService.queryGoodsByBarcodeCurrent(shopUnique, goodsBarcode);
    }

    /**
     * 扫码查询商品库存信息
     *
     * @param map
     * @return
     */
    @RequestMapping("/queryGoodsByGoodsMessage.do")
    @ResponseBody
    public ShopsResult queryGoodsByGoodsMessage(
            @RequestParam(value = "shopUnique", required = true) Long shopUnique,
            @RequestParam(value = "goodsMessage", required = true) String goodsMessage,
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize
            , HttpServletRequest request
    ) {

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shopUnique", shopUnique);
        map.put("goodsMessage", "%" + goodsMessage);
        map.put("startNum", (pageNum - 1) * pageSize);
        map.put("pageSize", pageSize);
        return invenService.queryGoodsByGoodsMessage(map);
    }


    /**
     * 添加或更新盘点记录
     *
     * @param map
     * @return
     */
    @RequestMapping("/addNewGoodsStockRecord.do")
    @ResponseBody
    public CommonResult addNewGoodsStockRecord(
            @RequestParam(value = "goodsBarcode") String goodsBarcode,
            Integer invenId,
            @RequestParam(value = "invenCount") Double invenCount,
            @RequestParam(value = "goodsCount") Double goodsCount,
            @RequestParam(value = "profitLoss") Double profitLoss,
            @RequestParam(value = "shopUnique", required = true) Long shopUnique,
            @RequestParam(value = "staffId") Integer staffId,
            //盘库模式  1 增量修改  2覆盖
            Integer stockType
            , HttpServletRequest request
    ) {

        Map<String, Object> map = new HashMap<String, Object>();
        goodsBarcode = goodsBarcode.trim();
        if (goodsBarcode.startsWith("00")) {
            goodsBarcode = goodsBarcode.substring(1);
        }
        map.put("goodsBarcode", goodsBarcode);
        map.put("goodsCount", goodsCount);
        map.put("invenId", invenId);
        map.put("invenCount", invenCount);
        map.put("profitLoss", profitLoss);
        map.put("shopUnique", shopUnique);
        map.put("staffId", staffId);
        map.put("stockType", stockType);
        System.out.println("盘库详情新增记录" + map);
        return invenService.addNewGoodsStockRecord(map, goodsBarcode);
    }

    /**
     * 提交盘点订单
     *
     * @param map
     * @param stockOrigin 操作源头1、手机；2、PC；3、网页
     * @return
     */
    @RequestMapping("/subInventory.do")
    @ResponseBody
    public CommonResult subInventory(
            @RequestParam(value = "invenId") Integer id,
            Integer staffId,
            String remarks
            , HttpServletRequest request
            , Integer stockOrigin
    ) {

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("invenId", id);
        map.put("staffId", staffId);
        map.put("remarks", remarks);
        return invenService.subInventory(map, staffId, stockOrigin);
    }

    /**
     * 某一商品的盘点详情查询
     *
     * @param map
     * @return
     */
    @RequestMapping("/queryGoodsInvenRecord.do")
    @ResponseBody
    public ShopsResult queryGoodsInvenRecord(
            @RequestParam(value = "shopUnique") Long shopUnique,
            @RequestParam(value = "goodsBarcode") String goodsBarcode
            , HttpServletRequest request
    ) {

        Map<String, Object> map = new HashMap<String, Object>();
        goodsBarcode = goodsBarcode.trim();
        if (goodsBarcode.startsWith("00")) {
            goodsBarcode = goodsBarcode.substring(1);
        }
        map.put("goodsBarcode", goodsBarcode);
        map.put("shopUnique", shopUnique);
        return invenService.queryGoodsInvenRecord(map);
    }

    /**
     * 编辑界面，删除勾选商品
     *
     * @param list
     * @return
     */
    @RequestMapping("/deleteInvenGoods.do")
    @ResponseBody
    public ShopsResult deleteInvenGoods(
            @RequestParam(value = "ids") String ids,
            @RequestParam(value = "id") Integer id
            , HttpServletRequest request
    ) {

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("id", id);
        map.put("invenId", id);
        List<Integer> list = new ArrayList<Integer>();
        if (null == ids || ids.equals("")) {
            ShopsResult sr = new ShopsResult();
            sr.setStatus(2);
            sr.setMsg("请传入符合规格的值");
            return sr;
        }
        String[] is = ids.split(",");
        for (String i : is) {
            list.add(Integer.parseInt(i));
        }
        map.put("list", list);

        return invenService.deleteInvenGoods(map);
    }

    /**
     * 删除未提交的盘点订单及详情
     *
     * @param map
     * @return
     */
    @RequestMapping("/deleteDraftLists.do")
    @ResponseBody
    public ShopsResult deleteDraftLists(
            @RequestParam(value = "shopUnique") Long shopUnique,
            @RequestParam(value = "ids") String ids
            , HttpServletRequest request
    ) {

        if (null == ids || ids.equals("")) {
            ShopsResult sr = new ShopsResult();
            sr.setStatus(2);
            sr.setMsg("删除失败，请输入合适的参数ids");
            return sr;
        }
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shopUnique", shopUnique);
        List<String> list = new ArrayList<String>();
        String[] strs = ids.split(";");
        for (String str : strs) {
            list.add(str);
        }
        map.put("list", list);
        return invenService.deleteDraftLists(map);
    }
}
