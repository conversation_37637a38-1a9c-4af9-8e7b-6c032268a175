package org.haier.shopUpdate.controller;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

//import org.apache.log4j.Logger;
import org.haier.shopUpdate.entity.CusCheckout;
import org.haier.shopUpdate.entity.PalmResult;
import org.haier.shopUpdate.log.annotation.RemoteLog;
import org.haier.shopUpdate.log.enums.BusinessType;
import org.haier.shopUpdate.service.CusCheckoutService;
import org.haier.shopUpdate.util.EvaluateResult;
import org.haier.shopUpdate.util.ShopsResult;
import org.haier.shopUpdate.util.ShopsUtil;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 描述： APP端信息相关的controller
 * 包括注册会员信息、修改会员信息、查询会员信息等
 * 方法顺序：增、删、改、查
 * 
 */
@Controller
@RequestMapping("/cuscheckout")
public class CusCheckoutContorller {

	@Resource
	private CusCheckoutService service;//service注解
//	private static Logger log=Logger.getLogger(CusCheckoutContorller.class);
	
	/**
	 * 1、添加或更新会员信息
	 * @param cusUnique
	 * @param cusName
	 * @param cusPhone
	 * @param shopUnique
	 * @return
	 */
	@RequestMapping("/addCusNP.do")
	@ResponseBody
	public PalmResult addCusNP(String cusUnique,String cusName,String cusPhone,String shopUnique) {
		return service.addCusNP(cusUnique, shopUnique, cusName, cusPhone);
	}
	
	/**
	 * 添加会员信息
	 * @param cus：用户实体类
	 * @return
	 */
	@RequestMapping("/addCus.do")
	@ResponseBody
	public PalmResult addCus(String cus_unique,String cusName,
							 String cusPhone ,Long shopUnique,
							 String cusEmail,String cusQQ,
							 String cusType,String cusBirthday,
							 String cusWeixin,String cusSex,
							 String cusPassword,
							 String cus_status,
							 String cus_remark,
							 Integer cus_level_id,
							 HttpServletRequest request){
		
		CusCheckout cus=new CusCheckout();
		cus.setCusUnique(cus_unique);
		cus.setCusName(cusName);
		cus.setCusPhone(cusPhone);
		cus.setShopUnique(shopUnique);
		cus.setCusEmail(cusEmail);
		cus.setCusQQ(cusQQ);
		cus.setCusType(cusType);
		cus.setCusBirthday(cusBirthday);
		cus.setCusWeixin(cusWeixin);
		cus.setCusSex(cusSex==null?1:Integer.parseInt(cusSex));
		SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
		String cusRegeditDate= sdf.format(new Date());
		cus.setCusRegeditDate(cusRegeditDate);
		cus.setCusLevelId(cus_level_id);
		if("".equals(cusPassword)||cusPassword==null){
			
		}else{
			cus.setCusPassword(ShopsUtil.string2MD5(cusPassword));
		}
		cus.setCus_status(cus_status);
		cus.setCus_remark(cus_remark);
		System.out.println("添加会员>>>>>>>cusAccount:"+cus.toString());
		//验证卡号是否存在
		Map<String, Object> params=new HashMap<String, Object>();
		params.put("cus_unique", cus_unique);
		params.put("shopUnique", shopUnique);
		System.out.println("手机号已经存在"+cus.toString());
		if(service.findCusById(cus_unique, shopUnique,1).getStatus()==1){
			PalmResult result=new PalmResult();
			result.setStatus(0);
			result.setMsg("卡号已存在");
			return result;
		}else if(cusPhone!=null&&!"".equals(cusPhone)&&service.findCusByCusPhone(cusPhone, shopUnique).getStatus()==1){
			PalmResult result=new PalmResult();
			result.setStatus(0);
			result.setMsg("手机号已存在");
			return result;
		}else{
			return service.addCus(cus,request);
		}
	}
	/**
	 * 查询会员列表
	 * @return
	 */
	@RequestMapping("/getCustList.do")
	@ResponseBody
	public PalmResult getCustList(String  searchKey,Long shopUnique,int pages,int perpage,HttpServletRequest request){
		
		return service.getCustList(searchKey,shopUnique,pages,perpage);
	}
	/**
	 * 查询会员详情
	 * @return
	 */
	@RequestMapping("/findCusById.do")
	@ResponseBody
	public PalmResult findCusById(String cus_unique,Long shopUnique,HttpServletRequest request,
			@RequestParam(defaultValue="2")Integer searchType){
		
		return service.findCusById(cus_unique,shopUnique,searchType);
	}
	/**
	 * 查询会员详情下半部分
	 * @return
	 */
	@RequestMapping("/queryCusDetailDown.do")
	@ResponseBody
	public PalmResult queryCusDetailDown(String cus_unique,Long shopUnique,HttpServletRequest request){
		
		return service.queryCusDetailDown(cus_unique,shopUnique);
	}
	/**
	 * 修改会员密码
	 * @return
	 */
	@RequestMapping("/editCusPassword.do")
	@ResponseBody
	public PalmResult editCusPassword(String cus_unique,Long shopUnique,String cus_password,HttpServletRequest request){
		
		return service.editCusPassword(cus_unique,shopUnique,cus_password);
	}
	/**
	 * 修改会员状态
	 * @return
	 */
	@RequestMapping("/editCusStatus.do")
	@ResponseBody
	public PalmResult editCusStatus(String cus_unique,Long shopUnique,String cus_status,HttpServletRequest request){
		
		return service.editCusStatus(cus_unique,shopUnique,cus_status);
	}
	/**
	 * 修改会员状态
	 * @return
	 */
	@RequestMapping("/editCusLevel.do")
	@ResponseBody
	public PalmResult editCusLevel(String cus_unique,Long shopUnique,Integer cus_level_id,HttpServletRequest request){
		
		return service.editCusLevel(cus_unique,shopUnique,cus_level_id);
	}
	/**
	 * 修改会员信息
	 * @return json
	 */
	@RequestMapping("/editCus.do")
	@ResponseBody
	@RemoteLog(title = "修改会员信息",businessType = BusinessType.UPDATE,isSendDingDingTalk = true)
	public PalmResult editCus(
			@RequestParam(value="cus_unique",required=true)String cus_unique,
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			@RequestParam(value="cusName",required=true)String cusName,
			 String cusPhone ,
			 String cusEmail,String cusQQ,
			 String cusWeixin ,String cusSex,
			 String cusPassword,
			 String cus_status,
			 String cus_remark,
			 Integer cus_level_id,
			 String cusType,
			 String cusBirthday,
			 String imageMsg,
			 String imgFormat,
			 String validityStart,
			 String validityEnd,
			 HttpServletRequest request){
		
		CusCheckout cus=new CusCheckout();
		cus.setShopUnique(shopUnique);
		cus.setCusUnique(cus_unique);
		cus.setCusName(cusName);
		cus.setCusPhone(cusPhone);
		cus.setCusEmail(cusEmail);
		cus.setCusQQ(cusQQ);
		cus.setCusWeixin(cusWeixin);
		cus.setCusSex(cusSex==null?1:Integer.parseInt(cusSex));
		cus.setCusLevelId(cus_level_id);
		cus.setCusBirthday(cusBirthday);
		cus.setImageMsg(imageMsg);
		cus.setImgFormat(imgFormat);
		cus.setValidityEnd(validityEnd);
		cus.setValidityStart(validityStart);
		System.out.println("上传的图片格式为：：："+imgFormat);
		//查询是否是连锁店铺需要会员同步会员保存到总店
		Map<String,Object> cusSynchroMap=service.queryCusSynchroStatus(shopUnique.toString());
		if(cusSynchroMap!=null){
			shopUnique=Long.parseLong((String)cusSynchroMap.get("manager_unique"));
			cus.setShopUnique(shopUnique);
		}
		@SuppressWarnings("unchecked")
		Map<String, Object> result=(Map<String, Object>)service.findCusById(cus_unique,shopUnique,1).getData();
		if(null==result||result.isEmpty()){
			return new PalmResult(2, "该用户信息不存在");
		}
		String  cusPassword2=(String) result.get("cusPassword");
		if(cusPassword2.equals(cusPassword)){
			cus.setCusPassword(cusPassword);
		}else if(!"".equals(cusPassword)&&cusPassword!=null){
			cus.setCusPassword(ShopsUtil.string2MD5(cusPassword));
		}else{
			cus.setCusPassword("");
		}
		
		cus.setCus_status(cus_status);
		cus.setCus_remark(cus_remark);
		cus.setCusType(cusType);
		return service.editCus(cus,request);
	}
	
	/**
	 * 查询消息列表
	 * @return
	 */
	@RequestMapping("/getMsgList.do")
	@ResponseBody
	public PalmResult getMsgList(
			@RequestParam(value="shopUnique")Long shopUnique,
			@RequestParam(value="shopMsgType",defaultValue="1")Integer shopMsgType,
			@RequestParam(value="pages",defaultValue="1")Integer  pages,
			@RequestParam(value="perpage",defaultValue="10")Integer perpage,HttpServletRequest request){
		
		return service.getMsgList(shopUnique,shopMsgType,pages,perpage);
	}
	
	/**
	 * 修改消息状态为已读
	 * @return json
	 */
	@RequestMapping("/editMsgById.do")
	@ResponseBody
	public PalmResult editMsgById(Long shopMsgId,HttpServletRequest request){
		
		return service.editMsgById(shopMsgId);
	}
	
	/**
	 * 用户评价和店铺回复
	 * @return
	 */
	@RequestMapping("/userEvaluate.do")
	@ResponseBody
	public EvaluateResult userEvaluate(
			int evaluateUserId,
			int evaluateScore,
			int goodsId,
			String evaluateContent,
			Long evaluateId,
			int userType,
			HttpServletRequest request
				){
		
		HashMap<String, Object> map=new HashMap<String, Object>();
		map.put("evaluateUserId", evaluateUserId);
		map.put("evaluateScore", evaluateScore);
		map.put("goodsId", goodsId);
		map.put("evaluateContent", evaluateContent);
		map.put("evaluateId", evaluateId);
		map.put("userType", userType);
		return service.userEvaluate(evaluateUserId,evaluateScore,goodsId,evaluateContent,
				evaluateId,userType,request);
	}
	/**
	 * 查询评价列表
	 * @return
	 */
	@RequestMapping("/getEvaluateList.do")
	@ResponseBody
	public EvaluateResult getEvaluateList(
			Long goodsId,
			int pages,
			int perpage,HttpServletRequest request
			){
		
		return service.getEvaluateList(goodsId,pages,perpage);
	}
	/**
	 * 查询轮播图
	 * @return
	 */
	@RequestMapping("/getImages.do")
	@ResponseBody
	public PalmResult getImages(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,HttpServletRequest request){
		
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		return service.getImages(map);
	}

	
	/**
	 * 查询促销商品
	 * @return
	 */
	@RequestMapping("/getPromotionList.do")
	@ResponseBody
	public PalmResult getPromotionList(
			Long supplierUnique,
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			@RequestParam(value="pages",defaultValue="1")int pages,
			@RequestParam(value="perpage",defaultValue="20")int perpage
			,HttpServletRequest request
			){
		
		return service.getPromotionList(supplierUnique,shopUnique,pages,perpage);
	}
	/**
	 * 查询商家头条
	 * @return
	 */
	@RequestMapping("/getBusinessHead.do")
	@ResponseBody
	public PalmResult getBusinessHead(Long pages,
			Long perpage,HttpServletRequest request){
		return service.getBusinessHead(pages,perpage);
	}
	/**
	 * 查询头条促销商品
	 * @return
	 */
	@RequestMapping("/getBusinessHeadGoods.do")
	@ResponseBody
	public PalmResult getBusinessHeadGoods(
			Long BusinessHeadId,
			int pages,
			int perpage,HttpServletRequest request
			){
		
		return service.getBusinessHeadGoods(BusinessHeadId,pages,perpage);
	}
	/**
	 * 更新头条点击量
	 * @return
	 */
	@RequestMapping("/updateClickCount.do")
	@ResponseBody
	public PalmResult updateClickCount(Long BusinessHeadId,HttpServletRequest request){
		
		return service.updateClickCount(BusinessHeadId);
	}
	/**
	 * 促销商品的采购
	 * @param map
	 * @return
	 */
	@RequestMapping("/promotionGoodsPurchase.do")
	@ResponseBody
	public ShopsResult  promotionGoodsPurchase(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			@RequestParam(value="goodsBarcode",required=true)String goodsBarcode,
			@RequestParam(value="supplierUnique",required=true)Long supplierUnique,
			@RequestParam(value="detailCount",required=true)Double detailCount
			,HttpServletRequest request){
		
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique",shopUnique);
		map.put("supplierUnique", supplierUnique);
		map.put("goodsBarcode", goodsBarcode);
		map.put("detailCount", detailCount);//采购的数量
		return service.promotionGoodsPurchase(map);
	}
	
	
	/**
	 * 促销商品的采购-添加新商品并加入购物车
	 * @param shopUnique
	 * @param goodsBarcode
	 * @param supplierUnique
	 * @param goodsPrice
	 * @return
	 */
	@RequestMapping("/newPromotionGoodsPurchase.do")
	@ResponseBody
	public ShopsResult newPromotionGoodsPurchase(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			@RequestParam(value="goodsBarcode",required=true)String goodsBarcode,
			@RequestParam(value="supplierUnique",required=true)Long supplierUnique,
			@RequestParam(value="goodsPrice",required=true)Double goodsPrice
			,HttpServletRequest request
			){
		
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique",shopUnique);
		map.put("supplierUnique", supplierUnique);
		map.put("goodsBarcode", goodsBarcode);
		map.put("detailCount", 1);//采购的数量
		map.put("goodsPrice", goodsPrice);
		map.put("goodsSalePrice", goodsPrice);
		return service.newPromotionGoodsPurchase(map);
	}
	
	/**
	 * 会员充值消费记录（缺少会员积分兑换记录）
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryCusConRecord.do")
	@ResponseBody
	public ShopsResult queryCusConRecord(
			@RequestParam(value="cus_unique")String cus_unique,
			@RequestParam(value="shopUnique")Long shopUnique,
			String datetime,
			String endtime,
			Integer type,
			Integer staffId,
			@RequestParam(value="cus_type",defaultValue="-1")int cus_type
			,HttpServletRequest request
			){
		
		
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("cus_unique", cus_unique);
		map.put("shopUnique", shopUnique);
		if(datetime!=null&&!"".equals(datetime)){
			map.put("datetime", datetime);
		}
		if(endtime!=null&&!"".equals(endtime)){
			map.put("endtime", endtime+" 23:59:59");
		}
		if(null==type||type==-1){
		}else{
			map.put("consumptionType", type);
		}
		if(null==staffId||staffId==-1){
		}else{
			map.put("staffId", staffId);
		}
		if(cus_type!=-1){
			map.put("cus_type", cus_type);
		}
//		System.out.println(map);
		return service.queryCusConRecord(map);
	}
	
	
	/**
	 * 会员消费方式查询
	 * @return
	 */
	@RequestMapping("/queryConType.do")
	@ResponseBody
	public ShopsResult queryConType(HttpServletRequest request){
		
		return service.queryConType();
	}
	
	/**
	 * 会员等级
	 */
	@RequestMapping("/getMemberLevel.do")
	@ResponseBody
	public ShopsResult getMemberLevel(
			@RequestParam(value="shopUnique",required=true)Long shopUnique
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);

		return service.getMemberLevel(map);
	}
	
	@RequestMapping("/updateMemberLevel.do")
	@ResponseBody
	public ShopsResult updateMemberLevel(
			@RequestParam(value="cus_level_id")String cus_level_id,
			@RequestParam(value="cus_level_discount")String cus_level_discount,
			@RequestParam(value="cus_level_points")String cus_level_points
			){
		Map<String,Object> map =new HashMap<String, Object>();
		map.put("cus_level_id", cus_level_id);
		map.put("cus_level_discount", cus_level_discount);
		map.put("cus_level_points", cus_level_points);
		return service.updateMemberLevel(map);
	}
	
	@RequestMapping("/queryCusCheckOut.do")
	@ResponseBody
	public ShopsResult queryCusCheckOut(
			@RequestParam(value="shop_unique",required=true)Long shop_unique,
			@RequestParam(value = "page", defaultValue = "0") int page,
			@RequestParam(value = "pageSize", defaultValue = "8") int pageSize,
			String cus_level_id
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		if(cus_level_id != null && !cus_level_id.equals("")){
			map.put("cus_level_id", cus_level_id);
		}
		
		return service.queryCusCheckOut(map);
	}
	@RequestMapping("/queryCusRechargeLog.do")
	@ResponseBody
	public ShopsResult queryCusRechargeLog(
			@RequestParam(value="shop_unique",required=true)Long shop_unique,
			@RequestParam(value = "page", defaultValue = "0") int page,
			@RequestParam(value = "pageSize", defaultValue = "8") int pageSize,
			String message,
			String start_time,
			String end_time
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		if(message != null && !message.equals("")){
			map.put("message", message);
		}
		if(start_time != null && !start_time.equals("")){
			map.put("start_time", start_time);
		}
		if(end_time != null && !end_time.equals("")){
			map.put("end_time", end_time+" 23:59:59");
		}
		
		return service.queryCusRechargeLog(map);
	}
}
