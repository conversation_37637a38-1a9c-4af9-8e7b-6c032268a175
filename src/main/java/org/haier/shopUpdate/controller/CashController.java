package org.haier.shopUpdate.controller;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import lombok.extern.slf4j.Slf4j;
import org.haier.shopUpdate.params.CreateOrderParams;
import org.haier.shopUpdate.service.CashService;
import org.haier.shopUpdate.service.DeliveryService;
import org.haier.shopUpdate.util.HandleMessyCode;
import org.haier.shopUpdate.util.PurResult;
import org.haier.shopUpdate.util.ShopsResult;
import org.haier.shopUpdate.util.task.ServletsUtil;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 安卓收银机
 * <AUTHOR>
 *
 */
@Slf4j
@Controller
@RequestMapping("/cash")
public class CashController {
	@Resource
	private CashService cashService;
	@Resource
	private DeliveryService deliveryService;

	@RequestMapping("/queryPayMethod.do")
	@ResponseBody
	public ShopsResult queryPayMethod(
			) {
		return cashService.queryPayMethod();
	}

	@RequestMapping("/uploadFile.do")
	@ResponseBody
	public ShopsResult uploadFile(String shop_unique,HttpServletRequest request){
		try {
			return cashService.uploadFile(request, shop_unique);
		}catch (Exception e) {
			log.error("上传失败",e);
			return new ShopsResult(0, "保存失败");
		}
	}

	@RequestMapping("/queryCarList.do")
	@ResponseBody
	public ShopsResult queryCarList(
			@RequestParam(value="shop_unique",defaultValue="")String shop_unique,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "15") int pageSize
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);

		return cashService.queryCarList(map);
	}
	@RequestMapping("/addCar.do")
	@ResponseBody
	public ShopsResult addCar(
			@RequestParam(value="shop_unique")String shop_unique,
			@RequestParam(value="bank")String bank,
			@RequestParam(value="bank_card")String bank_card,
			@RequestParam(value="bank_name")String bank_name,
			@RequestParam(value="bank_phone")String bank_phone,
			@RequestParam(value="default_type")String default_type,
			@RequestParam(value="card_id")String card_id
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("bank", bank);
		map.put("bank_card", bank_card);
		map.put("bank_name", bank_name);
		map.put("bank_phone", bank_phone);
		map.put("default_type", default_type);
		if("1".equals(default_type)){
			cashService.updateBankRoot(map);
		}

		if(card_id!=null&&!"".equals(card_id)){
			map.put("card_id", card_id);
			return cashService.updateCar(map);

		}else{
			return cashService.addCar(map);
		}
	}
	@RequestMapping("/deleteCar.do")
	@ResponseBody
	public ShopsResult deleteCar(
			@RequestParam(value="card_id")String card_id
			){
		Map<String,Object> map=new HashMap<String, Object>();
		if(card_id!=null&&!"".equals(card_id)){
			map.put("card_id", card_id);
			return cashService.deleteCar(map);
		}
		return null;
	}

	@RequestMapping("/queryRechargeConfigList.do")
	@ResponseBody
	public ShopsResult queryRechargeConfigList(HttpServletRequest request,
			@RequestParam(value="page",defaultValue="1")Integer page,
			@RequestParam(value="limit",defaultValue="15")Integer limit){
		ShopsResult result = new ShopsResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			params.put("limit", limit);
			params.put("page", (page-1)*limit);
			result=cashService.queryRechargeConfigList(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("查询失败!");
			log.error("查询失败，请检查异常信息：",e);
		}
		return result;
	}

	/**
	 * 新增配置
	 */
	@RequestMapping("/addCustomerRechargeConfig.do")
	@ResponseBody
	public ShopsResult addCustomerRechargeConfig(HttpServletRequest request){
		ShopsResult result = new ShopsResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			result=cashService.InsertRechargeConfig(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("操作失败!");
			log.error("操作失败，请检查异常信息：",e);
		}
		return result;
	}

	/**
	 * 更新配置
	 */
	@RequestMapping("/updateCustomerRechargeConfig.do")
	@ResponseBody
	public ShopsResult updateCustomerRechargeConfig(HttpServletRequest request){
		ShopsResult result = new ShopsResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			result=cashService.updateRechargeConfig(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("操作失败!");
			log.error("操作失败，请检查异常信息：",e);
		}
		return result;
	}

	@RequestMapping("/queyRechargeLog.do")
	@ResponseBody
	public ShopsResult queyRechargeLog(HttpServletRequest request,
			@RequestParam(value="page",defaultValue="1")Integer page,
			@RequestParam(value="limit",defaultValue="15")Integer limit){
		ShopsResult result = new ShopsResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			params.put("limit", limit);
			params.put("page", (page-1)*limit);
			result=cashService.queyRechargeLog(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("查询失败!");
			log.error("查询失败，请检查异常信息：",e);
		}
		return result;
	}

	/**
	 * 会员等级
	 */
	@RequestMapping("/getMemberLevel.do")
	@ResponseBody
	public ShopsResult getMemberLevel(
			@RequestParam(value="shop_unique",required=true)Long shop_unique,
			@RequestParam(value = "page", defaultValue = "0") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);

		return cashService.getMemberLevel(map);
	}
	@RequestMapping("/setMemberLevel.do")
	@ResponseBody
	public ShopsResult setMemberLevel(
			@RequestParam(value="cus_level_id",required=true)String cus_level_id,
			@RequestParam(value="cus_level_points",required=true)String cus_level_points,
			@RequestParam(value="cus_level_discount",required=true)String cus_level_discount
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("cus_level_id", cus_level_id);
		map.put("cus_level_points", cus_level_points);
		map.put("cus_level_discount", cus_level_discount);

		return cashService.setMemberLevel(map);
	}

	@RequestMapping("/queryCusCheckOut.do")
	@ResponseBody
	public ShopsResult queryCusCheckOut(
			@RequestParam(value="shop_unique",required=true)Long shop_unique,
			@RequestParam(value = "page", defaultValue = "0") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize,
			String cus_level_id
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);

		if(cus_level_id != null && !cus_level_id.equals("")){
			map.put("cus_level_id", cus_level_id);
		}
		return cashService.queryCusCheckOut(map);
	}
	@RequestMapping("/queryPointUseList.do")
	@ResponseBody
	public ShopsResult queryPointUseList(
			@RequestParam(value="shop_unique",required=true)Long shop_unique,
			@RequestParam(value = "page", defaultValue = "0") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize,
			String message,
			String start_time,
			String end_time
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);

		if(message != null && !message.equals("")){
			map.put("message", "%"+message+"%");
		}
		if(start_time != null && !start_time.equals("")){
			map.put("start_time", start_time);
		}
		if(end_time != null && !end_time.equals("")){
			map.put("end_time", end_time);
		}
		return cashService.queryPointUseList(map);
	}

	@ResponseBody
	@RequestMapping("/queryOrderListByPage2.do")
	public ShopsResult queryOrderListByPage2(HttpServletRequest request,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int limit){
		Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		params.put("pageSize", limit);
		params.put("startNum", (page-1)*limit);
		if(params.get("goodsMessage")!=null&&!"".equals(params.get("goodsMessage"))){
			params.put("goodsMessage", "%"+params.get("goodsMessage")+"%");
		}
		return cashService.queryOrderListByPage2(params);
	}

	@RequestMapping("/queryTransactionList2.do")
	@ResponseBody
	public ShopsResult queryTransactionList2(HttpServletRequest request,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int limit){
		Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		params.put("pageSize", limit);
		params.put("startNum", (page-1)*limit);
		return cashService.queryTransactionList2(params);
	}

	@RequestMapping("/queryShopBeansPromation.do")
	@ResponseBody
	public ShopsResult queryShopBeansPromation(HttpServletRequest request,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int limit){
		Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		params.put("pageSize", limit);
		params.put("startNum", (page-1)*limit);
		return cashService.queryShopBeansPromation(params);
	}
	@RequestMapping("/queryPcActivityMenuList.do")
	@ResponseBody
	public ShopsResult queryPcActivityMenuList(HttpServletRequest request
			){
		Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		return cashService.queryPcActivityMenuList(params);
	}
	@RequestMapping("/queryGoldByShop2.do")
	@ResponseBody
	public ShopsResult queryGoldByShop2(HttpServletRequest request
			){
		Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		return cashService.queryGoldByShop2(params);
	}
	@RequestMapping("/saveBindingJiGuang.do")
	@ResponseBody
	public ShopsResult saveBindingJiGuang(HttpServletRequest request
			){
		Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		return cashService.saveBindingJiGuang(params);
	}
	@RequestMapping("/updateShopsPwd.do")
	@ResponseBody
	public ShopsResult updateShopsPwd(HttpServletRequest request
			){
		Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		return cashService.updateShopsPwd(params);
	}
	@RequestMapping("/queryBankName.do")
	@ResponseBody
	public ShopsResult queryBankName(HttpServletRequest request
			){
		return cashService.queryBankName();
	}
	@RequestMapping("/queryDownload.do")
	@ResponseBody
	public ShopsResult queryDownload(HttpServletRequest request
			){
		return cashService.queryDownload();
	}
	@RequestMapping("/queryShopAppDownload.do")
	@ResponseBody
	public ShopsResult queryShopAppDownload(HttpServletRequest request
			){
		return cashService.queryShopAppDownload();
	}
	@RequestMapping("/queryFuncitonImage.do")
	@ResponseBody
	public ShopsResult queryFuncitonImage(HttpServletRequest request
			){
		Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
		return cashService.queryFuncitonImage(params);
	}

	/**
	 * 商品信息查询
	 * @param shop_unique 店铺编号
	 * @param goods_message 输入的商品信息
	 * @param goods_kind_unique 商品分类编号
	 * @param shelf_state 上架状态：1、已上架；2、已下架
	 * @param time 时长
	 */
	@RequestMapping("/queryShelfStateGoodsMessage.do")
	@ResponseBody
	public PurResult queryShelfStateGoodsMessage(HttpServletRequest request,
			@RequestParam(value="page",defaultValue="1")Integer page,
			@RequestParam(value="limit",defaultValue="15")Integer limit){

		PurResult result = new PurResult();
		try {
			Map<String, Object> params = HandleMessyCode.handle(ServletsUtil.getParameters(request));
			params.put("limit", limit);
			params.put("page", (page-1)*limit);
			result=cashService.queryShelfStateGoodsMessage(params);
		} catch (Exception e) {
			result.setStatus(0);
			result.setMsg("查询失败!");
			log.error("商品信息查询失败,异常信息：",e);
		}
		return result;
	}
	/**
	 * 修改商品上下架状态
	 * @param goods_ids 商品id集合，已逗号隔开
	 * @param shelf_state 线上上架状态：1、已上架；2、已下架
	 * @return
	 */
	@RequestMapping("updateShelfState.do")
	@ResponseBody
	public ShopsResult updateShelfState(
			@RequestParam(value="goods_ids",required=true)String goods_ids,
			@RequestParam(value="shelf_state",required=true)String shelf_state){
		return cashService.updateShelfState(goods_ids, shelf_state);
	}

	/**
	 * 查询配送信息
	 */
	@RequestMapping("/queryShopDelivery.do")
	@ResponseBody
	public ShopsResult queryShopDelivery(String shop_unique){
		ShopsResult result=new ShopsResult();
		//获取店铺配送信息
		Map<String ,Object> deliveryInfo = cashService.queryShopDelivery(shop_unique);
		result.setData(deliveryInfo);
		result.setStatus(1);
		return result;
	}

	/**
	 * 修改配送信息
	 * @param shop_unique 店铺唯一标示
	 * @param distribution_scope 配送范围，单位米
	 * @param delivery_type 配送方式：0自配送 1美团配送 2蜂鸟配送
	 * @param take_fee 起送费（元）
	 * @param take_free_price 免配送费价格（元）
	 * @param take_estimate_time 配送预计时长（分钟）
	 * @param shop_take_price 商家自配送配送费
	 * @param is_order_taking 是否自动接单：0是 1否
	 * @param subsidy_delivery_price 每单补贴配送费
	 * @return
	 */
	@RequestMapping("/updateShopDelivery.do")
	@ResponseBody
	public ShopsResult updateShopDelivery(
			String shop_unique,String distribution_scope,String delivery_type,
			String take_fee,String take_free_price,String take_estimate_time,
			String shop_take_price,String is_order_taking,String subsidy_delivery_price){
		ShopsResult shopsResult = new ShopsResult();
		try {
			Map<String ,Object> params = new HashMap<String, Object>();
			params.put("shop_unique", shop_unique);
			if(distribution_scope != null && Double.parseDouble(distribution_scope)>10){
				shopsResult.setStatus(0);
				shopsResult.setMsg("配送范围不能超过10千米");
				return shopsResult;
			}
			params.put("distribution_scope", distribution_scope);
			params.put("delivery_type", delivery_type);
			params.put("take_fee", take_fee);
			params.put("take_free_price", take_free_price);
			params.put("take_estimate_time", take_estimate_time);
			params.put("shop_take_price", shop_take_price);
			params.put("is_order_taking", is_order_taking);
			params.put("subsidy_delivery_price", subsidy_delivery_price);
			shopsResult = cashService.updateShopDelivery(params);
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());
		}
		return shopsResult;
	}

	/**
	 * 店铺骑手列表分页查询
	 * @param map
	 * @return
	 */
	@RequestMapping("/quertShopCourierList.do")
	@ResponseBody
	public PurResult quertShopCourierList(
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize,
			String shop_unique,
			String message){
		PurResult result = new PurResult();
		try {
			Map<String,Object> map=new HashMap<>();
			map.put("shop_unique", shop_unique);
			map.put("pageSize", pageSize);
			map.put("startNum", (page-1)*pageSize);
			if(message!=null&&!"".equals(message)){
				map.put("message","%"+message+"%");
			}
			result = cashService.getShopCourierList(map);
		} catch (Exception e) {
			log.error("查询店铺骑手列表异常",e);
			result.setStatus(0);
			result.setMsg("异常："+e.getMessage());
		}

		return result;
	}

	/**
	 * 添加店铺骑手
	 * @param shop_unique 店铺唯一标示
	 * @param courier_name 配送员姓名
	 * @param courier_phone 配送员电话
	 * @param id 原骑手ID
	 * @param delete_courier
	 * @return
	 */
	@RequestMapping("/addShopCourier.do")
	@ResponseBody
	public ShopsResult addShopCourier(String shop_unique,Integer id,
			String courier_name,String courier_phone,Integer delete_courier){
		ShopsResult shopsResult = new ShopsResult();
		try {
			Map<String,Object> map=new HashMap<>();
			map.put("shop_unique", shop_unique);
			map.put("courier_name", courier_name);
			map.put("courier_phone", courier_phone);
			map.put("id", id);

			if(id != null && delete_courier != null && delete_courier == 1) {
				return cashService.deleteShopCourier(id + "");
			}else if(id != null) {
				shopsResult = cashService.updateShopCourier(map);
			}else {
				if(null == shop_unique || null == courier_name || null == courier_phone) {
					shopsResult.setStatus(0);
					shopsResult.setMsg("缺少必要的参数");
					return shopsResult;
				}
				shopsResult = cashService.addShopCourier(map);
			}
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());
		}
		return shopsResult;
	}

	/**
	 * 修改店铺骑手
	 * @param id 唯一标示
	 * @param courier_name 配送员姓名
	 * @param courier_phone 配送员电话
	 * @return
	 */
	@RequestMapping("/updateShopCourier.do")
	@ResponseBody
	public ShopsResult updateShopCourier(String id,
			String courier_name,String courier_phone){
		ShopsResult shopsResult = new ShopsResult();
		try {
			Map<String,Object> map=new HashMap<>();
			map.put("id", id);
			map.put("courier_name", courier_name);
			map.put("courier_phone", courier_phone);
			shopsResult = cashService.updateShopCourier(map);
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());
		}
		return shopsResult;
	}

	/**
	 * 删除店铺骑手
	 * @param courier_id 配送员id
	 * @return
	 */
	@RequestMapping("/deleteShopCourier.do")
	@ResponseBody
	public ShopsResult deleteShopCourier(String courier_id){
		ShopsResult shopsResult = new ShopsResult();
		try {
			shopsResult = cashService.deleteShopCourier(courier_id);
		} catch (Exception e) {
			shopsResult.setStatus(0);
			shopsResult.setMsg("异常："+e.getMessage());
		}
		return shopsResult;
	}

	/**
	 * 查询优惠券列表
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryShopCouponList.do")
	@ResponseBody
	public PurResult queryShopCouponList(
			@RequestParam(value="shop_unique",required=false)String shop_unique,
			String start_time,
			String end_time,
			String type,
			String coupon_name,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize
			){
		return cashService.queryShopCouponList(shop_unique,start_time,end_time,page,pageSize,coupon_name,type);
	}

	/**
	 * 新增优惠券
	 * @param shop_unique 店铺编号
	 * @param start_time 开始时间
	 * @param end_time 结束时间
	 * @param meet_amount 满足金额
	 * @param coupon_amount 优惠金额
	 * @param type 1：全品类 2：仅限非折扣商品
	 * @param is_time 是否分时段优惠券：1否 1是
	 * @param is_daily 分时段优惠券是否可每天使用：1否 2是
	 * @param daily_num 分时段优惠券每天使用次数
	 * @param times 时段集合，以逗号隔开
	 * @param is_auto_grant 是否自动发放：1否 2是
	 * @param is_grant_num 是否限制发放次数：-1 不发放 1发放
	 * @param grant_num 发放次数
	 * @param exclusive_type 专享优惠券类型：1联通专享
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/addShopCoupon.do")
	public PurResult addShopCoupon(String shop_unique, String start_time, String end_time, String meet_amount,
			String coupon_amount, String type, Integer is_time, Integer is_daily, Integer daily_num, String times,
			Integer is_auto_grant, Integer is_grant_num, Integer grant_num, Integer exclusive_type, String days,
			Integer is_online, String coupon_name){
		System.out.println(days);
		return cashService.addShopCoupon(shop_unique,start_time,end_time,
				meet_amount,coupon_amount,type,
				is_time,is_daily,daily_num,
				times,is_auto_grant,is_grant_num,
				grant_num,exclusive_type,days,is_online,coupon_name);
	}

	/**
	 * 停用启用优惠券
	 * @param supplier_unique
	 * @param uniques
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/deleteShopCoupon.do")
	public PurResult deleteShopCoupon(
			String shop_coupon_id,
			int delete_status
			){
		return cashService.deleteShopCoupon( shop_coupon_id,delete_status);
	}

	/**
	 * 查询优惠券领取记录
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryCouponRecord.do")
	@ResponseBody
	public PurResult queryCouponRecord(	String start_time,
			String end_time,
			Integer use_status,
			String cus_unique,
			@RequestParam(value="shop_coupon_id",required=true)String shop_coupon_id,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("pageSize", pageSize);
		if(start_time != null && !start_time.equals("")){
			map.put("start_time", start_time);
		}
		if(end_time != null && !end_time.equals("")){
			map.put("end_time", end_time);
		}
		if(cus_unique != null && !cus_unique.equals("")){
			map.put("cus_unique", "%"+cus_unique+"%");
		}
		if(use_status != null && !use_status.equals("")){
			map.put("use_status", use_status);
		}
		map.put("startNum", (page-1)*pageSize);
		map.put("shop_coupon_id", shop_coupon_id);
		return cashService.queryCouponRecord(map);
	}

	/**
	 * 查询促销列表
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryPromotionList.do")
	@ResponseBody
	public PurResult queryPromotionList(
			@RequestParam(value="shop_unique",required=true)Long shop_unique,
			String type,
			String start_time,
			String end_time,
			String promotion_activity_name,
			String time_status,
			String status,
			@RequestParam(value="activity_range",defaultValue = "1")String activity_range,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		map.put("type", type);
		map.put("activity_range", activity_range);
		map.put("time_status", time_status);
		map.put("status", status);
		if(start_time != null && !start_time.equals("")){
			map.put("start_time", start_time);
		}
		if(end_time != null && !end_time.equals("")){
			map.put("end_time", end_time);
		}
		if(promotion_activity_name!=null&&!"".equals(promotion_activity_name)){
			map.put("promotion_activity_name", "%"+promotion_activity_name+"%");
		}
		return cashService.queryPromotionList(map);
	}

	/**
	 * 提交商品打折活动
	 * @param supplier_unique
	 * @param uniques
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/submitSupplierStorageOrder.do")
	public PurResult submitSupplierStorageOrder(
			String shop_unique,
			String promotion_activity_name,
			String detailJson,
			String startDate,
			String endDate,
			String order_activity,
			String cus_activity
			){
		return cashService.submitSupplierStorageOrder( shop_unique,promotion_activity_name,startDate,endDate,order_activity,detailJson,cus_activity);
	}

	//修改活动状态
	@RequestMapping("/updateActivityStatus.do")
	@ResponseBody
	public PurResult updateActivityStatus(@RequestParam(value="promotion_activity_id",required=true)Integer promotion_activity_id,String status){
		Map<String,Object> map = new HashMap<String, Object>();
		map.put("promotion_activity_id", promotion_activity_id);
		map.put("status", status);
		return cashService.updateActivityStatus(map);
	}

	/**
	 * 查询商品折扣详情
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryGoodsMarkdownDetail.do")
	@ResponseBody
	public PurResult queryGoodsMarkdownDetail(
			@RequestParam(value="promotion_activity_id",required=true)Integer promotion_activity_id
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("promotion_activity_id", promotion_activity_id);
		return cashService.queryGoodsMarkdownDetail(map);
	}

	//删除活动
	@RequestMapping("/deleteActivity.do")
	@ResponseBody
	public PurResult deleteActivity(@RequestParam(value="promotion_activity_id",required=true)Integer promotion_activity_id){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("promotion_activity_id", promotion_activity_id);
		return cashService.deleteActivity(map);
	}

	/**
	 * 提交商品赠品
	 * @param supplier_unique
	 * @param uniques
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/submitGoodsGift.do")
	public PurResult submitGoodsGift(
			String shop_unique,
			String promotion_activity_name,
			String detailJson,
			String startDate,
			String endDate,
			String order_activity,
			String cus_activity

			){
		return cashService.submitGoodsGift( shop_unique,promotion_activity_name,startDate,endDate,order_activity,detailJson,cus_activity);
	}

	/**
	 * 查询商品赠品详情
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryGoodsGiftDetail.do")
	@ResponseBody
	public PurResult queryGoodsGiftDetail(
			@RequestParam(value="promotion_activity_id",required=true)Integer promotion_activity_id
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("promotion_activity_id", promotion_activity_id);
		return cashService.queryGoodsGiftDetail(map);
	}
	/**
	 * 提交单品促销
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/submitSingleGoodsPromotion.do")
	public PurResult submitSingleGoodsPromotion(
			String shop_unique,
			String promotion_activity_name,
			String detailJson,
			String startDate,
			String endDate,
			String order_activity,
			String cus_activity
			){
		return cashService.submitSingleGoodsPromotion( shop_unique,promotion_activity_name,startDate,endDate,detailJson,"1",order_activity,cus_activity);
	}

	/**
	 * 查询单品促销详情
	 * @param map
	 * @return
	 */
	@RequestMapping("/querySingleGoodsPromotionDetail.do")
	@ResponseBody
	public PurResult querySingleGoodsPromotionDetail(
			@RequestParam(value="promotion_activity_id",required=true)Integer promotion_activity_id
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("promotion_activity_id", promotion_activity_id);
		return cashService.querySingleGoodsPromotionDetail(map);
	}

	/**
	 * 提交订单满减
	 * @param supplier_unique
	 * @param uniques
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/addOrderMarkdown.do")
	public PurResult addOrderMarkdown(
			String shop_unique,
			String promotion_activity_name,
			String startDate,
			String endDate,
			String meet_price1,
			String discount_price1,
			String meet_price2,
			String discount_price2,
			String meet_price3,
			String discount_price3,
			String goods_id1,
			String goods_id2,
			String goods_id3,
			String gift_count1,
			String gift_count2,
			String gift_count3
			){
		return cashService.addOrderMarkdown( shop_unique,promotion_activity_name,startDate,endDate,meet_price1,discount_price1,meet_price2,discount_price2,
				meet_price3,discount_price3,goods_id1,goods_id2,goods_id3,gift_count1,gift_count2,gift_count3);
	}

	/**
	 * 查询订单活动赠品详情
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryOrderMarkdownDetail.do")
	@ResponseBody
	public PurResult queryOrderMarkdownDetail(
			@RequestParam(value="promotion_activity_id",required=true)Integer promotion_activity_id
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("promotion_activity_id", promotion_activity_id);
		return cashService.queryOrderMarkdownDetail(map);
	}

	@RequestMapping("/queryShopsBinding.do")
	@ResponseBody
	public PurResult queryShopsBinding(Long shop_unique,Integer useType,String goodsMessage,@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shop_unique);
		map.put("useType", useType);
		if(null != goodsMessage && !goodsMessage.equals("")){
			map.put("goodsMessage", "%"+goodsMessage+"%");
		}
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		return cashService.queryShopsBinding(map);
	}

	/**
	 * 添加捆绑关系
	 * @param goodsBarcodes
	 * @param goodsCounts
	 * @param shopUnique
	 * @param bindingTotal
	 * @return
	 */
	@RequestMapping("/newBindingGoods.do")
	@ResponseBody
	public ShopsResult newBindingGoods(
			@RequestParam(value="goodsBarcodes",required=true)String goodsBarcodes,
			@RequestParam(value="goodsCounts",required=true)String goodsCounts,
			@RequestParam(value="shop_unique",required=true)Long shop_unique,
			@RequestParam(value="bindingTotal",required=true)Double bindingTotal
			){
		return cashService.newBindingGoods(goodsBarcodes, goodsCounts, shop_unique,bindingTotal);
	}

	/**
	 * 修改商品捆绑消息
	 * @param map
	 * @return
	 */
	@RequestMapping("/modifyBinding.do")
	@ResponseBody
	public ShopsResult modifyBinding(Long shop_unique,Long bindingUnique,Integer useType){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shop_unique);
		map.put("bindingUnique", bindingUnique);
		map.put("useType", useType);
		return cashService.modifyBinding(map);
	}

	/**
	 * 删除已有的商品捆绑关系
	 * @param bindingUnique
	 * @return
	 */
	@RequestMapping("/deleteBindingGoods.do")
	@ResponseBody
	public ShopsResult deleteBindingGoods(Long bindingUnique,Long shop_unique){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("bindingUnique", bindingUnique);
		map.put("shopUnique", shop_unique);
		return  cashService.deleteBindingGoods(map);
	}
	/**
	 * 查询本店精选商品
	 * @param bindingUnique
	 * @return
	 */
	@RequestMapping("/queryOurShopGoods.do")
	@ResponseBody
	public ShopsResult queryOurShopGoods(Integer type,Long shop_unique){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("type", type);
		map.put("shop_unique", shop_unique);
		return  cashService.queryOurShopGoods(map);
	}


	/**
	 * 新增本店精选商品
	 * @param bindingUnique
	 * @return
	 */
	@RequestMapping("/addOurShopGoods.do")
	@ResponseBody
	public synchronized ShopsResult addOurShopGoods(Integer type,
			Long shop_unique,
			String promotion_activity_name,
			String startDate,
			String endDate,
			String goods_id,
			Double promotion_price,
			String per_count,
			String total_count
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("type", type);
		map.put("shop_unique", shop_unique);
		map.put("activity_range", 2);
		map.put("promotion_activity_name", promotion_activity_name);
		map.put("startDate", startDate);
		map.put("endDate", endDate);
		map.put("goods_id", goods_id);
		map.put("promotion_price", promotion_price);
		map.put("per_count", per_count);
		map.put("total_count", total_count);
		return  cashService.addOurShopGoods(map);
	}
	/**
	 * 编辑本店精选商品
	 * @param bindingUnique
	 * @return
	 */
	@RequestMapping("/editOurShopGoods.do")
	@ResponseBody
	public ShopsResult editOurShopGoods(Integer type,
			Long shop_unique,
			String promotion_activity_name,
			String startDate,
			String endDate,
			String goods_id,
			Double promotion_price,
			String per_count,
			String total_count,
			int promotion_activity_id
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("type", type);
		map.put("shop_unique", shop_unique);
		map.put("activity_range", 2);
		map.put("promotion_activity_name", promotion_activity_name);
		map.put("startDate", startDate);
		map.put("endDate", endDate);
		map.put("goods_id", goods_id);
		map.put("promotion_price", promotion_price);
		map.put("per_count", per_count);
		map.put("total_count", total_count);
		map.put("promotion_activity_id", promotion_activity_id);
		return  cashService.editOurShopGoods(map);
	}

	@RequestMapping("/queryOurShopGoodsDetail.do")
	@ResponseBody
	public ShopsResult queryOurShopGoodsDetail(
			int promotion_activity_id
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("promotion_activity_id", promotion_activity_id);
		return  cashService.queryOurShopGoodsDetail(map);
	}
	@RequestMapping("/queryGoodsPromotionList.do")
	@ResponseBody
	public ShopsResult queryGoodsPromotionList(
			String shop_unique,
			String goods_id
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("goods_id", goods_id);
		return  cashService.queryGoodsPromotionList(map);
	}
	@RequestMapping("/queryOrderMeetMoney.do")
	@ResponseBody
	public ShopsResult queryOrderMeetMoney(
			String shop_unique,
			Double money
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("money", money);
		return  cashService.queryOrderMeetMoney(map);
	}
	@RequestMapping("/queryShopOpenStatus.do")
	@ResponseBody
	public ShopsResult queryShopOpenStatus(
			String shop_unique
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		return  cashService.queryShopOpenStatus(map);
	}
	@RequestMapping("/queryBeansDiKu.do")
	@ResponseBody
	public ShopsResult queryBeansDiKu(
			String shopUnique
			){
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shopUnique);
		return  cashService.queryBeansDiKu(map);
	}

	/**
	 * 查询店铺的自营供货商信息
	 * @param shopUnique
	 * @return
	 */
	@RequestMapping("/getGoodsSupplierMsg.do")
	@ResponseBody
	public PurResult getGoodsSupplierMsg(String shopUnique) {

		return cashService.getGoodsSupplierMsg(shopUnique);
	}

	@RequestMapping("/queryGoodsByPage1.do")
	@ResponseBody
	public PurResult queryGoodsByPage1(
			@RequestParam(value="shop_unique",required=true)String shop_unique,
			String goodsMessage,
			String goods_kind_parunique,
			String goods_kind_unique,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "limit", defaultValue = "8") int pageSize,
			Integer is_online
			){
		return cashService.queryGoodsByPage1(shop_unique,goodsMessage,goods_kind_parunique,goods_kind_unique,page,pageSize,is_online);
	}

	/**
	 * 配送订单创建
	 * @param sale_list_unique 订单编号,不能为空
	 * @param delivery_type 配送方式，0：自配送 1：美团配送 2:一刻钟配送，不能为空
	 * @param goods_weight 订单商品重量（kg），不能为空
	 * @param shop_courier_id 自配送商家快递员id，可为空，自配送时不能为空
	 * @param courier_name 配送员姓名，可为空，自配送时不能为空
	 * @param courier_phone 配送员电话，可为空，自配送时不能为空
	 * @param sale_list_cashier 收银员id
	 *
	 * @param return_price 退还差价
	 * @param goodsList 核实订单商品信息，json字符串
	 * 		 [
	 * 			{
	 * 				goods_barcode 商品编码
	 * 				goods_name 商品名称
	 * 				goods_count 商品数量
	 * 				goods_price 商品单价
	 * 				goods_subtotal 商品价格小计
	 * 			}
	 * 		]
	 * @return
	 */
	@RequestMapping("/createOrder.do")
	@ResponseBody
	public PurResult doCreateOrder(@RequestBody CreateOrderParams params){
		log.info("配送订单创建，params="+params);
		String sale_list_unique = params.getSale_list_unique();
		String delivery_type = params.getDelivery_type();
		String goods_weight = params.getGoods_weight();
		String shop_courier_id = params.getShop_courier_id();
		String courier_name = params.getCourier_name();
		String courier_phone = params.getCourier_phone();
		String sale_list_cashier = params.getSale_list_cashier();
		PurResult shopsResult = deliveryService.shopSelf(sale_list_unique, goods_weight,shop_courier_id,courier_name,courier_phone,sale_list_cashier);
		//订单核实信息
		BigDecimal return_price = params.getReturn_price();
		String goodsList = params.getGoodsList();
		if(return_price != null && !return_price.equals("") && goodsList != null && !goodsList.equals("")) {
			Map<String ,Object> verifyParams = new HashMap<String, Object>();
			verifyParams.put("sale_list_unique", sale_list_unique);
			verifyParams.put("return_price", return_price);
			verifyParams.put("verify_staff_id", sale_list_cashier);
			verifyParams.put("goodsList", goodsList);
			deliveryService.verifyOrderNew(verifyParams);
		}

		return shopsResult;
	}
}
