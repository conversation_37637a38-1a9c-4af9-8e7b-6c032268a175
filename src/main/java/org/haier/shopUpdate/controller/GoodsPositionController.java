package org.haier.shopUpdate.controller;



import com.haier.controller.BaseController;
import org.haier.shopUpdate.entity.GoodsPositionEntity;
import org.haier.shopUpdate.service.GoodsPositionServiceImpl;
import org.haier.shopUpdate.util.ShopsResult;
import org.haier.shopUpdate.validator.AddGroup;
import org.haier.shopUpdate.validator.DeleteGroup;
import org.haier.shopUpdate.validator.SearchGroup;
import org.haier.shopUpdate.validator.UpdateGroup;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;


/**
 * <AUTHOR>
 * @description 货位管理
 * @date 2024-12-19 15:31
 * @Table:
 */
@Controller
@RequestMapping("/shops/goods/position")
public class GoodsPositionController {

    @Autowired
    private GoodsPositionServiceImpl goodsPositionService;


    /**
     * 查询货位集合
      */
    @RequestMapping("/list.do")
    @ResponseBody
    public ShopsResult query(@RequestBody  @Validated(value = {SearchGroup.class}) GoodsPositionEntity vo) {
        return goodsPositionService.selectList(vo.getPositionName(),String.valueOf(vo.getShopUnique()));
    }

    /**
     * 新增货位
     */
    @RequestMapping("/add.do")
    @ResponseBody
    public ShopsResult add(@RequestBody @Validated(value = {AddGroup.class}) GoodsPositionEntity vo) {
      return  goodsPositionService.add(vo);
    }

    /**
     * 获取货位
     */
    @RequestMapping("/queryAdDetail.do")
    @ResponseBody
    public ShopsResult queryAdDetail(@RequestBody @Validated(value = {DeleteGroup.class}) GoodsPositionEntity vo) {
      return  goodsPositionService.queryDetail(vo.getId());
    }
    /**
     * 修改货位
     */
    @RequestMapping("/update.do")
    @ResponseBody
    public ShopsResult update(@RequestBody @Validated(value = {UpdateGroup.class}) GoodsPositionEntity vo) {
      return   goodsPositionService.update(vo);
    }
    /**
     * 删除货位
     */
    @RequestMapping("/delete.do")
    @ResponseBody
    public  ShopsResult delete(@RequestBody @Validated(value = {DeleteGroup.class}) GoodsPositionEntity vo) {
     return   goodsPositionService.delete(vo.getId());
    }

    /**
     * 校验货位是否绑定商品
     */
    @RequestMapping("/check.do")
    @ResponseBody
    public  ShopsResult check(@RequestBody @Validated(value = {DeleteGroup.class}) GoodsPositionEntity vo) {
        return   goodsPositionService.check(vo.getId());
    }
}
