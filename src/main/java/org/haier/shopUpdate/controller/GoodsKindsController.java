package org.haier.shopUpdate.controller;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

//import org.apache.log4j.Logger;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import org.haier.shopUpdate.entity.GoodsKind;
import org.haier.shopUpdate.service.GoodsKindsService;
import org.haier.shopUpdate.util.I18nLanguageStaticReturnParamsUtil;
import org.haier.shopUpdate.util.ShopsResult;
//import org.haier.shopUpdate.util.ShopsUtil;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/goodsKinds")
public class GoodsKindsController {
    @Resource
    private GoodsKindsService kindsService;
    @Resource
    private I18nLanguageStaticReturnParamsUtil i18nRtUtil;
//	private static Logger log=Logger.getLogger(GoodsKindsController.class);

    /**
     * 查询店铺所有商品分类
     *
     * @param shop_unique
     * @return
     */
    @RequestMapping("/queryGoodsKindsByShop.do")
    @ResponseBody
    public ShopsResult queryGoodsKindsByShop(
            @RequestParam(value = "shopUnique", required = true) String shopUnique,
            @RequestParam(value = "kindType", defaultValue = "1") Integer kindType
            , @RequestParam(value = "kindAll", defaultValue = "1") Integer kindAll
            , @RequestParam(value = "status", required = false) Integer status,
            HttpServletRequest request) {
        ;
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shopUnique", shopUnique);
        map.put("kindAll", kindAll);
        if (ObjectUtils.isNotNull(status)) {
            map.put("validType", status);
        }
        return kindsService.queryGoodsKindsByShop(map);
    }

    /**
     * 查询店铺所有商品分类
     *
     * @param shop_unique
     * @return
     */
    @RequestMapping("/queryGoodsKindsByShop2.do")
    @ResponseBody
    public ShopsResult queryGoodsKindsByShop2(
            @RequestParam(value = "shopUnique", required = true) String shopUnique,
            @RequestParam(value = "kindType", defaultValue = "1") Integer kindType
            , @RequestParam(value = "kindAll", defaultValue = "0") Integer kindAll
            , HttpServletRequest request) {
        ;
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shopUnique", shopUnique);
        map.put("kindAll", kindAll);
//		map.put("kindType", 2);
        return kindsService.queryGoodsKindsByShop2(map);
    }


    /**
     * 商品分类查询
     *
     * @param map
     * @return
     */
    @RequestMapping("/appQueryGoodsKinds.do")
    @ResponseBody
    public ShopsResult appQueryGoodsKinds(
            @RequestParam(value = "shopUnique", required = true) String shopUnique,
            @RequestParam(value = "groupUnique", required = true) String groupUnique
            , HttpServletRequest request) {
        ;
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shopUnique", shopUnique);
        map.put("groupUnique", groupUnique);
        return kindsService.appQueryGoodsKinds(map);
    }

    /**
     * 查询商品大类
     *
     * @param shop_unique:店铺编号
     * @param kindType:1
     * @return
     */
    @RequestMapping("/queryGoodsBigKindsByShop.do")
    @ResponseBody
    public ShopsResult queryGoodsBigKindsByShop(
            @RequestParam(value = "shop_unique", required = true) String shop_unique
            , HttpServletRequest request) {
//		;
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shop_unique", shop_unique);
        return kindsService.queryGoodsBigKindsByShop(map);
    }

    /**
     * 查询更多商品大类
     *
     * @param map
     * @return
     */
    @RequestMapping("/queryMoreGoodsBigKinds.do")
    @ResponseBody
    public ShopsResult queryMoreGoodsBigKinds(
            @RequestParam(value = "shop_unique", required = true) String shop_unique
            , @RequestParam(value = "kindType", defaultValue = "1") Integer kindType
            , @RequestParam(value = "validType", defaultValue = "1") Integer validType
            , HttpServletRequest request) {
//		;
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shop_unique", shop_unique);
        map.put("kindType", kindType);
        map.put("validType", validType);
        return kindsService.queryMoreGoodsBigKinds(map);
    }

    /**
     * 保存分类排序
     *
     * @param map
     * @return
     */
    @RequestMapping("/saveGoodsBigKindsByShop.do")
    @ResponseBody
    public ShopsResult saveGoodsBigKindsByShop(
            @RequestParam(value = "shop_unique", required = true) String shop_unique,
            @RequestParam(value = "goods_kind_list", required = true) String goods_kind_list
            , HttpServletRequest request
    ) {
        ;
        return kindsService.saveGoodsBigKindsByShop(shop_unique, goods_kind_list);
    }

    @RequestMapping("/useCustomeKind.do")
    @ResponseBody
    public ShopsResult useCustomeKind(Integer kindType, String shopUnique, HttpServletRequest request) {
        ;
        return kindsService.useCustomeKind(kindType, shopUnique);
    }

    /**
     * 添加新的商品自定义分类信息
     *
     * @param kind:必传信息：goodsKindName,goodsKindParunique,shopUnique,
     * @return
     */
    @RequestMapping("/modifyCustomKinds.do")
    @ResponseBody
    public ShopsResult modifyCustomKinds(GoodsKind kind, HttpServletRequest request) {
        return kindsService.modifyCustomKinds(kind);
    }

    /**
     * 删除商品分类前，查询该分类下的商品数量
     *
     * @param goodsKind
     * @return
     */
    @RequestMapping("/getGoodsCountByKindUnique.do")
    @ResponseBody
    public ShopsResult getGoodsCountByKindUnique(GoodsKind goodsKind, HttpServletRequest request) {
        ;
        return kindsService.getGoodsCountByKindUnique(goodsKind);
    }

    @RequestMapping("/getRemarksForCustomKind.do")
    @ResponseBody
    public ShopsResult getRemarksForCustomKind() {
        ShopsResult sr = new ShopsResult(1, "操作成功");
        Map<String, Object> map = new HashMap<>();
        map.put("title", i18nRtUtil.getMessage("分类管理说明"));
        map.put("content", i18nRtUtil.getMessage("开启自定义分类后，所有商品分类会更新为默认自定义分类；可先创建自定义分类，再对商品分类进行修改；"));
        sr.setData(map);
        return sr;
    }

    /**
     * 获取当前店铺的分类使用状态
     *
     * @param goodsKind
     * @return
     */
    @RequestMapping("/getNowKindStatus.do")
    @ResponseBody
    public ShopsResult getNowKindStatus(GoodsKind goodsKind) {
        return kindsService.getNowKindStatus(goodsKind);
    }

    /**
     * 查询图标库
     *
     * @param icon_type   图标类型
     * @param shop_unique 店铺名称
     * @return
     */
    @RequestMapping("/queryGoodsKindIconByIconType.do")
    @ResponseBody
    public ShopsResult queryGoodsKindIconByIconType(@RequestParam(value = "icon_type", required = true) Integer icon_type, String shop_unique, HttpServletRequest request) {
        return kindsService.queryListByIconType(icon_type, shop_unique);
    }

    /**
     * 删除
     *
     * @param kindUnique
     * @return
     */
    @RequestMapping("deleteKind.do")
    @ResponseBody
    public ShopsResult deleteKind(@RequestParam("kindUnique") Long kindUnique, @RequestParam("shopUnique") String shopUnique) {
        return kindsService.deleteKind(kindUnique, shopUnique);
    }
}
