package org.haier.shopUpdate.controller.validate;

import cc.buyhoo.common.i18n.I18nUtil;
import cc.buyhoo.common.i18n.params.I18nQueryByKeyParams;
import cc.buyhoo.common.i18n.params.I18nQueryParams;
import cc.buyhoo.common.i18n.result.I18nQueryDto;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import org.haier.shopUpdate.params.i18n.I18nLanguageQueryByKeyParams;
import org.haier.shopUpdate.params.i18n.I18nLanguageQueryParams;
import org.haier.shopUpdate.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 多语言
 * @ClassName I18nController
 * <AUTHOR>
 * @Date 2024/12/19 11:14
 */
@Controller
@RequestMapping("/i18n")
public class I18nController {

    @Resource
    private I18nUtil i18nUtil;

    /**
     * 根据key和语言查询多语言值
     * @param params
     * @return
     */
    @RequestMapping(method = RequestMethod.POST,value = "/queryI18nByKey.do")
    @ResponseBody
    public ShopsResult queryI18nByKey(@Validated @RequestBody I18nLanguageQueryByKeyParams params) {
        ShopsResult result = new ShopsResult(1, "查询成功！");
        I18nQueryByKeyParams queryParams = new I18nQueryByKeyParams();
        BeanUtil.copyProperties(params, queryParams);
        I18nQueryDto dto = i18nUtil.getI18nValueByLanguageAndKey(queryParams);
        if (ObjectUtil.isNotEmpty(dto)) {
            Map<String, String> map = new HashMap<>();
            map.put(dto.getKey(), dto.getValue());
            result.setData(map);
        }
        return result;
    }

    /**
     * 根据语言查询多语言值
     * @param params
     * @return
     */
    @RequestMapping(method = RequestMethod.POST,value = "/queryI18n.do")
    @ResponseBody
    public ShopsResult queryI18n(@Validated @RequestBody I18nLanguageQueryParams params) {
        ShopsResult result = new ShopsResult(1, "查询成功！");
        I18nQueryParams queryParams = new I18nQueryParams();
        BeanUtil.copyProperties(params, queryParams);
        List<I18nQueryDto> list = i18nUtil.getI18nValueByLanguage(queryParams);
        Map<String, String> map = new HashMap<>();
        if (ObjectUtil.isNotEmpty(list)) {
            for (I18nQueryDto dto :
                    list) {
                map.put(dto.getKey(), dto.getValue());
            }
            result.setData(map);
        }
        return result;
    }
}
