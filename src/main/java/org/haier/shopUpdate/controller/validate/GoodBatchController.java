package org.haier.shopUpdate.controller.validate;

import org.haier.shopUpdate.params.goodBatch.GoodBatchListQueryParams;
import org.haier.shopUpdate.service.GoodBatchService;
import org.haier.shopUpdate.util.ShopsResult;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

/**
 * 批次管理
 * @ClassName GoodBatchController
 * <AUTHOR>
 * @Date 2024/4/28 13:46
 */
@Controller
@RequestMapping("/goodBatch")
public class GoodBatchController {
    @Resource
    private GoodBatchService goodBatchService;

    /**
     * 查询批次列表
     * @param params
     * @return
     */
    @RequestMapping(method = RequestMethod.POST,value = "/queryGoodBatchList.do")
    @ResponseBody
    public ShopsResult queryGoodBatchList(@Validated @RequestBody GoodBatchListQueryParams params) {
        return goodBatchService.queryGoodBatchList(params);
    }
}
