package org.haier.shopUpdate.controller.validate;

import org.haier.shopUpdate.params.shopSupBill.*;
import org.haier.shopUpdate.params.shopSupBill.supExternalCall.StorageAllGoodsParams;
import org.haier.shopUpdate.service.ShopSupBillService;
import org.haier.shopUpdate.util.ShopsResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
/**
 * 购销单
 * <AUTHOR>
 * @Date 2023/9/11 10:03
 **/
@Controller
@RequestMapping("/supBill")
public class ShopSupBillController {
    @Autowired
    private ShopSupBillService shopSupBillService;

    /**
     * 查询购销单列表
     * @param params
     * @return
     */
    @RequestMapping(method = RequestMethod.POST,value = "/querySupBillList.do")
    @ResponseBody
    public ShopsResult querySupBillList(@RequestBody QueryBillListParams params){
        return shopSupBillService.querySupBillList(params);
    }

    /**
     * 查询购销单商品信息
     * @param params
     * @return
     */
    @RequestMapping(method = RequestMethod.POST,value = "/querySupBillGoodsList.do")
    @ResponseBody
    public ShopsResult querySupBillGoodsList(@RequestBody QueryBillGoodsListParams params){
        return shopSupBillService.querySupBillGoodsList(params);
    }

    /**
     * 全部商品入库
     * @param params
     * @return
     */
    @RequestMapping(method = RequestMethod.POST,value = "/storageAllGoods.do")
    @ResponseBody
    public ShopsResult storageAllGoods(@RequestBody StorageAllGoodsParams params){
        return shopSupBillService.storageAllGoods(params);
    }

    /**
     * 单个商品入库
     * @param params
     * @return
     */
    @RequestMapping(method = RequestMethod.POST,value = "/storageGoods.do")
    @ResponseBody
    public ShopsResult storageGoods(@RequestBody StorageGoodsParams params){
        return shopSupBillService.storageGoods(params);
    }

    /**
     * 单个商品核对
     * @param params
     * @return
     */
    @RequestMapping(method = RequestMethod.POST,value = "/checkGoods.do")
    @ResponseBody
    public ShopsResult checkGoods(@RequestBody CheckGoodsParams params){
        return shopSupBillService.checkGoods(params);
    }

    /**
     * 撤销单个商品核对
     * @param params
     * @return
     */
    @RequestMapping(method = RequestMethod.POST,value = "/cancelCheckGoods.do")
    @ResponseBody
    public ShopsResult cancelCheckGoods(@RequestBody CancelCheckGoodsParams params){
        return shopSupBillService.cancelCheckGoods(params);
    }

    /**
     * 添加付款凭证
     * @param params
     * @return
     */
    @RequestMapping(method = RequestMethod.POST,value = "/addPaymentOrder.do")
    @ResponseBody
    public ShopsResult addPaymentOrder(@RequestBody AddPaymentOrderParams params){
        return shopSupBillService.addPaymentOrder(params);
    }

    /**
     * 更新付款凭证
     * @param params
     * @return
     */
    @RequestMapping(method = RequestMethod.POST,value = "/modifyPaymentOrder.do")
    @ResponseBody
    public ShopsResult modifyPaymentOrder(@RequestBody ModifyPaymentOrderParams params){
        return shopSupBillService.modifyPaymentOrder(params);
    }

    /**
     * 撤销单个商品入库
     * @param params
     * @return
     */
    @RequestMapping(method = RequestMethod.POST,value = "/cancelStorageGoods.do")
    @ResponseBody
    public ShopsResult cancelStorageGoods(@RequestBody CancelStorageGoodsParams params){
        return shopSupBillService.cancelStorageGoods(params);
    }

    /**
     * 更新购销单状态
     * @param params
     * @return
     */
    @RequestMapping(method = RequestMethod.POST,value = "/updateBillStatus.do")
    @ResponseBody
    public ShopsResult updateBillStatus(@RequestBody UpdateBillStatusParams params){
        return shopSupBillService.updateBillStatus(params);
    }

    /**
     * 查看付款凭证
     * @param params
     * @return
     */
    @RequestMapping(method = RequestMethod.POST,value = "/queryPayment.do")
    @ResponseBody
    public ShopsResult queryPayment(@RequestBody QueryBillGoodsListParams params){
        return shopSupBillService.queryPayment(params);
    }
}
