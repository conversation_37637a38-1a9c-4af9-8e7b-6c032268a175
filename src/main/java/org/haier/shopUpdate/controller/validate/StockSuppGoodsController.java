package org.haier.shopUpdate.controller.validate;

import org.haier.shopUpdate.params.shopSupBill.AddSupRelParams;
import org.haier.shopUpdate.params.shopSupBill.externalCall.*;
import org.haier.shopUpdate.service.ShopSupBillService;
import org.haier.shopUpdate.service.ShopSupplierService;
import org.haier.shopUpdate.util.ShopsResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 外部调用
 *
 * <AUTHOR>
 * @Date 2023/9/11 10:03
 **/
@Controller
@RequestMapping("/stockSuppGoods")
public class StockSuppGoodsController {
    @Autowired
    private ShopSupBillService shopSupBillService;
    @Autowired
    private ShopSupplierService shopSupplierService;

    /**
     * 添加购销单
     *
     * @param params
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/addShopSupBill.do")
    @ResponseBody
    public ShopsResult addShopSupBill(@RequestBody BillInfoParams params) {
        return shopSupBillService.addShopSupBill(params);
    }

    /**
     * 添加单个店铺商品
     *
     * @param params
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/addGoods.do")
    @ResponseBody
    public ShopsResult addGoods(@RequestBody GoodsInfoParams params) {
        return shopSupplierService.addGoods(params);
    }
    /**
     * 添加多店铺商品
     *
     * @param params
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/addShopsGood.do")
    @ResponseBody
    public ShopsResult addShopsGood(@RequestBody ShopGoodsInfoParams params) {
        return shopSupplierService.addShopsGood(params);
    }

    /**
     * 添加供应商关系信息
     *
     * @param params
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/addShopSupRel.do")
    @ResponseBody
    public ShopsResult addShopSupRel(@RequestBody AddSupRelParams params) {
        return shopSupplierService.addShopSupRel(params);
    }

    /**
     * 更新购销单状态
     *
     * @param params
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/updateSupBillStatus.do")
    @ResponseBody
    public ShopsResult updateSupBillStatus(@RequestBody UpdateSupBillStatusParams params) {
        return shopSupBillService.updateSupBillStatus(params);
    }

    /**
     * 购销单逻辑删除
     *
     * @param params
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/cancelSupBill.do")
    @ResponseBody
    public ShopsResult cancelSupBill(@RequestBody CancelSupBillParams params) {
        return shopSupBillService.cancelSupBill(params);
    }
}
