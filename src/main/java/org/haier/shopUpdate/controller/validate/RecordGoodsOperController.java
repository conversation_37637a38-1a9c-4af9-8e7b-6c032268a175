package org.haier.shopUpdate.controller.validate;

import org.haier.shopUpdate.params.RecordGoodsOper.QueryRecordGoodsOperDetailParams;
import org.haier.shopUpdate.params.RecordGoodsOper.QueryRecordGoodsOperListParams;
import org.haier.shopUpdate.service.RecordGoodsOperService;
import org.haier.shopUpdate.util.ShopsResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 商品信息/变更记录
 */
@Controller
@RequestMapping("/recordGoodsOper")
public class RecordGoodsOperController {
	@Autowired
	private RecordGoodsOperService recordGoodsOperService;

	/**
	 * 查询商品变更记录
	 * @param params
	 * @return
	 */
	@RequestMapping("/queryRecordGoodsOper.do")
	@ResponseBody
	public ShopsResult queryRecordGoodsOper(@RequestBody QueryRecordGoodsOperListParams params){
		return recordGoodsOperService.queryRecordGoodsOper(params);
	}

	/**
	 * 商品变更记录详情
	 * @param params
	 * @return
	 */
	@RequestMapping("/queryRecordGoodsOperDetail.do")
	@ResponseBody
	public ShopsResult queryRecordGoodsOperDetail(@RequestBody QueryRecordGoodsOperDetailParams params){
		return recordGoodsOperService.queryRecordGoodsOperDetail(params);
	}

}
