package org.haier.shopUpdate.controller.validate;

import org.haier.shopUpdate.params.agriculturalSupplier.*;
import org.haier.shopUpdate.service.AgriculturalSupplierService;
import org.haier.shopUpdate.util.ShopsResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;


/**
 * 农批功能
 */
@Controller
@RequestMapping("/agriculturalSupplier")
public class AgriculturalSupplierController {

    @Autowired
    private AgriculturalSupplierService agriculturalSupplierService;

    /**
     * 新增供货商、货主
     * @return
     */
    @RequestMapping( "/addSupplier.do")
    @ResponseBody
    public ShopsResult addSupplier(@RequestBody AddSupplierParams req){
        return agriculturalSupplierService.addSupplier(req);
    }

    /**
     * 修改供货商、货主
     * @return
     */
    @RequestMapping( "/updateSupplier.do")
    @ResponseBody
    public ShopsResult addSupplier(@RequestBody UpdateSupplierParams req){
        return agriculturalSupplierService.updateSupplier(req);
    }

    /**
     * 删除供货商、货主
     * @param req
     * @return
     */
    @RequestMapping( "/deleteSupplier.do")
    @ResponseBody
    public ShopsResult deleteSupplier(@RequestBody DeleteSupplierParams req){
        return agriculturalSupplierService.deleteSupplier(req);
    }

    /**
     * 根据id查询数据
     * @param req
     * @return
     */
    @RequestMapping( "/querySupplierDetail.do")
    @ResponseBody
    public ShopsResult querySupplierDetail(@RequestBody QuerySupplierDetailParams req){
        return agriculturalSupplierService.querySupplierDetail(req);
    }

    /**
     * 列表查询
     * @param req
     * @return
     */
    @RequestMapping( "/querySupplierList.do")
    @ResponseBody
    public ShopsResult querySupplierList(@RequestBody QuerySupplierListParams req){
        return agriculturalSupplierService.querySupplierList(req);
    }


}
