package org.haier.shopUpdate.controller.validate;

import cn.hutool.core.bean.BeanUtil;
import org.haier.shopUpdate.entity.goods.goodsRecord.GoodsOperParam;
import org.haier.shopUpdate.log.annotation.RemoteLog;
import org.haier.shopUpdate.log.enums.BusinessType;
import org.haier.shopUpdate.params.goods.AddGoodsBaseParam;
import org.haier.shopUpdate.params.goods.UpdateGoodsBaseParam;
import org.haier.shopUpdate.service.GoodsService;
import org.haier.shopUpdate.util.ShopsResult;
import org.haier.shopUpdate.util.common.HeaderUtil;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 第二版商品操作
 *
 * <AUTHOR> Administrator
 * @create 2023-07-06 17:37
 */
@Controller
@RequestMapping("/goods/v2")
public class GoodsV2Controller {
    @Resource
    private GoodsService goodsService;

    /**
     * V2.0 新增商品
     * @param addGoodsBaseParam
     * @return
     */
    @RequestMapping("/addGoods.do")
    @ResponseBody
    @RemoteLog(title = "新增商品",businessType = BusinessType.INSERT,isSendDingDingTalk = true)
    public ShopsResult addGoods(@RequestBody AddGoodsBaseParam addGoodsBaseParam, HttpServletRequest request) {
        GoodsOperParam goodsOperParam = HeaderUtil.getHeadParam(request);
        BeanUtil.copyProperties(goodsOperParam, addGoodsBaseParam);
        addGoodsBaseParam.setStaffId(goodsOperParam.getUserId());
        return goodsService.addGoods(addGoodsBaseParam);
    }
    /**
     * V2.0 修改商品
     * @param updateGoodsBaseParam
     * @return
     */
    @RequestMapping("/updateGoods.do")
    @ResponseBody
    @RemoteLog(title = "修改商品",businessType = BusinessType.UPDATE,isSendDingDingTalk = true)
    public ShopsResult updateGoods(@RequestBody UpdateGoodsBaseParam updateGoodsBaseParam, HttpServletRequest request) {
        GoodsOperParam goodsOperParam = HeaderUtil.getHeadParam(request);
        BeanUtil.copyProperties(goodsOperParam, updateGoodsBaseParam);
        return goodsService.updateGoods(updateGoodsBaseParam);
    }
}
