package org.haier.shopUpdate.controller.validate;

import cn.hutool.json.JSONUtil;
import org.apache.log4j.Logger;
import org.haier.shopUpdate.controller.DiamondsController;
import org.haier.shopUpdate.params.inventoryTask.*;
import org.haier.shopUpdate.service.InventoryTaskService;
import org.haier.shopUpdate.util.ShopsResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 盘库功能
 */
@Controller
@RequestMapping("/inventoryTask")
public class InventoryTaskController {

    private static Logger log=Logger.getLogger(InventoryTaskController.class);

    @Autowired
    private InventoryTaskService inventoryTaskService;

    /**
     * 新增盘点任务
     * @param req
     * @return
     */
    @RequestMapping( "/addTask.do")
    @ResponseBody
    public ShopsResult addTask(@RequestBody AddTaskParams req){
        return inventoryTaskService.addTask(req);
    }

    /**
     * 盘点任务列表
     * @param req
     * @return
     */
    @RequestMapping( "/taskList.do")
    @ResponseBody
    public ShopsResult taskList(@RequestBody TaskListParams req){
        return inventoryTaskService.taskList(req);
    }

    /**
     * 删除盘点任务
     * @param req
     * @return
     */
    @RequestMapping( "/deleteTask.do")
    @ResponseBody
    public ShopsResult deleteTask(@RequestBody DeleteTaskParams req){
        return inventoryTaskService.deleteTask(req);
    }

    /**
     * 盘点任务详情
     * @param req
     * @return
     */
    @RequestMapping( "/taskDetail.do")
    @ResponseBody
    public ShopsResult taskDetail(@RequestBody TaskDetailParams req){
        return inventoryTaskService.taskDetail(req);
    }

    /**
     * 修改盘点单名称
     * @param req
     * @return
     */
    @RequestMapping( "/updateTaskName.do")
    @ResponseBody
    public ShopsResult updateTaskName(@RequestBody UpdatetTaskNameParams req){
        return inventoryTaskService.updateTaskName(req);
    }

    /**
     * 保存盘点详情
     * @param req
     * @return
     */
    @RequestMapping( "/addTaskDetail.do")
    @ResponseBody
    public ShopsResult addTaskDetail(@RequestBody AddTaskDetailParams req){
        return inventoryTaskService.addTaskDetail(req);
    }

    /**
     * 修改盘点详情
     * @param req
     * @return
     */
    @RequestMapping( "/updateTaskDetail.do")
    @ResponseBody
    public ShopsResult updateTaskDetail(@RequestBody UpdateTaskDetailParams req){
        String jsonStr = JSONUtil.toJsonStr(req);
        log.info("修改盘点详情入参:"+jsonStr);
        return inventoryTaskService.updateTaskDetail(req);
    }

    /**
     * 删除盘点详情
     * @param req
     * @return
     */
    @RequestMapping( "/deleteTaskDetail.do")
    @ResponseBody
    public ShopsResult deleteTaskDetail(@RequestBody DeleteTaskDetailParams req){
        return inventoryTaskService.deleteTaskDetail(req);
    }

    /**
     * 货位列表
     * @param req
     * @return
     */
    @RequestMapping( "/goodsLocationList.do")
    @ResponseBody
    public ShopsResult goodsLocationList(@RequestBody GoodsLocationListParams req){
        return inventoryTaskService.goodsLocationList(req);
    }

    /**
     * 货位新增
     * @param req
     * @return
     */
    @RequestMapping( "/addGoodsLocation.do")
    @ResponseBody
    public ShopsResult addGoodsLocation(@RequestBody AddGoodsLocationParams req){
        return inventoryTaskService.addGoodsLocation(req);
    }

    /**
     * 修改货位
     * @param req
     * @return
     */
    @RequestMapping( "/updateGoodsLocation.do")
    @ResponseBody
    public ShopsResult updateGoodsLocation(@RequestBody UpdateGoodsLocationParams req){
        return inventoryTaskService.updateGoodsLocation(req);
    }

    /**
     * 删除货位
     * @param req
     * @return
     */
    @RequestMapping( "/deleteGoodsLocation.do")
    @ResponseBody
    public ShopsResult deleteGoodsLocation(@RequestBody DeleteGoodsLocationParams req){
        return inventoryTaskService.deleteGoodsLocation(req);
    }

    /**
     * 盘库单预览
     * @param req
     * @return
     */
    @RequestMapping( "/taskPreview.do")
    @ResponseBody
    public ShopsResult taskPreview(@RequestBody TaskPreviewParams req){
        return inventoryTaskService.taskPreview(req);
    }

    /**
     * 提交盘点
     * @param req
     * @return
     */
    @RequestMapping( "/submitTask.do")
    @ResponseBody
    public ShopsResult submitTask(@RequestBody SubmitTaskParams req){
        return inventoryTaskService.submitTask(req);
    }

    /**
     * 单个商品盘点明细-商品盘点
     * @param req
     * @return
     */
    @RequestMapping( "/taskGoodsDetail.do")
    @ResponseBody
    public ShopsResult taskGoodsDetail(@RequestBody TaskGoodsDetailParams req){
        return inventoryTaskService.taskGoodsDetail(req);
    }

    /**
     * 新增桶重量
     * @param req
     * @return
     */
    @RequestMapping( "/addBucketWeight.do")
    @ResponseBody
    public ShopsResult addBucketWeight(@RequestBody AddBucketWeightParams req){
        return inventoryTaskService.addBucketWeight(req);
    }

    /**
     * 修改桶重量
     * @param req
     * @return
     */
    @RequestMapping( "/updateBucketWeight.do")
    @ResponseBody
    public ShopsResult updateBucketWeight(@RequestBody UpdateBucketWeightParams req){
        return inventoryTaskService.updateBucketWeight(req);
    }

    /**
     * 删除桶重量
     * @param req
     * @return
     */
    @RequestMapping( "/deleteBucketWeight.do")
    @ResponseBody
    public ShopsResult deleteBucketWeight(@RequestBody DeleteBucketWeightParams req){
        return inventoryTaskService.deleteBucketWeight(req);
    }

    /**
     * 筐重量列表
     * @param req
     * @return
     */
    @RequestMapping( "/bucketWeightList.do")
    @ResponseBody
    public ShopsResult bucketWeightList(@RequestBody BucketWeightListParams req){
        return inventoryTaskService.bucketWeightList(req);
    }

    /**
     * 商品盘库记录
     * @param req
     * @return
     */
    @RequestMapping("/inventoryGoodsRecord.do")
    @ResponseBody
    public ShopsResult inventoryGoodsRecord(@RequestBody InventoryGoodsRecordParams req){
        return inventoryTaskService.inventoryGoodsRecord(req);
    }


    /**
     * 盘库单下载
     * @param req
     * @return
     */
    @RequestMapping( "/taskPreviewDownload.do")
    @ResponseBody
    public ShopsResult taskPreviewDownload(@RequestBody TaskPreviewDowloadParams req){
        return inventoryTaskService.taskPreviewDownload(req);
    }

}
