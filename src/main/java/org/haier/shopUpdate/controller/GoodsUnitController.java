package org.haier.shopUpdate.controller;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

//import org.apache.log4j.Logger;
import org.haier.shopUpdate.log.annotation.RemoteLog;
import org.haier.shopUpdate.log.enums.BusinessType;
import org.haier.shopUpdate.service.GoodsUnitService;
import org.haier.shopUpdate.util.ShopsResult;
//import org.haier.shopUpdate.util.ShopsUtil;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/goodsUnit")
public class GoodsUnitController {
	@Resource
	private GoodsUnitService goodsUnitService;
//	private static Logger log=Logger.getLogger(GoodsUnitController.class);
	
	/**
	 * 查询商品单位列表
	 */
	@RequestMapping("/findGoodsUnitList.do")
	@ResponseBody
	public ShopsResult findGoodsUnitList(
			String shop_unique,
			String goods_unit
			,HttpServletRequest request
			){ 
		
		return goodsUnitService.findGoodsUnitList(shop_unique,goods_unit);
	}
	/**
	 * 新增商品单位
	 */
	@RequestMapping("/addGoodsUnit.do")
	@ResponseBody
	@RemoteLog(title = "新增单位",businessType = BusinessType.INSERT,isSendDingDingTalk = true)
	public ShopsResult addGoodsUnit(
			String shop_unique,
			String goods_unit
			,HttpServletRequest request
			){ 
		
		return goodsUnitService.addGoodsUnit(shop_unique,goods_unit);
	}
	/**
	 * 编辑商品单位
	 */
	@RequestMapping("/editGoodsUnit.do")
	@ResponseBody
	public ShopsResult editGoodsUnit(
			Integer goods_unit_id,
			String goods_unit
			,HttpServletRequest request
			){ 
		
		return goodsUnitService.editGoodsUnit(goods_unit_id,goods_unit);
	}
	/**
	 * 删除商品单位
	 */
	@RequestMapping("/deleteGoodsUnit.do")
	@ResponseBody
	public ShopsResult deleteGoodsUnit(
			Integer goods_unit_id
			,HttpServletRequest request
			){ 
		
		return goodsUnitService.deleteGoodsUnit(goods_unit_id);
	}
	
}
