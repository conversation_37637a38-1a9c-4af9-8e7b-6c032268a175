package org.haier.shopUpdate.controller;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

//import org.apache.log4j.Logger;
import org.haier.shopUpdate.entity.RechargeOrder;
import org.haier.shopUpdate.params.QueryStaffListByStaffIdParams;
import org.haier.shopUpdate.redism.RedisCache;
import org.haier.shopUpdate.service.ShopsStaffService;
import org.haier.shopUpdate.util.*;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * 员工列表
 */

@Controller
@RequestMapping("/shopsStaff")
public class ShopsStaffController {
	@Resource
	private ShopsStaffService staffService;
	@Resource
	private I18nLanguageStaticReturnParamsUtil i18nRtUtil;
//	private static Logger log=Logger.getLogger(ShopsStaffController.class);


	/**
	 * 查询员工列表
	 * @param staffId
	 * @return
	 */
	@RequestMapping(value = "/queryStaffListByStaffId.do",method = RequestMethod.POST)
	@ResponseBody
	public ShopsResult queryStaffListByStaffId(@RequestBody QueryStaffListByStaffIdParams params) {
		return staffService.queryStaffListByStaffId(params);
	}
	
	/**
	 * 管理员登录
	 * @param map
	 * @return
	 */
	@RequestMapping("/staffLoginByAccountPwd.do")
	@ResponseBody
	public ShopsResult staffLoginByAccountPwd(
				HttpServletRequest request,
				HttpServletResponse response,
				@RequestParam(value="staffAccount",required=true)String staffAccount,
				@RequestParam(value="staffPwd",required=true)String staffPwd
			){
		
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("staffAccount", staffAccount);
		map.put("staffPwd", ShopsUtil.string2MD5(staffPwd));
		return staffService.staffLoginByAccountPwd(map);
	}
	
	/**
	 * 更新管理员信息后，查询管理员更新后的信息
	 * @param staffAccount
	 * @return
	 */
	@RequestMapping("/staffNewMessage.do")
	@ResponseBody
	public ShopsResult staffNewMessage(
				@RequestParam(value="staffAccount",required=true)Long staffAccount
				,HttpServletRequest request
			){
		
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("staffAccount", staffAccount);
		return staffService.staffLoginByAccountPwd(map);
	}
	
	/**
	 * 根据管理员信息，查询管理员管理的店铺信息
	 * @param managerUnique 管理员编号
	 * @param shopUnique 店铺编号
	 * @param staffPosition 职位信息
	 * @return
	 */
	@RequestMapping("/queryShopsByManager.do")
	@ResponseBody
	public ShopsResult queryShopsByManager(
			@RequestParam(value="managerUnique",required=true)String managerUnique,
			@RequestParam(value="shopUnique",required=true)String shopUnique,
			@RequestParam(value="staffPosition",required=true)Integer staffPosition,
			String shop_name,
			HttpServletRequest request,
			HttpServletResponse response,
			String token,
			Integer pageNum,
			Integer pageSize,
			String shopMsg
			){
		
		Map<String,Object> map=new HashMap<String, Object>();
		if(staffPosition==1){
			map.put("shopUnique", shopUnique);
		}
		if(staffPosition==3){
			map.put("managerUnique", managerUnique);
		}
		if(null != pageNum && null != pageSize && pageNum > 0 && pageSize > 0) {
			map.put("startNum", (pageNum-1)*pageSize);
			map.put("pageSize", pageSize);
			System.out.println(map);
		}
		if(null != shop_name && !shop_name.equals("")) {
			map.put("shopMsg", shop_name);
		}
		map.put("excludeShopUnique",shopUnique); //排除自己返回
		return staffService.queryShopsByManager(map);
	}
	
	
	/**
	 * 获取短信验证码
	 * @param staffAccount
	 * @param phoneType（手机型号，1：ANDRIOD;2：IOS)
	 * @return
	 */
	@RequestMapping("/sendMessage.do")
	@ResponseBody
	public ShopsResult sendMessage(
			@RequestParam(value="staffAccount",required=true)String staffAccount,
			HttpServletRequest request,
			HttpServletResponse response,
			@RequestParam(value="phoneType",defaultValue="2")Integer phoneType
			){
		
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("staffAccount", staffAccount);
		return staffService.sendMessage(map, request, response,phoneType);
	}
	
	/**
	 * 验证码验证通过（快捷登录）
	 * @param request
	 * @param response
	 * @param map
	 * @return
	 */
	@RequestMapping("/passMsg.do")
	@ResponseBody
	public ShopsResult passMsg(
			HttpServletRequest request,
			HttpServletResponse response,
			@RequestParam(value="staffAccount",required=true)String staffAccount,
			@RequestParam(value="smsCode",required=true)String smsCode,
			@RequestParam(value="phoneType",defaultValue="2")Integer phoneType,
			String sessionId
			){
		
//		System.out.println("新的验证码为"+smsCode);
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("staffAccount", staffAccount);
		map.put("smsCode", smsCode);
		return staffService.passMsg(request, response, map,phoneType,sessionId);
	}
	
	/**
	 * 修改登录密码
	 * @param request
	 * @return
	 */
	@RequestMapping("/updateShopsPwd.do")
	@ResponseBody
	public ShopsResult updateShopsPwd(
			HttpServletRequest request,
			HttpServletResponse response,
			@RequestParam(value="token",required=true)String token,
			@RequestParam(value="staffAccount",required=true)String staffAccount,
			@RequestParam(value="newStaffPwd",required=true)String newStaffPwd,
			@RequestParam(value="phoneType",defaultValue="2")Integer phoneType,
			String sessionId
			){
		
		Object oldToken;
		
		ShopsResult sr=new ShopsResult();
		int numD=PwdCheckUtil.checkPassward(newStaffPwd);
		if(numD!=0)
		{
			sr.setStatus(2);
			sr.setMsg("密码为8-16位大小写字母、数字或英文特殊符号组合");
			return sr;
		}
		RedisCache rc = new RedisCache();
		oldToken = rc.getObject(staffAccount+"token");
//		if(phoneType==2){
//			oldToken=SessionUtil.getToken(request, response);
//		}else{
//			oldToken=ShopsUtil.map.get("sessionId");
////			System.out.println("服务器存储的token::::"+oldToken);
//		}
		if(null==oldToken){
			sr.setStatus(2);
			sr.setMsg("验证超时，请重新验证！");
			return sr;
		}
		if(!token.equals(oldToken)){
			sr.setStatus(2);
			sr.setMsg("token验证失败！");
			return sr;
		}

		rc.removeObject(staffAccount+"token");
		SessionUtil.map.remove(sessionId);//删除无用的sessionId
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("staffAccount", staffAccount);
		map.put("pwdOk",newStaffPwd);
		map.put("staffPwd", ShopsUtil.string2MD5(newStaffPwd));
		return staffService.updateStaffPwd(map,phoneType,sessionId);
	}
	/**
	 * 更新用户信息
	 * @param staffAccount 登录账号
	 * @param channelId 百度推送ID
	 * @param phoneType手机类型：1、ANDRIOD;2、IOS
	 * @param systemVersion 系统版本号例：10.3
	 * @param phoneModel 手机型号：例：IPHONE 5
	 * @return
	 */
	@RequestMapping("/updateShopStaffMessage.do")
	@ResponseBody
	public ShopsResult updateShopStaffMessage(
		@RequestParam(value="staffAccount",required=true)String staffAccount,
		String channelId,
		Integer phoneType,
		String systemVersion,
		String phoneModel
		,HttpServletRequest request
		){
		
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("staffAccount", staffAccount);
		map.put("channelId", channelId);
		map.put("phoneType", phoneType);
		map.put("phoneModel", phoneModel);
		map.put("systemVersion", systemVersion);
		return staffService.updateStaffPwd(map);
	}
	
	/**
	 * 更新店铺信息
	 * @param map
	 * @return
	 * @throws Exception 
	 */
	@RequestMapping("/updateShopsMessage.do")
	@ResponseBody
	public ShopsResult updateShopsMessage(
			String shopUnique,
			String shopName,
			String shopHours,
			Double distributionScope,
			String shopAddress,
			String shopLongitude,
			String shopLatitude,
			Integer examinestatus,
			HttpServletRequest request
			) throws Exception{
		
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("shopHours", shopHours);
		map.put("shopName", shopName);
		map.put("distributionScope", distributionScope);
		map.put("shopAddress", shopAddress);
		map.put("shopLongitude", shopLongitude);
		map.put("shopLatitude", shopLatitude);
		map.put("examinestatus", examinestatus);
		return staffService.updateShopsMessage(map,request);
	}
	
	/**
	 * 店铺新信息查询
	 * @param map
	 * @return
	 */
	@RequestMapping("/shopNewMessage.do")
	@ResponseBody
	public ShopsResult shopNewMessage(
			@RequestParam(value="shopUnique",required=true)String shopUnique,HttpServletRequest request){
		
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		return staffService.shopNewMessage(map);
	}
	
	/**
	 * 个人信息修改
	 * @param staffAccount 登录账号
	 * @return
	 * @throws Exception 
	 * @throws IOException 
	 */
	@RequestMapping("/editShopsInfo.do")
	@ResponseBody
	public ShopsResult editShopsInfo(
		@RequestParam(value="staffAccount",required=true)String staffAccount,
		String staffBirthday,
		String staffPhone,
		String staffName,
		HttpServletRequest request
		) throws IOException, Exception{
		
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("staffAccount", staffAccount);
		map.put("staffBirthday", staffBirthday);
		map.put("staffPhone", staffPhone);
		map.put("staffName", staffName);
		return staffService.editShopsInfo(map,request);
	}
	
	/**
	 * 退出管理员的登录信息
	 * @param map
	 * @return
	 */
	@RequestMapping("/loginOut.do")
	@ResponseBody
	public ShopsResult loginOut(
			@RequestParam(value="staffAccount",required=true)String staffAccount
			,HttpServletRequest request
			){
		
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("staffAccount", staffAccount);
		map.put("channelId", "");
		return staffService.loginOut(map);
	}
	/**
	 * 查询店铺管理员信息
	 * @param map
	 * @return
	 */
	@RequestMapping("/shopsStaffsSearchByShopUnique.do")
	@ResponseBody
	public ShopsResult shopsStaffsSearchByShopUnique(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			Integer staffId
			,HttpServletRequest request){
		
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("staffId", staffId);
		return staffService.shopsStaffsSearchByShopUnique(map);
	}
	
	/**
	 * 修改员工权限
	 * @param map
	 * @return
	 */
	@RequestMapping("/modifyStaffPower.do")
	@ResponseBody
	public ShopsResult modifyStaffPower(
			@RequestParam(value="staffId",required=true) Integer staffId,
			Integer powerPrice,
			Integer powerCount,
			Integer powerSupplier,
			Integer powerKind,
			Integer powerInPrice,
			Integer powerName,
			Integer powerDelete,
			Integer powerPur,
			Integer powerAdd,
			Integer powerChange
			,HttpServletRequest request
			){
		
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("staffId", staffId);
		map.put("powerPrice", powerPrice);
		map.put("powerCount", powerCount);
		map.put("powerKind", powerKind);
		map.put("powerSupplier", powerSupplier);
		map.put("powerInPrice", powerInPrice);
		map.put("powerName", powerName);
		map.put("powerDelete", powerDelete);
		map.put("powerPur", powerPur);
		map.put("powerAdd", powerAdd);
		map.put("powerChange", powerChange);
		return staffService.modifyStaffPower(map);
	}
	/**
	 * 修改密码
	 * @param map
	 * @return
	 */
	@RequestMapping("/editStafPwd.do")
	@ResponseBody
	public ShopsResult editStafPwd(
				@RequestParam(value="staffAccount",required=true)String staffAccount,
				@RequestParam(value="staffPwd",required=true)String staffPwd,
				@RequestParam(value="staffNewPwd",required=true)String staffNewPwd
				,HttpServletRequest request
			){
		ShopsResult sr=new ShopsResult();
		if(staffPwd==null||staffPwd.isEmpty()||staffNewPwd==null||staffNewPwd.isEmpty())
		{
			sr.setStatus(0);
			sr.setMsg("参数不能为空");
			return sr;
		}
		int numD=PwdCheckUtil.checkPassward(staffNewPwd);
		if(numD!=0)
		{
			sr.setStatus(0);
			sr.setMsg("密码为8-16位大小写字母、数字或英文特殊符号组合");
			return sr;
		}else
		{
			Map<String,Object> map=new HashMap<String, Object>();
			map.put("staffAccount", staffAccount);
			map.put("staffPwd", ShopsUtil.string2MD5(staffPwd));
			map.put("pwd_ok", staffNewPwd);
			map.put("staffNewPwd", ShopsUtil.string2MD5(staffNewPwd));
			
			sr=staffService.editStafPwd(map);
		}

		 
		return sr;
	}
	/**
	 * 登录传推送注册ID
	 * @param map
	 * @return
	 */
	@RequestMapping("/updateRegistrationId.do")
	@ResponseBody
	public ShopsResult updateRegistrationId(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			@RequestParam(value="registration_id",required=true)String registration_id,
			@RequestParam(value="registration_phone_type",required=true)Integer registration_phone_type
			,HttpServletRequest request
			){
		
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("registration_id", registration_id);
		map.put("registration_phone_type", registration_phone_type);
		return staffService.updateRegistrationId(map);
	}

	/**
	 * 查询交接班记录
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @param shopUnique 店铺编码
	 * @param saleListCashier 收银员ID
	 * @return
	 */
	@RequestMapping("/queryHandoverRecord.do")
	@ResponseBody
	public ShopsResult queryHandoverRecord(
			@RequestParam(value="startTime",required=true)String startTime,
			@RequestParam(value="endTime",required=true)String endTime,
			@RequestParam(value="shop_unique",required=true)Long shop_unique,
			@RequestParam(value="sale_list_cashier",required=true)Long sale_list_cashier,
			@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
			@RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize
	) {
		return staffService.queryHandoverRecord(startTime, endTime, shop_unique, sale_list_cashier, pageNum, pageSize);
	}
	/**
	 * 查询反馈类型
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryFeedBackTypeList.do")
	@ResponseBody
	public ShopsResult queryFeedBackTypeList(
			HttpServletRequest request
			){
		
		ShopsResult result=new ShopsResult();
		List<Map<String, Object>> list=new ArrayList<Map<String,Object>>();
		Map<String, Object> map1=new HashMap<String, Object>();
		map1.put("name", i18nRtUtil.getMessage("收银流程"));
		map1.put("value", 1);
		Map<String, Object> map2=new HashMap<String, Object>();
		map2.put("name", i18nRtUtil.getMessage("订单数据"));
		map2.put("value", 2);
		Map<String, Object> map3=new HashMap<String, Object>();
		map3.put("name", i18nRtUtil.getMessage("商品信息"));
		map3.put("value", 3);
		Map<String, Object> map4=new HashMap<String, Object>();
		map4.put("name", i18nRtUtil.getMessage("支付问题"));
		map4.put("value", 4);
		Map<String, Object> map5=new HashMap<String, Object>();
		map5.put("name", i18nRtUtil.getMessage("流程优化"));
		map5.put("value", 5);
		Map<String, Object> map6=new HashMap<String, Object>();
		map6.put("name", i18nRtUtil.getMessage("新增需求"));
		map6.put("value", 6);
		Map<String, Object> map7=new HashMap<String, Object>();
		map7.put("name", i18nRtUtil.getMessage("售后服务"));
		map7.put("value", 7);
		Map<String, Object> map8=new HashMap<String, Object>();
		map8.put("name", i18nRtUtil.getMessage("硬件问题"));
		map8.put("value", 8);
		Map<String, Object> map9=new HashMap<String, Object>();
		map9.put("name", i18nRtUtil.getMessage("其他"));
		map9.put("value", 9);
		list.add(map1);
		list.add(map2);
		list.add(map3);
		list.add(map4);
		list.add(map5);
		list.add(map6);
		list.add(map7);
		list.add(map8);
		list.add(map9);
		result.setData(list);
		result.setStatus(1);
		return result;
	}
	
	/**
	 * 提交意见反馈
	 * @param map
	 * @return
	 */
	@RequestMapping("/submitFeedBack.do")
	@ResponseBody
	public ShopsResult submitFeedBack(
			@RequestParam(value="shop_unique",required=true)String shop_unique,
			@RequestParam(value="staff_id",required=true)String staff_id,
			@RequestParam(value="feed_back_source",required=true)String feed_back_source,
			@RequestParam(value="feed_back_type",required=true)String feed_back_type,
			@RequestParam(value="feed_back_content",required=true)String feed_back_content,
			@RequestParam(value="feed_back_system",required=true)String feed_back_system,
			HttpServletRequest request
			){
		
		return staffService.submitFeedBack(shop_unique,staff_id,feed_back_source,feed_back_type,feed_back_content,feed_back_system,request);
	}
	/**
	 * 提交店铺认证资质
	 * @param map
	 * @return
	 * @throws Exception 
	 */
	@RequestMapping("/submitShopQualification.do")
	@ResponseBody
	public ShopsResult submitShopQualification(
			@RequestParam(value="shop_unique",required=true)String shop_unique,
			@RequestParam(value="legal_name",required=true)String legal_name,
			@RequestParam(value="shop_phone",required=true)String shop_phone,
			@RequestParam(value="register_shop_name",required=true)String register_shop_name,
			@RequestParam(value="use_shop_name",required=true)String use_shop_name,
			HttpServletRequest request
			) throws Exception{
		
		return staffService.submitShopQualification(shop_unique,legal_name,shop_phone,register_shop_name,use_shop_name,request);
	}
	/**
	 * 查询店铺认证资质
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryShopQualification.do")
	@ResponseBody
	public ShopsResult queryShopQualification(
			@RequestParam(value="shop_unique",required=true)String shop_unique
			,HttpServletRequest request
			){
		
		return staffService.queryShopQualification(shop_unique);
	}
	
	@RequestMapping("/getRechargeOrder.do")
	@ResponseBody
	public ShopsResult getRechargeOrder(HttpServletRequest request) {
		
		ShopsResult sr=new ShopsResult(1,"查询成功！");
		List<RechargeOrder> list=new ArrayList<>();
		list.add(new RechargeOrder(1,"充值时间降序"));
		list.add(new RechargeOrder(2,"充值时间升序"));
		list.add(new RechargeOrder(3,"充值金额降序"));
		list.add(new RechargeOrder(4,"充值金额升序"));
		sr.setData(list);
		return sr;
	}
	/**
	 * 会员充值详情记录
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryCusRechargeRecord.do")
	@ResponseBody
	public ShopsResult queryCusRechargeRecord(
			@RequestParam(value="shopUnique")String shopUnique,
			String startTime,
			String endTime,
			@RequestParam(value="order",defaultValue="1")Integer order,
			String cusMsg,
			@RequestParam(value="cusType",defaultValue="1")Integer cusType
			,HttpServletRequest request
			){
		
		Map<String,Object> map=new HashMap<>();
		map.put("shopUnique", shopUnique);
		map.put("startTime", startTime);
		map.put("endTime", endTime);
		map.put("cusType", cusType);
		if(cusMsg!=null){
			map.put("cusMsg", "%"+cusMsg+"%");
		}
		if(order==1){
			map.put("order", "rechargeTime");
			map.put("orderType", "DESC");
		}
		if(order==2){
			map.put("order", "rechargeTime");
			map.put("orderType", "ASC");
		}
		if(order==3){
			map.put("order", "rechargeMoney");
			map.put("orderType", "DESC");
		}
		if(order==4){
			map.put("order", "rechargeMoney");
			map.put("orderType", "ASC");
		}
		return staffService.queryCusRechargeRecord(map);
	}
}
