package org.haier.shopUpdate.controller;

import java.sql.Timestamp;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import com.alibaba.fastjson.JSON;
import org.haier.shopUpdate.entity.SaleListInvalidReq;
import org.haier.shopUpdate.entity.goods.goodsRecord.GoodsOperParam;
import org.haier.shopUpdate.redism.RedisCache;
//import org.apache.log4j.Logger;
import org.haier.shopUpdate.service.SaleListService;
import org.haier.shopUpdate.util.ShopsResult;
//import org.haier.shopUpdate.util.ShopsUtil;
import org.haier.shopUpdate.util.common.HeaderUtil;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/saleList")
public class SaleListController {
    @Resource
    private SaleListService saleService;
    //	private static Logger log=Logger.getLogger(SaleListController.class);
    @Resource
    private RedisCache redis;

    /**
     * @param shopUnique      店铺编号
     * @param cusUnique       会员编号
     * @param saleListPayment 1、现金；2、支付宝；3、微信；4、银行卡
     * @param goodsMsg
     * @return
     */
    @RequestMapping("/saveFarmOrder.do")
    @ResponseBody
    public ShopsResult saveFarmOrder(String shopUnique, String cusUnique, String saleListPayment, String goodsMsg, String staffId, String macId, Double saleListActuallyReceived) {
        return saleService.saveFarmOrder(shopUnique, cusUnique, saleListPayment, goodsMsg, staffId, macId, saleListActuallyReceived);
    }

    /**
     * 查询退款订单详情
     *
     * @param retListUnique
     * @return
     */
    @RequestMapping("/queryReturnDetail.do")
    @ResponseBody
    public ShopsResult queryReturnDetail(String retListUnique) {
        return saleService.queryReturnDetail(retListUnique);
    }

    @RequestMapping("/testRedis.do")
    @ResponseBody
    public ShopsResult testRedis(String key, String value) {
        ShopsResult sr = new ShopsResult(1, "查询成功！");

        if (null == redis) {
            sr.setStatus(0);
            sr.setMsg("redis不存在");
            return sr;
        } else {
            if (null == value) {
                sr.setData(redis.getObject(key));
            } else {
                redis.putObject(key, value);
            }
        }

        return sr;
    }

    /**
     * 修改退款订单状态
     *
     * @param retListUnique      退款单号
     * @param retListHandlestate 退款审核状态
     * @param retListRemarks     如果拒绝退款，需要填写拒绝原因
     * @param staffId            操作员工ID
     * @param macId              操作设备的macId或浏览器型号
     * @return
     */
    @RequestMapping("/modifyReturnMsg.do")
    @ResponseBody
    public ShopsResult modifyReturnMsg(String retListUnique, Integer retListHandlestate, String retListRemarks, String staffId, String macId) {
        //请求shop项目接口
        return saleService.modifyReturnMsg(retListUnique, retListHandlestate, retListRemarks, staffId, macId);
    }

    /**
     * 查询退款订单的申请信息
     *
     * @param shopUnique 店铺编号
     * @param page       页码
     * @param pageSize   单页查询数量
     * @param startTime  开始查询时间
     * @param endTime    结束查询时间
     * @return
     */
    @RequestMapping("/queryRetLists.do")
    @ResponseBody
    public ShopsResult queryRetLists(String shopUnique, Integer page, Integer pageSize, String startTime, String endTime, Integer retListHandlestate) {
        return saleService.queryRetLists(shopUnique, page, pageSize, startTime, endTime, retListHandlestate);
    }

    /**
     * 修改话术内容或状态
     *
     * @param id      id
     * @param delFlag 1、正常；2、删除
     * @param msg     话术内容
     * @return
     */
    @RequestMapping("/modifyReturnListMsg.do")
    @ResponseBody
    public ShopsResult modifyReturnListMsg(String id, Integer delFlag, String msg) {
        return saleService.modifyReturnListMsg(id, delFlag, msg);
    }

    /**
     * 添加新的拒绝退款订单话术信息
     *
     * @param shopUnique
     * @param staffId
     * @param msg
     * @return
     */
    @RequestMapping("/addNewReturnListMsg.do")
    @ResponseBody
    public ShopsResult addNewReturnListMsg(String shopUnique, String staffId, String msg) {
        return saleService.addNewReturnListMsg(shopUnique, staffId, msg);
    }

    /**
     * 查询常用话术
     *
     * @param shopUnique
     * @return
     */
    @RequestMapping("/queryReturnListMsg.do")
    @ResponseBody
    public ShopsResult queryReturnListMsg(String shopUnique) {
        return saleService.queryReturnListMsg(shopUnique);
    }

    /**
     * 各状态订单量查询
     *
     * @param map
     * @return
     */
    @RequestMapping("/shopsSaleListCount.do")
    @ResponseBody
    public ShopsResult shopsSaleListCount(
            @RequestParam(value = "shopUnique", required = true) Long shopUnique,
            String saleListMessage,
            HttpServletRequest request
    ) {

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shopUnique", shopUnique);
        if (null != saleListMessage && !saleListMessage.equals("")) {
            map.put("saleListMessage", "%" + saleListMessage + "%");
        }
        return saleService.shopsSaleListCount(map);
    }

    /**
     * 网络订单查询
     *
     * @param map
     * @return
     */
    @RequestMapping("/querySaleList.do")
    @ResponseBody
    public ShopsResult querySaleList(
            String shopUnique,
            @RequestParam(value = "pageIndex", defaultValue = "1") Integer pageIndex,
            @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize,
            String saleListMessage,
            @RequestParam(value = "vague", defaultValue = "2") Integer vague,
            boolean netWork,
            Integer handleState,
            String orderStartDate,
            String orderEndDate,
            Integer saleType,
            Integer saleListPayment,
            Integer noCancelOrder,
            HttpServletRequest request) {

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shopUnique", shopUnique);
        map.put("startNum", (pageIndex - 1) * pageSize);
        if (null != saleListMessage && !saleListMessage.equals("")) {
            map.put("saleListMessage", "%" + saleListMessage + "%");
        }
        map.put("vague", vague);
//		map.put("netWork", netWork);
        if (null != handleState) {
            if (handleState == -1 || handleState == 0) {
                handleState = null;
            }
        }

        // 查询全部订单时，是否包含取消逻辑生效
        if (handleState == null) {
            map.put("noCancelOrder", (noCancelOrder != null && Integer.valueOf(1).equals(noCancelOrder) ? 1 : 0));
        }


        map.put("handleState", handleState);
        map.put("pageSize", pageSize);
        map.put("orderStartDate", orderStartDate); //订单开始时间：yyyy-MM-dd
        map.put("orderEndDate", orderEndDate); //订单结束时间：yyyy-MM-dd
        map.put("saleType", saleType); //订单类型：1店内收银2自提3送货上门
        map.put("saleListPayment", saleListPayment); //订单类型：3-微信、2-支付宝、14-金圈聚合码、13-金圈扫码、8-混合支付、1-现金、4-银行卡、5-会员余额、10-积分兑换
        ShopsResult result = saleService.querySaleList(map);

        System.err.println("********************  ********************" + JSON.toJSONString(result));
        return result;
    }

    /**
     * 采购订单详情查询
     *
     * @param saleListUnique 订单详情查询
     * @return
     */
    @RequestMapping("/querySaleListDetail.do")
    @ResponseBody
    public ShopsResult querySaleListDetail(Long saleListUnique, HttpServletRequest request) {

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("saleListUnique", saleListUnique);
        return saleService.querySaleListDetail(map);
    }


    /**
     * 修改订单支付状态或处理状态
     * 发货和取消功能，确认订单支付功能
     *
     * @param map
     * @return
     */

    @RequestMapping("/modifySaleListState.do")
    @ResponseBody
    public ShopsResult modifySaleListState(
            @RequestParam(value = "saleListUnique", required = true) Long saleListUnique,
            @RequestParam(value = "handleState", required = true) Integer handleState, HttpServletRequest request) {

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("saleListUnique", saleListUnique);
        map.put("handleState", handleState);
        return saleService.modifySaleListState(map);
    }

    /**
     * 收银端赊账-平账
     */
    @RequestMapping("/flatOrderAccount.do")
    @ResponseBody
    public ShopsResult flatOrderAccount(
            @RequestParam(value = "saleListUnique", required = true) Long saleListUnique,
            @RequestParam(value = "saleListPayment", required = true) Integer saleListPayment, HttpServletRequest request) {

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("saleListUnique", saleListUnique);
        map.put("saleListPayment", saleListPayment);
        map.put("saleListState", "3");
        return saleService.flatOrderAccount(map);
    }

    /**
     * 经营助手（商品销量分时间段查询）
     *
     * @param map
     * @return
     */
    @RequestMapping("/querySaleGoodsCount.do")
    @ResponseBody
    public ShopsResult querySaleGoodsCount(
            @RequestParam(value = "shopUnique", required = true) Long shopUnique,
            @RequestParam(value = "startTime", required = true) String startTime,
            @RequestParam(value = "endTime", required = true) String endTime
            , HttpServletRequest request
    ) {

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shopUnique", shopUnique);
        map.put("startTime", startTime);
        map.put("endTime", endTime);
//		System.out.println(map);
        return saleService.querySaleGoodsCount(map);
    }

    /**
     * 经营助手
     *
     * @param map
     * @return
     */
    @RequestMapping("/queryGroupsGoodsSaleMessage.do")
    @ResponseBody
    public ShopsResult queryGroupsGoodsSaleMessage(
            @RequestParam(value = "shopUnique", required = true) String shopUnique,
            @RequestParam(value = "queryType", required = true) Integer queryType,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize
            , HttpServletRequest request
    ) {

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shopUnique", shopUnique);
        Calendar calendar = Calendar.getInstance();
        Timestamp endTime = new Timestamp(calendar.getTimeInMillis());//截至查询时间
        Timestamp startTime = null;
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
//		calendar.add(Calendar.DATE, -1);
        if (queryType == 1) {//当日
        } else if (queryType == 2) {
            calendar.add(Calendar.DATE, -7);
        } else if (queryType == 3) {
            calendar.add(Calendar.DATE, -30);
        }
        startTime = new Timestamp(calendar.getTimeInMillis());//开始时间
        map.put("startTime", startTime);
        map.put("endTime", endTime);
        map.put("queryType", queryType);
        map.put("pageSize", pageSize);
        return saleService.queryGroupsGoodsSaleMessage(map, queryType);
    }

    /**
     * 百货商家端农批首页统计
     */
    @RequestMapping("/indexStatisticsNP.do")
    @ResponseBody
    public ShopsResult indexCountNP(
            @RequestParam(value = "shopUnique", required = true) Long shopUnique
    ) {

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("shopUnique", shopUnique);
        return saleService.indexStatisticsNP(map);
    }

    /**
     * 农批订单作废
     *
     * @param req
     * @return
     */
    @RequestMapping("/saleListInvalidNP.do")
    @ResponseBody
    public ShopsResult saleListInvalidNP(SaleListInvalidReq req, HttpServletRequest request) {

        GoodsOperParam goodsOperParam = HeaderUtil.getHeadParam(request);
        return saleService.saleListInvalidNP(req, goodsOperParam);
    }

}

