package org.haier.shopUpdate.controller;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

//import org.apache.log4j.Logger;
import org.haier.shopUpdate.service.AppPayService;
import org.haier.shopUpdate.util.NumRandomUntil;
import org.haier.shopUpdate.util.ShopsResult;
//import org.haier.shopUpdate.util.ShopsUtil;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/appPay")
public class AppPayController {
	@Resource
	private AppPayService appPayService;
//	private static Logger log=Logger.getLogger(AppPayController.class);
	
	/**
	 * 生成订单号
	 */
	@RequestMapping("/createSaleListUnique.do")
	@ResponseBody
	public ShopsResult createSaleListUnique(HttpServletRequest request){
		
		ShopsResult result=new ShopsResult();
		Date currentTime = new Date();
		SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
		String dateString = formatter.format(currentTime);
		String sale_list_unique= dateString+""+NumRandomUntil.getFixLenthString(3);
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("sale_list_unique", sale_list_unique);
		result.setStatus(1);
		result.setData(map);
		return result;
	}
	
	
	/**
	 * 扫码添加商品
	 */
	@RequestMapping("/addGoodsToCar.do")
	@ResponseBody
	public ShopsResult addGoodsToCar(
			String shop_unique,
			String sale_list_unique,
			String goods_barcode,
			Double sale_list_detail_count,
			Double sale_list_detail_price,
			String goods_type,
			Integer sale_list_cashier,
			Integer table_id
			,HttpServletRequest request
			){ 
		
		return appPayService.addGoodsToCar(shop_unique,sale_list_unique,goods_barcode,sale_list_detail_count,sale_list_detail_price,goods_type,sale_list_cashier,table_id);
	}
	/**
	 * 搜索商品
	 */
	@RequestMapping("/searchGoods.do")
	@ResponseBody
	public ShopsResult searchGoods(
			String shop_unique,
			String goods_message
			,HttpServletRequest request
			){
		
		return appPayService.searchGoods(shop_unique,goods_message);
	}
	/**
	 * 修改商品信息
	 */
	@RequestMapping("/editGoodsToCar.do")
	@ResponseBody
	public ShopsResult editGoodsToCar(
			String shop_unique,
			String sale_list_unique,
			String goods_barcode,
			Double sale_list_detail_count,
			Double sale_list_detail_price,
			Double goods_cus_price
			,HttpServletRequest request
			){
		
		return appPayService.editGoodsToCar(shop_unique,sale_list_unique,goods_barcode,sale_list_detail_count,sale_list_detail_price,goods_cus_price);
	}
	/**
	 * 删除订单商品
	 */
	@RequestMapping("/deleteGoods.do")
	@ResponseBody
	public ShopsResult deleteGoods(
			String sale_list_detail_id
			,HttpServletRequest request
			){
		
		return appPayService.deleteGoods(sale_list_detail_id);
	}
	/**
	 * 查询挂单列表
	 */
	@RequestMapping("/querySaleListWaitList.do")
	@ResponseBody
	public ShopsResult querySaleListWaitList(
			String shop_unique,
			Integer sale_list_cashier
			,HttpServletRequest request
			){
		
		return appPayService.querySaleListWaitList(shop_unique,sale_list_cashier);
	}
	/**
	 * 查询挂单详情
	 */
	@RequestMapping("/querySaleListDetailWait.do")
	@ResponseBody
	public ShopsResult querySaleListDetailWait(
			String shop_unique,
			String sale_list_unique
			,HttpServletRequest request
			){
		
		return appPayService.querySaleListDetailWait(shop_unique,sale_list_unique);
	}
	/**
	 * 取消挂单
	 */
	@RequestMapping("/cancelSaleListWait.do")
	@ResponseBody
	public ShopsResult cancelSaleListWait(
			String shop_unique,
			String sale_list_unique
			,HttpServletRequest request
			){
		
		return appPayService.cancelSaleListWait(shop_unique,sale_list_unique);
	}
	/**
	 * 页面加载查询最近挂单
	 */
	@RequestMapping("/queryLatelySaleListDetailWait.do")
	@ResponseBody
	public ShopsResult queryLatelySaleListDetailWait(
			String shop_unique
			,HttpServletRequest request
			){
		
		return appPayService.queryLatelySaleListDetailWait(shop_unique);
	}
	/**
	 * 会员关联订单号
	 */
	@RequestMapping("/addCusJoinSaleList.do")
	@ResponseBody
	public ShopsResult addCusJoinSaleList(
			String shop_unique,
			String sale_list_unique,
			String cus_unique,
			String sale_list_remarks
			,HttpServletRequest request
			){
		
		return appPayService.addCusJoinSaleList(shop_unique,sale_list_unique,cus_unique,sale_list_remarks);
	}
	/**
	 * 支付收银
	 */
	@RequestMapping("/shop_pay.do")
	@ResponseBody
	public ShopsResult shop_pay(
			@RequestParam(value="shop_unique", required=true)String shop_unique,
			@RequestParam(value="sale_list_unique", required=true)String sale_list_unique,
			Integer sale_list_payment,
			Double sale_list_discount,
			Double sale_list_total,
			Double sale_list_actually_received,
			String sale_list_remarks,
			@RequestParam(value="spbill_create_ip", required=true)String spbill_create_ip,
			@RequestParam(value="auth_code", required=false)String auth_code
			,HttpServletRequest request
			){
		
		return appPayService.shop_pay(shop_unique,sale_list_unique,sale_list_payment,sale_list_discount,sale_list_total,
				sale_list_actually_received,sale_list_remarks,spbill_create_ip,auth_code);
	}
	/**
	 * 查询桌号列表
	 */
	@RequestMapping("/queryFoodTableList.do")
	@ResponseBody
	public ShopsResult queryFoodTableList(
			@RequestParam(value="shop_unique", required=true)String shop_unique
			){
		
		return appPayService.queryFoodTableList(shop_unique);
	}
	/**
	 * 新增桌号
	 */
	@RequestMapping("/addFoodTable.do")
	@ResponseBody
	public ShopsResult addFoodTable(
			@RequestParam(value="shop_unique", required=true)String shop_unique,
			@RequestParam(value="table_name", required=true)String table_name
			){
		
		return appPayService.addFoodTable(shop_unique,table_name);
	}
	/**
	 * 删除桌号
	 */
	@RequestMapping("/deleteFoodTable.do")
	@ResponseBody
	public ShopsResult deleteFoodTable(
			@RequestParam(value="id", required=true)String id
			){
		
		return appPayService.deleteFoodTable(id);
	}
	
	/**
	 * 更新打印商品数量
	 */
	@RequestMapping("/upadatePrintCount.do")
	@ResponseBody
	public ShopsResult upadatePrintCount(
			@RequestParam(value="shop_unique", required=true)String shop_unique,
			@RequestParam(value="sale_list_unique", required=true)String sale_list_unique,
			@RequestParam(value="goods_barcode", required=true)String goods_barcode,
			@RequestParam(value="print_count", required=true)String print_count
			){
		
		return appPayService.upadatePrintCount(shop_unique,sale_list_unique,goods_barcode,print_count);
	}
	

}
