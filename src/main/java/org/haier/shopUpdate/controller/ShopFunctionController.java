package org.haier.shopUpdate.controller;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

//import org.apache.log4j.Logger;
import org.haier.shopUpdate.service.ShopFunctionService;
import org.haier.shopUpdate.util.ShopsResult;
//import org.haier.shopUpdate.util.ShopsUtil;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/shopFunction")
public class ShopFunctionController {
	@Resource
	private ShopFunctionService funService;
//	private static Logger log=Logger.getLogger(ShopFunctionController.class);
	
//	public ShopsResult queryShopFunction(String shopUnique) {
//		return funService.modifyPayPic(request, map);
//	}
	/**
	 * 查询店铺功能列表
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryShopFunction.do")
	@ResponseBody
	public ShopsResult queryShopFunction(
			@RequestParam(value="shopUnique",required=true)Long shopUnique
			,HttpServletRequest request){
		
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		return funService.queryShopFunction(map);
	}
	
	@RequestMapping("/modifyShopFunction.do")
	@ResponseBody
	public ShopsResult modifyShopFunction(
			String shopUnique,
			Integer shopFlower,
			Integer shopDeliverwater,
			Integer shopLaundry,
			Integer shopExpress,
			Integer shopHomemarking,
			Integer shopCake,
			Integer shopFruit,
			Integer shopPur
			,HttpServletRequest request
			){
		
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("shopFlower", shopFlower);
		map.put("shopDeliverwater",shopDeliverwater );
		map.put("shopLaundry",shopLaundry );
		map.put("shopExpress", shopExpress);
		map.put("shopHomemarking", shopHomemarking);
		map.put("shopCake", shopCake);
		map.put("shopFruit",shopFruit );
		map.put("shopPur", shopPur);
		return funService.modifyShopFunction(map);
	}
	/**
	 * 查询店铺付款码信息
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryShopPayCode.do")
	@ResponseBody
	public ShopsResult queryShopPayCode(
			@RequestParam(value="shopUnique",required=true) String shopUnique
			,HttpServletRequest request
			){
		
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		return funService.queryShopPayCode(map);
	}
	

	/**
	 * 修改店铺付款码信息
	 * @param request
	 * @param map
	 * @return
	 * @throws Exception 
	 */
	@RequestMapping("/modifyPayPic.do")
	@ResponseBody
	public ShopsResult modifyPayPic(HttpServletRequest request
			,@RequestParam(value="shopUnique",required=true)String shopUnique
			,@RequestParam(value="picType",defaultValue="1")Integer picType
			) throws Exception{
		
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		map.put("picType", picType);
		return funService.modifyPayPic(request, map);
	}
	
	
	@RequestMapping("/tttt.do")
	@ResponseBody
	public ShopsResult getPath(HttpServletRequest request){
		
        String path = request.getServletContext().getRealPath("/");
        path = path.replaceAll("\\\\", "/");
        return new ShopsResult(1, path);
    }
}
