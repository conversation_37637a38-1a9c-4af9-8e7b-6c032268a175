package org.haier.shopUpdate.controller;

import java.beans.IntrospectionException;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.InvocationTargetException;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.TreeMap;

import javax.annotation.Resource;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.httpclient.HttpStatus;
import org.apache.http.ParseException;
import org.apache.http.client.ClientProtocolException;
import org.apache.log4j.Logger;
import org.haier.shopUpdate.project.ProjectConfig;
import org.haier.shopUpdate.redism.RedisCache;
import org.haier.shopUpdate.service.WechatService;
import org.haier.shopUpdate.util.ShopsResult;
import org.haier.shopUpdate.util.XMLUtils;
import org.haier.shopUpdate.util.helibao.HelibaoPayConfig;
import org.haier.shopUpdate.util.helibao.IPGet;
import org.haier.shopUpdate.util.mqtt.MqttxUtil;
import org.haier.shopUpdate.util.unionpay.HttpUtil;
import org.haier.shopUpdate.util.wechat.wxpush.WXPush;
import org.haier.shopUpdate.wechat.PublicPayConfig;
import org.haier.shopUpdate.wechat.WXPay;
import org.haier.shopUpdate.wechat.WXPayConfig;
import org.haier.shopUpdate.wechat.WXPayUtil;
import org.haier.shopUpdate.wechat.AuthUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.haier.constant.Constant;
import com.haier.util.Disguiser;
import com.haier.util.HttpClientService;
import com.haier.util.MyBeanUtils;
import com.haier.vo.AppCreateOrderVo;
import com.haier.vo.AppPayPublicCreateOrderVo;
import com.haier.vo.AppPayPublicOrderResponseVo;


/**
* @author: 作者:王恩龙
* @version: 2020年11月7日 上午8:40:12
*
*/
@Slf4j
@Controller
@RequestMapping("/wechat")
public class WEChatController {

    private static final Logger logger = Logger.getLogger(WEChatController.class);
	private WXPay wxPay;
	private WXPayConfig config;
	@Autowired
	private RedisCache redis;

    @Autowired
    private HttpServletRequest request;

    @Resource
    private WechatService wechatService;

	/**
	 * 通过url获取微信加密信息，用于网页调整小程序
	 * @param url
	 * @return
	 */
	public ShopsResult getSignatureByUrl(String url) {
		ShopsResult sr = new ShopsResult();

		if (null == redis.getObject("jsapi_ticket")) {

		}

		return sr;
	}
    @RequestMapping("/saveOrder.do")
    @ResponseBody
    public ShopsResult saveOrder(String shopUnique,String saleListUnique,String goodsMsg,Double saleListTotal,Integer staffId,String macId) {
    	ShopsResult sr = new ShopsResult(1,"提交成功!");

    	//发送MQTT消息到收银机
    	Map<String,Object> map = new HashMap<String,Object>();
    	map.put("shopUnique", shopUnique);
    	map.put("saleListUnique", saleListUnique);
    	map.put("staffId", staffId);
    	map.put("macId",macId);

    	Map<String,Object> mqttMap = new HashMap<String,Object>();
    	mqttMap.put("ctrl", "msg_shop_order");
    	mqttMap.put("ID", macId);
    	mqttMap.put("status", 200);
    	mqttMap.put("msg", "支付订单信息");
    	mqttMap.put("data", map);

    	//将消息发送给前端
    	MqttxUtil.sendMapMsg(mqttMap, macId);

    	return sr;
    }

    /**
     * 提交订单，并获取用户支付信息，返回给页面，等待页面支付
     * @param shopUnique 店铺编号
     * @param orderNo 订单编号
     * @param payMoney 订单需要支付的金额
     */
    @RequestMapping("/getPayInfomation.do")
    public String getPayInfomation(String shopUnique,Double payMoney,HttpServletRequest request,String orderNo,String staffId,String mqttId,String goodsMsg) {
    	String userAgent = request.getHeader("User-Agent");
    	System.out.println("当前浏览器信息!" + userAgent);
    	Integer existOrder = 0;
    	if(null == orderNo || orderNo.equals("")) {
    		//如果是没有收银机的客户，需要自己创建订单号
    		existOrder = 1;
    		orderNo = System.nanoTime() + "";
    	}

    	Map<String,Object> map = new HashMap<String,Object>();
    	map.put("orderNo", orderNo);
    	map.put("payMoney", payMoney);
    	map.put("shopUnique", shopUnique);
    	map.put("orderType", "2");
    	if(null != mqttId && !mqttId.equals("")) {
    		map.put("mqttId", mqttId);
    	}
    	if(null != goodsMsg && !goodsMsg.equals("")) {
    		map.put("goodsMsg", JSONArray.parse(goodsMsg));
    	}

    	String state = JSONObject.toJSONString(map);
    	try {
			state = URLEncoder.encode(state,"UTF-8");
		} catch (UnsupportedEncodingException e) {
			log.error("编码失败",e);
		}
    	//此处需要判定是否有订单信息，如果没有，需要模拟创建一个无名产品订单，等待支付
    	if(existOrder != null && existOrder == 1) {
    		wechatService.createNewOrder(shopUnique, orderNo, payMoney, staffId);
    	}

    	//判断网页类型
    	if(userAgent != null && userAgent.indexOf("WeChat") >= 0) {
    		System.out.println("我是微信支付");
    		// 这个url的域名必须要进行再公众号中进行注册验证，这个地址是成功后的回调地址
    		String getCodeUrl = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" + AuthUtil.APPbaihouID + "&redirect_uri="
    				+ URLEncoder.encode(AuthUtil.AUTHPAYCALLBACKURLWXPAY) + "&response_type=code" + "&scope=snsapi_userinfo"
    				+ "&state=" ;
    		getCodeUrl = getCodeUrl + state + "#wechat_redirect";
    		System.out.println("重定向到微信");
    		System.out.println(getCodeUrl);
    		return "redirect:" + getCodeUrl;// 必须重定向，否则不能成功
    	}else if(userAgent != null && userAgent.indexOf("AliApp") >= 0) {
    		System.out.println("我是支付宝用户");
    		//使用支付宝支付
    		//如果是支付宝，重定向到支付宝授权界面
    		String authCodeUrl = "https://openauth.alipay.com/oauth2/publicAppAuthorize.htm?app_id=" + AuthUtil.AUTHPAYALIPAYAPPID +"&scope=auth_base&redirect_uri=" + AuthUtil.AUTHPAYCALLBACKURLALIPAY;
    		authCodeUrl += "&state=" + state;
    		System.out.println("重定向到支付宝");
    		System.out.println(authCodeUrl);
    		return "redirect:" + authCodeUrl;
    	}

    	request.setAttribute("shopUnique", shopUnique);

    	return "/WEB-INF/pay/payForShop.jsp";
    }
    /**
     *
     * @param shopUnique
     * @param request
     * @return
     */
    @RequestMapping("/payForShop.do")
    public String payForShop(String shopUnique,HttpServletRequest request,String mqttId) {
    	request.setAttribute("shopUnique", shopUnique);
    	request.setAttribute("mqttId", mqttId);
    	request.setAttribute("payStatus", 0);

    	//根据店铺信息获取店铺名称
    	Map<String,Object> payMap = wechatService.queryShopMsg(shopUnique);
    	request.setAttribute("shopName", payMap.get("shopName"));

    	return "/WEB-INF/pay/payForShop.jsp";
    }

    @RequestMapping("/refreshToken.do")
    @ResponseBody
    public ShopsResult refreshToken() {
    	IPGet.getIp();
    	WXPush.getToken();
    	return new ShopsResult(1, "刷新成功！");
    }

    /**
	 * 查询宁宇会员消费记录
	 * @param cusId 会员ID
	 * @param page 页码
	 * @param rows 每页查询数量
	 * @return
	 */
    @RequestMapping("/searchRechargeRecord.do")
    @ResponseBody
	public ShopsResult searchRechargeRecord(String cusId,Integer page,Integer rows ) {
		return wechatService.searchRechargeRecord(cusId, page, rows);
	}

    /**
	 * 查询宁宇会员消费记录
	 * @param cusId 会员ID
	 * @param page 页码
	 * @param rows 每页查询数量
	 * @return
	 */
    @RequestMapping("/queryCusConsumptionRecord.do")
    @ResponseBody
	public ShopsResult queryCusConsumptionRecord(String cusId,Integer page,Integer rows ) {
		return wechatService.queryCusConsumptionRecord(cusId, page, rows);
	}

    public String authPayCallBack() {
    	String respString = "success";
		try {
			String xmlString = XMLUtils.parseRequst(request);
			JSONObject j = JSONObject.parseObject(xmlString);
			String orderNo = j.getString("orderNo");

			Map<String, Object> paramsMap = new HashMap<String, Object>();
			paramsMap.put("order_code", orderNo.split("sub")[0]);
			paramsMap.put("main_order_no", orderNo.split("sub")[0]);
			paramsMap.put("pay_date", new Date());
			paramsMap.put("pay_status","2");
			paramsMap.put("order_status","1" );
			paramsMap.put("pay_type","3" );
			paramsMap.put("pay_mode", 6);



		}catch (Exception e) {
		  	log.error("微信支付回调失败",e);
		}
		return respString;
    }

    @RequestMapping("/toUnionPay.do")
    public String toUnionPay(String respCode,String userAuthCode) {
    	System.out.println("标识信息：：："+respCode);
    	System.out.println("用户授权码" + userAuthCode);
    	return "/WEB-INF/yunPay/payMsg.jsp";
    }

    @RequestMapping("/toPayFunSelect.do")
    public String toPayFunSelect(String orderNo,String orderAmount,HttpServletRequest request) {

    	request.setAttribute("orderNo", orderNo);
    	request.setAttribute("orderAmount", orderAmount);

    	//如果订单已支付，或者支付中，应提示支付中



    	return "/WEB-INF/yunPay/payFunSelect.jsp";
    }

    @SuppressWarnings("deprecation")
	@RequestMapping(value = "/authPayLogin.do", method = RequestMethod.GET)
    public String authPayLogin(String orderNo,String orderAmount,Integer type) throws ParseException {
    	String state = orderNo + ";" +orderAmount;
    	System.out.println("当前订单信息：" + state);
    	if(type == 2) {
    		// 这个url的域名必须要进行再公众号中进行注册验证，这个地址是成功后的回调地址
    		String getCodeUrl = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" + AuthUtil.APPbaihouID + "&redirect_uri="
    				+ URLEncoder.encode(AuthUtil.AUTHPAYCALLBACKURLWXPAY) + "&response_type=code" + "&scope=snsapi_userinfo"
    				+ "&state=" ;
    		if(null == orderNo || orderAmount == null) {
    			return "/WEB-INF/yunPay/payFunSelect.jsp";
    		}else {

    		}
    		getCodeUrl = getCodeUrl + state + "#wechat_redirect";
    		System.out.println("我是微信支付");

    		return "redirect:" + getCodeUrl;// 必须重定向，否则不能成功
    	}else if(type == 1){
    		//如果是支付宝，重定向到支付宝授权界面
    		String authCodeUrl = "https://openauth.alipay.com/oauth2/publicAppAuthorize.htm?app_id=" + AuthUtil.AUTHPAYALIPAYAPPID +"&scope=auth_base&redirect_uri=" + AuthUtil.AUTHPAYCALLBACKURLALIPAY;
    		authCodeUrl += "&state=" + state;
    		return "redirect:" + authCodeUrl;
    	}else if(type == 3) {
    		System.out.println("云闪付支付");
    		//如果是云闪付
    		String authCodeUrl = "https://qr.95516.com/qrcGtwWeb-web/api/ userAuth?version=1.0.0&redirectUrl=" + AuthUtil.AUTHPAYCALLBACKURLUNIONPAY;
    		return "redirect:" + authCodeUrl;
    	}
    	return "/WEB-INF/yunPay/payFunSelect.jsp";
    }

    @RequestMapping("/toPaySuccess.do")
    public String toPaySuccess(String state,HttpServletRequest request) {
    	Map<String,Object> parMap = new HashMap<String,Object>();
    	parMap.put("orderNo", state.split(";")[0]);
    	parMap.put("payMoney", state.split(";")[1]);
    	request.setAttribute("payMsg", "");
    	request.setAttribute("payStatus", 1);
    	return "/WEB-INF/yunPay/payMsg.jsp";
    }

    //授权支付回调成功界面
    @RequestMapping("/toPurWxCallBack.do")
    public String toPurWxCallBack(HttpServletRequest request) throws ClientProtocolException, IOException, IllegalAccessException, InvocationTargetException, IntrospectionException {
    	 String code = request.getParameter("code");
         String state = request.getParameter("state");
         System.out.println("当前的code"+ code);
         System.out.println("当前的数据信息" + state);
         String orderNo = null;
         String orderAmount = null;
         String shopUnique = null;
         String type = null;
         String goodsMsg = null;
         String mqttId = null;
         String mchId = HelibaoPayConfig.PURMCHID;
         String mchKey = HelibaoPayConfig.PUBMCHKEY;
         String subMchId = "";
         if(null != state) {
        	 String[] stateArr = state.split(";");
        	 if(stateArr.length <= 1) {
        		 //转换为json形式
        		 JSONObject jo = JSONObject.parseObject(state);
        		 orderNo = jo.getString("orderNo");
        		 orderAmount = jo.getString("payMoney");
        		 type = jo.getString("orderType");
        		 shopUnique = jo.getString("shopUnique");
        		 goodsMsg = jo.containsKey("goodsMsg") ? jo.getString("goodsMsg") : "";
        		 mqttId = jo.containsKey("mqttId") ? jo.getString("") : "";
        	 }else {
        		 orderNo = stateArr.length >= 1 ? stateArr[0] : null;
        		 orderAmount = stateArr.length >= 2 ? stateArr[1] : null;
        		 type = stateArr.length >= 3 ? stateArr[2] : null;
        		 shopUnique = stateArr.length >= 4 ? stateArr[3] : null;
        	 }
         }
         request.setAttribute("payStatus", 0);
         //根据店铺信息获取店铺名称
     	Map<String,Object> payMap = wechatService.queryShopMsg(shopUnique);
     	if(type != null && type.equals("2")) {
			request.setAttribute("shopName", payMap.get("shopName"));
			mchId = payMap.get("mch_id").toString();
     		mchKey = payMap.get("mch_key").toString();
     		subMchId = payMap.get("wx_sub_id").toString();
     	}
         //保存用户的信息到数据库
         RedisCache rc = new RedisCache();
         Object o = rc.getObject(code+state);
         String cus_weixin = null;
         if(o != null) {
         	cus_weixin = o.toString();
         	System.out.println("当前微信openId===="+cus_weixin);
         	rc.putObject(code+state, cus_weixin, 7200);
         }else {

         	// 第二步：通过code换取网页授权access_token
         	String getTokenUrl = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=" + AuthUtil.APPbaihouID + "&secret="
         			+ AuthUtil.APPbaihouSECRET + "&code=" + code + "&grant_type=authorization_code";
         	JSONObject getTokenJson = AuthUtil.doGetJson(getTokenUrl);
         	logger.info("获取token,getTokenJson=" + getTokenJson.toJSONString());
         	if(null != getTokenJson.getString("errcode") && !"0".equals(getTokenJson.getString("errcode"))) {
         		//如果已经失效，重定向到登录界面，重新登录
         		return "redirect:/wechat/authPayLogin.do?&type=2&orderNo="+orderNo+"&orderAmount="+orderAmount;
         	}

         	String openid = getTokenJson.getString("openid");
         	String access_token = getTokenJson.getString("access_token");
         	String refresh_token = getTokenJson.getString("refresh_token");

         	// 第五步验证access_token是否失效；展示都不需要
         	String vlidTokenUrl = "https://api.weixin.qq.com/sns/auth?access_token=" + access_token + "&openid=" + openid;
         	logger.info("验证token,vlidTokenUrl=" + vlidTokenUrl);
         	JSONObject validTokenJson = AuthUtil.doGetJson(vlidTokenUrl);
         	logger.info("验证token,validTokenJson=" + validTokenJson.toJSONString());
         	if (!"0".equals(validTokenJson.getString("errcode"))) {
         		// 第三步：刷新access_token（如果需要）-----暂时没有使用,参考文档https://mp.weixin.qq.com/wiki，
         		String refreshTokenUrl = "https://api.weixin.qq.com/sns/oauth2/refresh_token?appid=" + openid
         				+ "&grant_type=refresh_token&refresh_token=" + refresh_token;
         		logger.info("刷新token,refreshTokenUrl=" + refreshTokenUrl);
         		JSONObject refreshTokenJson = AuthUtil.doGetJson(refreshTokenUrl);
         		/*
         		 * { "access_token":"ACCESS_TOKEN", "expires_in":7200,
         		 * "refresh_token":"REFRESH_TOKEN", "openid":"OPENID", "scope":"SCOPE" }
         		 */
         		logger.info("刷新token,refreshTokenJson=" + refreshTokenJson.toJSONString());
         		access_token = refreshTokenJson.getString("access_token");
         	}

         	// 第四步：拉取用户信息(需scope为 snsapi_userinfo)
         	String getUserInfoUrl = "https://api.weixin.qq.com/sns/userinfo?access_token=" + access_token + "&openid=" + openid
         			+ "&lang=zh_CN";
         	logger.info("获取用户信息，getUserInfoUrl=" + getUserInfoUrl.toString());
         	JSONObject getUserInfoJson = AuthUtil.doGetJson(getUserInfoUrl);
         	logger.info("获取用户信息，getUserInfoJson=" + getUserInfoJson.toString());
         	/*
         	 * end 获取微信用户基本信息
         	 */
         	cus_weixin = getUserInfoJson.getString("openid");
         	rc.putObject(code+state, cus_weixin, 7200);
         }

         //登录成功，跳转支付界面
         AppPayPublicCreateOrderVo orderVo = new AppPayPublicCreateOrderVo();
 		//随机产生新的订单号，防止更换支付方式

       //根据不同的客户支付类型，回调不同的路径
		String notifyUrl = null;
		if(null == type || type.equals("") || type.equals("1")) {
			notifyUrl = AuthUtil.AUTHPAYSUCCESSNOTIFYURL;
		}else if(type.equals("2")) {
			notifyUrl = AuthUtil.QRCODEPAYCALLBACK;
		}

 		orderVo.setP1_bizType("AppPayPublic");
 		orderVo.setP2_orderId(orderNo);
 		orderVo.setP3_customerNumber(mchId);
 		orderVo.setP4_payType("PUBLIC");
 		orderVo.setP5_appid("wxd7ad1410f6eb7757");
 		orderVo.setP6_deviceInfo("WEB");
 		orderVo.setP7_isRaw("1");
 		orderVo.setP8_openid(cus_weixin);
 		orderVo.setP9_orderAmount(orderAmount);
 		orderVo.setP10_currency("CNY");
 		orderVo.setP11_appType("WXPAY");
 		orderVo.setP12_notifyUrl(notifyUrl);
 		orderVo.setP13_successToUrl(ProjectConfig.PROJECT_URL + "c/shopUpdate/wechat/toPaySuccess.do?state=" + state);
 		orderVo.setP14_orderIp("***************");
 		orderVo.setP15_goodsName("金圈供货商城");
 		orderVo.setP18_desc(shopUnique);
 		orderVo.setP20_subMerchantId(subMchId);


 		 Map<String, String> map = MyBeanUtils.convertBean(orderVo, new LinkedHashMap());
 		 String oriMessage = MyBeanUtils.getSigned(map, new String[]{"P19_subscribeAppId" , "P20_subMerchantId", "P21_goodsTag",
                  "P22_guid", "timeExpire", "industryRefluxInfo", "foodOrderType", "termInfo"},mchKey);
          logger.info("签名原文串：" + oriMessage);
          String sign = Disguiser.disguiseMD5(oriMessage.trim());
          logger.info("签名串：" + sign);
          map.put("sign", sign);
          logger.info("发送参数：" + map);
          Map<String, Object> resultMap = HttpClientService.getHttpResp(map, Constant.REQUEST_URL);
          logger.info("响应结果：" + resultMap);
          if ((Integer) resultMap.get("statusCode") == HttpStatus.SC_OK) {
         	  String resultMsg = (String) resultMap.get("response");
               AppPayPublicOrderResponseVo orderResponseVo = JSONObject.parseObject(resultMsg, AppPayPublicOrderResponseVo.class);
               //建议是加签/验签的固定参数,具体按照文档要求加签.因为新增参数需要历史兼容是排除签名的
               String[] excludes = {"rt3_retMsg", "rt13_channelRetCode", "subMerchantNo"};
               String assemblyRespOriSign = MyBeanUtils.getSigned(orderResponseVo, excludes, mchKey);
               logger.info("组装返回结果签名串：" + assemblyRespOriSign);
               String responseSign = orderResponseVo.getSign();
               logger.info("响应签名：" + responseSign);
               String checkSign = Disguiser.disguiseMD5(assemblyRespOriSign.trim());
               if (checkSign.equals(responseSign)) {
                   if ("0000".equals(orderResponseVo.getRt2_retCode())) {
                       JSONObject jsonObject = JSONObject.parseObject(resultMsg);
                       request.setAttribute("payMsg", jsonObject.getJSONObject("rt10_payInfo"));
                       request.setAttribute("orderNo", orderNo);
                       request.setAttribute("orderAmount", orderAmount);
                       request.setAttribute("shopUnique", shopUnique);
                       request.setAttribute("mqttId", mqttId);
                       request.setAttribute("goodsMsg", goodsMsg);
                   } else {

                   }
               } else {
               }
          }
          System.out.println("当前请求类型" + type);
         if(type == null || type.equals("") || type.equals("1")) {
        	 return "/WEB-INF/yunPay/payMsg.jsp";
         }else if(type.equals("2")){
        	 return "/WEB-INF/pay/payForShop.jsp";
         }else if(type.equals("3")) {
        	 return "";
         }
         return null;
    }


    @RequestMapping("/luckDraw.do")
    @ResponseBody
    public ShopsResult luckDraw(String saleListUnique,String openId) {
    	return wechatService.luckDraw(saleListUnique, openId);
    }

    @RequestMapping("/getLoList.do")
    @ResponseBody
    public ShopsResult getLoList() {
    	RedisCache rc = new RedisCache();
    	List<String> loList = null;
		loList = rc.getList(AuthUtil.NINGYULOTTERYID);

		ShopsResult sr = new ShopsResult();
		sr.setData(loList);
		return sr;
    }

    @RequestMapping("/toLottery.do")
    public String toLottery(String saleListUnique,String openId,HttpServletRequest request) {
    	RedisCache rc = new RedisCache();
    	String lot = rc.getObject(saleListUnique+openId) == null ? "0" : rc.getObject(saleListUnique+openId).toString();
    	rc.putObject(saleListUnique + openId, 4,3600);
    	System.out.println("当前可用的抽奖次数为"+lot);
    	if(lot.startsWith("NO")) {
    		request.setAttribute("lotteryCount", 0);
    	}else {
    		request.setAttribute("lotteryCount", lot);
    	}

    	request.setAttribute("saleListUnique", saleListUnique);
    	request.setAttribute("openId", openId);

    	//获取配置信息
    	request.setAttribute("lotSet", wechatService.queryRechargeActive());
    	return "/WEB-INF/wechat/index.jsp";
    }

    @RequestMapping("/toPayOrder.do")
    public String toPayOrder(String orderNo,HttpServletRequest request) {
    	request.setAttribute("orderNo", orderNo);

    	return "/WEB-INF/wechat/payOrder.jsp";
    }



    /**
  	 * 生成普通支付订单，使用无码商品
  	 */
  	@RequestMapping("/generateNewOrder.do")
  	@ResponseBody
  	public ShopsResult generateWXOrderN(
  			HttpServletRequest request,int total_fee,String out_trade_no,Double saleListTotal,String cus_weixin) {
  		ShopsResult rs=new ShopsResult();
  		try {
  			if(null == out_trade_no) {
  				Calendar c = Calendar.getInstance();
  				out_trade_no = "" + c.getTimeInMillis();
  			}
  			AppCreateOrderVo vo = new AppCreateOrderVo();
  			vo.setP1_bizType("AppPayPublic");
  			vo.setP2_orderId(out_trade_no);
  		}catch(Exception e) {
  			log.error("微信统一下单失败！",e);
  			rs.setStatus(0);
  			rs.setMsg("微信统一下单失败！");
  		}
  		return rs;
  	}
  	/**
  	 * 1、超市展示付款码，客户扫码后，通过公众号支付回调，包含合利宝和微信
  	 * @param request
  	 * @param response
  	 * @return
  	 */
  	@RequestMapping("/qrcodePayCallBack.do")
  	@ResponseBody
  	public synchronized String qrcodePayCallBack(HttpServletRequest request,HttpServletResponse response) {
  		String respString = "success";
		try {
			String xmlString = XMLUtils.parseRequst(request);
			JSONObject j = JSONObject.parseObject(xmlString);
			String orderId = j.getString("orderNo");
			String payAmt = j.getString("payAmt");
			System.out.println("支付回调结果" + j);
			System.out.println(orderId + "===" + payAmt);

			String cupsReqReserved = j.getString("cupsReqReserved");
			//将结果发送到shop项目，完成回调
			Map<String,Object> parMap = new HashMap<String,Object>();
			parMap.put("orderNo", orderId);
			parMap.put("payAmt", payAmt);
			System.out.println(cupsReqReserved);
			//
			//找到对应的订单信息，修改订单信息，并添加支付记录，各店铺需要有自己的付款码，所以不再判断余额信息
			HttpUtil.doGet(ProjectConfig.PROJECT_URL + "/harricane/payOnline/queryOrderYT.do?out_trade_no=" + orderId + "&shopUnique=" + cupsReqReserved);
			
		}catch (Exception e) {
		  	log.error("支付回调失败！",e);
		}
		return respString;
  	}

	/**
	 * 供货商城-微信支付，支付宝支付回调
	 */
	@RequestMapping(value = "/authPaySuccessCallBack.do" ,method = RequestMethod.POST)
	@ResponseBody
	public synchronized String authPaySuccessCallBack(HttpServletRequest request,HttpServletResponse response) throws ParseException{
		String respString = "success";
		try {
			String xmlString = XMLUtils.parseRequst(request);
			JSONObject j = JSONObject.parseObject(xmlString);
			String orderId = j.getString("orderNo");
			String payAmt = j.getString("payAmt");
			System.out.println("支付回调结果" + j);
			System.out.println(orderId + "===" + payAmt);

			//将结果发送到shop项目，完成回调
			Map<String,Object> parMap = new HashMap<String,Object>();
			parMap.put("orderNo", orderId);
			parMap.put("payAmt", payAmt);

			HttpUtil.doGet(AuthUtil.AUTHPAYSUCCESSSHOPNOTIFYURL+"?orderNo=" + orderId + "&payAmt=" + payAmt,null);

		}catch (Exception e) {
		  	log.error("支付回调失败！",e);
		}
		return respString;
	}

    @RequestMapping("/generateNewWXOrderNy.do")
    @ResponseBody
    public ShopsResult generateNewWXOrderNy(String orderNo,String cus_weixin,String orderAmount) {
    	ShopsResult rs = new ShopsResult();
    	Map<String, String> paramMap = new TreeMap<String,String>();
		paramMap.put("body", "宁宇会员充值");
		paramMap.put("out_trade_no", orderNo);
		paramMap.put("total_fee", String.valueOf(orderAmount));
//		paramMap.put("total_fee", "1");
		paramMap.put("spbill_create_ip", "***************");
		paramMap.put("notify_url", AuthUtil.NYRECHARGENOTEURL);
		paramMap.put("trade_type", "JSAPI");
		paramMap.put("openid", cus_weixin);
		//统一下单
		Map<String, String> resMap = null;
		try {
			resMap = wxPay.unifiedOrder(paramMap);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			log.error("微信统一下单失败！",e);
		}
		rs.setStatus(1);
		rs.setData(resMap);
		rs.setMsg("微信统一下单成功！");

    	return rs;
    }

    @SuppressWarnings("deprecation")
	@RequestMapping(value = "/wxLogin", method = RequestMethod.GET)
    public String wxLogin(String orderNo,String orderAmount) throws ParseException {
        // 这个url的域名必须要进行再公众号中进行注册验证，这个地址是成功后的回调地址
        // 第一步：用户同意授权，获取code
        String getCodeUrl = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" + AuthUtil.APPID + "&redirect_uri="
                + URLEncoder.encode(AuthUtil.AUTHCALLBACKURL) + "&response_type=code" + "&scope=snsapi_userinfo"
                + "&state=" ;
        String state = "";
        if(null != orderNo && null != orderAmount) {
        	state = "1;"+orderNo+";"+orderAmount;
        }else {
        	state = "STATE";
        }
        getCodeUrl = getCodeUrl + state + "#wechat_redirect";
        return "redirect:" + getCodeUrl;// 必须重定向，否则不能成功
    }


    /**
     * 公众号微信登录授权回调函数
     *
     * @return
     * @throws ServletException
     * @throws IOException
     * @parameter
     */
    @RequestMapping(value = "/callBack")
    public String callBack() throws ServletException, IOException {

        String code = request.getParameter("code");
        String state = request.getParameter("state");
        String type = null;
        if(null != state) {
        	type = state.split(";")[0];
        	if(type.equals("1")) {
        		String orderNo = state.split(";")[1];
        		String orderAmount = state.split(";")[2];
        		request.setAttribute("orderNo", orderNo);
        		request.setAttribute("orderAmount", orderAmount);
        	}
        }
        //保存用户的信息到数据库
        Map<String,Object> cusMap = null;
        RedisCache rc = new RedisCache();
        Object o = rc.getObject(code);
        String cus_weixin = null;
        if(o != null) {
        	cus_weixin = o.toString();
        	System.out.println("当前微信openId===="+cus_weixin);
        	rc.putObject(code, cus_weixin, 7200);
        }else {

        	// 第二步：通过code换取网页授权access_token
        	String getTokenUrl = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=" + AuthUtil.APPID + "&secret="
        			+ AuthUtil.APPSECRET + "&code=" + code + "&grant_type=authorization_code";
        	JSONObject getTokenJson = AuthUtil.doGetJson(getTokenUrl);
        	/*
        	 * { "access_token":"ACCESS_TOKEN", "expires_in":7200,
        	 * "refresh_token":"REFRESH_TOKEN", "openid":"OPENID", "scope":"SCOPE" }
        	 */
        	logger.info("获取token,getTokenJson=" + getTokenJson.toJSONString());
        	if(null != getTokenJson.getString("errcode") && !"0".equals(getTokenJson.getString("errcode"))) {
        		//如果已经失效，重定向到登录界面，重新登录
        		return "redirect:/wechat/wxLogin.do";
        	}

        	String openid = getTokenJson.getString("openid");
        	String access_token = getTokenJson.getString("access_token");
        	String refresh_token = getTokenJson.getString("refresh_token");

        	// 第五步验证access_token是否失效；展示都不需要
        	String vlidTokenUrl = "https://api.weixin.qq.com/sns/auth?access_token=" + access_token + "&openid=" + openid;
        	logger.info("验证token,vlidTokenUrl=" + vlidTokenUrl);
        	JSONObject validTokenJson = AuthUtil.doGetJson(vlidTokenUrl);
        	logger.info("验证token,validTokenJson=" + validTokenJson.toJSONString());
        	if (!"0".equals(validTokenJson.getString("errcode"))) {
        		// 第三步：刷新access_token（如果需要）-----暂时没有使用,参考文档https://mp.weixin.qq.com/wiki，
        		String refreshTokenUrl = "https://api.weixin.qq.com/sns/oauth2/refresh_token?appid=" + openid
        				+ "&grant_type=refresh_token&refresh_token=" + refresh_token;
        		logger.info("刷新token,refreshTokenUrl=" + refreshTokenUrl);
        		JSONObject refreshTokenJson = AuthUtil.doGetJson(refreshTokenUrl);
        		/*
        		 * { "access_token":"ACCESS_TOKEN", "expires_in":7200,
        		 * "refresh_token":"REFRESH_TOKEN", "openid":"OPENID", "scope":"SCOPE" }
        		 */
        		logger.info("刷新token,refreshTokenJson=" + refreshTokenJson.toJSONString());
        		access_token = refreshTokenJson.getString("access_token");
        	}

        	// 第四步：拉取用户信息(需scope为 snsapi_userinfo)
        	String getUserInfoUrl = "https://api.weixin.qq.com/sns/userinfo?access_token=" + access_token + "&openid=" + openid
        			+ "&lang=zh_CN";
        	logger.info("获取用户信息，getUserInfoUrl=" + getUserInfoUrl.toString());
        	JSONObject getUserInfoJson = AuthUtil.doGetJson(getUserInfoUrl);
        	logger.info("获取用户信息，getUserInfoJson=" + getUserInfoJson.toString());
        	/*
        	 * end 获取微信用户基本信息
        	 */
        	cus_weixin = getUserInfoJson.getString("openid");
        	rc.putObject(code, cus_weixin, 7200);
        }
        if(type != null && type.equals("1")) {
        	request.setAttribute("cus_weixin", cus_weixin);
        	return "/WEB-INF/wechat/payMsg.jsp";
        }
        cusMap = wechatService.checkCusMsg(null, cus_weixin,null, null,null);

        request.setAttribute("cus_weixin", cusMap.get("cus_weixin"));
        request.setAttribute("cus_phone", cusMap.get("cus_phone") == null ? "" : cusMap.get("cus_phone"));
        String status = cusMap.get("status").toString();
        if(status.equals("0")) {
        	return "/WEB-INF/wechat/login.jsp";
//        	return "redirect:/shopUpdate/wechat/toMain.do";
        }else {
        	request.setAttribute("cus_points", cusMap.get("cus_points"));
    		request.setAttribute("cus_balance", cusMap.get("cus_balance"));
    		request.setAttribute("cus_id", cusMap.get("cus_id"));

    		String cusPhone = cusMap.get("cus_phone").toString();
    		request.setAttribute("phone1", cusPhone.substring(0, 3));
    		request.setAttribute("phone2", cusPhone.substring(3, 7));
    		request.setAttribute("phone3", cusPhone.substring(7, 11));

    		System.out.println(cusPhone.substring(0,3));
    		System.out.println(cusPhone.substring(3,7));
    		System.out.println(cusPhone.substring(7,11));

        	return "/WEB-INF/wechat/main.jsp";

        }

    }

    @RequestMapping("/addNewCusMsg.do")
    @ResponseBody
    public ShopsResult addNewCusMsg(String cus_phone,String cus_name,String cus_sex,String cus_birthday,String cus_weixin) {
    	try {
    		System.out.println(cus_phone);
    		System.out.println(cus_name);
    		System.out.print(cus_birthday);
    		System.out.println(cus_weixin);
    		return wechatService.addNewCusMsg(cus_phone,cus_weixin, cus_name, cus_birthday, cus_sex);
    	}catch (Exception e) {
    		log.error("添加会员信息失败！",e);
    		return new ShopsResult(0,"添加会员信息失败！");
		}
    }

    @RequestMapping("/toMain.do")
    public String toMain(String cus_weixin,HttpServletRequest request) {
    	Map<String,Object> cusMsg = wechatService.getCusMsg(cus_weixin);
    	if(null != cusMsg && !cusMsg.isEmpty()) {
    		request.setAttribute("cus_weixin", cus_weixin);
    		request.setAttribute("cus_phone", cusMsg.get("cus_phone"));
    		request.setAttribute("cus_points", cusMsg.get("cus_points"));
    		request.setAttribute("cus_balance", cusMsg.get("cus_balance"));
    		request.setAttribute("cus_id", cusMsg.get("cus_id"));
    		String cusPhone = cusMsg.get("cus_phone").toString();
    		request.setAttribute("phone1", cusPhone.substring(0, 3));
    		request.setAttribute("phone2", cusPhone.substring(3, 7));
    		request.setAttribute("phone3", cusPhone.substring(7, 11));

    		System.out.println(cusPhone.substring(0,3));
    		System.out.println(cusPhone.substring(3,7));
    		System.out.println(cusPhone.substring(7,11));
    	}
    	return "/WEB-INF/wechat/main.jsp";
    }

    @RequestMapping("/toCusRule.do")
    public String toCusRule() {
    	return "/WEB-INF/wechat/cusRule.jsp";
    }

    @RequestMapping("/toCusExplain.do")
    public String toCusExplain() {
    	return "/WEB-INF/wechat/cusExplain.jsp";
    }

    @RequestMapping("/toCusBarcode.do")
    public String toCusBarcode() {
    	return "/WEB-INF/wechat/cusBarcode.jsp";
    }

    @RequestMapping("/toRecharge.do")
    public String toRecharge() {
    	return "/WEB-INF/wechat/recharge.jsp";
    }

    public WEChatController() {
    	try {
		      //初始化微信支付客户端
		      config = new PublicPayConfig();
		      wxPay = new WXPay(config);
		   } catch (Exception e) {
		      log.error("初始化微信支付客户端失败！",e);
		   }
    }

    @RequestMapping("/twxPayCallBack.do")
    @ResponseBody
    public void twxPayCallBack(String saleListUnique,Integer rechargeMoney) {
    	wechatService.completeCusRecharge(saleListUnique,rechargeMoney);
    }
	/**
	 * 供货商城-微信支付回调
	 */
	@RequestMapping(value = "/wxPayCallBack.do" ,method = RequestMethod.POST)
	@ResponseBody
	public synchronized String wxPayCallBack(HttpServletRequest request,HttpServletResponse response) throws ParseException{
		String respString = "fail";
		try {
			 String xmlString = XMLUtils.parseRequst(request);
			 System.out.println("微信支付回调===="+xmlString);
//			 xmlString = "<xml><appid><![CDATA[wxd7ad1410f6eb7757]]></appid><bank_type><![CDATA[OTHERS]]></bank_type><cash_fee><![CDATA[1]]></cash_fee><fee_type><![CDATA[CNY]]></fee_type><is_subscribe><![CDATA[Y]]></is_subscribe><mch_id><![CDATA[**********]]></mch_id><nonce_str><![CDATA[qsxfNxYvg2zb5wh7oIA3GZ5xJnoBS57i]]></nonce_str><openid><![CDATA[omeMtw91SOngSk4UyjOxUX2hHy7w]]></openid><out_trade_no><![CDATA[*************]]></out_trade_no><result_code><![CDATA[SUCCESS]]></result_code><return_code><![CDATA[SUCCESS]]></return_code><sign><![CDATA[5C4EE118079E8EBA3BD5F72585F314D6]]></sign><time_end><![CDATA[**************]]></time_end><total_fee>1</total_fee><trade_type><![CDATA[JSAPI]]></trade_type><transaction_id><![CDATA[4200000848202011206806962034]]></transaction_id></xml>";
			 Map<String,String> map = WXPayUtil.xmlToMap(xmlString);
			 if(map.containsKey("sign")){
                if(!AuthUtil.checkKey(map, AuthUtil.MCHKEY)){
                    respString = "fail";
                }else{
                	if ("SUCCESS".equals(map.get("result_code"))) {
               			//支付成功
                		String saleListUnique = map.get("out_trade_no").toString();
                		Integer rechargeMoney = Integer.parseInt(map.get("total_fee").toString());
                		String openId = map.get("openid");

                		String id = saleListUnique+openId;
                		RedisCache rc = new RedisCache();
                		System.out.println("回调时的redisId" + id);
                		String lot = rc.getObject(id) == null ? "0":rc.getObject(id).toString();
                		System.out.println("当前充值剩余次数" + lot);
                		if(lot.startsWith("NO")) {
                			lot = lot.substring(2);
                			System.out.println("修改后的充值次数"+lot);
                			rc.putObject(id, lot,3600*24);
                			System.out.println("存储的抽奖信息："+rc.getObject(id));
                		}

                		boolean flag = wechatService.completeCusRecharge(saleListUnique,rechargeMoney);
                		if(flag) {
                			respString = "success";
                		}
                	}
                }
            }
		}catch (Exception e) {
		  	log.error("微信支付回调失败",e);
		}
		return respString;
	}

	@RequestMapping("/createNewOrderForWX.do")
	@ResponseBody
	public ShopsResult createNewOrderForWX(String orderNo,Double total_fee,String openId) throws IllegalAccessException, InvocationTargetException, IntrospectionException {
		ShopsResult sr = new ShopsResult();
		AppPayPublicCreateOrderVo orderVo = new AppPayPublicCreateOrderVo();
		//随机产生新的订单号，防止更换支付方式
		Random r = new Random();
		orderVo.setP1_bizType("AppPayPublic");
		orderVo.setP2_orderId(orderNo+r.nextInt(100));
		orderVo.setP3_customerNumber(HelibaoPayConfig.PURMCHID);
		orderVo.setP4_payType("PUBLIC");
		orderVo.setP5_appid("wxd7ad1410f6eb7757");
		orderVo.setP6_deviceInfo("WEB");
		orderVo.setP7_isRaw("1");
		orderVo.setP8_openid(openId);
		orderVo.setP9_orderAmount(total_fee+"");
		orderVo.setP10_currency("CNY");
		orderVo.setP11_appType("WXPAY");
		orderVo.setP12_notifyUrl("");
		orderVo.setP14_orderIp("***************");
		orderVo.setP15_goodsName("测试商品");


		 Map<String, String> map = MyBeanUtils.convertBean(orderVo, new LinkedHashMap());
		 String oriMessage = MyBeanUtils.getSigned(map, new String[]{"P19_subscribeAppId", "P21_goodsTag",
                 "P22_guid", "timeExpire", "industryRefluxInfo", "foodOrderType", "termInfo"},HelibaoPayConfig.PUBMCHKEY);
         logger.info("签名原文串：" + oriMessage);
         String sign = Disguiser.disguiseMD5(oriMessage.trim());
         logger.info("签名串：" + sign);
         map.put("sign", sign);
         logger.info("发送参数：" + map);
         Map<String, Object> resultMap = HttpClientService.getHttpResp(map, Constant.REQUEST_URL);
         logger.info("响应结果：" + resultMap);
         if ((Integer) resultMap.get("statusCode") == HttpStatus.SC_OK) {
        	  String resultMsg = (String) resultMap.get("response");
              AppPayPublicOrderResponseVo orderResponseVo = JSONObject.parseObject(resultMsg, AppPayPublicOrderResponseVo.class);
              //建议是加签/验签的固定参数,具体按照文档要求加签.因为新增参数需要历史兼容是排除签名的
              String[] excludes = {"rt3_retMsg", "rt13_channelRetCode", "subMerchantNo"};
              String assemblyRespOriSign = MyBeanUtils.getSigned(orderResponseVo, excludes,HelibaoPayConfig.PUBMCHKEY);
              logger.info("组装返回结果签名串：" + assemblyRespOriSign);
              String responseSign = orderResponseVo.getSign();
              logger.info("响应签名：" + responseSign);
              String checkSign = Disguiser.disguiseMD5(assemblyRespOriSign.trim());
              if (checkSign.equals(responseSign)) {
                  if ("0000".equals(orderResponseVo.getRt2_retCode())) {
                      JSONObject jsonObject = JSONObject.parseObject(resultMsg);
                	  sr.setData(jsonObject);
                	  sr.setData(jsonObject.getJSONObject("rt10_payInfo"));
                  } else {
                  }
              } else {
              }
         }
		return sr;
	}

    /**
	 * 生成预支付交易单-微信
	 */
	@RequestMapping("/generateNewWXOrder.do")
	@ResponseBody
	public ShopsResult generateWXOrder(
			HttpServletRequest request,int total_fee,String out_trade_no,Double rechargeMoney,Integer points,
			String cus_weixin,String cus_id,Integer give_money) {
		ShopsResult rs=new ShopsResult();
		try {
			if(null == out_trade_no) {
				Calendar c = Calendar.getInstance();
				out_trade_no = "" + c.getTimeInMillis();
			}

			Map<String,Object> rechargeRecord = new HashMap<String,Object>();
			rechargeRecord.put("cusId", cus_id);
			rechargeRecord.put("rechargeMoney", rechargeMoney);
			rechargeRecord.put("cusType", 1);//充值
			rechargeRecord.put("saleListUnique", out_trade_no);
			rechargeRecord.put("sale_points", points);
			rechargeRecord.put("recharge_method", 7);
			rechargeRecord.put("remarks", "微信公众号充值");
			rechargeRecord.put("give_money", give_money);
			rechargeRecord.put("shop_unique", AuthUtil.SHOPUNIQUE);
			rechargeRecord.put("recharge_status", 2);

			//添加充值记录
			rechargeRecord = wechatService.addRecharge(rechargeRecord,rechargeMoney,cus_id,cus_weixin);
			/*
			 * 支付成功后的操作;
			 * 1、修改支付记录，增加充值后余额等信息，修改订单状态
			 * 2、增加充值使用表数据；
			 * 3、增加用户余额信息；
			 */
			System.out.println("本次会员充值金额为：：："+total_fee);

			Map<String, String> paramMap = new TreeMap<String,String>();
			paramMap.put("body", "宁宇会员充值");
			paramMap.put("out_trade_no", out_trade_no);
			paramMap.put("total_fee", String.valueOf(total_fee));
//			paramMap.put("total_fee", "1");
			paramMap.put("spbill_create_ip", "***************");
			paramMap.put("notify_url", AuthUtil.RECHARGENOTEURL);
			paramMap.put("trade_type", "JSAPI");
			paramMap.put("openid", cus_weixin);
			//统一下单
			Map<String, String> resMap = wxPay.unifiedOrder(paramMap);

			System.out.println("返回请求的数据" + rechargeRecord);
			resMap.put("lotteryCount", rechargeRecord.get("lotteryCount").toString());
			resMap.put("saleListUnique", out_trade_no);
			rs.setStatus(1);
			rs.setData(resMap);
			rs.setMsg("微信统一下单成功！");
		}catch(Exception e) {
			log.error("微信统一下单失败！",e);
			rs.setStatus(0);
			rs.setMsg("微信统一下单失败！");
		}
		return rs;
	}

	/**
	 * 查询订单
	 */
	@RequestMapping("/queryWXOrder.do")
	@ResponseBody
	public ShopsResult queryOrderWX(HttpServletRequest request,String out_trade_no) {
		ShopsResult rs=new ShopsResult();
		try {
			Map<String, String> paramMap = new TreeMap<String,String>();
			paramMap.put("out_trade_no", out_trade_no);
			//统一下单
			Map<String, String> resMap =wxPay.orderQuery(paramMap);
			rs.setStatus(0);
			rs.setData(resMap);
			rs.setMsg("微信-订单查询成功！");
		}catch(Exception e) {
			log.error("微信-订单查询失败！",e);
			rs.setStatus(1);
			rs.setMsg("微信-订单查询失败！");
		}
		return rs;
	}
	/**
	 * 微信公众号开发-JS-SDK使用权限签名
	 * @param url
	 * @return
	 */
	@RequestMapping(value = "/getSignature.do",method = RequestMethod.POST)
	@ResponseBody
	public ShopsResult getSignature(HttpServletRequest request,String url) {
		ShopsResult rs=new ShopsResult();
		 try {
//				Enumeration<String> enu=request.getParameterNames();//参数列表
//				while (enu.hasMoreElements()) {
//					String string = (String) enu.nextElement();
////						url=request.getParameter(param);
//						url+=request.getParameter(string);
//				}
			  if (url==null||url.equals("")) {
			    	rs.setStatus(1);
					rs.setMsg("获取失败！");
					 return rs;
			    }
			  logger.info("url:"+url);
			 //反编译url
//	    	 url= URLDecoder.decode(url);
	    	//查询缓存
			RedisCache rc = new RedisCache();
	        Object o = rc.getObject("jsapi_ticket");
	        String ticket = null;
	        if(o != null) {
	        	ticket = o.toString();
	        	logger.info("ticket使用缓存"+ticket);
	        }else {

	    	    //1获取accessToken
	    	    String getTokenUrl = "https://api.weixin.qq.com/cgi-bin/token?appid=" + AuthUtil.APPbaihouID + "&secret="+ AuthUtil.APPbaihouSECRET +"&grant_type=client_credential";
				System.out.println("获取tokenURL" + getTokenUrl);
	    	    JSONObject jsonObject = AuthUtil.doGetJson(getTokenUrl);
				System.out.println("获取token结果" + jsonObject);
	    	    String accessToken = jsonObject.getString("access_token");
				System.out.println("获取token" + accessToken);
	    		//2获取jsapi_ticket
	    		String getJsapi_ticketUrl="https://api.weixin.qq.com/cgi-bin/ticket/getticket?type=jsapi&access_token="+accessToken;
	    		JSONObject getJsapi_ticket;
	    		getJsapi_ticket = AuthUtil.doGetJson(getJsapi_ticketUrl);
				System.out.println("获取ticket" + getJsapi_ticket);
				ticket = getJsapi_ticket.getString("ticket");
				rc.putObject("jsapi_ticket", ticket, 7200);
				logger.info("ticket添加缓存"+ticket);

	        }
	        String noncestr =  WXPayUtil.generateNonceStr();
	        long timeMillis = System.currentTimeMillis() / 1000;

		    String str = "jsapi_ticket=" + ticket + "&noncestr=" + noncestr + "&timestamp=" + timeMillis + "&url=" + url;
		    String signature = sha1(str);
		    logger.info("String1"+str);
		    Map<String, String> map = new HashMap<>();
		    map.put("noncestr", noncestr);
		    map.put("time_millis", timeMillis + "");
		    map.put("app_id", AuthUtil.APPbaihouID);
		    map.put("signature", signature);
		    rs.setStatus(0);
			rs.setData(map);
		} catch (ClientProtocolException e) {
			// TODO Auto-generated catch block
			log.error("获取失败！", e);
			rs.setStatus(1);
			rs.setMsg("获取失败！");
		} catch (IOException e) {
			// TODO Auto-generated catch block
			log.error("获取失败！", e);
			rs.setStatus(1);
			rs.setMsg("获取失败！");
		}

	    return rs;
	}
	public static String sha1(String decript) {
	    try {
	        MessageDigest digest = java.security.MessageDigest.getInstance("SHA-1");
	        digest.update(decript.getBytes());
	        byte[] messageDigest = digest.digest();
	        // Create Hex String
	        StringBuilder hexString = new StringBuilder();
	        // 字节数组转换为 十六进制 数
	        for (byte b : messageDigest) {
	            String shaHex = Integer.toHexString(b & 0xFF);
	            if (shaHex.length() < 2) {
	                hexString.append(0);
	            }
	            hexString.append(shaHex);
	        }
	        return hexString.toString();
	    } catch (NoSuchAlgorithmException e) {
	        logger.error("微信签名时失败,请检查!", e);
	    }
	    return "";
	}
}
