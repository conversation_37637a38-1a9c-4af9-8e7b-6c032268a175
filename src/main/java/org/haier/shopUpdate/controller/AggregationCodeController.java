package org.haier.shopUpdate.controller;

import org.haier.shopUpdate.params.GetAuditStatusParams;
import org.haier.shopUpdate.params.GetQualificationsParams;
import org.haier.shopUpdate.params.SaveAggregationCodeParams;
import org.haier.shopUpdate.service.AggregationCodeService;
import org.haier.shopUpdate.util.ShopsResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/shopQualificationInfo/aggregationCode")
public class AggregationCodeController {

    @Autowired
    private AggregationCodeService aggregationCodeService;

    /**
     * 查询聚合码审核状态
     * @param params
     * @return
     */
    @RequestMapping("/getAuditStatus.do")
    @ResponseBody
    public ShopsResult getAuditStatus(GetAuditStatusParams params){
        return aggregationCodeService.getAuditStatus(params);
    }

    /**
     * 查询资质信息
     * @param params
     * @return
     */
    @RequestMapping("/getQualifications.do")
    @ResponseBody
    public ShopsResult getQualifications(GetQualificationsParams params){
        return aggregationCodeService.getQualifications(params);
    }

    /**
     * 保存资质信息
     * @param params
     * @return
     */
    @RequestMapping(value = "/saveAggregationCode.do",method = RequestMethod.POST)
    @ResponseBody
    public ShopsResult saveAggregationCode(SaveAggregationCodeParams params){
        return aggregationCodeService.saveAggregationCode(params);
    }

}
