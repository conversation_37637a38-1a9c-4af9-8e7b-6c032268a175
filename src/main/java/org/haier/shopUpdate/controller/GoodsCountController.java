package org.haier.shopUpdate.controller;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

//import org.apache.log4j.Logger;
import org.haier.shopUpdate.service.GoodsService;
import org.haier.shopUpdate.util.ShopsResult;
//import org.haier.shopUpdate.util.ShopsUtil;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/goodsCount")
public class GoodsCountController {
	@Resource
	private GoodsService goodsService;
	
//	private static Logger log=Logger.getLogger(GoodsCountController.class);
	/**
	 * 库存查询
	 */
	@RequestMapping("/queryGoodsCount.do")
	@ResponseBody
	public ShopsResult queryGoodsCount(
			String shopUnique,
			String goodsMessage,
			String goods_kind_unique,
			String goods_kind_parunique,
			@RequestParam(value="pageIndex",defaultValue="1")Integer pageIndex,
			@RequestParam(value="pageSize",defaultValue="10")Integer pageSize,
			@RequestParam(value="orderType",defaultValue="1",required=false)Integer orderType
			,HttpServletRequest request
			){
		
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("startNum", (pageIndex-1)*pageSize);
		map.put("pageSize", pageSize);
		if(null!=goodsMessage&&!"".equals(goodsMessage)){
			map.put("goodsMessage", "%"+goodsMessage+"%");
		}
		map.put("goods_kind_unique", goods_kind_unique);
		map.put("goods_kind_parunique", goods_kind_parunique);
		if(orderType==1){
			map.put("orderType", " asc ");
		}else{
			map.put("orderType", " desc ");
		}
		System.out.println("库存查询--"+map);
		return goodsService.queryGoodsCount(map);
	}
	/**
	 * 库存预警
	 */
	@RequestMapping("/queryGoodsCountWarning.do")
	@ResponseBody
	public ShopsResult queryGoodsCountWarning(
			String shopUnique,
			String goodsMessage,
			String goods_kind_unique,
			String goods_kind_parunique,
			@RequestParam(value="pageIndex",defaultValue="1")Integer pageIndex,
			@RequestParam(value="pageSize",defaultValue="10")Integer pageSize,
			@RequestParam(value="warningType",defaultValue="1")Integer warningType
			,HttpServletRequest request
			){
		
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		map.put("startNum", (pageIndex-1)*pageSize);
		map.put("pageSize", pageSize);
		map.put("warningType", warningType);
		if(null!=goodsMessage&&!"".equals(goodsMessage)){
			map.put("goodsMessage", "%"+goodsMessage+"%");
		}
		if(!"-1".equals(goods_kind_unique)){
			map.put("goods_kind_unique", goods_kind_unique);
			
		}
		if(!"-1".equals(goods_kind_parunique)){
			map.put("goods_kind_parunique", goods_kind_parunique);
		}
		map.put("orderType", " asc ");
		System.out.println("库存预警--"+map);
		return goodsService.queryGoodsCountWarning(map);
	}
	
	

}
