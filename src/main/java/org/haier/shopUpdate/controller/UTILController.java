package org.haier.shopUpdate.controller;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

//import org.apache.log4j.Logger;
import org.haier.shopUpdate.service.UtilService;
import org.haier.shopUpdate.util.ShopsResult;
//import org.haier.shopUpdate.util.ShopsUtil;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 此CONTROLLER用于处理一些常见的BUG 
 * <AUTHOR>
 *
 */
@Controller
public class UTILController {
	@Resource
	private UtilService utilService;
//	private static Logger log=Logger.getLogger(UTILController.class);
	//去除重复商品
	/**
	 * 将店铺中重复的商品删除并将云库中没有的商品添加到云库中
	 * @param map
	 * @return
	 */
	@RequestMapping("/deleteSameGoods.do")
	@ResponseBody
	public ShopsResult deleteSameGoods(
			@RequestParam(value="shopUnique",required=true)Long shopUnique,
			HttpServletRequest request
			){
		
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		return utilService.deleteSameGoods(map);
	}
	
}
