package org.haier.shopUpdate.controller;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

//import org.apache.log4j.Logger;
import org.haier.shopUpdate.service.SupplierService;
import org.haier.shopUpdate.util.ShopsResult;
//import org.haier.shopUpdate.util.ShopsUtil;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/supplier")
public class SupplierController {
	@Resource
	private SupplierService supService;
//	private static Logger log=Logger.getLogger(SupplierController.class);
	/**
	 * 查询商品供货商信息
	 * @param map
	 * @return
	 */
	@RequestMapping("/queryShopSuppliers.do")
	@ResponseBody
	public ShopsResult queryShopSuppliers(
			@RequestParam(value="shopUnique",required=true)Long shopUnique
			,HttpServletRequest request
			){
		
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shopUnique", shopUnique);
		return supService.queryShopSuppliers(map);
	}
}
