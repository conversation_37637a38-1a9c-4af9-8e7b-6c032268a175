package org.haier.shopUpdate.enums;

public enum AllocationShopStatus {
    WAIT_ALLOCATE(1, "待调拨"),
    // 待发货、
    WAIT_SHIPMENTS(2, "待调出"),
    // 待收货、
    WAIT_RECEIVED(3, "待调入"),
    FINISH(4, "已完成"),
    QUASH(5, "已撤销"),
    ;

    private Integer status;
    private String desc;

    public static AllocationShopStatus findByStatus(Integer status){
        for(AllocationShopStatus enums: AllocationShopStatus.values()){
            if (status.equals(enums.getStatus())){
                return enums;
            }
        }
        return null;
    }
    AllocationShopStatus(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }
}
