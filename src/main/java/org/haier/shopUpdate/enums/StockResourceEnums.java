package org.haier.shopUpdate.enums;

public enum StockResourceEnums {
    MANUAL(1, "手动"),
    SALES(2, "销售订单"),
    PURCHASE(3, "进货订单"),
    TO_MAKE_AN_INVENTORY(4, "盘库"),
    ONLINE_ORDERS(5, "网上订单"),
    DEPOSIT(6, "寄存"),
    PROCUREMENT(7, "云商采购"),
    ALLOCATE(8, "调拨"),
    RETURN_OF_GOODS(9, "退货"),
    ;


    private final Integer code;
    private final String desc;

    StockResourceEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
