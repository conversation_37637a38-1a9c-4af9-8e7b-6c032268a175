package org.haier.shopUpdate.enums;

public enum ValidStatus {

    EFFECTIVE("有效的",1),
    INVALID("无效的",0),
    ;
    //状态名字
    private String name;
    //状态值
    private Integer value;

    ValidStatus(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public Integer getValue() {
        return value;
    }
}