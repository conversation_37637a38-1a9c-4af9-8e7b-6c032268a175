package org.haier.shopUpdate.enums;

import org.haier.shopUpdate.project.ProjectConfig;

public enum PayMethodIconsEnums {

    CASH(1, ProjectConfig.FILE_URL + "/common/publicImage/payMethod/1.png" , "现金"),
    ZFBPAY(2, ProjectConfig.FILE_URL + "/common/publicImage/payMethod/2.png","支付宝"),
    WXPAY(3,ProjectConfig.FILE_URL + "/common/publicImage/payMethod/3.png", "微信"),
    BANKPAY(4,ProjectConfig.FILE_URL + "/common/publicImage/payMethod/4.png","银行卡"),
    CUSTOMERPAY(5,ProjectConfig.FILE_URL + "/common/publicImage/payMethod/5.png","会员支付"),
    SCANPAYNINE(9,ProjectConfig.FILE_URL + "/common/publicImage/payMethod/13.png","金圈支付"),
    POINTPAY(10,ProjectConfig.FILE_URL + "/common/publicImage/payMethod/10.png","积分支付"),
    BEANSPAY(11,ProjectConfig.FILE_URL + "/common/publicImage/payMethod/11.png","百货豆支付"),
    SCANPAY(13,ProjectConfig.FILE_URL + "/common/publicImage/payMethod/13.png","金圈支付"),
    JHPAY(14,ProjectConfig.FILE_URL + "/common/publicImage/payMethod/14.png","聚合支付")

    ;
    //支付方式
    private Integer payMethodCode;
    //支付方式图标
    private String payMethodIcon;
    //支付方式名称
    private String payMethodName;

    PayMethodIconsEnums(Integer payMethodCode, String payMethodIcon, String payMethodName) {
        this.payMethodCode = payMethodCode;
        this.payMethodIcon = payMethodIcon;
        this.payMethodName = payMethodName;
    }

    PayMethodIconsEnums() {
    }

    public Integer getPayMethodCode() {
        return payMethodCode;
    }

    public void setPayMethodCode(Integer payMethodCode) {
        this.payMethodCode = payMethodCode;
    }

    public String getPayMethodIcon() {
        return payMethodIcon;
    }

    public void setPayMethodIcon(String payMethodIcon) {
        this.payMethodIcon = payMethodIcon;
    }

    public String getPayMethodName() {
        return payMethodName;
    }

    public void setPayMethodName(String payMethodName) {
        this.payMethodName = payMethodName;
    }

    /**
     * 根据支付方式，获取对应的图标路径
     * @param payMethodCode
     * @return
     */
    public static String getIconByMethod(Integer payMethodCode) {
        PayMethodIconsEnums[] list = PayMethodIconsEnums.values();
        for (PayMethodIconsEnums payMethodIcon : list) {
            if (payMethodCode == payMethodIcon.getPayMethodCode()) {
                return payMethodIcon.getPayMethodIcon();
            }
        }
        return null;
    }

    /**
     * 根据支付方式，获取对应的支付名称
     * @param payMethodCode
     * @return
     */
    public static String getPayMethodNameByMethod(Integer payMethodCode) {
        PayMethodIconsEnums[] list = PayMethodIconsEnums.values();
        for (PayMethodIconsEnums payMethodIcon : list) {
            if (payMethodCode == payMethodIcon.getPayMethodCode()) {
                return payMethodIcon.getPayMethodName();
            }
        }
        return null;
    }
}
