package org.haier.shopUpdate.enums;

public enum StockOriginEnums {
    APP(1, "手机"),
    PC(2, "PC端"),
    WEB(3, "web网页端"),
    ;

    private final Integer code;
    private final String desc;

    StockOriginEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
