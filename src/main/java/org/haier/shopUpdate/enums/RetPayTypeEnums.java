package org.haier.shopUpdate.enums;

/**
 * 退款方式
 */
public enum RetPayTypeEnums {
    CASHPAY(1 , "现金"),
    ZFBPAY(2, "支付宝"),
    WXPAY(3, "微信"),
    BANKPAY(4,"银行卡"),
    CUSTOMERPAY(5,"会员支付"),
    COUPONPAY(7,"优惠券"),
    BEANSPAYEIGHT(8,"百货豆支付"),
    SCANPAYNINE(9,"金圈支付"),
    POINTPAY(10,"积分支付"),
    BEANSPAY(11,"百货豆支付"),
    SCANPAY(13,"金圈支付"),
    ;
    private Integer payType;
    private String payTypeName;

    RetPayTypeEnums(Integer payType, String payTypeName) {
        this.payType = payType;
        this.payTypeName = payTypeName;
    }

    public Integer getPayType() {
        return payType;
    }

    public void setPayType(Integer payType) {
        this.payType = payType;
    }

    public String getPayTypeName() {
        return payTypeName;
    }

    public void setPayTypeName(String payTypeName) {
        this.payTypeName = payTypeName;
    }

    /**
     * 获取支付方式制定的值
     * @param payMethod
     * @return
     */
    public static String getPayTypeNameByValue(Integer payMethod) {
        RetPayTypeEnums[] list = RetPayTypeEnums.values();
        for (RetPayTypeEnums retPayTypeEnums : list) {
            if (retPayTypeEnums.getPayType() == payMethod) {
                return retPayTypeEnums.getPayTypeName();
            }
        }
        return "";
    }
}
