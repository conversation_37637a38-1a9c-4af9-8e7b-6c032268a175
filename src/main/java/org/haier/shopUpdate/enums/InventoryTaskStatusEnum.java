package org.haier.shopUpdate.enums;

public enum InventoryTaskStatusEnum {
    WAITING(1,"待提交"),
    FINISH(2,"已盘点")
    ;

    private Integer status;

    private String msg;

    InventoryTaskStatusEnum(Integer status,String msg){
        this.status = status;
        this.msg = msg;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
