package org.haier.shopUpdate.enums.payCenter;

public enum RefundStateEnum {

    DOING("处理中", "DOING"),
    SUCCESS("退款成功", "SUCCESS"),
    FAIL("退款失败", "FAIL"),
    CANCEL("撤销退款", "CANCEL"),
    CLOSED("关闭退款", "CLOSED")
    ;
    private String label;
    private String code;

    public String getLabel() {
        return label;
    }
    public String getCode() {
        return code;
    }
    RefundStateEnum(String label, String code) {
        this.label = label;
        this.code = code;
    }
}
