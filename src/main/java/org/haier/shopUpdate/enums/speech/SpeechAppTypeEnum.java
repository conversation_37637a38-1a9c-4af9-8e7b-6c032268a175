package org.haier.shopUpdate.enums.speech;

public enum SpeechAppTypeEnum {
    BUYHOOAPPFORSHOP("百货商家端超市版", "001"),
    BUYHOOAPPFORFOOR("百货商家端餐饮版", "002"),
    BUYHOOAPPFORFARM("百货商家端农贸版", "003"),
    JINQUANYUNSHANG("金圈云商", "004"),
    JINQUANYUNSHANG_("金圈云商供应商版", "005"),
    YIKEZHONG("一刻钟到家APP", "006"),
    ;
    private String name;
    private String code;

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }
    SpeechAppTypeEnum (String name,String code){
        this.name = name;
        this.code = code;
    }
}
