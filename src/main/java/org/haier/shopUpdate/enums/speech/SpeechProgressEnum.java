package org.haier.shopUpdate.enums.speech;

public enum SpeechProgressEnum {

    ACCEPTED("已接收",1),
    PROCESSING("处理中",2),
    SUCCESS("处理成功",3),
    FAIL("处理失败",4),
    ;
    private String name;
    private Integer code;
    public String getName() {
        return name;
    }
    public Integer getCode() {
        return code;
    }
    SpeechProgressEnum (String name,Integer code){
        this.name = name;
        this.code = code;
    }
}
