package org.haier.shopUpdate.enums.speech;

/**
 * 语音识别过程中，可能出现的错误
 */
public enum SpeechErrorEnum {

    SUCCESS("识别成功", "0"),
    UNKNOWN_ERROR("未知错误", "1"),
    POWER_LIMIT("权限不够", "2"),
    CMD_UNKNOWN("未匹配到指定命令", "3"),
    PARAM_ERROR("未上传指定格式参数", "4")
    ;
    private String name;
    private String code;

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }

    SpeechErrorEnum(String name, String code) {
        this.name = name;
        this.code = code;
    }
}