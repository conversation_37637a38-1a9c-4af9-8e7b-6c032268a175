package org.haier.shopUpdate.enums;

public enum DeviceTypeEnum {
    APP("APP","app端"),
    WEB("WEB","web端"),
    MP_WX("MP_WX","微信小程序端"),
    CASH("CASH","收银机端"),
    ;

    private String name;

    private String msg;

    DeviceTypeEnum( String name, String msg){
        this.name = name;
        this.msg = msg;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
