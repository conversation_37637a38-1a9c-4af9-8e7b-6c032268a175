package org.haier.shopUpdate.enums;

public enum InventoryOperateLogTypeEnum {
    ADDTASK(1,"创建盘点任务"),
    UPDATETASK(2,"修改盘点任务"),
    DELTASK(3,"删除盘点任务"),
    SUBMITTASK(4,"提交盘点任务"),
    ADDTASKDETAIL(5,"员工保存盘点单"),
    UPDATETASKDETAIL(6,"员工修改盘点单"),
    DELETETASKDETAIL(7,"员工删除盘点单"),
    ;


    private Integer type;

    private String msg;

    InventoryOperateLogTypeEnum(Integer type,String msg){
        this.type = type;
        this.msg = msg;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
