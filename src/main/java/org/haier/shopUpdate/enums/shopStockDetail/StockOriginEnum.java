package org.haier.shopUpdate.enums.shopStockDetail;

public enum StockOriginEnum {
    MOBILE(1, "手机"),
    PC(2, "PC端"),
    WEB(3, "Web网页端"),
    MINI_PROGRAM(4, "小程序");

    private int code;
    private String description;

    StockOriginEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
