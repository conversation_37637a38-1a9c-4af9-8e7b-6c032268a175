package org.haier.shopUpdate.enums.shopStockDetail;

public enum StockKindEnum {
    DAMAGED(1, "报损"),
    RETURN(2, "退货"),
    EXCHANGE(3, "换货"),
    INITIALIZED(4, "初始化");

    private int code;
    private String description;

    StockKindEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
