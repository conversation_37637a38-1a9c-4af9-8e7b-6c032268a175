package org.haier.shopUpdate.enums.shopStockDetail;

public enum StockTypeEnum {
    INVENTORY_IN(1, "入库"),
    INVENTORY_OUT(2, "出库");

    private int code;
    private String description;

    StockTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
