package org.haier.shopUpdate.enums.shopStockDetail;

public enum StockResourceEnum {
    MANUAL(1, "手动出入库"),
    SALES_ORDER(2, "销售订单出入库"),
    PURCHASE_ORDER(3, "进货订单出入库（出库为退货）"),
    STOCK_TAKING(4, "盘库"),
    ONLINE_ORDER_OUT(5, "网上订单出库"),
    STORAGE(6, "寄存"),
    CLOUD_PURCHASE(7, "云商采购"),
    ALLOCATION(8, "调拨"),
    RETURN_GOODS(9, "退货"); // 注意调整了序号以避免与之前的重复，并保持连续性

    private int code;
    private String description;

    StockResourceEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
