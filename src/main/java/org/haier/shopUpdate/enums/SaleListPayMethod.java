package org.haier.shopUpdate.enums;

import org.haier.shopUpdate.result.common.NameAndValue;

import java.util.ArrayList;
import java.util.List;

public enum SaleListPayMethod {

    CASH(1,"现金"),
    ALIPAY(2,"支付宝"),
    WECHAT(3,"微信"),
    BANKCARD(4,"银行卡"),
    MEMBERCARD(5,"储值卡"),
    MEITUAN(6,"美团"),
    ELEME(7,"饿了吗"),
    BLENDPAY(8,"混合支付"),
    POINTPAY(10,"积分支付"),
    BEANSPAY(11,"百货豆支付"),
    JINQUANPAY(13,"金圈支付"),
    JUHEMAPAY(14,"聚合码支付"),

    ;
    private Integer payMethod;
    private String payMethodName;

    SaleListPayMethod(Integer payMethod,String payMethodName){
        this.payMethod = payMethod;
        this.payMethodName = payMethodName;
    }

    /**
     * 获取支付类型列表
     * @return
     */
    public static List<NameAndValue> getList(){
        List<NameAndValue> list = new ArrayList<>();
        for(SaleListPayMethod payMethod : SaleListPayMethod.values()){
            list.add(new NameAndValue(payMethod.getPayMethodName().toString(),payMethod.getPayMethod().toString()));
        }
        return list;
    }

    public Integer getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(Integer payMethod) {
        this.payMethod = payMethod;
    }

    public String getPayMethodName() {
        return payMethodName;
    }

    public void setPayMethodName(String payMethodName) {
        this.payMethodName = payMethodName;
    }
}
