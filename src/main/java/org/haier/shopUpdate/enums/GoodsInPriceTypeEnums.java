package org.haier.shopUpdate.enums;

/**
 * 商品进价类型枚举
 */
public enum GoodsInPriceTypeEnums {
    CUSTOM(0,"自定义"),
    AVG(1,"平均值"),
    BATCH(2,"先进先出"),
    ;

    private final Integer code;
    private final String desc;

    GoodsInPriceTypeEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
