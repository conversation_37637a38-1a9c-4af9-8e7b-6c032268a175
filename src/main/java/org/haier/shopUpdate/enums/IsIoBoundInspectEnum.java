package org.haier.shopUpdate.enums;

public enum IsIoBoundInspectEnum {
    NO(0,"否"),
    YES(1,"是"),
    ;

    private final Integer code;
    private final String desc;

    IsIoBoundInspectEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
