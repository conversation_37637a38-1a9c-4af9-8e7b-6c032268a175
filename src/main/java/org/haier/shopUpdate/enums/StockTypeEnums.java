package org.haier.shopUpdate.enums;

public enum StockTypeEnums {
    STOCK_IN(1,"入库"),
    STOCK_OUT(2,"出库"),
    ;

    private final Integer status;
    private final String desc;

    StockTypeEnums(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }
}
