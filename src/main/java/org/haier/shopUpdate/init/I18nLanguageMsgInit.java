package org.haier.shopUpdate.init;

import cc.buyhoo.common.i18n.I18nMsgUtil;
import cc.buyhoo.common.i18n.I18nStaticReturnParamsUtil;
import cn.hutool.core.util.ObjectUtil;
import org.haier.shopUpdate.config.i18n.I18nMsgConfig;
import org.haier.shopUpdate.redism.RedisCache;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;
import org.springframework.web.context.ServletContextAware;

import javax.annotation.Resource;
import javax.servlet.ServletContext;
import java.util.Map;

/**
 * @ClassName I18nLanguageMsgInit
 * <AUTHOR>
 * @Date 2025/1/20 17:21
 */
@Component
public class I18nLanguageMsgInit implements ApplicationContextAware, ServletContextAware,
        InitializingBean, ApplicationListener<ContextRefreshedEvent> {

    @Resource
    private RedisCache redisCache;
    @Resource
    private I18nMsgUtil i18nMsgUtil;
    @Resource
    private I18nStaticReturnParamsUtil i18nStaticReturnParamsUtil;

    @Override
    public void afterPropertiesSet() throws Exception {

    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {

    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {
        redisCache.removeObject(I18nMsgConfig.REDIS_LOCALE_MESSAGE_KEY + ":" + I18nMsgConfig.appName);
        Map<String, Map<String, String>> i18nMap = i18nMsgUtil.getI18nMsg();
        if (ObjectUtil.isNotEmpty(i18nMap)) {
            redisCache.putObjectNoExpire(I18nMsgConfig.REDIS_LOCALE_MESSAGE_KEY + ":" + I18nMsgConfig.appName, i18nMap);
        }

        redisCache.removeObject(I18nMsgConfig.REDIS_LOCALE_STATIC_KEY + ":" + I18nMsgConfig.appName);
        Map<String, Map<String, String>> i18nStaticReturnParamsMap = i18nStaticReturnParamsUtil.getI18nStaticReturnParams();
        if (ObjectUtil.isNotEmpty(i18nStaticReturnParamsMap)) {
            redisCache.putObjectNoExpire(I18nMsgConfig.REDIS_LOCALE_STATIC_KEY + ":" + I18nMsgConfig.appName, i18nStaticReturnParamsMap);
        }
    }

    @Override
    public void setServletContext(ServletContext servletContext) {

    }
}
