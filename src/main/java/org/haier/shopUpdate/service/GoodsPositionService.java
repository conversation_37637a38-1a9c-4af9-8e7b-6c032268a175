package org.haier.shopUpdate.service;


import org.haier.shopUpdate.entity.GoodsPositionEntity;
import org.haier.shopUpdate.entity.GoodsPositionResponse;
import org.haier.shopUpdate.util.ShopsResult;

import java.util.List;
import java.util.Map;

/**
 * 货币管理
 * 
 * <AUTHOR>
 * @date 2024-12-18
 */
public interface GoodsPositionService
{
    /**
     * 货币查询列表
     */
    ShopsResult selectList(String positionName, String shopUnique);

    /**
     * 添加货币类型
     */
    ShopsResult add(GoodsPositionEntity vo);

    /**
     * 查询货币详情
     */
    ShopsResult queryDetail(Long id);

    /**
     * 修改货币
     */
    ShopsResult update(GoodsPositionEntity vo);

    /**
     * 删除货币
     */
    ShopsResult delete(Long id);

    /**
     * 通过货位ids 批量查询货位名称 1-2-3
     */
    Map<String, String> queryDetailByPositionId(List<String> ids);

    ShopsResult check(Long id);
}
