package org.haier.shopUpdate.service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.haier.shopUpdate.util.ShopsResult;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
@Component
@Slf4j
public class QuerySaleListTask {
	@Resource
	private PurService purService;

	/**
     * 每天凌晨0点30分
     */
	@Scheduled(cron="0 30 0 * * ?")
    public void print(){  
    	System.out.println("定时查询销售排行");
    	//检测服务器地址，如果副服务器地址不匹配，不执行定时任务
    	try {
			//定时查询销售排行
			Map<String, Object> map=new HashMap<String, Object>();
			map.put("startDate", new SimpleDateFormat("yyyy-MM-dd").format(new Date()));
			ShopsResult result=purService.querySellList(map);
			if(result.getStatus()<=0){
				System.out.println("开始");
				purService.sellListTask();
			}
		} catch (Exception e) {
			log.error("定时任务异常：",e);
		}

    }
}
