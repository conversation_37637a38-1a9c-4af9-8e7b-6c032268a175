package org.haier.shopUpdate.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.haier.rabbitmq.service.ReturnListSyncService;
import org.haier.rabbitmq.thread.ThreadForSyncReturnList;
import org.haier.shopUpdate.config.PayCenterConfig;
import org.haier.shopUpdate.dao.*;
import org.haier.shopUpdate.entity.*;
import org.haier.shopUpdate.entity.goods.goodsRecord.GoodsOperParam;
import org.haier.shopUpdate.entity.ret.ReturnListMain;
import org.haier.shopUpdate.entity.ret2.ReturnMain;
import org.haier.shopUpdate.entity.ret2.ReturnPayDetail;
import org.haier.shopUpdate.enums.GoodsInPriceTypeEnums;
import org.haier.shopUpdate.enums.RetPayTypeEnums;
import org.haier.shopUpdate.enums.StockTypeEnums;
import org.haier.shopUpdate.enums.nova.LanguageEnum;
import org.haier.shopUpdate.enums.shopStockDetail.StockOriginEnum;
import org.haier.shopUpdate.enums.shopStockDetail.StockResourceEnum;
import org.haier.shopUpdate.enums.shopStockDetail.StockTypeEnum;
import org.haier.shopUpdate.params.goodBatch.GoodBatchUpdateParams;
import org.haier.shopUpdate.params.goodsSaleBatch.GoodSaleBatchByListUniqueQueryParams;
import org.haier.shopUpdate.params.goodsSaleBatch.GoodsSaleBatchUpdateParams;
import org.haier.shopUpdate.params.payCenter.RefundParams;
import org.haier.shopUpdate.redism.RedisCache;
import org.haier.shopUpdate.result.goodsSaleBatch.GoodSaleBatchQueryDto;
import org.haier.shopUpdate.util.*;
import org.haier.shopUpdate.util.common.utils.StringUtils;
import org.haier.shopUpdate.util.payCenter.PayCenterUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.haier.shopUpdate.util.BadiDuPushUtil;
import org.haier.shopUpdate.util.ShopsResult;
import org.haier.shopUpdate.util.ShopsUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.yxl.heLiBaoPay.util.HeLiBaoPay;
import org.yxl.heLiBaoPay.vo.AppPayRefundOrderResponseVo;
import org.yxl.heLiBaoPay.vo.AppPayRefundOrderVo;
import org.yxl.heLiBaoPay.vo.AppPayRefundQueryResponseVo;
import org.yxl.heLiBaoPay.vo.AppPayRefundQueryVo;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
@Slf4j
@Service
@Transactional
public class SaleListServiceImpl implements SaleListService{
	@Resource
	private SaleListDao saleDao;
	@Resource
	private CollectionPageDao pageDao;
	@Resource
	private GoodsKindsDao kindDao;
	@Resource
	private RedisCache redis;
	@Resource
	private AppPayDao appDao;
	@Resource
	private ShopsConfigDao shopsConfigDao;
	@Resource
	private GoodsBatchDao goodsBatchDao;
	@Resource
	private GoodsSaleBatchDao goodsSaleBatchDao;
	@Resource
	private StockDao stockDao;
	@Autowired
	private StockService stockService;
	@Autowired
	private ReturnListSyncService returnListSyncService;
	@Resource
	private I18nLanguageStaticReturnParamsUtil i18nRtUtil;
	@Resource
	private PayDao payDao;


	/**
	 *
	 * @param shopUnique 店铺编号
	 * @param cusUnique 会员编号
	 * @param saleListPayment 1、现金；2、支付宝；3、微信；4、银行卡
	 * @param goodsMsg
	 * @return
	 */
	@Transactional
	public ShopsResult saveFarmOrder(String shopUnique, String cusUnique,String saleListPayment,String goodsMsg,String staffId,String macId,Double saleListActuallyReceived) {
		ShopsResult sr = new ShopsResult(1,"保存成功");
		if(null == goodsMsg || goodsMsg.length() <= 1) {
			sr.setStatus(0);
			sr.setMsg("请上传商品信息");
			return sr;
		}
		String saleListUnique = System.currentTimeMillis() + shopUnique.substring(shopUnique.length() - 2);

		JSONArray ja = JSONArray.parseArray(goodsMsg);
		BigDecimal sale_list_total = new BigDecimal("0");
		BigDecimal sale_list_totalCount = new BigDecimal("0");
		for(Integer i = 0; i < ja.size(); i++) {
			Map<String,Object> tmp = new HashMap<String,Object>();
			JSONObject jo = ja.getJSONObject(i);
			tmp.put("sale_list_unique", saleListUnique);
			tmp.put("goods_barcode", jo.get("goodsBarcode"));
			tmp.put("sale_list_detail_count", jo.get("goodsCount"));
			tmp.put("sale_list_detail_price", jo.get("goodsPrice"));
			tmp.put("goods_name", jo.get("goodsName"));
			tmp.put("goods_id", null);
			tmp.put("goods_picturepath", "");
			tmp.put("goods_purprice", "0");
			tmp.put("commission_total", "0");

			appDao.addrSaleListDetail(tmp);
			BigDecimal goodsCount = jo.getBigDecimal("goodsCount");
			BigDecimal goodsPrice = jo.getBigDecimal("goodsPrice");

			sale_list_total = sale_list_total.add(goodsCount.multiply(goodsPrice));
			sale_list_totalCount = sale_list_totalCount.add(goodsCount);

			//扣除商品库存
			tmp.put("shopUnique", shopUnique);
			appDao.updateGoodsCount(tmp);
		}

		Map<String,Object> map = new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		map.put("shop_unique", shopUnique);

		map.put("sale_list_unique", saleListUnique);
		map.put("sale_list_total", sale_list_total);
		map.put("sale_list_totalCount", sale_list_totalCount);
		map.put("sale_list_pur", 0);
		map.put("cus_unique", cusUnique == null ? "" : cusUnique);
		map.put("sale_type", "0");
		map.put("sale_list_delfee", "0");
		map.put("sale_list_discount", 1);
		map.put("sale_list_state", "3");
		map.put("sale_list_handlestate", "4");
		map.put("sale_list_payment",saleListPayment);
		map.put("sale_list_cashier", staffId);
		map.put("sale_list_same_type", "2");
		map.put("sale_list_actually_received", saleListActuallyReceived == null ? sale_list_total : saleListActuallyReceived);
		map.put("machine_num", 1);
		map.put("sale_list_remarks", "");
		map.put("commission_sum", "0");

		//会员信息查询
		Map<String,Object> cusMap = appDao.queryCusMsg(map);
		if(null != cusMap && !cusMap.isEmpty()) {
			map.put("cus_id", cusMap.get("cus_id"));
			map.put("sale_list_name", cusMap.get("cus_name"));
		}else {
			map.put("sale_list_name", "");
		}



		//根据支付类型，判断是否需要添加支付信息
		if(saleListPayment != null && !saleListPayment.equals("6")) {
			map.put("sale_list_unique", saleListUnique);
			map.put("pay_method", saleListPayment);
			map.put("pay_money", sale_list_total);
		}else if(saleListPayment != null && saleListPayment.equals("6")) {
			//存储缓存信息，并发送通知
			String juheId = "juhepay" + (macId == null ? "" : macId);

			map.put("sale_list_state", "2");
			map.put("sale_list_handlestate", "8");

			List<Map<String,Object>> goodsList = new ArrayList<Map<String,Object>>();

			for(Integer i = 0; i < ja.size(); i++) {
				JSONObject jo = ja.getJSONObject(i);
				Map<String,Object> tmp = new HashMap<String,Object>();
				tmp.put("goodsBarcode", jo.get("goodsBarcode"));
				tmp.put("goodsName", jo.get("goodsName"));
				tmp.put("saleListDetailCount", jo.get("goodsCount"));
				tmp.put("saleListDetailPrice", jo.get("goodsPrice"));
				tmp.put("goodsId", "");
				tmp.put("goodsInPrice", "0");

				goodsList.add(tmp);
			}


			Map<String,Object> orderMap = new HashMap<String,Object>();
			orderMap.put("orderNo", saleListUnique);
			orderMap.put("saleListTotal", sale_list_total);
			orderMap.put("goodsList", goodsList);

			//直接保存订单，并向聚合支付收款网页发送消息
			Map<String,Object> msgMap = new HashMap<String,Object>();
			msgMap.put("ctrl", "msg_shop_order");
			msgMap.put("status", 200);
			msgMap.put("msg", "商品数据！");
			msgMap.put("data", orderMap);

			//将信息存储
			System.out.println("缓存的订单信息 " + juheId);
			System.out.println(net.sf.json.JSONObject.fromObject(orderMap));
			redis.putObject(juheId, net.sf.json.JSONObject.fromObject(orderMap));
		}

		//保存订单信息
		appDao.addrSaleList(map);

		appDao.addSaleListPayDetail(map);

		sr.setData(map);

		return sr;
	}
	/**
	 * 查询退款订单详情
	 * @param retListUnique
	 * @return
	 */
	public ShopsResult queryReturnDetail(String retListUnique) {
		ShopsResult sr = new ShopsResult(1, "查询成功！");

		Map<String,Object> map = new HashMap<>();
		map.put("retListUnique", retListUnique);
		ReturnMain returnMain = saleDao.queryReturnDetail(map);
		String retPayMsg = "本次已选商品退款总计：" + returnMain.getRetListTotal() + "元，";
		//国际化
		if (ObjectUtil.isNotEmpty(returnMain.getRetListHandlestateMsg())) {
			returnMain.setRetListHandlestateMsg(i18nRtUtil.getMessage(returnMain.getRetListHandlestateMsg()));
		}
		if (ObjectUtil.isNotEmpty(returnMain.getRetListStateMsg())) {
			returnMain.setRetListStateMsg(i18nRtUtil.getMessage(returnMain.getRetListStateMsg()));
		}
		if (ObjectUtil.isNotEmpty(returnMain.getRetOriginMsg())) {
			returnMain.setRetOriginMsg(i18nRtUtil.getMessage(returnMain.getRetOriginMsg()));
		}
		if (ObjectUtil.isNotEmpty(returnMain.getRetListReason())) {
			returnMain.setRetListReason(i18nRtUtil.getMessage(returnMain.getRetListReason()));
		}
		//标注退款详情
		List<ReturnPayDetail> retPayList = returnMain.getPayDetailList();
		BigDecimal payMoney = new BigDecimal(0.0);
		for(Integer i = 0; i < retPayList.size(); i++) {
			//国际化
			if (ObjectUtil.isNotEmpty(retPayList.get(i).getPayTypeMsg())) {
				retPayList.get(i).setPayTypeMsg(i18nRtUtil.getMessage(retPayList.get(i).getPayTypeMsg()));
			}
			if (ObjectUtil.isNotEmpty(retPayList.get(i).getServiceTypeMsg())) {
				retPayList.get(i).setServiceTypeMsg(i18nRtUtil.getMessage(retPayList.get(i).getServiceTypeMsg()));
			}

			retPayList.get(i).setPayTypeMsg(RetPayTypeEnums.getPayTypeNameByValue(retPayList.get(i).getPayType()));
			retPayMsg += retPayList.get(i).getPayTypeMsg() + "退款：" + retPayList.get(i).getPayMoney() + "元；";
			System.out.println(retPayList.get(i));
			if(retPayList.get(i).getServiceType() != 1 || retPayList.get(i).getPayType() == 5 || retPayList.get(i).getPayType() == 3 || retPayList.get(i).getPayType() == 2) {
				payMoney = payMoney.add(new BigDecimal(retPayList.get(i).getPayMoney()));
			}
		}

		returnMain.setRetPayMsg(retPayMsg);

		sr.setData(returnMain);

		return sr;
	}

	/**
	 * 修改退款订单状态
	 * @param retListUnique 退款单号
	 * @param retListHandlestate 退款审核状态
	 * @param retListRemarks 如果拒绝退款，需要填写拒绝原因
 	 * @param staffId 操作员工ID
	 * @param macId 操作设备的macId或浏览器型号
	 * @return
	 */
	@Transactional
	public ShopsResult modifyReturnMsg(String retListUnique, Integer retListHandlestate, String retListRemarks, String staffId, String macId) {
		ShopsResult sr = new ShopsResult(1,"操作成功!");

		//需要防止重复操作
//		String retBackId = "retBackId" + retListUnique + retListHandlestate;
//		if(null != redis.getObject(retBackId)) {
//			sr.setStatus(0);
//			sr.setMsg("已经提交操作申请,请刷新或等待结果");
//			return sr;
//		}else {
//			redis.putObject(retBackId, retBackId);
//		}


		Map<String,Object> map = new HashMap<String,Object>();
		map.put("retListUnique", retListUnique);
		map.put("retListHandlestate", retListHandlestate);
		map.put("retListState", 2);
		map.put("retListRemarks", retListRemarks);
		map.put("staffId", staffId);
		map.put("macId", macId);
		//分为拒绝和退款两种，如果拒绝，需要提交拒绝原因；
		if(retListHandlestate == 4) {
			if(null == retListRemarks || retListRemarks.trim().equals("")) {
				sr.setStatus(0);
				sr.setMsg("请输入拒绝原因");
				return sr;
			}

			//修改退款申请单
			Integer retCount = saleDao.modifyReturnMsg(map);
			if(retCount == 1) {
				sr.setStatus(1);
				sr.setMsg("拒绝退款成功!");
				return sr;
			}else {
				sr.setStatus(0);
				sr.setMsg("改订单已完成退款，请勿重复退款");
				return sr;
			}
		}

		/*
		 * 同意退款
		 * 1、查询该订单的退款信息,该订单的退款详情信息
		 * 2、如果需要三方机构退款，向三方机构发起退款申请
		 * 2.1、获取三方机构退款申请结果，如果退款成功，继续下面的流程
		 * 2.2、如果三方机构退款失败，返回退款失败原因，退款结束
		 * 3、增加用户的余额，百货豆等信息，扣除订单赠送给用户的百货豆
		 * 3.1、分别增加用户的余额，百货豆变更信息
		 * 4、如果有优惠券，扣除订单优惠券金额；
		 * 4、如果已经确认收货，减少店铺的百货豆、余额信息；
		 * 5、增加商品库存信息；
		 * 6、增加商品出入口信息；
		 */
		//1、获取订单的退款信息，退款详情信息
		ReturnMain retMain = saleDao.queryReturnDetail(map);
		if(null == retMain) {
			sr.setStatus(0);
			sr.setMsg("退款信息不存在");
			return sr;
		}

		List<ReturnPayDetail> payList = retMain.getPayDetailList();
		//需要扣除店铺的余额
		BigDecimal shopBalance = new BigDecimal(0.0);
		String cusUnique = retMain.getCusUnique();
		String shopUnique = retMain.getShopUnique();
		//需要给客户增加的余额
		BigDecimal cusBalance = new BigDecimal(0.0);
		//需要给客户增加的百货豆及需要扣除的百货豆
		Integer cusBeans = 0, giveBean = 0;
		for(Integer i = 0; i < payList.size(); i++) {
			ReturnPayDetail payDetail = payList.get(i);
			if(payDetail.getServiceType() == 12) {
				String currency = payDao.queryCurrency(shopUnique);
				if (ObjectUtil.isEmpty(currency)) {
					sr.setStatus(0);
					sr.setMsg("请先配置币种");
					return sr;
				}
				//申请线上退款
				Map<String,Object> payMap = saleDao.querySaleListPayMsg(retMain.getSaleListUnique());
				RefundParams refundParams = new RefundParams();
				refundParams.setMchId(String.valueOf(payMap.get("mch_id")));
				refundParams.setClientIp(IPUtils.getHostIp());
				refundParams.setMchTradeNo(retMain.getSaleListUnique());
				refundParams.setMchRefundNo(retListUnique);
				refundParams.setRefundFee((new BigDecimal(payDetail.getPayMoney()).setScale(2, BigDecimal.ROUND_HALF_UP)).multiply(new BigDecimal(100.0)).intValue());
				refundParams.setFeeType(currency);
				refundParams.setRemark("商家退款");
				int count = 1;
				ShopsResult retRes = PayCenterUtil.post(PayCenterConfig.REFUNDURL, refundParams, String.valueOf(payMap.get("mch_key")), count);
				if(retRes.getStatus() == 1) {
					//退款成功，继续操作
					shopBalance = shopBalance.add(new BigDecimal(payDetail.getPayMoney()));
				}else {
					sr.setStatus(0);
					sr.setMsg(retRes.getMsg());
					return sr;
				}
			}
			//储值卡支付
			if(payDetail.getServiceType() == 1 && payDetail.getPayType() == 5) {
				shopBalance = shopBalance.add(new BigDecimal(payDetail.getPayMoney()));
				cusBalance = cusBalance.add(new BigDecimal(payDetail.getPayMoney()));
			}
			//百货豆
			if(payDetail.getServiceType() == 1 && payDetail.getPayType() == 8) {
				cusBeans = cusBeans + new BigDecimal(payDetail.getPayMoney()).multiply(new BigDecimal(100.0)).intValue();
			}

			//优惠券退款
			if(payDetail.getServiceType() == 1 && payDetail.getPayType() == 7) {
				shopBalance = shopBalance.add(new BigDecimal(payDetail.getPayMoney()));
			}
		}
		//将订单修改为已退款状态，需要先确认线上退款已完成
		map.put("isBack", 1);
		saleDao.modifyReturnMsg(map);

//		giveBean = retMain.getRetListBean();

		//如果需要退款，意味着需要扣除百货赠送的百货豆，计算好，更新退款订单并增加退款记录
//		Integer rate = saleDao.queryRewardList(shopUnique);
//		giveBean = new Double(cusBalance.setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue()*rate/10).intValue();
		//增加客户余额，百货豆信息
		Map<String,Object> cusMap = new HashMap<String,Object>();
		cusMap.put("cusUnique", cusUnique);
		cusMap.put("cusBeans", cusBeans - giveBean);
		cusMap.put("cusBalance", cusBalance.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());

		//需要确认退款时的返回百货豆比率

		//分别增加会员的余额变动记录，百货豆变动记录
		if((cusBeans - giveBean) != 0 || cusBalance.compareTo(new BigDecimal(0.0))!= 0) {
			saleDao.modifyPlatcusMsg(cusMap);
		}

		//判断是否需要增加会员余额，百货豆变动记录
		List<Map<String,Object>> cusList = new ArrayList<>();
		Map<String,Object> pubMap = new HashMap<>();
		pubMap.put("shopUnique", shopUnique);
		pubMap.put("cusUnique", cusUnique);
		pubMap.put("saleListUnique", retListUnique);
		pubMap.put("payStatus", "1");
		pubMap.put("serverType", "1");
		if(cusBalance.compareTo(new BigDecimal(0.0)) != 0) {
			//会员余额有变动，添加变动记录
			Map<String,Object> tm = new HashMap<String,Object>();
			tm.putAll(pubMap);
			tm.put("moneyType", 1);
			tm.put("saleType", "1");
			tm.put("money", cusBalance.setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue());
			tm.put("rechargeMethod", "3");
			tm.put("serverType", 6);
			cusList.add(tm);
		}

		if(cusBeans.compareTo(0) != 0) {
			//会员百货豆有退款
			Map<String,Object> tm = new HashMap<String,Object>();
			tm.putAll(pubMap);
			tm.put("moneyType", 2);
			tm.put("saleType", "8");
			tm.put("money", cusBeans);
			tm.put("rechargeMethod", "1");

			cusList.add(tm);
		}

		if(giveBean.compareTo(0) != 0) {
			//会员百货豆有退款
			Map<String,Object> tm = new HashMap<String,Object>();
			tm.putAll(pubMap);
			tm.put("moneyType", 2);
			tm.put("saleType", "15");
			tm.put("money", giveBean);
			tm.put("rechargeMethod", "1");


			cusList.add(tm);
		}
		//添加会员余额，百货豆变动记录
		if(null != cusList && !cusList.isEmpty()) {
			saleDao.addCusChangRecord(cusList);
		}

		/*
		 * 修改店铺余额信息
		 * 1、确认订单是否已经确认收货了，
		 * 1.1、如果已确认收货，修改店铺的余额信息，并添加百货豆记录
		 * 1.2、如果未确认收货，确认收货时，根据退货信息，修改收款到帐情况
		 * 1.3、统计订单时，根据订单退款情况，去掉退款对应的信息
		 */

		Integer saleListHandlestate = retMain.getSaleListHandlestate();
		if(saleListHandlestate == 4 || saleListHandlestate == 6) {
			//已确认收货，需要店铺对应的退款余额和百货豆
			Map<String,Object> shopMap = new HashMap<String,Object>();
			shopMap.put("shopBeans", -cusBeans);
			shopMap.put("shopBalance", -(shopBalance.setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue()));
			shopMap.put("shopUnique", shopUnique);

			if(cusBeans != null && cusBeans != 0 && shopBalance != null && shopBalance.compareTo(new BigDecimal(0.0)) != 0) {
				//有需要修改的店铺余额、百货豆信息
				saleDao.modifyShopsMsg(shopMap);
			}
		}else {
			//订单未确认收货，如果订单完全退款，需要将订单设置为已完成配送状态。减少后期订单确认收货去修改订单的信息
			//怎么判断是否全部退款，退款对于不同配送方式的订单配送费的退款规则不一致，暂时没有对接第三方，按照全额退款计算
			//由于全部退款时，不再赠送百货豆，所以不需要额外扣除用户余额
			BigDecimal saleListTotal = new BigDecimal(retMain.getSaleListTotal()).setScale(2,BigDecimal.ROUND_HALF_UP);
			BigDecimal saleListDelfee = new BigDecimal(retMain.getSaleListDelfee()).setScale(2, BigDecimal.ROUND_HALF_UP);

			BigDecimal retListTotal = new BigDecimal(retMain.getRetListTotal()).setScale(2, BigDecimal.ROUND_HALF_UP);

			//如果退款总金额 = 订单商品总金额 + 订单配送费，说明订单为全额退款,防止精度失准，改用bigDecimal计算
			System.out.println(retListTotal);
			System.out.println(saleListTotal);
			System.out.println(saleListDelfee);
			System.out.println("退款判断" + (retListTotal.compareTo(saleListTotal.add(saleListDelfee)) == 0));
			if(retListTotal.compareTo(saleListTotal.add(saleListDelfee)) == 0) {
				//需要将订单修改为已确认收货状态
				Map<String,Object> listMap = new HashMap<>();
				listMap.put("saleListUnique", retMain.getSaleListUnique());
				listMap.put("handleState", "4");

				saleDao.updateSaleList(listMap);
			}
		}

		/*
		 *
		 * 修改商品库存信息
		 * 1、计算商品对应的小规格商品
		 * 2、修改对应小规格商品的库存信息
		 * 3、添加商品出入库记录，添加商品出入口记录主记录
		 */
		map.put("shopUnique", shopUnique);
		map.put("retListUnique", retListUnique);
		List<Map<String,Object>> goodsList = saleDao.querySmallGoodsMsg(map);

		if(null != goodsList && !goodsList.isEmpty()) {
			Map<String,Object> goodsMap = new HashMap<>();
			List<Map<String, Object>> goodsMapList = new ArrayList<>();
			for(Map<String,Object> goods : goodsList) {
				BigDecimal goodsContain = new BigDecimal(String.valueOf(goods.get("goodsContain")));
				if(ObjectUtil.isNotEmpty(goodsMap.get(String.valueOf(goods.get("goodsBarcode"))))){
					goodsMap.put(String.valueOf(goods.get("goodsBarcode")),new BigDecimal(String.valueOf(goodsMap.get(String.valueOf(goods.get("goodsBarcode"))))).add(new BigDecimal(String.valueOf(goods.get("goodsCount")))));
					if (ObjectUtil.isNotEmpty(goodsMapList)) {
						boolean existFlag = false;
						for (Map<String, Object> goodsMaps : goodsMapList) {
							if (ObjectUtil.equals(String.valueOf(goodsMaps.get("goodsBarcode")), String.valueOf(goods.get("goodsBarcode")))) {
								existFlag = true;
								goodsMaps.put("stockCount", goodsMap.get(String.valueOf(goods.get("goodsBarcode"))));
							}
						}
						if (!existFlag) {
							Map<String, Object> goodsMapOther = new HashMap<>();
							goodsMapOther.put("shopUnique", shopUnique);
							goodsMapOther.put("goodsBarcode",String.valueOf(goods.get("goodsBarcode")));
							goodsMapOther.put("stockCount",goodsMap.get(String.valueOf(goods.get("goodsBarcode"))));
							goodsMapList.add(goodsMapOther);
						}
					} else {
						Map<String, Object> goodsMapOther = new HashMap<>();
						goodsMapOther.put("shopUnique", shopUnique);
						goodsMapOther.put("goodsBarcode",String.valueOf(goods.get("goodsBarcode")));
						goodsMapOther.put("stockCount",goodsMap.get(String.valueOf(goods.get("goodsBarcode"))));
						goodsMapList.add(goodsMapOther);
					}
				} else {
					goodsMap.put(String.valueOf(goods.get("goodsBarcode")),new BigDecimal(String.valueOf(goods.get("oldGoodsCount"))).add(new BigDecimal(String.valueOf(goods.get("goodsCount")))));
					if (ObjectUtil.isNotEmpty(goodsMapList)) {
						boolean existFlag = false;
						for (Map<String, Object> goodsMaps : goodsMapList) {
							if (ObjectUtil.equals(String.valueOf(goodsMaps.get("goodsBarcode")), String.valueOf(goods.get("goodsBarcode")))) {
								existFlag = true;
								goodsMaps.put("stockCount", goodsMap.get(String.valueOf(goods.get("goodsBarcode"))));
							}
						}
						if (!existFlag) {
							Map<String, Object> goodsMapOther = new HashMap<>();
							goodsMapOther.put("shopUnique", shopUnique);
							goodsMapOther.put("goodsBarcode",String.valueOf(goods.get("goodsBarcode")));
							goodsMapOther.put("stockCount",goodsMap.get(String.valueOf(goods.get("goodsBarcode"))));
							goodsMapList.add(goodsMapOther);
						}
					} else {
						Map<String, Object> goodsMapOther = new HashMap<>();
						goodsMapOther.put("shopUnique", shopUnique);
						goodsMapOther.put("goodsBarcode",String.valueOf(goods.get("goodsBarcode")));
						goodsMapOther.put("stockCount",goodsMap.get(String.valueOf(goods.get("goodsBarcode"))));
						goodsMapList.add(goodsMapOther);
					}
				}
				goods.put("stockCount",new BigDecimal(String.valueOf(goodsMap.get(String.valueOf(goods.get("goodsBarcode"))))).divide(goodsContain,2, RoundingMode.HALF_UP));
				goods.put("stockGoodsCount",new BigDecimal(String.valueOf(goods.get("goodsCount"))).divide(goodsContain,2, RoundingMode.HALF_UP));
				if (ObjectUtil.isNotEmpty(goods.get("goodsChengType")) && ObjectUtil.equals(String.valueOf(goods.get("goodsChengType")), "0")) {
					goods.put("stockCount",new BigDecimal(String.valueOf(goods.get("stockCount"))).setScale(0,RoundingMode.DOWN).doubleValue());
				}
			}

			//修改商品库存信息
			saleDao.modifyGoodsMsg(goodsList);
			//修改其他规格商品库存数量
			for (Map<String, Object> smap : goodsMapList) {
				List<Map<String,Object>> lists=stockDao.queryGoodsStand(smap);
				if(null!=lists&&lists.size()>0){
					smap.put("list", lists);
					stockDao.modifyGoodsCount(smap);
				}
			}
			//添加商品出入口记录主记录
			map.put("stockKind", "2");
			map.put("auditStatus", 1);
			if(ObjectUtil.isNotEmpty(retMain.getRetOrigin()) && retMain.getRetOrigin() == 4){
				map.put("stockRemarks", "百货商家APP端退款");
			} else {
				map.put("stockRemarks", "小程序退款");
			}
			map.put("staffId",staffId);
			map.put("auditId",staffId);
			map.put("auditTime", DateUtil.date());
			map.put("stockType", StockTypeEnum.INVENTORY_IN.getCode());
			map.put("stockResource", StockResourceEnum.RETURN_GOODS.getCode());
			if (ObjectUtil.isNotEmpty(retMain.getRetOrigin())) {
				if (retMain.getRetOrigin() == 1) {
					map.put("stockOrigin", StockOriginEnum.PC.getCode());
				} else if (retMain.getRetOrigin() == 2) {
					map.put("stockOrigin", StockOriginEnum.WEB.getCode());
				} else if (retMain.getRetOrigin() == 3) {
					map.put("stockOrigin", StockOriginEnum.MINI_PROGRAM.getCode());
				} else if (retMain.getRetOrigin() == 4) {
					map.put("stockOrigin", StockOriginEnum.MOBILE.getCode());
				}
			}
			map.put("totalCount",retMain.getRetListCount());
			map.put("totalAmount",retMain.getRetListTotalMoney());
			map.put("updateId",staffId);
			map.put("updateTime", DateUtil.date());
			map.put("stockTime",DateUtil.date());
			saleDao.addShopStockDetail(map);

			//添加商品出入库记录
//			map.put("stockType", 1);
//			map.put("stockResource", 9);
//			map.put("stockOrigin", "3");
//			map.put("staffId", staffId);
			map.put("list", goodsList);
			saleDao.addShopStockList(map);

			List<GoodBatchUpdateParams> goodBatchUpdateParamsList = new ArrayList<>();
			List<GoodsSaleBatchUpdateParams> goodsSaleBatchUpdateParamsList = new ArrayList<>();
			List<Long> goodsSaleBatchIdList = new ArrayList<>();
			ShopsConfig shopsConfig = shopsConfigDao.selectByShopUnique(shopUnique);
			for(Map<String,Object> gli : goodsList) {
				GoodSaleBatchByListUniqueQueryParams goodSaleBatchByListUniqueQueryParams = new GoodSaleBatchByListUniqueQueryParams();
				goodSaleBatchByListUniqueQueryParams.setShopUnique(shopUnique);
				goodSaleBatchByListUniqueQueryParams.setListUnique(retMain.getSaleListUnique());
				goodSaleBatchByListUniqueQueryParams.setGoodsBarcode(String.valueOf(gli.get("oldGoodsBarcode")));
				List<GoodSaleBatchQueryDto> goodSaleBatchQueryList = goodsSaleBatchDao.queryGoodsSaleBatchByListUnique(goodSaleBatchByListUniqueQueryParams);
				if (ObjectUtil.isNotEmpty(goodSaleBatchQueryList)) {
					BigDecimal goodsInCount = new BigDecimal(String.valueOf(gli.get("goodsCount")));
					for (GoodSaleBatchQueryDto goodSaleBatchQueryDto : goodSaleBatchQueryList) {
						goodSaleBatchQueryDto.setGoodsOutCount(goodSaleBatchQueryDto.getGoodsOutCount().multiply(new BigDecimal(String.valueOf(gli.get("goodsContain")))));
						if (ObjectUtil.isNotEmpty(goodBatchUpdateParamsList)) {
							for (GoodBatchUpdateParams goodBatchUpdateParams :
									goodBatchUpdateParamsList) {
								//如果存在选择相同商品不同规格时，需出相同批次
								if (ObjectUtil.equals(goodBatchUpdateParams.getGoodsBatchId(), goodSaleBatchQueryDto.getGoodsBatchId())) {
									goodSaleBatchQueryDto.setGoodsCount(goodBatchUpdateParams.getGoodsCount());
									break;
								}
							}
						}
						if (goodsInCount.compareTo(BigDecimal.ZERO) > 0) {
							GoodBatchUpdateParams goodBatchUpdateParams = new GoodBatchUpdateParams();
							goodBatchUpdateParams.setGoodsBatchId(goodSaleBatchQueryDto.getGoodsBatchId());
							if (goodsInCount.compareTo(goodSaleBatchQueryDto.getGoodsOutCount()) < 0) {
								GoodsSaleBatchUpdateParams goodsSaleBatchUpdateParams = new GoodsSaleBatchUpdateParams();
								goodsSaleBatchUpdateParams.setGoodsSaleBatchId(goodSaleBatchQueryDto.getGoodsSaleBatchId());
								goodsSaleBatchUpdateParams.setGoodsOutCount(goodSaleBatchQueryDto.getGoodsOutCount().subtract(goodsInCount));
								goodsSaleBatchUpdateParams.setUpdateId(Long.valueOf(staffId));
								goodsSaleBatchUpdateParamsList.add(goodsSaleBatchUpdateParams);
								goodBatchUpdateParams.setGoodsCount(goodSaleBatchQueryDto.getGoodsCount().add(goodsInCount));
								goodsInCount = BigDecimal.ZERO;
							} else {
								goodsSaleBatchIdList.add(goodSaleBatchQueryDto.getGoodsSaleBatchId());
								goodBatchUpdateParams.setGoodsCount(goodSaleBatchQueryDto.getGoodsCount().add(goodSaleBatchQueryDto.getGoodsOutCount()));
								goodsInCount = goodsInCount.subtract(goodSaleBatchQueryDto.getGoodsOutCount());
							}
							goodBatchUpdateParams.setUpdateId(Long.valueOf(staffId));
							goodBatchUpdateParamsList.add(goodBatchUpdateParams);
						}
					}
				}
			}
			if (ObjectUtil.isNotEmpty(shopsConfig) && ObjectUtil.equals(GoodsInPriceTypeEnums.BATCH.getCode(), shopsConfig.getGoodsInPriceType())) {
				if (ObjectUtil.isNotEmpty(goodBatchUpdateParamsList)) {
					goodsBatchDao.batchUpdateGoodsBatch(goodBatchUpdateParamsList);
				}
				if (ObjectUtil.isNotEmpty(goodsSaleBatchUpdateParamsList)) {
					goodsSaleBatchDao.updateGoodsSaleBatchList(goodsSaleBatchUpdateParamsList);
				}
				if (ObjectUtil.isNotEmpty(goodsSaleBatchIdList)) {
					goodsSaleBatchDao.deleteGoodsSaleBatchList(goodsSaleBatchIdList);
				}
			}

		}
		// 如果退款成功，发动退款单到mq
		if (sr.getStatus() == 1) {
			try {
				ThreadForSyncReturnList threadForSyncReturnList = new ThreadForSyncReturnList(returnListSyncService,retMain.getSaleListUnique(), retMain.getRetListUnique());
				ThreadUtil.execAsync(threadForSyncReturnList);
			} catch (Exception e) {
				System.out.println("RabbitMq退款订单同步到纳统异常：["+retMain.getRetListUnique()+"]");
			}
		}
		sr.setMsg("退款操作成功!");
		return sr;
	}

	public ShopsResult HelibaoReturn(String saleListUnique, String retListUnique, String mchId, String keys, String retAmount) {
		ShopsResult ns = new ShopsResult();
		AppPayRefundOrderVo orderVo = new AppPayRefundOrderVo();
		orderVo.setP2_orderId(saleListUnique);
		orderVo.setP3_customerNumber(mchId);
		orderVo.setP4_refundOrderId(saleListUnique);
		orderVo.setP5_amount(retAmount);
		orderVo.setP7_desc("商家退款");

		HeLiBaoPay heLiBaoPay = new HeLiBaoPay();
		AppPayRefundOrderResponseVo orderResponseVo =  heLiBaoPay.appPayRefund(orderVo,keys);

		//根据请求结果，判断是否需要查询退款结果
		if( orderResponseVo.getRt2_retCode().equals("0000") || orderResponseVo.getRt2_retCode().equals("0001")) {
			//如果退款申请成功，请求退款结果
			AppPayRefundQueryVo queryVo = new AppPayRefundQueryVo();
			queryVo.setP2_refundOrderId(orderVo.getP4_refundOrderId());
			queryVo.setP3_customerNumber(orderVo.getP3_customerNumber());
			try {
				Thread.sleep(1000);
			} catch (InterruptedException e) {
				log.error("退款失败，线程休眠异常:",e);
			}
			AppPayRefundQueryResponseVo responseVo =  heLiBaoPay.toRefundQuery(queryVo,keys);
			//接收成功，其他状态，继续查询，直到成功!或者失败
			if(responseVo.getRt2_retCode().equals("0000") && (responseVo.getRt8_orderStatus().equals("SUCCESS") ||responseVo.getRt8_orderStatus().equals("DOING") )) {


				ns.setStatus(1);
				ns.setMsg("退款成功!");
				return ns;
			}else if(responseVo.getRt2_retCode().equals("0000") ){
				return queryHeLiBaoReturnResult(orderVo.getP4_refundOrderId(), orderVo.getP3_customerNumber(), keys, 5);
			}

		}else {
			ns.setStatus(0);
			ns.setMsg(orderResponseVo.getRt3_retMsg());
		}
		return ns;
	}

	public ShopsResult queryHeLiBaoReturnResult(String retListUnique,String mchId,String keys,Integer count) {
		ShopsResult ns = new ShopsResult(0, "退款失败");
		if(count == 0) {
			return ns;
		}
		AppPayRefundQueryVo queryVo = new AppPayRefundQueryVo();
		queryVo.setP2_refundOrderId(retListUnique);
		queryVo.setP3_customerNumber(mchId);
		HeLiBaoPay heLiBaoPay = new HeLiBaoPay();
		try {
			Thread.sleep(1000);
			AppPayRefundQueryResponseVo responseVo =  heLiBaoPay.toRefundQuery(queryVo,keys);
			if(responseVo.getRt2_retCode().equals("0000") && (responseVo.getRt8_orderStatus().equals("SUCCESS") ||responseVo.getRt8_orderStatus().equals("DOING") )) {
				ns.setStatus(1);
				ns.setMsg("退款成功!");
				return ns;
			}else if(responseVo.getRt2_retCode().equals("0000")) {
				//继续查询
				return queryHeLiBaoReturnResult(retListUnique,mchId,keys,--count);
			}
		}catch (Exception e) {
			log.error("退款失败，查询退款结果异常:",e);
		}

		return ns;
	}
	/**
	 * 查询退款订单的申请信息
	 * @param shopUnique 店铺编号
	 * @param page 页码
	 * @param pageSize 单页查询数量
	 * @param startTime 开始查询时间
	 * @param endTime 结束查询时间
	 * @return
	 */
	public ShopsResult queryRetLists(String shopUnique, Integer page, Integer pageSize, String startTime, String endTime, Integer retListHandlestate) {
		ShopsResult sr = new ShopsResult(1,"查询成功！");

		Map<String,Object> map = new HashMap<String,Object>();
		map.put("startNum", (page - 1) * pageSize);
		map.put("pageSize", pageSize);
		map.put("shopUnique", shopUnique);
		map.put("startTime", startTime);
		map.put("endTime",  endTime);
		map.put("retListHandlestate", retListHandlestate);

		List<ReturnListMain> list = saleDao.queryRetLists(map);
		Integer count = saleDao.queryRetListsCount(map);
		//国际化
		if (ObjectUtil.isNotEmpty(list)) {
			for (ReturnListMain returnListMain: list) {
				if (ObjectUtil.isNotEmpty(returnListMain.getRetListStateMsg())) {
					returnListMain.setRetListStateMsg(i18nRtUtil.getMessage(returnListMain.getRetListStateMsg()));
				}
				if (ObjectUtil.isNotEmpty(returnListMain.getRetListHandlestateMsg())) {
					returnListMain.setRetListHandlestateMsg(i18nRtUtil.getMessage(returnListMain.getRetListHandlestateMsg()));
				}
				if (ObjectUtil.isNotEmpty(returnListMain.getRetOriginMsg())) {
					returnListMain.setRetOriginMsg(i18nRtUtil.getMessage(returnListMain.getRetOriginMsg()));
				}
				if (ObjectUtil.isNotEmpty(returnListMain.getRetListReason())) {
					returnListMain.setRetListReason(i18nRtUtil.getMessage(returnListMain.getRetListReason()));
				}
			}
		}
		sr.setData(list);
		sr.setCount(count);

		Map<String,Object> retMap = saleDao.queryRetStatistics(map);
		if(null == retMap) {
			retMap = new HashMap<String,Object>();
			retMap.put("orderCount", 0);
			retMap.put("retTotal", 0.0);
		}

		List<Map<String,Object>> payList = saleDao.queryRetStatisticsDetail(map);
		retMap.put("payList", payList);
		sr.setObject(retMap);

		return sr;
	}
	/**
	 * 修改话术内容或状态
	 * @param id id
	 * @param delFlag 1、正常；2、删除
	 * @param msg 话术内容
	 * @return
	 */
	public ShopsResult modifyReturnListMsg(String id,Integer delFlag,String msg) {
		ShopsResult sr = new ShopsResult(1, "查询成功！");

		Map<String,Object> map = new HashMap<String,Object>();

		map.put("id", id);
		map.put("delFlag", delFlag);
		map.put("msg", msg);

		saleDao.modifyReturnListMsg(map);

		return sr;
	}

	/**
	 * 添加新的拒绝退款订单话术信息
	 * @param shopUnique
	 * @param staffId
	 * @param msg
	 * @return
	 */
	public ShopsResult addNewReturnListMsg(String shopUnique,String staffId,String msg) {
		ShopsResult sr = new ShopsResult(1, "添加成功!");

		if(null == msg || msg.length() == 0) {
			sr.setStatus(0);
			sr.setMsg("话术内容不能为空");
			return sr;
		}

		if(msg.length() > 200) {
			sr.setStatus(0);
			sr.setMsg("话术的内容不能超过100字");
			return sr;
		}

		Map<String,Object> map = new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		map.put("staffId", staffId);
		map.put("msg", msg);

		saleDao.addNewReturnListMsg(map);

		return sr;
	}
	/**
	 * 查询常用话术
	 * @param shopUnique
	 * @return
	 */
	public ShopsResult queryReturnListMsg(String shopUnique) {
		ShopsResult sr = new ShopsResult(1, "查询成功！");

		Map<String,Object> map = new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		map.put("delFlag", "1");
		List<Map<String,Object>> resList = saleDao.queryReturnListMsg(map);

		sr.setData(resList);
		sr.setCount(resList.size());

		return sr;
	}

	/**
	 * 查询店铺各类型订单的数量
	 * @param map
	 * @return
	 */
	public ShopsResult shopsSaleListCount(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> data=saleDao.shopsSaleListCount(map);
		if(null==data||data.isEmpty()){
			sr.setData(ShopsUtil.map);
		}
		for (Map<String, Object> map2 : data) {
			if((Integer.parseInt(map2.get("handleState").toString()))==6) {
				/*boolean flag=false;
				for (Map<String, Object> map3 : data) {
					if((Integer.parseInt(map3.get("handleState").toString()))==4) {
						flag=true;
						map3.put("count", (Long)map2.get("count")+(Long)map3.get("count"));
					}
				}
				if(!flag) {
					map2.put("handleState", 4);
				}*/
			}
			if((Integer.parseInt(map2.get("handleState").toString()))==7) {
				/*boolean flag=false;
				for (Map<String, Object> map3 : data) {
					if((Integer.parseInt(map3.get("handleState").toString()))==3) {
						flag=true;
						map3.put("count", (Long)map2.get("count")+(Long)map3.get("count"));
					}
				}
				if(!flag) {
					map2.put("handleState", 3);
				}*/
			}
			if((Integer.parseInt(map2.get("handleState").toString()))==10) {
				/*boolean flag=false;
				for (Map<String, Object> map3 : data) {
					if((Integer.parseInt(map3.get("handleState").toString()))==3) {
						flag=true;
						map3.put("count", (Long)map2.get("count")+(Long)map3.get("count"));
					}
				}
				if(!flag) {
					map2.put("handleState", 3);
				}*/
			}


			/*if((Integer.parseInt(map2.get("handleState").toString()))==9) {
				boolean flag=false;
				for (Map<String, Object> map3 : data) {
					if((Integer.parseInt(map3.get("handleState").toString()))==2) {
						flag=true;
						map3.put("count", (Long)map2.get("count")+(Long)map3.get("count"));
					}
				}
				if(!flag) {
					map2.put("handleState", 2);
				}
			}*/

		}
		//查询全部
		Map<String,Object> all_count=saleDao.shopsSaleListCountAll(map);
		List<Map<String,Object>> retList = saleDao.queryReturnListCount(map);

		Integer retAllCount = 0;
		for(Integer i = 0; i < retList.size(); i++) {
			retAllCount += Integer.parseInt(retList.get(i).get("count").toString());
		}
		Map<String,Object> tmp = new HashMap<String,Object>();
		tmp.put("handleState", "-5");
		tmp.put("count", retAllCount);
		retList.add(tmp);

		data.add(all_count);
		data.addAll(retList);
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}

	/**
	 * 网络订单查询
	 * @param map
	 * @return
	 */
	public ShopsResult  querySaleList(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
//		System.out.println("网络订单查询！0"+map);

		/*订单时间处理*/
		String orderStartDate = MapUtils.getString(map, "orderStartDate");
		String orderEndDate = MapUtils.getString(map, "orderEndDate");
		if (StringUtils.isNotEmpty(orderStartDate)){
			map.put("orderStartDate",StringUtils.join(orderStartDate," 00:00:00"));
		}
		if (StringUtils.isNotEmpty(orderEndDate)){
			map.put("orderEndDate",StringUtils.join(orderEndDate," 23:59:59"));
		}

		List<SaleList> data=saleDao.querySaleList(map);
		for (SaleList saleList : data) {
			if (ObjectUtil.isNotEmpty(saleList.getSaleListPayment())) {
				saleList.setSaleListPayment(i18nRtUtil.getMessage(saleList.getSaleListPayment()));
			}
			if (ObjectUtil.isNotEmpty(saleList.getSaleListState())) {
				saleList.setSaleListState(i18nRtUtil.getMessage(saleList.getSaleListState()));
			}
			if (ObjectUtil.isNotEmpty(saleList.getDelivery_status())) {
				saleList.setDelivery_status(i18nRtUtil.getMessage(saleList.getDelivery_status()));
			}
			List<SaleListDetail> listDetail = saleList.getListDetail();
			BigDecimal totalCount = BigDecimal.ZERO;
			for (SaleListDetail sld : listDetail) {
				totalCount = NumberUtil.add(totalCount, sld.getSaleListDetailCount());
			}
			saleList.setTotalCount(totalCount.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
		}

		sr.setStatus(1);
		sr.setMsg("查询成功！");
		if(null==data){
			sr.setData(ShopsUtil.map);
		}else{
			sr.setData(data);
		}
		return sr;
	}

	/**
	 * 订单详情查询
	 * @param map
	 * @return
	 */
	public ShopsResult querySaleListDetail(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		SaleList saleList=saleDao.querySaleListDetail(map);

		if(null==saleList){
			sr.setStatus(2);
			sr.setMsg("该订单编号不存在！");
			return sr;
		}
		// 查询配送费
		/*Double peisong=0.0;
		Map<String, Object> peiSongMap = saleDao.getPeiSongMoney(map);
		if(peiSongMap!=null) {
			peisong = ((BigDecimal) peiSongMap.get("delivery_price")).doubleValue();
			saleList.setPeisong_money(peisong);
		}*/
		saleList.setPeisong_money(saleList.getSaleListDelfee());

		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(saleList);
		return sr;
	}


	/**
	 * 修改订单支付状态或处理状态
	 * 发货和取消功能，确认订单支付功能
	 * @param map
	 * @return
	 */
	public ShopsResult modifySaleListState(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		int k=saleDao.updateSaleList(map);
		if(k==0){
			sr.setStatus(2);
			sr.setMsg("更新失败");
			return sr;
		}
		Map<String,Object> cus=saleDao.queryManagerMessage(map);
		if(null==cus||cus.isEmpty()){
			sr.setStatus(1);
			sr.setMsg("更新成功，未推送");
			return sr;
		}
		cus.putAll(map);
		String handleState=map.get("handleState").toString();
//		System.out.println("订单处理状态"+handleState);
		String saleListUnique=map.get("saleListUnique").toString();
		String cusSource=cus.get("cusSource").toString();
//		System.out.println(cus);
		//向购物端推送消息
		String pushMessage="";
		String message="";
		if("3".equals(handleState)){//发货时，推送发货消息
			message = "[已发货]   商家：" + cus.get("shop_name").toString() + "，订单：" + saleListUnique + "已发货";
		}else if("5".equals(handleState)){//取消时，推送取消消息
			message="[已取消]  商家：" +cus.get("shop_name").toString()+"，订单："+saleListUnique+"已取消";
		}
//		System.out.println(message);
		if("2".equals(cusSource)){
			pushMessage = "{\"aps\": {\"alert\":\"" + message
					+ "\",\"sound\":\"default\", \"badge\":1,\"content-available\":1},\"sale_list_unique\":\"" + saleListUnique
					+ "\",\"message\":\"查看订单详情\"}";
			cus.put("pushMessage", pushMessage);
		}
		if("1".equals(cusSource)){
			pushMessage = "{\"title\":\""+ message + "\","+
					"\"description\": \"" + message + "\"," + // 必选
					"\"notification_builder_id\": 0," + // 可选
					"\"notification_basic_style\": 7," + // 可选// 4响铃，2，震动，1，可清除；可累加；7=1+2+4
					"\"open_type\":2," + // 可选 //1：打开网址；2：打开应用
					"\"url\": \"http://developer.baidu.com\"," + // 可选，当open_type==1时起作用；
					"\"pkg_content\":\"\"," + // 可选 ，当open_type==2时起作用
					"\"custom_content\":{\"sale_list_unique\":\"" + saleListUnique + "\"}" + // 用户自定义信息
					"}";
		}
		cus.put("pushMessage", pushMessage);
		cus.put("appType", 1);
		BadiDuPushUtil.pushMessageToBuy(cus);
		sr.setStatus(1);
		sr.setMsg("修改成功！");
		return sr;
	}


	public ShopsResult flatOrderAccount(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		int k=saleDao.updateSaleList(map);
		saleDao.updateSaleListPayDetail(map);
		if(k==0){
			sr.setStatus(2);
			sr.setMsg("更新失败");
			return sr;
		}
		sr.setStatus(1);
		sr.setMsg("修改成功！");
		return sr;
	}

	/**
	 * 经营助手（商品销量分时间段查询）
	 * @param map
	 * @return
	 */
	public ShopsResult querySaleGoodsCount(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		map.putAll(kindDao.queryShopKindType(map));
		List<Map<String,Object>> data=saleDao.querySaleGoodsCount(map);
		data=pageDao.turnover(map);
		if(null==data){
			sr.setData(ShopsUtil.map);
		}else{
			sr.setData(data);
		}
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		return sr;
	}

	/**
	 * 经营助手
	 * @param map
	 * @return
	 */
	public ShopsResult queryGroupsGoodsSaleMessage(Map<String,Object> map,Integer queryType){
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> data1=saleDao.queryGroupsGoodsSaleMessage(map);
		List<Map<String,Object>> data2=null;
//		System.out.println(map);
		if(queryType==1){
			data2=saleDao.queryShopsSaleVolumeByHour(map);
		}else{
//			System.out.println("信息查询！！！"+map);
			data2=saleDao.queryShopsSaleVolumeByDay(map);
		}
		//查询销量排行前十的商品
		List<Map<String,Object>> data3=saleDao.queryGoodsBeforeTen(map);
		List<Map<String,Object>> data4=new ArrayList<Map<String,Object>>();
		for(int i=0;i<data3.size();i++){
			data4.add(data3.get(data3.size()-i-1));
		}
//		System.out.println(data4);
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data1);
		sr.setRedundant(data2);
		sr.setObject(data4);
		return sr;
	}

	/**
	 * 百货商家端农批首页统计
	 * @param map
	 * @return
	 */
	@Override
	public ShopsResult indexStatisticsNP(Map<String, Object> map) {
		ShopsResult result = new ShopsResult();
		IndexStatisticsNPResp resp = new IndexStatisticsNPResp();

		//shopUnique
		Long shopUnique = MapUtils.getLong(map, "shopUnique");

		//月销售额
		handleMonthSale(shopUnique,resp);
		//月欠款
		handleMonthLoan(shopUnique,resp);
		//欠款总额
		handleLoanTotal(shopUnique,resp);
		//今日销售金额
		handleTodaySale(shopUnique,resp);
		//今日欠款
		handleTodayLoan(shopUnique,resp);
		//实收汇总
		handleIncome(shopUnique,resp);
		//代卖收益
		handleTodayAgentMoney(shopUnique,resp);

		result.setStatus(1);
		result.setMsg("");
		result.setData(resp);
		return result;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public ShopsResult saleListInvalidNP(SaleListInvalidReq req, GoodsOperParam goodsOperParam) {
		ShopsResult result = new ShopsResult(0,"");

		//请求参数校验
		ShopsResult checkResult = checkInvalidNPParam(req);
		if (checkResult.getStatus() != 1){
			return checkResult;
		}

		//校验订单
		GetSaleListDetailBySaleListUniqueVo saleQuery = new GetSaleListDetailBySaleListUniqueVo();
		saleQuery.setSaleListUnique(req.getSaleListUnique());
		saleQuery.setShopUnique(req.getShopUnique());
		List<GetSaleListDetailBySaleListUniqueEntity> detaiList = saleDao.getSaleListDetailBySaleListUnique(saleQuery);
		if (CollectionUtils.isEmpty(detaiList)){
			result.setMsg("未查询到符合条件的订单");
			return result;
		}
		Integer saleListHandlestate = detaiList.get(0).getSaleListHandlestate();
		if (saleListHandlestate == 5){
			result.setMsg("该订单已作废");
			return result;
		}

		//更新库存
		for (GetSaleListDetailBySaleListUniqueEntity d : detaiList){
			Map<String,Object> map=new HashMap<>();
			map.put("shopUnique", req.getShopUnique());
			map.put("goodsBarcode", d.getGoodsBarcode());
			map.put("goodsCount", d.getSaleListDetailCount());
			map.put("stockType", StockTypeEnums.STOCK_IN.getStatus());
			map.put("stockTime", new Timestamp(new Date().getTime()));
			map.put("stockPrice", d.getGoodsPurprice());
			map.put("stockOrigin", 1);
			map.put("staffId", -1);
			map.put("reason", "订单作废");
			map.put("list_unique", System.currentTimeMillis());
			stockService.newStockRecord(map, goodsOperParam, StockTypeEnums.STOCK_IN.getStatus());
		}

		//修改订单为作废状态
		Map<String,Object> updSaleListHandlestate = new HashMap<>();
		updSaleListHandlestate.put("saleListUnique",req.getSaleListUnique());
		updSaleListHandlestate.put("handleState",5); //已取消
		saleDao.updateSaleList(updSaleListHandlestate);

		result.setStatus(1);
		result.setMsg("作废成功");
		return result;
	}

	/**
	 * 月销售额
	 * @param shopUnique
	 * @param resp
	 */
	private void handleMonthSale(Long shopUnique,IndexStatisticsNPResp resp){

		GetSaleMoneyNPVo monthVo = getMonthVo(shopUnique);
		GetSaleMoneyNOEntity entity = saleDao.getSaleMoneyNP(monthVo);
		resp.setMonthSale(entity.getSaleListActuallyReceived());
	}

	/**
	 * 月欠款
	 * @param shopUnique
	 * @param resp
	 */
	private void handleMonthLoan(Long shopUnique,IndexStatisticsNPResp resp){

		GetSaleMoneyNPVo monthVo = getMonthVo(shopUnique);
		monthVo.setSaleListState(4);
		GetSaleMoneyNOEntity entity = saleDao.getSaleMoneyNP(monthVo);
		resp.setMonthLoan(entity.getSaleListActuallyReceived());
	}

	/**
	 * 欠款总额
	 * @param shopUnique
	 * @param resp
	 */
	private void handleLoanTotal(Long shopUnique,IndexStatisticsNPResp resp){

		GetSaleMoneyNPVo monthVo = getMonthVo(shopUnique);
		monthVo.setSaleListState(4);
		monthVo.setDateStart("");
		monthVo.setDateEnd("");
		GetSaleMoneyNOEntity entity = saleDao.getSaleMoneyNP(monthVo);
		resp.setLoanTotal(entity.getSaleListActuallyReceived());
	}

	/**
	 * 今日销售金额
	 * @param shopUnique
	 * @param resp
	 */
	private void handleTodaySale(Long shopUnique,IndexStatisticsNPResp resp){

		GetSaleMoneyNPVo monthVo = getDayVo(shopUnique);
		GetSaleMoneyNOEntity entity = saleDao.getSaleMoneyNP(monthVo);
		resp.setTodaySale(entity.getSaleListActuallyReceived());
		resp.setTodaySaleCount(entity.getSaleListCount());
	}

	/**
	 * 今日欠款
	 * @param shopUnique
	 * @param resp
	 */
	private void handleTodayLoan(Long shopUnique,IndexStatisticsNPResp resp){

		GetSaleMoneyNPVo monthVo = getDayVo(shopUnique);
		monthVo.setSaleListState(4);
		GetSaleMoneyNOEntity entity = saleDao.getSaleMoneyNP(monthVo);
		resp.setTodayLoan(entity.getSaleListActuallyReceived());
		resp.setTodayLoanCount(entity.getSaleListCount());
	}

	/**
	 * 实收汇总
	 * @param shopUnique
	 * @param resp
	 */
	private void handleIncome(Long shopUnique,IndexStatisticsNPResp resp){
		resp.setTodayIncome(UtilForJAVA.subDouble(resp.getTodaySale(),resp.getTodayLoan()));
		resp.setTodayIncomeCount(resp.getTodaySaleCount() - resp.getTodayLoanCount());
	}

	/**
	 * 代卖收益
	 * @param shopUnique
	 * @param resp
	 */
	private void handleTodayAgentMoney(Long shopUnique,IndexStatisticsNPResp resp){
		resp.setTodayAgentMoney(0d);
	}

	/**
	 * 生成按月日期统计入参
	 * @param shopUnique
	 * @return
	 */
	private GetSaleMoneyNPVo getMonthVo(Long shopUnique){
		GetSaleMoneyNPVo vo = new GetSaleMoneyNPVo();
		vo.setShopUnique(shopUnique);
		vo.setDateStart(StringUtils.join(DateCommon.getCurrMonthFirst()," 00:00:00"));
		vo.setDateEnd(StringUtils.join(DateCommon.getCurrMonthLast()," 23:59:59"));

		return vo;
	}

	/**
	 * 生成按天日期统计入参
	 * @param shopUnique
	 * @return
	 */
	private GetSaleMoneyNPVo getDayVo(Long shopUnique){
		GetSaleMoneyNPVo vo = new GetSaleMoneyNPVo();
		vo.setShopUnique(shopUnique);

		String today = DateCommon.dateToStr(new Date());
		vo.setDateStart(StringUtils.join(today," 00:00:00"));
		vo.setDateEnd(StringUtils.join(today," 23:59:59"));

		return vo;
	}

	/**
	 * 农批作废订单参数校验
	 * @param req
	 * @return
	 */
	private ShopsResult checkInvalidNPParam(SaleListInvalidReq req){
		ShopsResult result = new ShopsResult(0,"");

		if (req.getSaleListUnique() == null){
			result.setMsg("请输入订单编号");
			return result;
		}

		if (req.getShopUnique() == null){
			result.setMsg("请输入店铺编号");
			return result;
		}

		result.setStatus(1);
		return result;
	}

	private String getLanguage(String key, Map<String, Object> temMap) {
		String result = null;
		try {
			HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
			String language = request.getHeader("language");
			boolean languageFlag = false;
			if (ObjectUtil.isNotEmpty(language)){
				for (LanguageEnum languageEnum : LanguageEnum.values()) {
					if (ObjectUtil.equals(languageEnum.getCode(), language)) {
						languageFlag = true;
						break;
					}
				}
			}
			if (languageFlag) {
				for (Map.Entry<String, Object> entry : temMap.entrySet()) {
					if (ObjectUtil.equals(entry.getKey(), key)) {
						result = entry.getValue().toString();
						return result;
					}
				}

				Object rcObject = redis.getObject("i18nLanguage");
				cn.hutool.json.JSONArray ja = new cn.hutool.json.JSONArray();
				if (ObjectUtil.isNotEmpty(rcObject)) {
					ja = JSONUtil.parseArray(JSONUtil.toJsonStr(rcObject));
				}
				for (int i = 0; i < ja.size(); i++) {
					cn.hutool.json.JSONObject jo = ja.getJSONObject(i);
					if (ObjectUtil.isNotEmpty(jo)) {
						if (ObjectUtil.isNotEmpty(jo.get("keyName")) && String.valueOf(jo.get("keyName")).equals(key)) {
							if (ObjectUtil.isNotEmpty(jo.get(language))) {
								result = String.valueOf(jo.get(language));
								temMap.put(key, result);
								break;
							}
						}
					}
				}
			}
			if (ObjectUtil.isEmpty(result)) {
				result = key;
			}
		} catch (Exception e) {
			log.error("获取语言失败", e);
		}
		return result;
	}
}
