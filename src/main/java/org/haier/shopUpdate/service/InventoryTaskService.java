package org.haier.shopUpdate.service;

import org.haier.shopUpdate.params.inventoryTask.*;
import org.haier.shopUpdate.util.ShopsResult;

public interface InventoryTaskService {

    /**
     * 新增盘点任务
     * @param req
     * @return
     */
    public ShopsResult addTask(AddTaskParams req);

    /**
     * 盘点任务列表
     * @param req
     * @return
     */
    public ShopsResult taskList(TaskListParams req);

    /**
     * 删除盘点任务
     * @param req
     * @return
     */
    public ShopsResult deleteTask(DeleteTaskParams req);

    /**
     * 盘点任务详情
     * @param req
     * @return
     */
    public ShopsResult taskDetail(TaskDetailParams req);

    /**
     * 修改盘点单名称
     * @param req
     * @return
     */
    public ShopsResult updateTaskName(UpdatetTaskNameParams req);

    /**
     * 保存盘点详情
     * @param req
     * @return
     */
    public ShopsResult addTaskDetail(AddTaskDetailParams req);

    /**
     * 修改盘点详情
     * @param req
     * @return
     */
    public ShopsResult updateTaskDetail(UpdateTaskDetailParams req);

    /**
     * 删除盘点详情
     * @param req
     * @return
     */
    public ShopsResult deleteTaskDetail(DeleteTaskDetailParams req);

    /**
     * 货位列表
     * @param req
     * @return
     */
    public ShopsResult goodsLocationList(GoodsLocationListParams req);

    /**
     * 货位新增
     * @param req
     * @return
     */
    public ShopsResult addGoodsLocation(AddGoodsLocationParams req);

    /**
     * 修改货位
     * @param req
     * @return
     */
    public ShopsResult updateGoodsLocation(UpdateGoodsLocationParams req);

    /**
     * 删除货位
     * @param req
     * @return
     */
    public ShopsResult deleteGoodsLocation(DeleteGoodsLocationParams req);

    /**
     * 盘库单预览
     * @param req
     * @return
     */
    public ShopsResult taskPreview(TaskPreviewParams req);

    /**
     * 提交盘点
     * @param req
     * @return
     */
    public ShopsResult submitTask(SubmitTaskParams req);

    /**
     * 单个商品盘点明细-商品盘点
     * @param req
     * @return
     */
    public ShopsResult taskGoodsDetail(TaskGoodsDetailParams req);

    /**
     * 新增筐
     * @param req
     * @return
     */
    public ShopsResult addBucketWeight(AddBucketWeightParams req);

    /**
     * 修改筐重量
     * @param req
     * @return
     */
    public ShopsResult updateBucketWeight(UpdateBucketWeightParams req);

    /**
     * 删除筐重量
     * @param req
     * @return
     */
    public ShopsResult deleteBucketWeight(DeleteBucketWeightParams req);

    /**
     * 筐重量列表
     * @param req
     * @return
     */
    public ShopsResult bucketWeightList(BucketWeightListParams req);

    /**
     * 商品盘库记录
     * @param req
     * @return
     */
    public ShopsResult inventoryGoodsRecord(InventoryGoodsRecordParams req);

    /**
     * 盘点预览下载
     * @param req
     * @return
     */
    public ShopsResult taskPreviewDownload(TaskPreviewDowloadParams req);

}
