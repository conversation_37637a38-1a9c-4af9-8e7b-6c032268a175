package org.haier.shopUpdate.service;

import org.haier.shopUpdate.util.PurResult;

import java.util.Map;

public interface DeliveryService {
    /**
     * 商家自配送
     * @param sale_list_unique 订单编号
     * @param goods_weight 商品重量
     * @param shop_courier_id 自配送商家快递员id
     * @param courier_name 配送员姓名
     * @param courier_phone 配送员电话
     */
    public PurResult shopSelf(String sale_list_unique, String goods_weight, String shop_courier_id, String courier_name, String courier_phone, String sale_list_cashier);
    /**
     *订单核实
     * 		 [
     * 			{
     * 				goods_barcode 商品编码
     * 				goods_name 商品名称
     * 				goods_count 商品数量
     * 				goods_price 商品单价
     * 				goods_subtotal 商品价格小计
     * 			}
     * 		]
     * @return
     */
    public void verifyOrderNew(Map<String ,Object> params);
}
