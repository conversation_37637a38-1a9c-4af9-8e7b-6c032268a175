package org.haier.shopUpdate.service;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.haier.shopUpdate.util.PurResult;
import org.haier.shopUpdate.util.ShopsResult;

public interface CashService {

	public ShopsResult queryPayMethod();

	public ShopsResult uploadFile(HttpServletRequest request, String shop_unique);

	public ShopsResult queryCarList(Map<String, Object> map);

	public ShopsResult addCar(Map<String, Object> map);

	public ShopsResult updateCar(Map<String, Object> map);

	public ShopsResult deleteCar(Map<String, Object> map);

	public ShopsResult queryRechargeConfigList(Map<String, Object> params);

	public ShopsResult InsertRechargeConfig(Map<String, Object> params);

	public ShopsResult updateRechargeConfig(Map<String, Object> params);

	public ShopsResult queyRechargeLog(Map<String, Object> params);

	public ShopsResult getMemberLevel(Map<String, Object> map);

	public ShopsResult setMemberLevel(Map<String, Object> map);

	public ShopsResult queryCusCheckOut(Map<String, Object> map);

	public ShopsResult queryPointUseList(Map<String, Object> map);

	public ShopsResult queryOrderListByPage2(Map<String, Object> params);

	public ShopsResult queryTransactionList2(Map<String, Object> params);

	public ShopsResult queryShopBeansPromation(Map<String, Object> params);

	public ShopsResult queryPcActivityMenuList(Map<String, Object> params);

	public ShopsResult queryGoldByShop2(Map<String, Object> params);

	public ShopsResult saveBindingJiGuang(Map<String, Object> params);

	public ShopsResult updateShopsPwd(Map<String, Object> params);

	public ShopsResult queryBankName();

	public ShopsResult queryDownload();

	public ShopsResult queryShopAppDownload();

	public ShopsResult queryFuncitonImage(Map<String, Object> params);

	public PurResult queryShelfStateGoodsMessage(Map<String, Object> params);

	public ShopsResult updateShelfState(String goods_ids, String shelf_state);

	public Map<String, Object> queryShopDelivery(String shop_unique);

	public ShopsResult updateShopDelivery(Map<String, Object> params);

	public PurResult getShopCourierList(Map<String, Object> map);

	public ShopsResult addShopCourier(Map<String, Object> map);

	public ShopsResult updateShopCourier(Map<String, Object> map);

	public ShopsResult deleteShopCourier(String courier_id);

	public PurResult queryShopCouponList(String shop_unique, String start_time, String end_time, int page, int pageSize,
			String coupon_name, String type);

	public PurResult addShopCoupon(String shop_unique, String start_time, String end_time, String meet_amount,
			String coupon_amount, String type, Integer is_time, Integer is_daily, Integer daily_num, String times,
			Integer is_auto_grant, Integer is_grant_num, Integer grant_num, Integer exclusive_type, String days,
			Integer is_online, String coupon_name);

	public PurResult deleteShopCoupon(String shop_coupon_id, int delete_status);

	public PurResult queryCouponRecord(Map<String, Object> map);

	public PurResult queryPromotionList(Map<String, Object> map);

	public PurResult submitSupplierStorageOrder(String shop_unique, String promotion_activity_name, String startDate,
			String endDate, String order_activity, String detailJson, String cus_activity);

	public PurResult updateActivityStatus(Map<String, Object> map);

	public PurResult queryGoodsMarkdownDetail(Map<String, Object> map);

	public PurResult deleteActivity(Map<String, Object> map);

	public PurResult submitGoodsGift(String shop_unique, String promotion_activity_name, String startDate,
			String endDate, String order_activity, String detailJson, String cus_activity);

	public PurResult queryGoodsGiftDetail(Map<String, Object> map);

	public PurResult submitSingleGoodsPromotion(String shop_unique, String promotion_activity_name, String startDate,
			String endDate, String detailJson, String activity_range, String order_activity, String cus_activity);

	public PurResult querySingleGoodsPromotionDetail(Map<String, Object> map);

	public PurResult addOrderMarkdown(String shop_unique, String promotion_activity_name, String startDate,
			String endDate, String meet_price1, String discount_price1, String meet_price2, String discount_price2,
			String meet_price3, String discount_price3, String goods_id1, String goods_id2, String goods_id3,
			String gift_count1, String gift_count2, String gift_count3);

	public PurResult queryOrderMarkdownDetail(Map<String, Object> map);

	public PurResult queryShopsBinding(Map<String, Object> map);

	public ShopsResult newBindingGoods(String goodsBarcodes, String goodsCounts, Long shopUnique, Double bindingTotal);

	public ShopsResult modifyBinding(Map<String, Object> map);

	public ShopsResult deleteBindingGoods(Map<String, Object> map);

	public ShopsResult queryOurShopGoods(Map<String, Object> map);

	public ShopsResult addOurShopGoods(Map<String, Object> map);

	public ShopsResult editOurShopGoods(Map<String, Object> map);

	public ShopsResult queryOurShopGoodsDetail(Map<String, Object> map);

	public ShopsResult queryGoodsPromotionList(Map<String, Object> map);

	public ShopsResult queryOrderMeetMoney(Map<String, Object> map);

	public ShopsResult queryShopOpenStatus(Map<String, Object> map);

	public ShopsResult queryBeansDiKu(Map<String, Object> map);

	public void updateBankRoot(Map<String, Object> map);

	public PurResult getGoodsSupplierMsg(String shopUnique);

	public PurResult queryGoodsByPage1(String shop_unique, String goodsMessage, String goods_kind_parunique,
			String goods_kind_unique, int page, int pageSize, Integer is_online);
	
}
