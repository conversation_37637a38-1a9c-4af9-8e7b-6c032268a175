package org.haier.shopUpdate.service;

import org.haier.shopUpdate.params.shopSupBill.*;
import org.haier.shopUpdate.params.shopSupBill.externalCall.*;
import org.haier.shopUpdate.params.shopSupBill.supExternalCall.StorageAllGoodsParams;
import org.haier.shopUpdate.util.ShopsResult;
import org.springframework.web.bind.annotation.RequestBody;

public interface ShopSupBillService {

	ShopsResult querySupBillList(QueryBillListParams params);
	ShopsResult querySupBillGoodsList(QueryBillGoodsListParams params);
	ShopsResult storageAllGoods(StorageAllGoodsParams params);
	ShopsResult storageGoods(StorageGoodsParams params);
	ShopsResult addPaymentOrder(AddPaymentOrderParams params);
	ShopsResult modifyPaymentOrder(ModifyPaymentOrderParams params);
	ShopsResult cancelStorageGoods(CancelStorageGoodsParams params);
	ShopsResult addShopSupBill(BillInfoParams params);
	ShopsResult updateBillStatus(UpdateBillStatusParams params);
	ShopsResult updateSupBillStatus(UpdateSupBillStatusParams params);
	ShopsResult queryPayment(QueryBillGoodsListParams params);
	ShopsResult checkGoods(CheckGoodsParams params);
	ShopsResult cancelSupBill(CancelSupBillParams params);
	ShopsResult cancelCheckGoods(CancelCheckGoodsParams params);

}
