package org.haier.shopUpdate.service;

import java.util.Map;

import org.haier.shopUpdate.entity.BeansExchange;
import org.haier.shopUpdate.entity.BeansGetRule;
import org.haier.shopUpdate.entity.PageQuery;
import org.haier.shopUpdate.entity.ShopCard;
import org.haier.shopUpdate.util.ShopsResult;
import org.haier.shopUpdate.util.common.BeanResult;
import org.haier.shopUpdate.util.common.CommonResult;

public interface DiamondsService {
	/**
	 * 周期时间段内钻石的营收统计及余额显示
	 * @param map
	 * @return
	 */
	public ShopsResult statisticsShopsDiamondsByTime(Map<String,Object> map);
	
	/**
	 * 获取店铺的银行卡列表信息
	 * @param shopCard
	 * @return
	 */
	public CommonResult getShopCardList(ShopCard shopCard);
	
	/**
	 * 删除银行卡信息
	 * @param shopCard
	 * @return
	 */
	public CommonResult removeShopCard(ShopCard shopCard);
	
	/**
	 * 添加或保存银行卡信息
	 * @param card
	 * @return
	 */
	public CommonResult addNewShopCard(ShopCard card);
	
	/**
	 * 获取支持的银行列表信息
	 * @return
	 */
	public ShopsResult getBankListMsg();
	
	/**
	 * 获取店铺的当前钻石数量
	 * @return
	 */
	public ShopsResult getShopBeansAndRule(ShopCard card);
	
	/**
	 * 百货豆提现检测
	 * @param beansExchange
	 * @return
	 */
	public ShopsResult addNewCashRecord(BeansExchange beansExchange);
	
	/**
	 * 获取银行卡详情
	 * @param card
	 * @return
	 */
	public ShopsResult getCardDetail(ShopCard card);
	
	/**
	 * 查询店铺的规则设置信息
	 * @param card
	 * @return
	 */
	public ShopsResult getDiamondsRule(ShopCard card);
	
	/**
	 * 修改店铺的规则信息
	 * @param beansGetRule
	 * @return
	 */
	public ShopsResult addNewGetRule(BeansGetRule beansGetRule);
	
	/**
	 * 
	 * @param map
	 * @return
	 */
	public ShopsResult beansBuyRecordTotal(PageQuery pageQuery);
	
	/**
	 * 取现记录列表
	 * @param pageQuery
	 * @return
	 */
	public ShopsResult beansBuyRecordList(PageQuery pageQuery);
	
	/**
	 * 抵扣记录信息统计
	 * @param pageQuery
	 * @return
	 */
	public ShopsResult dikouTotal(PageQuery pageQuery);
	
	/**
	 * 抵扣信息分页查询
	 * @param pageQuery
	 * @return
	 */
	public ShopsResult dikouList(PageQuery pageQuery);
	
	
	/**
	 * 免密赠送商家百货豆统计
	 * @param pageQuery
	 * @return
	 */
	public ShopsResult mmGiveBeansTotal(PageQuery pageQuery);
	
	/**
	 * 免密赠送分页查询
	 * @param pageQuery
	 * @return
	 */
	public ShopsResult mmGiveBeansList(PageQuery pageQuery);
	
	/**
	 * 提现记录统计
	 * @param pageQuery
	 * @return
	 */
	public ShopsResult takeCashTotal(PageQuery pageQuery);
	/**
	 * 提现分页查询
	 * @param pageQuery
	 * @return
	 */
	public ShopsResult takeCashList(PageQuery pageQuery);
	
	/**
	 * 赠送信息统计
	 * @param pageQuery
	 * @return
	 */
	public ShopsResult giveTotal(PageQuery pageQuery);
	
	/**
	 * 钻石赠送记录分页查询
	 * @param pageQuery
	 * @return
	 */
	public ShopsResult giveList(PageQuery pageQuery);
	
	/**
	 * 钻石购买规则查询
	 * @return
	 */
	public ShopsResult beansBuyRule();
	
	/**
	 * 充值
	 * @param beansExchange
	 * @return
	 */
	public ShopsResult diamondsBuyNew(BeansExchange beansExchange);
	
	/**
	 * 支付宝成功回调函数
	 * @param beansExchange
	 * @return
	 */
	public boolean modifyBeansExchangeRecord(BeansExchange beansExchange);
	
	/**
	 * 取现规则和体现规则查询
	 * @param map
	 * @return
	 */
	public ShopsResult queryRulrForDiamonds(Map<String,Object> map);
	
	public Map<String,Object> queryExchangeMsgByOrderId(BeansExchange b);
	
	public Integer modifyShopDiamonds(Map<String,Object> map);
	
	
	//20230214 百货豆模块-首页
	public BeanResult queryBeanList(Map<String,Object> map);
	
	public CommonResult queryBeanMoney(Map<String,Object> map);
	
	public CommonResult addBeanMoney(BeansExchange beansExchange);
	
	
}
