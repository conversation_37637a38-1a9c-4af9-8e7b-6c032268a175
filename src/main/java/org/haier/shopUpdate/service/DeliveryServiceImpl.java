package org.haier.shopUpdate.service;

import lombok.extern.slf4j.Slf4j;
import org.haier.shopUpdate.dao.DeliveryDao;
import org.haier.shopUpdate.redism.RedisCache;
import org.haier.shopUpdate.util.MUtil;
import org.haier.shopUpdate.util.PurResult;
import org.haier.shopUpdate.util.UtilForJAVA;
import org.haier.shopUpdate.util.mqtt.MqttxUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName DeliveryServiceImpl
 * <AUTHOR>
 * @Date 2025/3/1 16:59
 */
@Service
@Transactional
@Slf4j
public class DeliveryServiceImpl implements DeliveryService {
    @Resource
    private DeliveryDao deliveryDao;
    @Resource
    private RedisCache redis;
    @Override
    public PurResult shopSelf(String sale_list_unique, String goods_weight, String shop_courier_id, String courier_name, String courier_phone, String sale_list_cashier) {
        PurResult shopsResult = new PurResult();
        try {
            //获取订单消息
            Map<String ,Object> orderInfo = deliveryDao.getOrderInfo(sale_list_unique);
            String shopUnique = orderInfo.get("shop_unique").toString();
            //本地创建配送订单
            Map<String ,Object> params1 = new HashMap<String, Object>();
            params1.put("sale_list_unique", sale_list_unique);
            params1.put("delivery_type", "0");//商家自配送
            params1.put("delivery_weight", goods_weight);
            params1.put("delivery_price", orderInfo.get("sale_list_delfee"));
            params1.put("shop_delivery_price", 0.00);
            params1.put("shop_subsidy_delfee", 0.00);
            params1.put("delivery_status", "4");
            params1.put("driver_name", courier_name);
            params1.put("driver_phone", courier_phone);
            params1.put("shop_courier_id", shop_courier_id);
            Map<String ,Object> deliveryInfo = deliveryDao.getDeliveryOrder(params1);
            if(deliveryInfo == null){
                deliveryDao.addDeliveryOrder(params1);
            }else{
                deliveryDao.updateDeliveryOrder(params1);
            }

            //修改订单状态为3待收货
            Map<String ,Object> params = new HashMap<String, Object>();
            params.put("sale_list_unique", sale_list_unique);
            params.put("delivery_type", 0);//商家自配送
            params.put("sale_list_handlestate", 3);
            params.put("sale_list_cashier", sale_list_cashier);
            deliveryDao.updateOrderStatus(params);
            shopsResult.setStatus(1);
            shopsResult.setMsg("成功");

            sendMsgToPc(shopUnique, sale_list_unique, 3);

        } catch (Exception e) {
            shopsResult.setStatus(0);
            shopsResult.setMsg("异常："+e.getMessage());
        }
        return shopsResult;
    }

    @Override
    public void verifyOrderNew(Map<String, Object> params) {
        try {
            String sale_list_unique = MUtil.strObject(params.get("sale_list_unique"));
            Double return_price = Double.valueOf(MUtil.strObject(params.get("return_price")));
            String verify_staff_id = MUtil.strObject(params.get("verify_staff_id"));
            String goodsList = MUtil.strObject(params.get("goodsList"));

            //获取订单消息
            Map<String ,Object> orderInfo = deliveryDao.getOrderInfo(sale_list_unique);

            //修改订单信息
            Map<String ,Object> statusParams = new HashMap<String, Object>();
            statusParams.put("sale_list_unique", sale_list_unique);
//	        statusParams.put("sale_list_handlestate", 11);
            statusParams.put("return_price", return_price);
            statusParams.put("verify_staff_id", verify_staff_id);
            deliveryDao.updateOrderStatus(statusParams);

            //添加订单核实信息
            List<Map<String ,Object>> list = MUtil.strToList(goodsList);
            for(int i=0;i<list.size();i++) {
                list.get(i).put("sale_list_unique", sale_list_unique);
            }
            deliveryDao.addSaleListVerify(list);

            //修改会员余额
            if(return_price > 0.00) {
                String cus_unique = MUtil.strObject(orderInfo.get("cus_unique"));
                Map<String ,Object> cusParams = new HashMap<String, Object>();
                cusParams.put("cus_unique", cus_unique);
                cusParams.put("return_price", return_price);
                deliveryDao.updateCusAmount(cusParams);

                //添加会员余额交易流水
                Map<String ,Object> rechargeParams = new HashMap<String, Object>();
                rechargeParams.put("shop_unique", orderInfo.get("shop_unique"));
                rechargeParams.put("cus_unique", cus_unique);
                rechargeParams.put("money_type", 1);
                rechargeParams.put("sale_type", 11);
                rechargeParams.put("money", return_price);
                rechargeParams.put("sale_list_unique", sale_list_unique);
                deliveryDao.addCustomerRecharge(rechargeParams);
            }

            //查询团长佣金信息
            List<Map<String,Object>> disList = deliveryDao.getOrderDisMsg(sale_list_unique);
            System.out.println("团长订单信息核单");
            System.out.println(disList);
            if(null != disList && ! disList.isEmpty()) {
                //获取退单前后的商品信息
                List<Map<String,Object>> proList = deliveryDao.getOrderCheckMsg(sale_list_unique);
                Double sub_trading_amount = 0.00;
                if(null != proList && !proList.isEmpty()) {
                    //修改详情记录
                    System.out.println("核单后的信息");
                    System.out.println(proList);
                    deliveryDao.modifyCaptainProfit(proList);
                    //修改总的记录
                    for(Integer i = 0 ; i < proList.size() ;i ++) {
                        sub_trading_amount = UtilForJAVA.addDouble(sub_trading_amount, proList.get(i).get("sub_trading_amount"), 6);
                    }
                }
                if(sub_trading_amount != 0) {
                    Map<String,Object> subMap = new HashMap<String,Object>();
                    subMap.put("trading_amount", -sub_trading_amount);
                    subMap.put("cus_unique",disList.get(0).get("cus_unique"));
                    subMap.put("shop_unique", disList.get(0).get("shop_unique"));
                    //修改店内待收益的信息

                    deliveryDao.modifyCaptionProfitR(subMap);
                }
            }

        } catch (Exception e) {
            log.error("异常信息",e);
        }
    }

    /**
     * 订单状态修改后，向收银机发送订单修改通知
     * @param shopUnique
     * @param saleListUnique
     * @param saleListHandlestate
     */
    public void sendMsgToPc(String shopUnique, String saleListUnique, Integer saleListHandlestate) {
        //向收银设备推送消息
        Map<String,Object> mqttMap = new HashMap<String,Object>();
        mqttMap.put("ctrl", "msg_order_change");
        mqttMap.put("shopUnique", shopUnique);
        Map<String,Object> detailMap = new HashMap<String,Object>();
        detailMap.put("saleListUnique", saleListUnique);
        detailMap.put("saleListHandlestate", saleListHandlestate);

        mqttMap.put("status", 200);
        mqttMap.put("errcode", 0);
        mqttMap.put("msg", "订单状态修改");

        //获取各个状态的订单数量
        List<Map<String, Object>> countList = queryOrderCount(shopUnique);
        detailMap.put("orderCount", countList);
        mqttMap.put("data", detailMap);
        if(null != redis.getObject("topic_" + shopUnique)) {
            //缓存信息不为空
            List<String> idList = (List<String>)redis.getObject("topic_" + shopUnique);
            for(String ID : idList) {
                MqttxUtil.sendMapMsg(mqttMap, ID);
            }
        }
    }

    public List<Map<String,Object>> queryOrderCount(String shopUnique){

        Map<String,Object> parMap = new HashMap<String,Object>();
        parMap.put("shopUnique", shopUnique);
        List<Map<String,Object>> data = deliveryDao.shopsSaleListCount(parMap);
        if(null == data) {
            data = new ArrayList<Map<String,Object>>();
        }

        Integer retAllCount = 0;
        Map<String,Object> all_count = deliveryDao.shopsSaleListCountAll(parMap);
        List<Map<String,Object>> retList = deliveryDao.queryReturnListCount(parMap);

        Map<String,Object> tmp = new HashMap<String,Object>();
        tmp.put("handleState", "-5");
        tmp.put("count", retAllCount);
        retList.add(tmp);

        data.add(all_count);
        data.addAll(retList);

        return data;
    }
}
