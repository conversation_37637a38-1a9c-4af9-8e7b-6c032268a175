package org.haier.shopUpdate.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import org.haier.shopUpdate.dao.RecordGoodsOperDao;
import org.haier.shopUpdate.entity.RecordGoodsOperDetailEntity;
import org.haier.shopUpdate.entity.RecordGoodsOperEntity;
import org.haier.shopUpdate.entity.RecordGoodsOperForDetailEntity;
import org.haier.shopUpdate.params.RecordGoodsOper.QueryRecordGoodsOperDetailParams;
import org.haier.shopUpdate.params.RecordGoodsOper.QueryRecordGoodsOperListParams;
import org.haier.shopUpdate.util.I18nLanguageStaticReturnParamsUtil;
import org.haier.shopUpdate.util.ShopsResult;
import org.haier.shopUpdate.util.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class RecordGoodsOperServiceImpl implements RecordGoodsOperService {
    @Autowired
    private RecordGoodsOperDao recordGoodsOperDao;
    @Resource
    private I18nLanguageStaticReturnParamsUtil i18nRtUtil;

    @Override
    public ShopsResult queryRecordGoodsOper(QueryRecordGoodsOperListParams params){
        ShopsResult sr=new ShopsResult();
        //请求参数处理
        if (ObjectUtil.isNotEmpty(params.getPageIndex()) && ObjectUtil.isNotEmpty(params.getPageSize())){
            params.setPageIndex((params.getPageIndex()-1)*params.getPageSize());
        }
        if (ObjectUtil.isNotEmpty(params.getStartDate())){
            params.setStartDate(StringUtils.join(params.getStartDate()," 00:00:00"));
        }
        if (ObjectUtil.isNotEmpty(params.getEndDate())){
            params.setEndDate(StringUtils.join(params.getEndDate()," 23:59:59"));
        }
        Map<String, Object> map = BeanUtil.beanToMap(params);
        List<RecordGoodsOperEntity> list = recordGoodsOperDao.queryRecordGoodsOperList(map);
        for (int i = 0; i < list.size(); i++) {
            RecordGoodsOperEntity record = list.get(i);
            if(record != null){
                List<RecordGoodsOperDetailEntity> detail = record.getRecordDetail();
                StringBuilder str = new StringBuilder();
                for (int j = 0; detail != null && !detail.isEmpty() && j < detail.size(); j++) {
                    RecordGoodsOperDetailEntity detailEntity = detail.get(j);
                    if(detailEntity.getGoodsOperClass() != null){
                        if(str.length() > 0){
                            str.append(",").append(detailEntity.getGoodsOperClass());
                        }else{
                            str.append(detailEntity.getGoodsOperClass());
                        }
                    }
                }
                record.setGoodsOperClass(str.toString());
                record.setRecordDetail(null);
            }
        }
        sr.setStatus(1);
        sr.setMsg("查询成功！");
        sr.setData(list);
        return sr;
    }

    @Override
    public ShopsResult queryRecordGoodsOperDetail(QueryRecordGoodsOperDetailParams params) {
        ShopsResult sr=new ShopsResult();
        Map<String, Object> map = BeanUtil.beanToMap(params);
        RecordGoodsOperForDetailEntity entity = recordGoodsOperDao.queryRecordGoodsOperDetail(map);
        if (ObjectUtil.isNotEmpty(entity.getOperType())) {
            entity.setOperType(i18nRtUtil.getMessage(entity.getOperType()));
        }
        if (ObjectUtil.isNotEmpty(entity.getDeviceSource())) {
            entity.setDeviceSource(i18nRtUtil.getMessage(entity.getDeviceSource()));
        }
        if (ObjectUtil.isNotEmpty(entity.getUserType())) {
            entity.setUserType(i18nRtUtil.getMessage(entity.getUserType()));
        }
        if (ObjectUtil.isNotEmpty(entity.getLastDeviceSource())) {
            entity.setLastDeviceSource(i18nRtUtil.getMessage(entity.getLastDeviceSource()));
        }
        if (ObjectUtil.isNotEmpty(entity.getOperSource())) {
            entity.setOperSource(i18nRtUtil.getMessage(entity.getOperSource()));
        }
        if (ObjectUtil.isNotEmpty(entity.getRecordDetail())) {
            for (RecordGoodsOperDetailEntity detailEntity : entity.getRecordDetail()) {
                if (ObjectUtil.isNotEmpty(detailEntity.getGoodsOperClass())) {
                    detailEntity.setGoodsOperClass(i18nRtUtil.getMessage(detailEntity.getGoodsOperClass()));
                }
            }
        }
        //需要将value转换为可读数据
        entity = changeValue(entity);

        sr.setStatus(1);
        sr.setMsg("查询成功！");
        sr.setData(entity);
        return sr;
    }

    /**
     * 将传入的数值转换为可读信息
     * @param entity
     * @return
     */
    public RecordGoodsOperForDetailEntity changeValue(RecordGoodsOperForDetailEntity entity){
        if(null == entity){
            return entity;
        }
        Long shopUnique = entity.getShopUnique();
        List<RecordGoodsOperDetailEntity> detailEntities = entity.getRecordDetail();
        for(RecordGoodsOperDetailEntity detail : detailEntities){
            String goodsOperClass = detail.getGoodsOperClassType();
            String valueSource = detail.getValueSource();
            String valueNew = detail.getValueNew();

            Map<String, Object> params = new HashMap<>();
            params.put("shopUnique", shopUnique);
            switch (goodsOperClass){
                case "1" :
                    //进价，不需要操作
                    break;
                case "2":
                    //售价，不需要操作
                    break;
                case "3":
                    //会员价，不需要操作
                    break;
                case "4":
                    //网购价，不需要操作
                    break;
                case "5":
                    //库存不需要操作
                    break;
                case "6":
                    //商品分类，需要转换成对应的分类名称
                    if (valueSource!= null){
                        params.put("goodsKindUnique", valueSource);

                        String sourceValue = recordGoodsOperDao.queryGoodsKindMsg(params);
                        if(null == sourceValue){
                            sourceValue = "已删除分类";
                        }
                        detail.setValueSource(sourceValue);
                    } else {
                        detail.setValueSource("");
                    }
                    params.put("goodsKindUnique", valueNew);
                    String newValue = recordGoodsOperDao.queryGoodsKindMsg(params);
                    detail.setValueNew(newValue);

                    break;
                case "7":
                    //商品图片，需要前端展示
                    break;
                case "8":
                    //商品规格
                    break;
                case "9":
                    //商品单位
                    break;
                case "10":
                    //商品保质期
                    break;
                case "11":
                    //积分设置
                    break;
                case "12":
                    //产地
                    break;
                case "13":
                    //换算比例
                    break;
                case "14":
                    if (valueSource != null){
                        //关联的商品信息
                        params.put("goodsBarcode", valueSource);
                        String sourceValue = recordGoodsOperDao.queryGoodsName(params);
                        detail.setValueSource(sourceValue);
                    } else {
                        detail.setValueSource("");
                    }
                    params.put("goodsBarcode", valueNew);
                    newValue = recordGoodsOperDao.queryGoodsName(params);

                    detail.setValueNew(newValue);
                    break;
                case "15":
                    //默认供应商
                    if (valueSource != null){
                        params.put("supplierUnique", valueSource);
                        String sourceValue = recordGoodsOperDao.queryGoodsSupplier(params);
                        detail.setValueSource(sourceValue);
                    } else {
                        detail.setValueSource("");
                    }
                    params.put("supplierUnique", valueNew);
                    newValue = recordGoodsOperDao.queryGoodsSupplier(params);

                    detail.setValueNew(newValue);

                    break;
                case "16":
                    //产品上下架
                    if (valueSource != null){
                        detail.setValueSource(valueSource.equals("1") ? "上架状态" : "下架状态");
                    } else {
                        detail.setValueSource("");
                    }
                    detail.setValueNew(valueNew.equals("1") ? "上架状态" : "下架状态");
                    break;
                case "17":
                    //小程序上下架
                    if (valueSource != null){
                        detail.setValueSource(valueSource.equals("1") ? "上架状态" : "下架状态");
                    } else {
                        detail.setValueSource("");
                    }
                    detail.setValueNew(valueNew.equals("1") ? "上架状态" : "下架状态");
                    break;
                case "18":
                    //捆绑商品
                    break;
                case "19":
                    //称重方式：1、称重商品；2、计件商品
                    if (valueSource != null){
                        detail.setValueSource(valueSource.equals("1") ? "称重商品" : "计件商品");
                    } else {
                        detail.setValueSource("");
                    }
                    detail.setValueNew(valueNew.equals("1") ? "称重商品" : "计件商品");
                    break;
                case "20":
                    //积分计算方法
                    break;
                case "21":
                    //提成计算方法
                    break;
                case "22":
                    //配送特殊商品类型
                    break;
                default:

            }
        }

        return entity;
    }
}

