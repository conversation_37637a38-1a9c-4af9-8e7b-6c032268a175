package org.haier.shopUpdate.service;

import org.haier.shopUpdate.entity.ShopQuickPayConfig;
import org.haier.shopUpdate.params.shopQuickPay.ConfigListParams;
import org.haier.shopUpdate.util.ShopsResult;

public interface ShopQuickPayService {
    /**
     * 查询配置列表
     * @param configListParams
     * @return
     */
    public ShopsResult configList(ConfigListParams configListParams);

    /**
     * 添加配置
     * @param shopQuickPayConfig
     * @return
     */
    public ShopsResult addConfig(ShopQuickPayConfig shopQuickPayConfig);

    /**
     * 修改配置
     * @param shopQuickPayConfig
     * @return
     */
    public ShopsResult updateConfig(ShopQuickPayConfig shopQuickPayConfig);
    /**
     * 删除配置
     * @param shopQuickPayConfig
     * @return
     */
    public ShopsResult deleteConfig(ShopQuickPayConfig shopQuickPayConfig);
}
