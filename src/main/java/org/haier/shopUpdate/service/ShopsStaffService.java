package org.haier.shopUpdate.service;

import java.io.IOException;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.haier.shopUpdate.params.QueryStaffListByStaffIdParams;
import org.haier.shopUpdate.util.ShopsResult;

/**
 * 管理员信息相关功能实现
 * <AUTHOR>
 */
public interface ShopsStaffService {

	/**
	 * 查询员工列表
	 * @param params
	 * @return
	 */
	public ShopsResult queryStaffListByStaffId(QueryStaffListByStaffIdParams params);
	
	/**
	 * 管理员登录
	 * @param map
	 * @return
	 */
	public ShopsResult staffLoginByAccountPwd(Map<String,Object> map);
	
	/**
	 * 根据管理员信息，查询管理员管理的店铺信息
	 * @param map
	 * @return
	 */
	public ShopsResult queryShopsByManager(Map<String,Object> map);
	
	/**
	 * 获取短信验证码
	 * @param staffAccount
	 * @return
	 */
	public ShopsResult sendMessage(Map<String,Object> map,HttpServletRequest request,HttpServletResponse response,Integer phoneType);
	
	/**
	 * 验证码验证通过
	 * @param request
	 * @param response
	 * @param map
	 * @return
	 */
	public ShopsResult passMsg(HttpServletRequest request,HttpServletResponse response,Map<String,Object> map,Integer phoneType,String sessionId);
	
	
	/**
	 * 更新管理员密码
	 * @param map
	 * @return
	 */
	public ShopsResult updateStaffPwd(Map<String,Object> map,Integer phoneType,String sessionId);
	/**
	 * 更新管理员信息
	 * @param map
	 * @return
	 */
	public ShopsResult updateStaffPwd(Map<String,Object> map);
	
	/**
	 * 更新店铺信息
	 * @param map
	 * @return
	 * @throws Exception 
	 */
	public ShopsResult updateShopsMessage(Map<String,Object> map,HttpServletRequest request) throws Exception;
	
	/**
	 * 店铺新信息查询
	 * @param map
	 * @return
	 */
	public ShopsResult shopNewMessage(Map<String,Object> map);
	
	public ShopsResult editShopsInfo(Map<String, Object> map, HttpServletRequest request) throws IOException, Exception;
	
	/**
	 * 退出管理员的登录信息
	 * @param map
	 * @return
	 */
	public ShopsResult loginOut(Map<String,Object> map);
	
	/**
	 * 查询店铺管理员信息
	 * @param map
	 * @return
	 */
	public ShopsResult shopsStaffsSearchByShopUnique(Map<String,Object> map);
	
	/**
	 * 修改员工权限
	 * @param map
	 * @return
	 */
	public ShopsResult modifyStaffPower(Map<String,Object> map);

	public ShopsResult editStafPwd(Map<String, Object> map);

	public ShopsResult updateRegistrationId(Map<String, Object> map);

	public ShopsResult queryHandoverRecord(String startTime, String endTime, Long shopUnique, Long saleListCashier, Integer pageNum, Integer pageSize);

	public ShopsResult submitFeedBack(String shop_unique, String staff_id, String feed_back_source,
			String feed_back_type, String feed_back_content, String feed_back_system, HttpServletRequest request);

	public ShopsResult submitShopQualification(String shop_unique, String legal_name, String shop_phone,
			String register_shop_name, String use_shop_name, HttpServletRequest request) throws IOException, Exception;

	public ShopsResult queryShopQualification(String shop_unique);

	/**
	 * 会员充值详情记录
	 * @param map
	 * @return
	 */
	public ShopsResult queryCusRechargeRecord(Map<String,Object> map);

}
