package org.haier.shopUpdate.service;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.shopUpdate.dao.RestaurantDao;
import org.haier.shopUpdate.util.ShopsResult;
import org.springframework.stereotype.Service;

/**
* @author: 作者:王恩龙
* @version: 2020年12月17日 下午4:00:00
*
*/
@Service
public class RestaurantServiceImpl implements RestaurantService{
	
	@Resource
	private RestaurantDao resDao;
	
	/**
	 * 查询店铺信息
	 */
	public ShopsResult queryShopMsg(String shopUnique) {
		ShopsResult sr = new ShopsResult(1, "查询成功！");
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		Map<String,Object> resMap = resDao.queryShopMsg(map);
		sr.setData(resMap);
		return sr;
	}

}
