package org.haier.shopUpdate.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.haier.shopUpdate.dao.*;
import org.haier.shopUpdate.dao.nova.I18nLanguageDao;
import org.haier.shopUpdate.dao.pojo.GoodsBatchAddPo;
import org.haier.shopUpdate.dao.pojo.GoodsBatchUpdatePo;
import org.haier.shopUpdate.dao.pojo.GoodsSaleBatchAddPo;
import org.haier.shopUpdate.dto.AddGoodsStockDto;
import org.haier.shopUpdate.entity.ShopsConfig;
import org.haier.shopUpdate.entity.StockReason;
import org.haier.shopUpdate.entity.goods.goodsEnum.OperSourceEnum;
import org.haier.shopUpdate.entity.goods.goodsEnum.OperTypeEnum;
import org.haier.shopUpdate.entity.goods.goodsEnum.UserTypeEnum;
import org.haier.shopUpdate.entity.goods.goodsRecord.GoodsOperParam;
import org.haier.shopUpdate.entity.goods.goodsRecord.RecordGoods;
import org.haier.shopUpdate.entity.goods.goodsRecord.RecordGoodsOper;
import org.haier.shopUpdate.entity.goods.goodsRecord.RecordGoodsOperParams;
import org.haier.shopUpdate.enums.GoodsInPriceTypeEnums;
import org.haier.shopUpdate.enums.IsIoBoundInspectEnum;
import org.haier.shopUpdate.enums.StockTypeEnums;
import org.haier.shopUpdate.enums.nova.LanguageEnum;
import org.haier.shopUpdate.enums.shopStockDetail.StockKindEnum;
import org.haier.shopUpdate.enums.shopStockDetail.StockResourceEnum;
import org.haier.shopUpdate.enums.shopStockDetail.StockTypeEnum;
import org.haier.shopUpdate.params.goodBatch.GoodBatchListQueryParams;
import org.haier.shopUpdate.params.goodBatch.GoodBatchQueryParams;
import org.haier.shopUpdate.params.shopStockDetail.ShopStockDetailAddParams;
import org.haier.shopUpdate.redism.RedisCache;
import org.haier.shopUpdate.result.goodBatch.GoodBatchListQueryDto;
import org.haier.shopUpdate.result.goodBatch.GoodBatchQueryResult;
import org.haier.shopUpdate.util.I18nLanguageStaticReturnParamsUtil;
import org.haier.shopUpdate.util.ShopsResult;
import org.haier.shopUpdate.util.UtilForJAVA;
import org.haier.shopUpdate.util.common.CommonResult;
import org.haier.shopUpdate.util.common.StringUtils;
import org.haier.shopUpdate.util.helibao.HelibaoPayConfig;
import org.haier.shopUpdate.util.thread.SendMqttMsg;
import org.haier.shopUpdate.util.unionpay.HttpUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.*;
@Slf4j
@Service
@Transactional
public class StockServiceImpl implements StockService{
	@Resource
	private StockDao stockDao;
	@Resource
	private GoodsDao goodsDao;
	@Resource
	private GoodsBatchDao goodsBatchDao;
	@Resource
	private GoodsSaleBatchDao goodsSaleBatchDao;
	@Resource
	private ShopStaffDao shopStaffDao;
	@Resource
	private ShopsConfigDao shopsConfigDao;
	@Resource
	private  AppPayDao appPayDao;
	@Resource
	private I18nLanguageDao i18nLanguageDao;
	@Resource
	private RedisCache rc;
	@Resource
	private I18nLanguageStaticReturnParamsUtil i18nRtUtil;

	/**
	 * 获取指定数据字典的字典数据
	 * @param code
	 * @return
	 */
	public CommonResult querySystemDict(String code) {
		CommonResult c = new CommonResult(1,"查询成功！");



		return c;
	}
	/**
	 * 添加新的入库记录
	 * @param map
	 * @return
	 */
	public ShopsResult newStockRecord(Map<String,Object> map, GoodsOperParam goodsOperParam, Integer stockType){
		ShopsResult sr=new ShopsResult();
		ShopStockDetailAddParams shopStockDetailAddParams = new ShopStockDetailAddParams();
		shopStockDetailAddParams.setTotalCount(new BigDecimal(String.valueOf(map.get("goodsCount"))));
		if (ObjectUtil.isNotEmpty(map.get("stockTotalPrice"))) {
			shopStockDetailAddParams.setTotalAmount(new BigDecimal(String.valueOf(map.get("stockTotalPrice"))));
		} else {
			shopStockDetailAddParams.setTotalAmount(new BigDecimal(String.valueOf(map.get("goodsCount"))).multiply(new BigDecimal(String.valueOf(map.get("stockPrice")))));
		}

		if (ObjectUtil.isNotEmpty(map.get("goodBatchMessage"))) {
			BigDecimal chooseGoodsCount = BigDecimal.ZERO;
			List<GoodBatchListQueryDto> goodBatchListQueryList = JSONUtil.toList(String.valueOf(map.get("goodBatchMessage")).replaceAll("=", ":"), GoodBatchListQueryDto.class);
			for (GoodBatchListQueryDto goodBatchListQueryDto :
					goodBatchListQueryList) {
				chooseGoodsCount = chooseGoodsCount.add(goodBatchListQueryDto.getOutStockCount());
			}
			if (chooseGoodsCount.compareTo(shopStockDetailAddParams.getTotalCount()) > 0) {
				sr.setStatus(2);
				sr.setMsg("出库数之和不得大于商品出库数量！");
				return sr;
			}
		}

		//商品修改记录
		RecordGoods sourceGoods = new RecordGoods();
		//操作信息
		RecordGoodsOper recordGoodsOper = new RecordGoodsOper();
		Map<String, Object> goodsParams = new HashMap<>();
		goodsParams.put("shopUnique" , map.get("shopUnique").toString());
		if (ObjectUtil.isNotEmpty(map.get("sourceGoodsBarcode"))) {
			goodsParams.put("goodsBarcode" , map.get("sourceGoodsBarcode").toString());
		}
		map.put("goodsBarcode", map.get("sourceGoodsBarcode"));
		RecordGoods resultGoods = new RecordGoods();
		RecordGoods recordGoods = goodsDao.querySourceGoods(goodsParams);

		Map<String,Object> maps = stockDao.getBottomGoodsMessage(map);
		if(null!=maps){
			if(ObjectUtil.equal(2,stockType)
					&& (ObjectUtil.isEmpty(maps.get("oldStockCount")) || ObjectUtil.equal(BigDecimal.ZERO, new BigDecimal(String.valueOf(maps.get("oldStockCount")))))){
				sr.setStatus(2);
				sr.setMsg("出库失败，商品库存为0！");
				return sr;
			}
			map.putAll(maps);
		}else{
			sr.setStatus(2);
			sr.setMsg("库存修改失败，所添加的商品不存在！");
			return sr;
		}

		boolean modifyGoodsCount = true;

		shopStockDetailAddParams.setShopUnique(map.get("shopUnique").toString());
		String listUnique = StrUtil.concat(true, DateUtil.format(DateUtil.date(), "yyyyMMddHHmmssSSS"), RandomUtil.randomNumbers(3));
		map.put("list_unique", listUnique);
		shopStockDetailAddParams.setListUnique(listUnique);
		shopStockDetailAddParams.setStockKind(StockKindEnum.INITIALIZED.getCode());
		Integer stockResource = ObjectUtil.isNotEmpty(map.get("stockResource"))?Integer.parseInt(map.get("stockResource").toString()):StockResourceEnum.MANUAL.getCode();
		shopStockDetailAddParams.setStockResource(stockResource);
		shopStockDetailAddParams.setStockOrigin(goodsOperParam.getDeviceSource());
		shopStockDetailAddParams.setStaffId(Long.valueOf(goodsOperParam.getUserId()));
		shopStockDetailAddParams.setStockTime(DateUtil.date());
		shopStockDetailAddParams.setUpdateId(Long.valueOf(goodsOperParam.getUserId()));
		shopStockDetailAddParams.setUpdateTime(DateUtil.date());
		if (ObjectUtil.isNotEmpty(map.get("stockRemarks"))) {
			shopStockDetailAddParams.setStockRemarks(String.valueOf(map.get("stockRemarks")));
		}
		if (ObjectUtil.isNotEmpty(map.get("stockPicture"))) {
			shopStockDetailAddParams.setStockPicture(String.valueOf(map.get("stockPicture")));
		}
		if (ObjectUtil.equal(StockTypeEnums.STOCK_IN.getStatus(), stockType)) {
			shopStockDetailAddParams.setStockType(StockTypeEnum.INVENTORY_IN.getCode());
		} else if (ObjectUtil.equal(StockTypeEnums.STOCK_OUT.getStatus(), stockType)) {
			shopStockDetailAddParams.setStockType(StockTypeEnum.INVENTORY_OUT.getCode());
		}
		ShopsConfig shopsConfig = shopsConfigDao.selectByShopUnique(map.get("shopUnique").toString());
		if (ObjectUtil.isNotNull(shopsConfig)) {
			if (ObjectUtil.equal(IsIoBoundInspectEnum.NO.getCode(), shopsConfig.getIsIoBoundInspect())) {
				shopStockDetailAddParams.setAuditStatus(1);
				shopStockDetailAddParams.setAuditId(Long.valueOf(goodsOperParam.getUserId()));
				shopStockDetailAddParams.setAuditTime(DateUtil.date());
			} else {
				modifyGoodsCount = false;
				shopStockDetailAddParams.setAuditStatus(0);
			}
			if (ObjectUtil.equals(GoodsInPriceTypeEnums.AVG.getCode(), shopsConfig.getGoodsInPriceType()) && StockTypeEnums.STOCK_IN.getStatus().compareTo(stockType) == 0) {
				if (BigDecimal.ZERO.compareTo(new BigDecimal(String.valueOf(map.get("oldGoodStockCount")))) < 0) {
					// 计算商品平均价
					BigDecimal inCount = new BigDecimal(map.get("goodsCount").toString());
					BigDecimal totalCount = NumberUtil.add(inCount, new BigDecimal(String.valueOf(map.get("oldStockCount"))));
					BigDecimal totalInAmount = shopStockDetailAddParams.getTotalAmount();
					BigDecimal stockAmount = NumberUtil.mul(new BigDecimal(String.valueOf(map.get("oldGoodStockCount"))), recordGoods.getGoodsInPrice());
					BigDecimal avgInPrice = NumberUtil.add(totalInAmount, stockAmount).divide(totalCount, 2, RoundingMode.HALF_UP);
					map.put("goodsAvgInPrice", avgInPrice);
				} else {
					map.put("goodsAvgInPrice", new BigDecimal(String.valueOf(map.get("stockPrice"))).divide(new BigDecimal(String.valueOf(map.get("goodsContain"))),2,RoundingMode.HALF_UP));
				}
			} else if (ObjectUtil.equals(GoodsInPriceTypeEnums.BATCH.getCode(), shopsConfig.getGoodsInPriceType())) {
				List<GoodsBatchAddPo> goodsBatchAddPoList = new ArrayList<>();
				List<GoodsBatchUpdatePo> goodsBatchUpdatePoList = new ArrayList<>();
				List<GoodsSaleBatchAddPo> goodsSaleBatchAddPoList = new ArrayList<>();
				if (ObjectUtil.equal(IsIoBoundInspectEnum.NO.getCode(), shopsConfig.getIsIoBoundInspect())) {
					if (ObjectUtil.equal(StockTypeEnums.STOCK_IN.getStatus(), stockType)) {
						GoodsBatchAddPo goodsBatchAddPo = new GoodsBatchAddPo();
						goodsBatchAddPo.setShopUnique(Long.valueOf(String.valueOf(map.get("shopUnique"))));
						goodsBatchAddPo.setBatchUnique(StrUtil.concat(true, String.valueOf(System.currentTimeMillis()), RandomUtil.randomNumbers(3)));
						goodsBatchAddPo.setStockListUnique(listUnique);
						goodsBatchAddPo.setGoodsBarcode(String.valueOf(map.get("goodsBarcode")));
						goodsBatchAddPo.setGoodsInPrice(new BigDecimal(String.valueOf(map.get("stockPrice"))).divide(new BigDecimal(String.valueOf(map.get("goodsContain"))),2,RoundingMode.HALF_UP));
						if (ObjectUtil.isNotEmpty(maps.get("oldStockCount")) && new BigDecimal(String.valueOf(maps.get("oldStockCount"))).compareTo(BigDecimal.ZERO) < 0 && (new BigDecimal(String.valueOf(map.get("goodsCount"))).add(new BigDecimal(String.valueOf(maps.get("oldStockCount"))))).compareTo(BigDecimal.ZERO) < 0) {
							goodsBatchAddPo.setGoodsCount(BigDecimal.ZERO);
						} else if (ObjectUtil.isNotEmpty(maps.get("oldStockCount")) && new BigDecimal(String.valueOf(maps.get("oldStockCount"))).compareTo(BigDecimal.ZERO) < 0) {
							goodsBatchAddPo.setGoodsCount(new BigDecimal(String.valueOf(map.get("goodsCount"))).add(new BigDecimal(String.valueOf(maps.get("oldStockCount")))));
						} else {
							goodsBatchAddPo.setGoodsCount(new BigDecimal(String.valueOf(map.get("goodsCount"))));
						}
						goodsBatchAddPo.setGoodsInCount(new BigDecimal(String.valueOf(map.get("goodsCount"))));
						if (ObjectUtil.isNotEmpty(map.get("goodsProd"))) {
							goodsBatchAddPo.setGoodsProd(DateUtil.parse(String.valueOf(map.get("goodsProd"))));
						}
						if (ObjectUtil.isNotEmpty(map.get("goodsExp"))) {
							goodsBatchAddPo.setGoodsExp(DateUtil.parse(String.valueOf(map.get("goodsExp"))));
						}
						if (ObjectUtil.isNotEmpty(map.get("goodsProd")) && ObjectUtil.isNotEmpty(map.get("goodsExp"))) {
							goodsBatchAddPo.setGoodsLife((int) DateUtil.between(goodsBatchAddPo.getGoodsProd(), goodsBatchAddPo.getGoodsExp(), DateUnit.DAY));
						}
						goodsBatchAddPo.setSourceBarcode(String.valueOf(map.get("sourceGoodsBarcode")));
						goodsBatchAddPo.setCreateId(Long.valueOf(goodsOperParam.getUserId()));
						goodsBatchAddPo.setCreateTime(DateUtil.date());
						goodsBatchAddPoList.add(goodsBatchAddPo);
					} else if (ObjectUtil.equal(StockTypeEnums.STOCK_OUT.getStatus(), stockType)) {
						BigDecimal goodsAvgOutPrice = BigDecimal.ZERO;
						BigDecimal goodsCount = new BigDecimal(String.valueOf(map.get("goodsCount")));
						if (ObjectUtil.isNotEmpty(map.get("goodBatchMessage"))) {
							BigDecimal chooseGoodsCount = BigDecimal.ZERO;
							List<GoodBatchListQueryDto> goodBatchListQueryList = JSONUtil.toList(String.valueOf(map.get("goodBatchMessage")).replaceAll("=",":"),GoodBatchListQueryDto.class);
							for (GoodBatchListQueryDto goodBatchListQueryDto :
									goodBatchListQueryList) {
								goodBatchListQueryDto.setOutStockCount(goodBatchListQueryDto.getOutStockCount().multiply(new BigDecimal(String.valueOf(map.get("goodsContain")))));

								GoodBatchQueryParams goodBatchQueryParams = new GoodBatchQueryParams();
								goodBatchQueryParams.setBatchUnique(goodBatchListQueryDto.getBatchUnique());
								goodBatchQueryParams.setShopUnique(map.get("shopUnique").toString());
								goodBatchQueryParams.setGoodsBarcode(String.valueOf(map.get("goodsBarcode")));
								GoodBatchQueryResult goodBatchQueryResult = goodsBatchDao.queryGoodsBatch(goodBatchQueryParams);
								if (ObjectUtil.isNotEmpty(goodBatchQueryResult)) {
									if (goodBatchQueryResult.getGoodsCount().compareTo(goodBatchListQueryDto.getOutStockCount()) < 0) {
										sr.setStatus(2);
										sr.setMsg("商品批次出库数大于剩余数量！");
										return sr;
									}
									GoodsBatchUpdatePo goodsBatchUpdatePo = new GoodsBatchUpdatePo();
									BeanUtil.copyProperties(goodBatchQueryResult, goodsBatchUpdatePo);
									goodsBatchUpdatePo.setGoodsCount(goodsBatchUpdatePo.getGoodsCount().subtract(goodBatchListQueryDto.getOutStockCount()));
									goodsBatchUpdatePo.setUpdateId(Long.valueOf(goodsOperParam.getUserId()));
									goodsBatchUpdatePoList.add(goodsBatchUpdatePo);

									GoodsSaleBatchAddPo goodsSaleBatchAddPo = new GoodsSaleBatchAddPo();
									goodsSaleBatchAddPo.setShopUnique(Long.valueOf(String.valueOf(map.get("shopUnique"))));
									goodsSaleBatchAddPo.setBatchUnique(goodBatchQueryResult.getBatchUnique());
									goodsSaleBatchAddPo.setGoodsBarcode(String.valueOf(map.get("sourceGoodsBarcode")));
									goodsSaleBatchAddPo.setStockListUnique(listUnique);
									goodsSaleBatchAddPo.setGoodsInPrice(goodBatchQueryResult.getGoodsInPrice().multiply(new BigDecimal(String.valueOf(map.get("goodsContain")))));
									goodsSaleBatchAddPo.setGoodsOutPrice(new BigDecimal(String.valueOf(map.get("stockPrice"))));
									goodsSaleBatchAddPo.setGoodsOutCount(goodBatchListQueryDto.getOutStockCount().divide(new BigDecimal(String.valueOf(map.get("goodsContain"))),2,RoundingMode.HALF_UP));
									goodsSaleBatchAddPo.setCreateId(Long.valueOf(goodsOperParam.getUserId()));
									goodsSaleBatchAddPo.setCreateTime(DateUtil.date());
									goodsSaleBatchAddPo.setUpdateId(Long.valueOf(goodsOperParam.getUserId()));
									goodsSaleBatchAddPo.setUpdateTime(DateUtil.date());
									goodsSaleBatchAddPoList.add(goodsSaleBatchAddPo);

									goodsAvgOutPrice = goodsAvgOutPrice.add(goodBatchQueryResult.getGoodsInPrice().multiply(goodBatchListQueryDto.getOutStockCount()));
									chooseGoodsCount = chooseGoodsCount.add(goodBatchListQueryDto.getOutStockCount());
								}
							}
							if (chooseGoodsCount.compareTo(goodsCount) < 0) {
								goodsCount = goodsCount.subtract(chooseGoodsCount);
								GoodBatchListQueryParams goodBatchListQueryParams = new GoodBatchListQueryParams();
								goodBatchListQueryParams.setShopUnique(String.valueOf(map.get("shopUnique")));
								goodBatchListQueryParams.setGoodsBarcode(String.valueOf(map.get("goodsBarcode")));
								goodBatchListQueryList = goodsBatchDao.queryGoodsBatchInList(goodBatchListQueryParams);
								if (ObjectUtil.isNotEmpty(goodBatchListQueryList)) {
									for (GoodBatchListQueryDto goodBatchListQueryDto :
											goodBatchListQueryList) {
										for (GoodsBatchUpdatePo goodsBatchUpdatePo : goodsBatchUpdatePoList) {
											if (ObjectUtil.equals(goodsBatchUpdatePo.getBatchUnique(), goodBatchListQueryDto.getBatchUnique())) {
												goodBatchListQueryDto.setGoodsCount(goodsBatchUpdatePo.getGoodsCount());
												break;
											}
										}
										if (goodBatchListQueryDto.getGoodsCount().compareTo(BigDecimal.ZERO) > 0 && goodBatchListQueryDto.getGoodsCount().compareTo(new BigDecimal(String.valueOf(map.get("goodsContain")))) >= 0) {
											GoodsBatchUpdatePo goodsBatchUpdatePo = new GoodsBatchUpdatePo();
											BeanUtil.copyProperties(goodBatchListQueryDto, goodsBatchUpdatePo);
											goodsBatchUpdatePo.setShopUnique(Long.valueOf(String.valueOf(map.get("shopUnique"))));
											goodsBatchUpdatePo.setGoodsBarcode(String.valueOf(map.get("goodsBarcode")));
											goodsBatchUpdatePo.setUpdateId(Long.valueOf(goodsOperParam.getUserId()));
											if (goodsCount.compareTo(goodBatchListQueryDto.getGoodsCount()) > 0) {
												if (new BigDecimal(String.valueOf(map.get("goodsContain"))).compareTo(BigDecimal.ONE) == 0) {
													goodsCount = goodsCount.subtract(goodBatchListQueryDto.getGoodsCount());
													goodsBatchUpdatePo.setGoodsCount(BigDecimal.ZERO);
												} else {
													goodsCount = goodsCount.subtract(BigDecimal.valueOf(goodBatchListQueryDto.getGoodsCount()
																	.divide(new BigDecimal(String.valueOf(map.get("goodsContain"))), 2, RoundingMode.HALF_UP).intValue())
															.multiply(new BigDecimal(String.valueOf(map.get("goodsContain")))));
													goodsBatchUpdatePo.setGoodsCount(goodBatchListQueryDto.getGoodsCount().subtract(
															BigDecimal.valueOf(goodBatchListQueryDto.getGoodsCount()
																			.divide(new BigDecimal(String.valueOf(map.get("goodsContain"))), 2, RoundingMode.HALF_UP).intValue())
																	.multiply(new BigDecimal(String.valueOf(map.get("goodsContain"))))
													));
												}
												goodsBatchUpdatePoList.add(goodsBatchUpdatePo);
												boolean saleBatchFlag = false;
												if (ObjectUtil.isNotEmpty(goodsSaleBatchAddPoList)) {
													for (GoodsSaleBatchAddPo goodsSaleBatchAddPo : goodsSaleBatchAddPoList) {
														if (ObjectUtil.equals(goodsSaleBatchAddPo.getBatchUnique(), goodBatchListQueryDto.getBatchUnique())) {
															goodsSaleBatchAddPo.setGoodsOutCount(goodsSaleBatchAddPo.getGoodsOutCount().add(BigDecimal.valueOf(goodBatchListQueryDto.getGoodsCount()
																	.divide(new BigDecimal(String.valueOf(map.get("goodsContain"))), 2, RoundingMode.HALF_UP).intValue())));
															saleBatchFlag = true;
														}
													}
												}
												if (!saleBatchFlag) {
													GoodsSaleBatchAddPo goodsSaleBatchAddPo = new GoodsSaleBatchAddPo();
													goodsSaleBatchAddPo.setShopUnique(Long.valueOf(String.valueOf(map.get("shopUnique"))));
													goodsSaleBatchAddPo.setBatchUnique(goodBatchListQueryDto.getBatchUnique());
													goodsSaleBatchAddPo.setGoodsBarcode(String.valueOf(map.get("sourceGoodsBarcode")));
													goodsSaleBatchAddPo.setStockListUnique(listUnique);
													goodsSaleBatchAddPo.setGoodsInPrice(goodBatchListQueryDto.getGoodsInPrice().multiply(new BigDecimal(String.valueOf(map.get("goodsContain")))));
													goodsSaleBatchAddPo.setGoodsOutPrice(new BigDecimal(String.valueOf(map.get("stockPrice"))));
													goodsSaleBatchAddPo.setGoodsOutCount(BigDecimal.valueOf(goodBatchListQueryDto.getGoodsCount()
															.divide(new BigDecimal(String.valueOf(map.get("goodsContain"))), 2, RoundingMode.HALF_UP).intValue()));
													goodsSaleBatchAddPo.setCreateId(Long.valueOf(goodsOperParam.getUserId()));
													goodsSaleBatchAddPo.setCreateTime(DateUtil.date());
													goodsSaleBatchAddPo.setUpdateId(Long.valueOf(goodsOperParam.getUserId()));
													goodsSaleBatchAddPo.setUpdateTime(DateUtil.date());
													goodsSaleBatchAddPoList.add(goodsSaleBatchAddPo);
												}
												goodsAvgOutPrice = goodsAvgOutPrice.add(goodBatchListQueryDto.getGoodsInPrice().multiply(goodBatchListQueryDto.getGoodsCount()));
											} else if (goodsCount.compareTo(BigDecimal.ZERO) > 0) {
												goodsBatchUpdatePo.setGoodsCount(goodBatchListQueryDto.getGoodsCount().subtract(goodsCount));
												goodsBatchUpdatePoList.add(goodsBatchUpdatePo);
												boolean saleBatchFlag = false;
												if (ObjectUtil.isNotEmpty(goodsSaleBatchAddPoList)) {
													for (GoodsSaleBatchAddPo goodsSaleBatchAddPo : goodsSaleBatchAddPoList) {
														if (ObjectUtil.equals(goodsSaleBatchAddPo.getBatchUnique(), goodBatchListQueryDto.getBatchUnique())) {
															goodsSaleBatchAddPo.setGoodsOutCount(goodsSaleBatchAddPo.getGoodsOutCount().add(goodsCount.divide(new BigDecimal(String.valueOf(map.get("goodsContain"))), 2, RoundingMode.HALF_UP)));
															saleBatchFlag = true;
														}
													}
												}
												if (!saleBatchFlag) {
													GoodsSaleBatchAddPo goodsSaleBatchAddPo = new GoodsSaleBatchAddPo();
													goodsSaleBatchAddPo.setShopUnique(Long.valueOf(String.valueOf(map.get("shopUnique"))));
													goodsSaleBatchAddPo.setBatchUnique(goodBatchListQueryDto.getBatchUnique());
													goodsSaleBatchAddPo.setGoodsBarcode(String.valueOf(map.get("sourceGoodsBarcode")));
													goodsSaleBatchAddPo.setStockListUnique(listUnique);
													goodsSaleBatchAddPo.setGoodsInPrice(goodBatchListQueryDto.getGoodsInPrice().multiply(new BigDecimal(String.valueOf(map.get("goodsContain")))));
													goodsSaleBatchAddPo.setGoodsOutPrice(new BigDecimal(String.valueOf(map.get("stockPrice"))));
													goodsSaleBatchAddPo.setGoodsOutCount(goodsCount.divide(new BigDecimal(String.valueOf(map.get("goodsContain"))), 2, RoundingMode.HALF_UP));
													goodsSaleBatchAddPo.setCreateId(Long.valueOf(goodsOperParam.getUserId()));
													goodsSaleBatchAddPo.setCreateTime(DateUtil.date());
													goodsSaleBatchAddPo.setUpdateId(Long.valueOf(goodsOperParam.getUserId()));
													goodsSaleBatchAddPo.setUpdateTime(DateUtil.date());
													goodsSaleBatchAddPoList.add(goodsSaleBatchAddPo);
												}
												goodsAvgOutPrice = goodsAvgOutPrice.add(goodBatchListQueryDto.getGoodsInPrice().multiply(goodsCount));
												break;
											}
										}
									}
								}
							}
						} else {
							GoodBatchListQueryParams goodBatchListQueryParams = new GoodBatchListQueryParams();
							goodBatchListQueryParams.setShopUnique(String.valueOf(map.get("shopUnique")));
							goodBatchListQueryParams.setGoodsBarcode(String.valueOf(map.get("goodsBarcode")));
							List<GoodBatchListQueryDto> goodBatchListQueryList = goodsBatchDao.queryGoodsBatchInList(goodBatchListQueryParams);
							if (ObjectUtil.isNotEmpty(goodBatchListQueryList)) {
								for (GoodBatchListQueryDto goodBatchListQueryDto :
										goodBatchListQueryList) {
									GoodsBatchUpdatePo goodsBatchUpdatePo = new GoodsBatchUpdatePo();
									BeanUtil.copyProperties(goodBatchListQueryDto, goodsBatchUpdatePo);
									goodsBatchUpdatePo.setShopUnique(Long.valueOf(String.valueOf(map.get("shopUnique"))));
									goodsBatchUpdatePo.setGoodsBarcode(String.valueOf(map.get("goodsBarcode")));
									goodsBatchUpdatePo.setUpdateId(Long.valueOf(goodsOperParam.getUserId()));
									if (goodsCount.compareTo(goodBatchListQueryDto.getGoodsCount()) > 0 && goodBatchListQueryDto.getGoodsCount().compareTo(new BigDecimal(String.valueOf(map.get("goodsContain")))) >= 0) {
										if (new BigDecimal(String.valueOf(map.get("goodsContain"))).compareTo(BigDecimal.ONE) == 0) {
											goodsCount = goodsCount.subtract(goodBatchListQueryDto.getGoodsCount());
											goodsBatchUpdatePo.setGoodsCount(BigDecimal.ZERO);
										} else {
											goodsCount = goodsCount.subtract(BigDecimal.valueOf(goodBatchListQueryDto.getGoodsCount()
															.divide(new BigDecimal(String.valueOf(map.get("goodsContain"))), 2, RoundingMode.HALF_UP).intValue())
													.multiply(new BigDecimal(String.valueOf(map.get("goodsContain")))));
											goodsBatchUpdatePo.setGoodsCount(goodBatchListQueryDto.getGoodsCount().subtract(
													BigDecimal.valueOf(goodBatchListQueryDto.getGoodsCount()
																	.divide(new BigDecimal(String.valueOf(map.get("goodsContain"))), 2, RoundingMode.HALF_UP).intValue())
															.multiply(new BigDecimal(String.valueOf(map.get("goodsContain"))))
											));
										}
										goodsBatchUpdatePoList.add(goodsBatchUpdatePo);

										boolean saleBatchFlag = false;
										if (ObjectUtil.isNotEmpty(goodsSaleBatchAddPoList)) {
											for (GoodsSaleBatchAddPo goodsSaleBatchAddPo : goodsSaleBatchAddPoList) {
												if (ObjectUtil.equals(goodsSaleBatchAddPo.getBatchUnique(), goodBatchListQueryDto.getBatchUnique())) {
													goodsSaleBatchAddPo.setGoodsOutCount(goodsSaleBatchAddPo.getGoodsOutCount().add(BigDecimal.valueOf(goodBatchListQueryDto.getGoodsCount()
															.divide(new BigDecimal(String.valueOf(map.get("goodsContain"))), 2, RoundingMode.HALF_UP).intValue())));
													saleBatchFlag = true;
												}
											}
										}
										if (!saleBatchFlag) {
											GoodsSaleBatchAddPo goodsSaleBatchAddPo = new GoodsSaleBatchAddPo();
											goodsSaleBatchAddPo.setShopUnique(Long.valueOf(String.valueOf(map.get("shopUnique"))));
											goodsSaleBatchAddPo.setBatchUnique(goodBatchListQueryDto.getBatchUnique());
											goodsSaleBatchAddPo.setGoodsBarcode(String.valueOf(map.get("sourceGoodsBarcode")));
											goodsSaleBatchAddPo.setStockListUnique(listUnique);
											goodsSaleBatchAddPo.setGoodsInPrice(goodBatchListQueryDto.getGoodsInPrice().multiply(new BigDecimal(String.valueOf(map.get("goodsContain")))));
											goodsSaleBatchAddPo.setGoodsOutPrice(new BigDecimal(String.valueOf(map.get("stockPrice"))));
											goodsSaleBatchAddPo.setGoodsOutCount(BigDecimal.valueOf(goodBatchListQueryDto.getGoodsCount()
													.divide(new BigDecimal(String.valueOf(map.get("goodsContain"))), 2, RoundingMode.HALF_UP).intValue()));
											goodsSaleBatchAddPo.setCreateId(Long.valueOf(goodsOperParam.getUserId()));
											goodsSaleBatchAddPo.setCreateTime(DateUtil.date());
											goodsSaleBatchAddPo.setUpdateId(Long.valueOf(goodsOperParam.getUserId()));
											goodsSaleBatchAddPo.setUpdateTime(DateUtil.date());
											goodsSaleBatchAddPoList.add(goodsSaleBatchAddPo);
										}
										goodsAvgOutPrice = goodsAvgOutPrice.add(goodBatchListQueryDto.getGoodsInPrice().multiply(goodBatchListQueryDto.getGoodsCount()));
									} else if (goodsCount.compareTo(BigDecimal.ZERO) > 0) {
										goodsBatchUpdatePo.setGoodsCount(goodBatchListQueryDto.getGoodsCount().subtract(goodsCount));
										goodsBatchUpdatePoList.add(goodsBatchUpdatePo);
										boolean saleBatchFlag = false;
										if (ObjectUtil.isNotEmpty(goodsSaleBatchAddPoList)) {
											for (GoodsSaleBatchAddPo goodsSaleBatchAddPo : goodsSaleBatchAddPoList) {
												if (ObjectUtil.equals(goodsSaleBatchAddPo.getBatchUnique(), goodBatchListQueryDto.getBatchUnique())) {
													goodsSaleBatchAddPo.setGoodsOutCount(goodsSaleBatchAddPo.getGoodsOutCount().add(goodsCount.divide(new BigDecimal(String.valueOf(map.get("goodsContain"))), 2, RoundingMode.HALF_UP)));
													saleBatchFlag = true;
												}
											}
										}
										if (!saleBatchFlag) {
											GoodsSaleBatchAddPo goodsSaleBatchAddPo = new GoodsSaleBatchAddPo();
											goodsSaleBatchAddPo.setShopUnique(Long.valueOf(String.valueOf(map.get("shopUnique"))));
											goodsSaleBatchAddPo.setBatchUnique(goodBatchListQueryDto.getBatchUnique());
											goodsSaleBatchAddPo.setGoodsBarcode(String.valueOf(map.get("sourceGoodsBarcode")));
											goodsSaleBatchAddPo.setStockListUnique(listUnique);
											goodsSaleBatchAddPo.setGoodsInPrice(goodBatchListQueryDto.getGoodsInPrice().multiply(new BigDecimal(String.valueOf(map.get("goodsContain")))));
											goodsSaleBatchAddPo.setGoodsOutPrice(new BigDecimal(String.valueOf(map.get("stockPrice"))));
											goodsSaleBatchAddPo.setGoodsOutCount(goodsCount.divide(new BigDecimal(String.valueOf(map.get("goodsContain"))), 2, RoundingMode.HALF_UP));
											goodsSaleBatchAddPo.setCreateId(Long.valueOf(goodsOperParam.getUserId()));
											goodsSaleBatchAddPo.setCreateTime(DateUtil.date());
											goodsSaleBatchAddPo.setUpdateId(Long.valueOf(goodsOperParam.getUserId()));
											goodsSaleBatchAddPo.setUpdateTime(DateUtil.date());
											goodsSaleBatchAddPoList.add(goodsSaleBatchAddPo);
										}
										goodsAvgOutPrice = goodsAvgOutPrice.add(goodBatchListQueryDto.getGoodsInPrice().multiply(goodsCount));
										break;
									}
								}
							}
						}
						goodsAvgOutPrice = goodsAvgOutPrice.divide(new BigDecimal(map.get("goodsCount").toString()).divide(new BigDecimal(String.valueOf(map.get("goodsContain"))),2,RoundingMode.HALF_UP), 2, BigDecimal.ROUND_HALF_UP);
						map.put("goodsAvgOutPrice",goodsAvgOutPrice);
					}

				} else if (ObjectUtil.equal(IsIoBoundInspectEnum.YES.getCode(), shopsConfig.getIsIoBoundInspect())) {
					map.put("stockCount", map.get("oldStockCount"));
					if (ObjectUtil.equal(StockTypeEnums.STOCK_OUT.getStatus(), stockType)) {
						if (ObjectUtil.isNotEmpty(map.get("goodBatchMessage"))) {
							BigDecimal goodsAvgOutPrice = BigDecimal.ZERO;
							BigDecimal goodsCount = new BigDecimal(String.valueOf(map.get("goodsCount")));
							BigDecimal chooseGoodsCount = BigDecimal.ZERO;
							List<GoodBatchListQueryDto> goodBatchListQueryList = JSONUtil.toList(String.valueOf(map.get("goodBatchMessage")),GoodBatchListQueryDto.class);
							for (GoodBatchListQueryDto goodBatchListQueryDto :
									goodBatchListQueryList) {
								goodBatchListQueryDto.setOutStockCount(goodBatchListQueryDto.getOutStockCount().multiply(new BigDecimal(String.valueOf(map.get("goodsContain")))));

								GoodBatchQueryParams goodBatchQueryParams = new GoodBatchQueryParams();
								goodBatchQueryParams.setBatchUnique(goodBatchListQueryDto.getBatchUnique());
								goodBatchQueryParams.setShopUnique(map.get("shopUnique").toString());
								goodBatchQueryParams.setGoodsBarcode(String.valueOf(map.get("goodsBarcode")));
								GoodBatchQueryResult goodBatchQueryResult = goodsBatchDao.queryGoodsBatch(goodBatchQueryParams);
								if (ObjectUtil.isNotEmpty(goodBatchQueryResult)) {
									if (goodBatchQueryResult.getGoodsCount().compareTo(goodBatchListQueryDto.getOutStockCount()) < 0) {
										sr.setStatus(2);
										sr.setMsg("商品批次出库数大于剩余数量！");
										return sr;
									}
									GoodsSaleBatchAddPo goodsSaleBatchAddPo = new GoodsSaleBatchAddPo();
									goodsSaleBatchAddPo.setShopUnique(Long.valueOf(String.valueOf(map.get("shopUnique"))));
									goodsSaleBatchAddPo.setBatchUnique(goodBatchQueryResult.getBatchUnique());
									goodsSaleBatchAddPo.setGoodsBarcode(String.valueOf(map.get("sourceGoodsBarcode")));
									goodsSaleBatchAddPo.setStockListUnique(listUnique);
									goodsSaleBatchAddPo.setGoodsInPrice(goodBatchQueryResult.getGoodsInPrice().multiply(new BigDecimal(String.valueOf(map.get("goodsContain")))));
									goodsSaleBatchAddPo.setGoodsOutPrice(new BigDecimal(String.valueOf(map.get("stockPrice"))));
									goodsSaleBatchAddPo.setGoodsOutCount(goodBatchListQueryDto.getOutStockCount().divide(new BigDecimal(String.valueOf(map.get("goodsContain"))), 2, RoundingMode.HALF_UP));
									goodsSaleBatchAddPo.setCreateId(Long.valueOf(goodsOperParam.getUserId()));
									goodsSaleBatchAddPo.setCreateTime(DateUtil.date());
									goodsSaleBatchAddPo.setUpdateId(Long.valueOf(goodsOperParam.getUserId()));
									goodsSaleBatchAddPo.setUpdateTime(DateUtil.date());
									goodsSaleBatchAddPoList.add(goodsSaleBatchAddPo);
									goodsAvgOutPrice = goodsAvgOutPrice.add(goodBatchQueryResult.getGoodsInPrice().multiply(goodBatchListQueryDto.getOutStockCount()));
									chooseGoodsCount = chooseGoodsCount.add(goodBatchListQueryDto.getOutStockCount());
								}
							}
							/*if (chooseGoodsCount.compareTo(goodsCount) < 0) {
								goodsCount = goodsCount.subtract(chooseGoodsCount);
								GoodBatchListQueryParams goodBatchListQueryParams = new GoodBatchListQueryParams();
								goodBatchListQueryParams.setShopUnique(String.valueOf(map.get("shopUnique")));
								goodBatchListQueryParams.setGoodsBarcode(String.valueOf(map.get("goodsBarcode")));
								goodBatchListQueryList = goodsBatchDao.queryGoodsBatchInList(goodBatchListQueryParams);
								if (ObjectUtil.isNotEmpty(goodBatchListQueryList)) {
									for (GoodBatchListQueryDto goodBatchListQueryDto :
											goodBatchListQueryList) {
										if (ObjectUtil.isNotEmpty(goodsBatchUpdatePoList)) {
											for (GoodsBatchUpdatePo goodsBatchUpdatePo : goodsBatchUpdatePoList) {
												if (ObjectUtil.equals(goodsBatchUpdatePo.getBatchUnique(), goodBatchListQueryDto.getBatchUnique())) {
													goodBatchListQueryDto.setGoodsCount(goodsBatchUpdatePo.getGoodsCount());
													break;
												}
											}
										}
										if (goodBatchListQueryDto.getGoodsCount().compareTo(BigDecimal.ZERO) > 0 && goodBatchListQueryDto.getGoodsCount().compareTo(new BigDecimal(String.valueOf(map.get("goodsContain")))) >= 0) {
											GoodsBatchUpdatePo goodsBatchUpdatePo = new GoodsBatchUpdatePo();
											BeanUtil.copyProperties(goodBatchListQueryDto, goodsBatchUpdatePo);
											goodsBatchUpdatePo.setShopUnique(Long.valueOf(String.valueOf(map.get("shopUnique"))));
											goodsBatchUpdatePo.setGoodsBarcode(String.valueOf(map.get("goodsBarcode")));
											goodsBatchUpdatePo.setUpdateId(Long.valueOf(goodsOperParam.getUserId()));
											if (goodsCount.compareTo(goodBatchListQueryDto.getGoodsCount()) > 0) {
												if (new BigDecimal(String.valueOf(map.get("goodsContain"))).compareTo(BigDecimal.ONE) == 0) {
													goodsCount = goodsCount.subtract(goodBatchListQueryDto.getGoodsCount());
													goodsBatchUpdatePo.setGoodsCount(BigDecimal.ZERO);
												} else {
													goodsCount = goodsCount.subtract(BigDecimal.valueOf(goodBatchListQueryDto.getGoodsCount()
																	.divide(new BigDecimal(String.valueOf(map.get("goodsContain"))), 2, RoundingMode.HALF_UP).intValue())
															.multiply(new BigDecimal(String.valueOf(map.get("goodsContain")))));
													goodsBatchUpdatePo.setGoodsCount(goodBatchListQueryDto.getGoodsCount().subtract(
															BigDecimal.valueOf(goodBatchListQueryDto.getGoodsCount()
																			.divide(new BigDecimal(String.valueOf(map.get("goodsContain"))), 2, RoundingMode.HALF_UP).intValue())
																	.multiply(new BigDecimal(String.valueOf(map.get("goodsContain"))))
													));
												}
												goodsBatchUpdatePoList.add(goodsBatchUpdatePo);

												boolean saleBatchFlag = false;
												if (ObjectUtil.isNotEmpty(goodsSaleBatchAddPoList)) {
													for (GoodsSaleBatchAddPo goodsSaleBatchAddPo : goodsSaleBatchAddPoList) {
														if (ObjectUtil.equals(goodsSaleBatchAddPo.getBatchUnique(), goodBatchListQueryDto.getBatchUnique())) {
															goodsSaleBatchAddPo.setGoodsOutCount(goodsSaleBatchAddPo.getGoodsOutCount().add(BigDecimal.valueOf(goodBatchListQueryDto.getGoodsCount()
																	.divide(new BigDecimal(String.valueOf(map.get("goodsContain"))), 2, RoundingMode.HALF_UP).intValue())));
															saleBatchFlag = true;
														}
													}
												}
												if (!saleBatchFlag) {
													GoodsSaleBatchAddPo goodsSaleBatchAddPo = new GoodsSaleBatchAddPo();
													goodsSaleBatchAddPo.setShopUnique(Long.valueOf(String.valueOf(map.get("shopUnique"))));
													goodsSaleBatchAddPo.setBatchUnique(goodBatchListQueryDto.getBatchUnique());
													goodsSaleBatchAddPo.setGoodsBarcode(String.valueOf(map.get("sourceGoodsBarcode")));
													goodsSaleBatchAddPo.setStockListUnique(listUnique);
													goodsSaleBatchAddPo.setGoodsInPrice(goodBatchListQueryDto.getGoodsInPrice().multiply(new BigDecimal(String.valueOf(map.get("goodsContain")))));
													goodsSaleBatchAddPo.setGoodsOutPrice(new BigDecimal(String.valueOf(map.get("stockPrice"))));
													goodsSaleBatchAddPo.setGoodsOutCount(BigDecimal.valueOf(goodBatchListQueryDto.getGoodsCount()
															.divide(new BigDecimal(String.valueOf(map.get("goodsContain"))), 2, RoundingMode.HALF_UP).intValue()));
													goodsSaleBatchAddPo.setCreateId(Long.valueOf(goodsOperParam.getUserId()));
													goodsSaleBatchAddPo.setCreateTime(DateUtil.date());
													goodsSaleBatchAddPo.setUpdateId(Long.valueOf(goodsOperParam.getUserId()));
													goodsSaleBatchAddPo.setUpdateTime(DateUtil.date());
													goodsSaleBatchAddPoList.add(goodsSaleBatchAddPo);

												}

												goodsAvgOutPrice = goodsAvgOutPrice.add(goodBatchListQueryDto.getGoodsInPrice().multiply(goodBatchListQueryDto.getGoodsCount()));
											} else {
												goodsBatchUpdatePo.setGoodsCount(goodBatchListQueryDto.getGoodsCount().subtract(goodsCount));
												goodsBatchUpdatePoList.add(goodsBatchUpdatePo);
												boolean saleBatchFlag = false;
												if (ObjectUtil.isNotEmpty(goodsSaleBatchAddPoList)) {
													for (GoodsSaleBatchAddPo goodsSaleBatchAddPo : goodsSaleBatchAddPoList) {
														if (ObjectUtil.equals(goodsSaleBatchAddPo.getBatchUnique(), goodBatchListQueryDto.getBatchUnique())) {
															goodsSaleBatchAddPo.setGoodsOutCount(goodsSaleBatchAddPo.getGoodsOutCount().add(goodsCount.divide(new BigDecimal(String.valueOf(map.get("goodsContain"))), 2, RoundingMode.HALF_UP)));
															saleBatchFlag = true;
														}
													}
												}
												if (!saleBatchFlag) {
													GoodsSaleBatchAddPo goodsSaleBatchAddPo = new GoodsSaleBatchAddPo();
													goodsSaleBatchAddPo.setShopUnique(Long.valueOf(String.valueOf(map.get("shopUnique"))));
													goodsSaleBatchAddPo.setBatchUnique(goodBatchListQueryDto.getBatchUnique());
													goodsSaleBatchAddPo.setGoodsBarcode(String.valueOf(map.get("sourceGoodsBarcode")));
													goodsSaleBatchAddPo.setStockListUnique(listUnique);
													goodsSaleBatchAddPo.setGoodsInPrice(goodBatchListQueryDto.getGoodsInPrice().multiply(new BigDecimal(String.valueOf(map.get("goodsContain")))));
													goodsSaleBatchAddPo.setGoodsOutPrice(new BigDecimal(String.valueOf(map.get("stockPrice"))));
													goodsSaleBatchAddPo.setGoodsOutCount(goodsCount.divide(new BigDecimal(String.valueOf(map.get("goodsContain"))), 2, RoundingMode.HALF_UP));
													goodsSaleBatchAddPo.setCreateId(Long.valueOf(goodsOperParam.getUserId()));
													goodsSaleBatchAddPo.setCreateTime(DateUtil.date());
													goodsSaleBatchAddPo.setUpdateId(Long.valueOf(goodsOperParam.getUserId()));
													goodsSaleBatchAddPo.setUpdateTime(DateUtil.date());
													goodsSaleBatchAddPoList.add(goodsSaleBatchAddPo);
												}
												goodsAvgOutPrice = goodsAvgOutPrice.add(goodBatchListQueryDto.getGoodsInPrice().multiply(goodsCount));
												break;
											}
										}
									}
								}
							}*/
							goodsAvgOutPrice = goodsAvgOutPrice.divide(new BigDecimal(map.get("goodsCount").toString()).divide(new BigDecimal(String.valueOf(map.get("goodsContain"))),2,RoundingMode.HALF_UP), 2, BigDecimal.ROUND_HALF_UP);
//							map.put("goodsAvgOutPrice",goodsAvgOutPrice);
						}
						map.put("goodsAvgOutPrice",0);
					}
				}
				if (ObjectUtil.isNotEmpty(goodsBatchUpdatePoList) && ObjectUtil.equal(IsIoBoundInspectEnum.NO.getCode(), shopsConfig.getIsIoBoundInspect())) {
					goodsBatchDao.updateGoodsBatchList(goodsBatchUpdatePoList);
				}
				if (ObjectUtil.isNotEmpty(goodsBatchAddPoList)) {
					goodsBatchDao.saveGoodsBatchList(goodsBatchAddPoList);
				}
				if (ObjectUtil.isNotEmpty(goodsSaleBatchAddPoList)) {
					goodsSaleBatchDao.saveGoodsSaleBatchList(goodsSaleBatchAddPoList);
				}
			} else {
				map.put("goodsAvgInPrice", new BigDecimal(String.valueOf(map.get("stockPrice"))).divide(new BigDecimal(String.valueOf(map.get("goodsContain"))),2,RoundingMode.HALF_UP));
			}
		}
		stockDao.addIntoStockDetailV2(shopStockDetailAddParams);

		int k=0;
		if (modifyGoodsCount) {
			k =	goodsDao.modifyGoodsCount(map);//修改最小规格商品数量
			if(k==0){
				sr.setStatus(2);
				sr.setMsg("库存修改失败，所添加的商品不存在！");
				return sr;
			}
			//修改其他规格商品库存数量
			List<Map<String,Object>> lists=stockDao.queryGoodsStand(map);
			if(null!=lists&&lists.size()>0){
				map.put("list", lists);
				k=stockDao.modifyGoodsCount(map);
			}
		}

		if (ObjectUtil.isNotNull(map.get("stockPrice"))) {
			map.put("goodsInPrice", map.get("stockPrice"));
			map.put("goodsSalePrice", map.get("stockPrice"));
		}
		map.put("goodsCount",new BigDecimal(String.valueOf(map.get("goodsCount"))).divide(new BigDecimal(String.valueOf(map.get("goodsContain"))),2,RoundingMode.HALF_UP));
		map.put("stockCount",new BigDecimal(String.valueOf(map.get("stockCount"))).divide(new BigDecimal(String.valueOf(map.get("goodsContain"))),2,RoundingMode.HALF_UP));
		if (ObjectUtil.isNotEmpty(maps.get("goodsChengType")) && ObjectUtil.equals(String.valueOf(maps.get("goodsChengType")), "0")) {
			map.put("stockCount", new BigDecimal(String.valueOf(map.get("stockCount"))).setScale(0,RoundingMode.DOWN));
		}
		if (ObjectUtil.equal(IsIoBoundInspectEnum.YES.getCode(), shopsConfig.getIsIoBoundInspect())) {
			map.put("stockCount", 0);
		}

		k=stockDao.newStockRecord(map);//创建库存修改记录
		//修改其他规格的商品库存信息
		sr.setStatus(1);
		if(k==0){
			sr.setMsg("入库成功！添加入库记录失败！");
			return sr;
		}
		//判断原因是否  存在
		if(map.containsKey("reason")&&map.get("reason")!=null)
		{
			stockDao.addIntoStockHandReason(map);
		}

		//添加MQTT消息
		try {
			SendMqttMsg sendMqttMsg = new SendMqttMsg(map.get("shopUnique").toString(), map.get("goodsBarcode").toString());
			sendMqttMsg.start();
		}catch (Exception e) {
			log.error("添加MQTT消息失败",e);
		}


		sr.setMsg("库存修改成功！");



		if (ObjectUtil.isNotEmpty(recordGoods)) {
			sourceGoods.setGoodsId(recordGoods.getGoodsId());
			sourceGoods.setShopUnique(recordGoods.getShopUnique());
			sourceGoods.setGoodsBarcode(recordGoods.getGoodsBarcode());
			sourceGoods.setGoodsCount(recordGoods.getGoodsCount());
			BeanUtil.copyProperties(sourceGoods, resultGoods);
			BigDecimal goodsCount = recordGoods.getGoodsCount();
			if (goodsCount == null) {
				goodsCount = BigDecimal.ZERO;
			}
			BigDecimal newGoodsCount = new BigDecimal(map.get("goodsCount").toString());

			recordGoodsOper.setGoodsId(Long.valueOf(recordGoods.getGoodsId()));
			recordGoodsOper.setGoodsBarcode(recordGoods.getGoodsBarcode());
			recordGoodsOper.setShopUnique(recordGoods.getShopUnique());
			recordGoodsOper.setOperType(OperTypeEnum.OPER_TYPE_UPDATE.getValue());
			recordGoodsOper.setDeviceSource(goodsOperParam.getDeviceSource());
			recordGoodsOper.setDeviceSourceMsg(goodsOperParam.getDevicesourcemsg());
			recordGoodsOper.setUserType(UserTypeEnum.SHOP_STAFF.getValue());

			recordGoodsOper.setUserId(goodsOperParam.getUserId() == null ? null : goodsOperParam.getUserId().toString());
			recordGoodsOper.setUserName(goodsOperParam.getUserName());

			recordGoodsOper.setCreateTtime(DateUtil.formatDateTime(DateUtil.date()));
			recordGoodsOper.setOperSource(OperSourceEnum.CHANGE_STOCK_CHANGE.getValue());
			if (ObjectUtil.equals(StockTypeEnums.STOCK_IN.getStatus(), Integer.parseInt(map.get("stockType").toString()))) {
				goodsCount = goodsCount.add(newGoodsCount);
			} else {
				goodsCount = goodsCount.subtract(newGoodsCount);
			}
			resultGoods.setGoodsCount(goodsCount);
			RecordGoodsOperParams recordGoodsOperParams = new RecordGoodsOperParams();
			recordGoodsOperParams.setSourceGoods(sourceGoods);
			recordGoodsOperParams.setResultGoods(resultGoods);
			recordGoodsOperParams.setRecordGoodsOper(recordGoodsOper);
			try{
				Map<String,Object> headerMap = new HashMap<>();
				headerMap.put("Content-Type", "application/json; charset=" + HttpUtil.DEFAULT_CHARSET);
				String jsonStr = com.alibaba.fastjson.JSONObject.toJSONString(recordGoodsOperParams);
				HttpUtil.doPostStr(HelibaoPayConfig.RECORDGOODSOPER, headerMap, jsonStr);
			} catch (Exception e){
				log.error("调用海利宝接口失败",e);
			}
		}

		return sr;
	}

	/**
	 * 查询店铺的出入库记录
	 * @param map
	 * @return
	 */
	public ShopsResult queryShopStockRecord(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> data=stockDao.queryShopStockRecord(map);
		if(null==data||data.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("没有满足条件的出入库记录");
			return sr;
		}
		Double entryTotal=0.0,outTotal=0.0;
		for(int i=0;i<data.size();i++){
			if(data.get(i).get("stockTypeCode").toString().equals("1")){
				entryTotal=UtilForJAVA.addDouble(entryTotal, Double.parseDouble(data.get(i).get("stockTotal").toString()));
			}else{
				outTotal=UtilForJAVA.addDouble(outTotal, Double.parseDouble(data.get(i).get("stockTotal").toString()));
			}
			if (ObjectUtil.isNotEmpty(data.get(i).get("goodsName"))) {
				data.get(i).put("goodsName", i18nRtUtil.getMessage(String.valueOf(data.get(i).get("goodsName"))));
			}
			if (ObjectUtil.isNotEmpty(data.get(i).get("stockType"))) {
				data.get(i).put("stockType", i18nRtUtil.getMessage(String.valueOf(data.get(i).get("stockType"))));
			}
			if (ObjectUtil.isNotEmpty(data.get(i).get("stockOrigin"))) {
				data.get(i).put("stockOrigin", i18nRtUtil.getMessage(String.valueOf(data.get(i).get("stockOrigin"))));
			}
			if (ObjectUtil.isNotEmpty(data.get(i).get("stockSource"))) {
				data.get(i).put("stockSource", i18nRtUtil.getMessage(String.valueOf(data.get(i).get("stockSource"))));
			}
		}
		Map<String,Object> rm=new HashMap<>();
		rm.put("entryTotal", entryTotal);
		rm.put("outTotal", outTotal);
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		sr.setObject(rm);
		return sr;
	}

	@Override
	public ShopsResult addBatchStockRecord(Long shopUnique, Integer stockType, String supplier_unique, Integer staffId,
										   String list_unique, String goods_stock_list,Integer stockOrigin,String stock_kind) {
		ShopsResult sr=new ShopsResult();
		Map<String, Object> params=new HashMap<>();
		params.put("list_unique", list_unique);
		params.put("shop_unique", shopUnique);
		params.put("audit_status", 0);
		params.put("supplier_unique", supplier_unique);
		if(stock_kind!=null&&!"".equals(stock_kind)){
			params.put("stock_kind", stock_kind);
		}
		int count= stockDao.queryStockListUnique(params);
		if(count>0){
			sr.setStatus(0);
			sr.setMsg("单号已经存在");
			return sr;
		}
		params.put("stockType", stockType);
		params.put("stockOrigin", stockOrigin);
		//保存
		stockDao.addIntoStockDetail(params);

		System.out.println("出入库提交订单商品集合---"+goods_stock_list);
		JSONArray array= JSONArray.fromObject(goods_stock_list);
		Timestamp time=new Timestamp(new Date().getTime());
		for (int i = 0; i < array.size(); i++) {
			JSONObject temp = (JSONObject) array.get(i);
			String goodsBarcode= temp.getString("goodsBarcode");
			double goodsCount= temp.getDouble("goodsCount");
			double stockPrice= temp.getDouble("stockPrice");
			Map<String,Object> map=new HashMap<String, Object>();
			map.put("shopUnique", shopUnique);
			map.put("goodsBarcode", goodsBarcode);
			map.put("goodsCount", goodsCount);
			map.put("stockType", stockType);
			map.put("stockTime", time);
			map.put("stockPrice", stockPrice);
			map.put("stockOrigin", stockOrigin);
			map.put("staffId", staffId);
			map.put("list_unique", list_unique);
			//查询最小规格
			Map<String,Object> maps=stockDao.getBottomGoodsMessage(map);
			if(null!=maps){
				map.putAll(maps);
			}else{
				sr.setStatus(2);
				sr.setMsg("库存修改失败，所添加的商品不存在！");
				return sr;
			}
			int k=stockDao.newStockRecord2(map);//创建库存修改记录
			//修改其他规格的商品库存信息
			sr.setStatus(1);
			if(k==0){
				sr.setMsg("入库成功！添加入库记录失败！");
				return sr;
			}
		}
		sr.setMsg("库存修改成功！");
		return sr;
	}

	@Override
	public ShopsResult queryShopStockRecordList(Map<String, Object> map) {
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> data=stockDao.queryShopStockRecordList(map);
		if(null==data||data.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("没有满足条件的出入库记录");
			return sr;
		}
		for (Map<String, Object> map2 : data) {
			//查询商品图片
			map2.put("shopUnique", map.get("shopUnique"));
			map2.put("goodsMessage", map.get("goodsMessage"));
			List<Map<String, Object>> goods_list= stockDao.queryGoodsImageByFailId(map2);
			map2.put("goods_list", goods_list);
		}

		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}

	@Override
	public ShopsResult queryShopStockRecordDetail(Long shopUnique, String list_unique) {
		ShopsResult sr=new ShopsResult();
		Map<String,Object> params=new HashMap<>();
		params.put("shopUnique", shopUnique);
		params.put("list_unique", list_unique);
		//查询入库供货商详情
		Map<String,Object> data=stockDao.queryShopStockRecordDetail(params);
		List<Map<String, Object>> goods_list= stockDao.queryGoodsImageByFailId(params);
		data.put("goods_list", goods_list);
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}

	@Override
	public ShopsResult queryGoodsStockLast(Long shopUnique, String goods_barcode, String stock_type) {
		ShopsResult sr=new ShopsResult();
		Map<String,Object> params=new HashMap<>();
		params.put("shopUnique", shopUnique);
		params.put("goods_barcode", goods_barcode);
		params.put("stock_type", stock_type);
		//查询入库供货商详情
		Map<String,Object> data=stockDao.queryGoodsStockLast(params);
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}

	@Override
	public ShopsResult addAuditStock(String list_unique, String shop_unique, String audit_status,
									 String stock_type_code) {
		ShopsResult result =new ShopsResult();
		Map<String, Object> params=new HashMap<>();
		params.put("list_unique", list_unique);
		params.put("shop_unique", shop_unique);
		params.put("stockType", Integer.parseInt(stock_type_code));
		params.put("audit_status", audit_status);
		//查询是否已经审核过
		int i= stockDao.querStockAuditStatus(params);
		if(i<=0){
			result.setStatus(1);
			result.setMsg("审核已完成");
			return result;
		}
		if("1".equals(audit_status)){
			List<Map<String,Object>> list=stockDao.queryStockDetail(params);
			for (Map<String, Object> map : list) {
				map.put("stockType", Integer.parseInt(stock_type_code));
				map.put("shopUnique", map.get("shop_unique"));
				map.put("goodsBarcode", map.get("goods_barcode"));
				map.put("goodsCount", map.get("goods_count"));
				//查询最小规格
				Map<String,Object> maps=stockDao.getBottomGoodsMessage(map);
				if(null!=maps){
					map.putAll(maps);
				}else{
					result.setStatus(2);
					result.setMsg("库存修改失败，所添加的商品不存在！");
					return result;
				}
				System.out.println(map);
				int k=stockDao.modifyGoodsCount2(map);//修改最小规格商品数量
				if(k==0){
					result.setStatus(2);
					result.setMsg("库存修改失败，所添加的商品不存在！");
					return result;
				}
				//修改其他规格商品库存数量
				List<Map<String,Object>> lists=stockDao.queryGoodsStand(map);
				if(null!=lists&&lists.size()>0){
					map.put("list", lists);
					k=stockDao.modifyGoodsCount(map);
				}
				map.put("audit_status", audit_status);
				stockDao.updateStock(map);//创建库存修改记录

			}
		}
		stockDao.updateStockStatus(params);
		result.setStatus(1);
		result.setMsg("审核完成");
		return result;
	}

	@Override
	public ShopsResult editIntoStock(String shop_unique, String detailJson, String list_unique, String startTime,
									 String user_id,String supplier_unique,String stock_kind) {
		ShopsResult result=new ShopsResult();
		Map<String,Object> params=new HashMap<String, Object>();
		params.put("list_unique", list_unique);
		params.put("supplier_unique", supplier_unique);
		if(stock_kind!=null&&!"".equals(stock_kind)){
			params.put("stock_kind", stock_kind);
		}
		params.put("shop_unique", shop_unique);
		stockDao.updateStockDetail(params);
		//删除商品
		stockDao.deleteStockGoods(params);
		System.out.println("修改出入库提交订单商品集合---"+detailJson);
		JSONArray array= JSONArray.fromObject(detailJson);
		Date create_time=new Date();
		for (int i = 0; i < array.size(); i++) {
			JSONObject temp = (JSONObject) array.get(i);
			String goods_barcode= temp.getString("goodsBarcode");
			double goods_count= temp.getDouble("goodsCount");
			double stock_price= temp.getDouble("stockPrice");
			Map<String,Object> map=new HashMap<String, Object>();
			map.put("shop_unique", shop_unique);
			map.put("goods_barcode", goods_barcode);
			map.put("goods_count", goods_count);
			map.put("stock_price", stock_price);
			map.put("stock_type", 1);
			map.put("stock_resource", 1);
			map.put("list_unique", list_unique);
			map.put("stock_origin", 3);
			map.put("staff_id", user_id);
			map.put("stock_count", 0);
			map.put("audit_status", 0);
			map.put("stock_time", create_time);
			//查询是否存在
			int zz=stockDao.queryStockGoods(map);
			if(zz>0){
				//修改
				stockDao.updateIntoStock(map);
			}else{
				stockDao.addIntoStock(map);
			}


		}
		result.setStatus(1);
		return result;
	}

	@Override
	public ShopsResult qeryGoodsStockLog(String shop_unique, String goods_barcode, String stock_type) {
		ShopsResult sr=new ShopsResult();
		Map<String,Object> params=new HashMap<>();
		params.put("shop_unique", shop_unique);
		params.put("goods_barcode", goods_barcode);
		params.put("stock_type", stock_type);
		//查询入库供货商详情
		List<Map<String,Object>> data=stockDao.qeryGoodsStockLog(params);
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}

	@Override
	public CommonResult reason(Map<String, Object> map) {
		CommonResult sr=new CommonResult(1,"查询成功！");
		List<StockReason> data=stockDao.reason(map);
		if (ObjectUtil.isNotEmpty(data)) {
			for (StockReason stockReason : data) {
				if (ObjectUtil.isNotEmpty(stockReason.getList())) {
					for (Map<String, Object> reasonMap : stockReason.getList()) {
						if (ObjectUtil.isNotEmpty(reasonMap.get("dictLabel"))) {
							reasonMap.put("dictLabel", i18nRtUtil.getMessage(String.valueOf(reasonMap.get("dictLabel"))));
						}
					}
				}
			}
		}
		sr.setData(data);
		return sr;
	}


	public ShopsResult addGoodsStock(AddGoodsStockDto dto, GoodsOperParam goodsOperParam){
//        //添加入库记录
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("shopUnique",dto.getShopUnique());
		map.put("goodsBarcode", dto.getGoodsBarcode());
		map.put("sourceGoodsBarcode", dto.getGoodsBarcode());
		map.put("goodsCount", dto.getChangeCount());
		map.put("stockType", dto.getStockType().getStatus());
		map.put("stockTime", new Timestamp(new Date().getTime()));
		map.put("stockPrice", dto.getStockPrice());
		map.put("stockOrigin", dto.getStockOrigin().getCode());
		if (ObjectUtil.isNotEmpty(dto.getStockResource())){
			map.put("stockResource", dto.getStockResource().getCode());
		}
		map.put("staffId", dto.getStaffId());
		if (StringUtils.isEmpty(dto.getReason())){
			map.put("reason", dto.getStockOrigin().getDesc()+ " " + dto.getStockResource().getDesc() + " " + dto.getStockType().getDesc());
		}else {
			map.put("reason", dto.getReason());
		}

		if (StringUtils.isEmpty(dto.getOrderNo())){
			map.put("list_unique", System.currentTimeMillis());
		}else {
			map.put("list_unique", dto.getOrderNo());
		}

		return newStockRecord(map, goodsOperParam,dto.getStockType().getStatus());
	}

	private String getLanguage(String key, Map<String, Object> temMap) {
		String result = null;
		try {
			HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
			String language = request.getHeader("language");
			boolean languageFlag = false;
			if (ObjectUtil.isNotEmpty(language)){
				for (LanguageEnum languageEnum : LanguageEnum.values()) {
					if (ObjectUtil.equals(languageEnum.getCode(), language)) {
						languageFlag = true;
						break;
					}
				}
			}
			if (languageFlag) {
				for (Map.Entry<String, Object> entry : temMap.entrySet()) {
					if (ObjectUtil.equals(entry.getKey(), key)) {
						result = entry.getValue().toString();
						return result;
					}
				}
				Object rcObject = rc.getObject("i18nLanguage");
				cn.hutool.json.JSONArray ja = new cn.hutool.json.JSONArray();
				if (ObjectUtil.isNotEmpty(rcObject)) {
					ja = JSONUtil.parseArray(JSONUtil.toJsonStr(rcObject));
				}
				for (int i = 0; i < ja.size(); i++) {
					cn.hutool.json.JSONObject jo = ja.getJSONObject(i);
					if (ObjectUtil.isNotEmpty(jo)) {
						if (ObjectUtil.isNotEmpty(jo.get("keyName")) && String.valueOf(jo.get("keyName")).equals(key)) {
							if (ObjectUtil.isNotEmpty(jo.get(language))) {
								result = String.valueOf(jo.get(language));
								temMap.put(key, result);
								break;
							}
						}
					}
				}
			}
			if (ObjectUtil.isEmpty(result)) {
				result = key;
			}
		} catch (Exception e) {
			log.error("getLanguage error:{}", e);
		}
		return result;
	}
}
