package org.haier.shopUpdate.service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import lombok.extern.slf4j.Slf4j;
import org.haier.shopUpdate.dao.PurDao;
import org.haier.shopUpdate.dao.StockDao;
import org.haier.shopUpdate.dao.SupFunDao;
import org.haier.shopUpdate.entity.*;
import org.haier.shopUpdate.util.BadiDuPushUtil;
import org.haier.shopUpdate.util.NoteResult;
import org.haier.shopUpdate.util.ShopsResult;
import org.haier.shopUpdate.util.ShopsUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
@Slf4j
@Service("purService")
@Transactional
public class PurServiceImpl implements PurService{
	@Resource
	private PurDao purDao;
	@Resource
	private StockDao stockDao;
	@Resource
	private SupFunDao funDao;
	/**
	 * 修改购物车商品数量
	 * @param map
	 * @return
	 */
	public ShopsResult modifyCartGoods(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> cartGoods=purDao.queryPurCartGoods(map);
		String goodsBarcode=map.get("goodsBarcode").toString();
		if(null==cartGoods||cartGoods.isEmpty()){//无购物车
			//创建新的购物车
			Long purListUnique= ShopsUtil.newUnique();
			map.put("purListUnique",purListUnique);
			map.put("purListDate",ShopsUtil.getRightNow());
			map.put("purListStatus", "2");
			//创建购物车
			purDao.createNewShopCart(map);
			//将新的商品添加到购物车详情
			purDao.addNewGoodsToCart(map);
		}else{//有购物车
			boolean flag=true;
			String purListUnique=cartGoods.get(0).get("purListUnique").toString();
			map.put("purListUnique",purListUnique);
			for(int i=0;i<cartGoods.size();i++){
				Object barcode=cartGoods.get(i).get("goodsBarcode");
				if(null==barcode){//购物车无商品
					break;
				}
				if(goodsBarcode.equals(barcode.toString())){//购物车有相同商品
					flag=false;
					//查看修改后的商品数量，若为零则删除，若不为零，则修改
					Double hCount=Double.parseDouble((cartGoods.get(i).get("detailCount").toString()));
					Double detailCount=Double.parseDouble((map.get("detailCount").toString()));
					if((hCount+detailCount)<=0){//若修改后商品数量为零，则删除
						purDao.deleteCartGoods(map);
					}else{//若修改后商品数量不为零，则更新
						map.put("detailCount",hCount+detailCount);
						purDao.modifyCartGoods(map);
					}
					break;
				}
			}
			if(flag){//购物车无此商品
				purDao.addNewGoodsToCart(map);
			}
		}
		sr.setStatus(1);
		sr.setMsg("修改购物车商品数量成功！");
		return sr;
	}

	/**
	 * 修改购物车商品数量
	 * 判断购物车中该商品的赠品相关，
	 * 判断购物车中该订单金额的赠品相关
	 * @param map
	 * @return
	 */
	public ShopsResult modifyCartGoodsNew(Map<String,Object> map,String giftMessage,String goodsGiftMessage){
		if(null==goodsGiftMessage||goodsGiftMessage.equals("")){
			goodsGiftMessage=null;
		}
		if(null==giftMessage||giftMessage.equals("")){
			giftMessage=null;
		}
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> cartGoods=purDao.queryPurCartGoods(map);
		String goodsBarcode=map.get("goodsBarcode").toString();
		if(null==cartGoods||cartGoods.isEmpty()){//无购物车
			//创建新的购物车
			Long purListUnique= ShopsUtil.newUnique();
			map.put("purListUnique",purListUnique);
			map.put("purListDate",ShopsUtil.getRightNow());
			map.put("purListStatus", "2");
			//创建购物车
			purDao.createNewShopCart(map);
			//将新的商品添加到购物车详情
			purDao.addNewGoodsToCart(map);
		}else{//有购物车
			boolean flag=true;
			String purListUnique=cartGoods.get(0).get("purListUnique").toString();
			map.put("purListUnique",purListUnique);
			for(int i=0;i<cartGoods.size();i++){
				Object barcode=cartGoods.get(i).get("goodsBarcode");
				if(null==barcode){//购物车无商品
					break;
				}
				if(goodsBarcode.equals(barcode.toString())){//购物车有相同商品
					flag=false;
					//查看修改后的商品数量，若为零则删除，若不为零，则修改
					Double hCount=Double.parseDouble((cartGoods.get(i).get("detailCount").toString()));
					Double detailCount=Double.parseDouble((map.get("detailCount").toString()));
					if((hCount+detailCount)<=0){//若修改后商品数量为零，则删除
						purDao.deleteCartGoods(map);
					}else{//若修改后商品数量不为零，则更新
						map.put("detailCount",hCount+detailCount);
						purDao.modifyCartGoods(map);
					}
					break;
				}
			}
			if(flag){//购物车无此商品
				purDao.addNewGoodsToCart(map);
			}
		}

		//查询购物车中此商品的供货商是否有满赠，是否有订单整体优惠
		List<Map<String,Object>> goodsGift=funDao.queryGiftCountByGoodsChange(map);
		Boolean gflag=PurServiceImpl.checkHaveGift(goodsGiftMessage, goodsGift);
		if(gflag){
			sr.setRedundant(1);
		}

		//查询订单总金额，判断是否达到满赠条件
		Double cartGoodsCheckedTotal=funDao.queryCartGoodsTotal(map);
		//根据总金额判断是否需要添加或删除赠品信息
		map.put("cartGoodsCheckedTotal",cartGoodsCheckedTotal);
		List<Map<String,Object>> giftGoods=funDao.queryGiftDelete(map);
		boolean flag=PurServiceImpl.checkHaveGift(giftMessage,giftGoods);

		if(flag){//赠品信息相同则不
			sr.setRedundant(1);
		}else{
			sr.setRedundant(2);//不需要刷新
		}
		sr.setStatus(1);
		sr.setMsg("修改购物车商品数量成功！");
		return sr;
	}
	/**
	 * 购物车商品数量查询
	 * @param map
	 * @return
	 */
	public ShopsResult cartGoodsCount(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		Double data=purDao.cartGoodsCount(map);
		if(null==data){
			data=0.0;
		}
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}
	/**
	 * 直接修改商品购物车数量
	 * @param map
	 * @return
	 */
	public ShopsResult directlyModifyCartGoods(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		List<Map<String, Object>> cartGoods=purDao.queryPurCartGoods(map);
		if(null==cartGoods||cartGoods.isEmpty()){
			//创建新的购物车
			Long purListUnique= ShopsUtil.newUnique();
			map.put("purListUnique",purListUnique);
			map.put("purListDate",ShopsUtil.getRightNow());
			map.put("purListStatus", "2");
			//创建购物车
			purDao.createNewShopCart(map);
			//将新的商品添加到购物车详情
			purDao.addNewGoodsToCart(map);
		}else{
			String purListUnique=cartGoods.get(0).get("purListUnique").toString();
			map.put("purListUnique",purListUnique);
			int k=purDao.modifyCartGoods(map);
			if(k!=1){
				purDao.addNewGoodsToCart(map);
			}
		}
		sr.setStatus(1);
		sr.setMsg("修改成功！");
		return sr;
	}

	/**
	 * 直接修改商品购物车数量（新）
	 * @param map
	 * @return
	 */
	public ShopsResult directlyModifyCartGoodsNew(Map<String,Object> map,String giftMessage,String goodsGiftMessage ){
		if(goodsGiftMessage.equals("")){
			goodsGiftMessage=null;
		}
		if(giftMessage.equals("")){
			giftMessage=null;
		}
		ShopsResult sr=new ShopsResult();
		List<Map<String, Object>> cartGoods=purDao.queryPurCartGoods(map);
		if(null==cartGoods||cartGoods.isEmpty()){
			//创建新的购物车
			Long purListUnique= ShopsUtil.newUnique();
			map.put("purListUnique",purListUnique);
			map.put("purListDate",ShopsUtil.getRightNow());
			map.put("purListStatus", "2");
			//创建购物车
			purDao.createNewShopCart(map);
			//将新的商品添加到购物车详情
			purDao.addNewGoodsToCart(map);
		}else{
			String purListUnique=cartGoods.get(0).get("purListUnique").toString();
			map.put("purListUnique",purListUnique);
			int k=purDao.modifyCartGoods(map);
			if(k!=1){
				purDao.addNewGoodsToCart(map);
			}
		}
		//修改商品后，检查该商品是否达到赠送商品的条件
		List<Map<String,Object>> goodsGift=funDao.queryGiftCountByGoodsChange(map);
		Boolean gflag=PurServiceImpl.checkHaveGift(goodsGiftMessage, goodsGift);
		if(gflag){
			sr.setRedundant(1);
		}

		//查询订单总金额，判断是否达到满赠条件
		Double cartGoodsCheckedTotal=funDao.queryCartGoodsTotal(map);
		//根据总金额判断是否需要添加或删除赠品信息
		map.put("cartGoodsCheckedTotal",cartGoodsCheckedTotal);
		List<Map<String,Object>> giftGoods=funDao.queryGiftDelete(map);
		boolean flag=PurServiceImpl.checkHaveGift(giftMessage,giftGoods);
		if(flag){
			sr.setRedundant(1);
		}else{
			sr.setRedundant(2);//不刷新界面
		}
		//若选中状态为1，

		sr.setStatus(1);
		sr.setMsg("修改成功！");
		return sr;
	}


	//统计剩余库存商品
	public ShopsResult countSurplusGoods(Map<String, Object> map) {
			ShopsResult sr=new ShopsResult();
			List<Map<String,Object>> data=purDao.countSurplusGoods(map);
			if(null==data||data.isEmpty()){
				sr.setStatus(0);
				sr.setMsg("没有满足条件的商品信息！");
				sr.setData(ShopsUtil.map);
				return sr;
			}
			sr.setStatus(1);
			sr.setMsg("查询成功！");
			sr.setData(data);
			return sr;
	}
	//批量采购
	public ShopsResult batchModifyCartGoods(Map<String, Object> map) {
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> cartGoods=purDao.queryPurCartGoods(map); //购物车
		String [] goodsBarcodes=((String)map.get("goodsBarcode")).split(",");//条码集合
		String [] detailCounts=((String)map.get("detailCount")).split(",");//数量集合
		String[] supplierUniques=map.get("supplierUnique").toString().split(",");
		if(null==cartGoods||cartGoods.isEmpty()){
			//无购物车创建新的购物车
			Long purListUnique= ShopsUtil.newUnique();
			map.put("purListUnique",purListUnique);
			map.put("purListDate",ShopsUtil.getRightNow());
			map.put("purListStatus", "2");
			purDao.createNewShopCart(map);
			for (int i = 0; i < goodsBarcodes.length; i++) {
				map.put("goodsBarcode", goodsBarcodes[i]);
				map.put("detailCount", detailCounts[i]);
				map.put("supplierUnique",supplierUniques[i]);
				//将新的商品添加到购物车详情
				purDao.addNewGoodsToCart(map);
			}
		}else{//有购物车
			Object barcode=cartGoods.get(0).get("goodsBarcode");
			map.put("purListUnique",cartGoods.get(0).get("purListUnique"));
			if(null==barcode){
				for (int i = 0; i < goodsBarcodes.length; i++) {
					map.put("goodsBarcode", goodsBarcodes[i]);
					map.put("detailCount", detailCounts[i]);
					map.put("supplierUnique",supplierUniques[i]);
					//将新的商品添加到购物车详情
					purDao.addNewGoodsToCart(map);
				}
			}else{
				String purListUnique=cartGoods.get(0).get("purListUnique").toString();
				map.put("purListUnique",purListUnique);
				//所有新商品依次查询
				for(int i=0;i<goodsBarcodes.length;i++){
					map.put("goodsBarcode", goodsBarcodes[i]);
					map.put("supplierUnique", supplierUniques[i]);
					Double detailCount=Double.parseDouble(detailCounts[i].toString());
					boolean dflag=true;
					for(int j=0;j<cartGoods.size();j++){
						if(goodsBarcodes[i].equals(cartGoods.get(j).get("goodsBarcode").toString())){//购物车里有与新商品相同的商品
							dflag=false;
							Double hCount=Double.parseDouble((cartGoods.get(j).get("detailCount").toString()));
							if((hCount+detailCount)<=0){//若修改后商品数量为零，则删除
								purDao.deleteCartGoods(map);
							}else{//若修改后商品数量不为零，则更新
								map.put("detailCount",hCount+detailCount);
								purDao.modifyCartGoods(map);
							}
						}
					}//内层for循环
					if(dflag){
						map.put("detailCount",detailCount);
						purDao.addNewGoodsToCart(map);
					}
				}//外层for循环
			}

		}
		sr.setStatus(1);
		sr.setMsg("添加购物车商品成功！");
		return sr;
	}
	/**
	 * 滞销商品查询
	 */
	public ShopsResult unsalableGoods(Map<String, Object> map) {
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> data=purDao.unsalableGoods(map);
		if(null==data||data.isEmpty()){
			sr.setStatus(0);
			sr.setMsg("没有满足条件的商品信息！");
			sr.setData(ShopsUtil.map);
			return sr;
		}
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}


	public ShopsResult bigKindCountCost(Map<String, Object> map) {
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> data=purDao.bigKindCountCost(map);
		if(null==data||data.isEmpty()){
			sr.setStatus(0);
			sr.setMsg("没有满足条件的商品信息！");
			sr.setData(ShopsUtil.map);
			return sr;
		}
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}


	public ShopsResult smallKindCountCost(Map<String, Object> map) {
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> data=purDao.smallKindCountCost(map);
		if(null==data||data.isEmpty()){
			sr.setStatus(0);
			sr.setMsg("没有满足条件的商品信息！");
			sr.setData(ShopsUtil.map);
			return sr;
		}
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}


	public ShopsResult smallKindCountProportion(Map<String, Object> map) {
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> data=purDao.smallKindCountProportion(map);
		if(null==data||data.isEmpty()){
			sr.setStatus(0);
			sr.setMsg("没有满足条件的商品信息！");
			sr.setData(ShopsUtil.map);
			return sr;
		}
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		//计算库存占比
		Double  sum=0.0;
		for (Map<String, Object> map2 : data) {
			BigDecimal s=(BigDecimal) map2.get("countNum");
			sum+= s.doubleValue();
		}
		for (Map<String, Object> map2 : data) {
			BigDecimal countNum=(BigDecimal) map2.get("countNum");
			BigDecimal cc= BigDecimal.valueOf(countNum.doubleValue()/sum).setScale(2,   BigDecimal.ROUND_HALF_UP);
			int i=(int) (cc.doubleValue()*100);
			map2.put("countNum", i+"%");
			//System.out.println(countNum+"---"+i+"%");
		}
		sr.setData(data);
		return sr;
	}


	public ShopsResult bigKindCountProportion(Map<String, Object> map) {
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> data=purDao.bigKindCountProportion(map);
		if(null==data||data.isEmpty()){
			sr.setStatus(0);
			sr.setMsg("没有满足条件的商品信息！");
			sr.setData(ShopsUtil.map);
			return sr;
		}
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		//计算库存占比
		Double  sum=0.0;
		for (Map<String, Object> map2 : data) {
			BigDecimal s=(BigDecimal) map2.get("countNum");
			sum+= s.doubleValue();
		}
		for (Map<String, Object> map2 : data) {
			BigDecimal countNum=(BigDecimal) map2.get("countNum");
			BigDecimal cc= BigDecimal.valueOf(countNum.doubleValue()/sum).setScale(2,   BigDecimal.ROUND_HALF_UP);
			int i=(int) (cc.doubleValue()*100);
			map2.put("countNum", i+"%");
			//System.out.println(countNum+"---"+i+"%");
		}
		sr.setData(data);
		return sr;
	}

	/**
	 * 购物车商品信息查询
	 * @param map
	 * @return
	 */
	public ShopsResult purCartGoodsSearch(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		List<PurCartGoods> data=purDao.purCartGoodsSearch(map);
		List<PurCartGoods> ndata=new ArrayList<PurCartGoods>();
		if(null==data||data.isEmpty()||null==data.get(0).getListDetail()||data.get(0).getSupplierUnique()==null){
			sr.setStatus(2);
			sr.setMsg("没有满足条件的商品信息！");
			return sr;
		}
		for(int i=0;i<data.size();i++){
			PurCartGoods pcg=data.get(i);
			Double sum=0.0;
			double total=0.0;
			for(int j=0;j<pcg.getListDetail().size();j++){
				sum=ShopsUtil.addDoubleSum(sum, pcg.getListDetail().get(j).getGoodsCount());
				total=ShopsUtil.addDoubleSum(total, ShopsUtil.multiplicationDouble(pcg.getListDetail().get(j).getGoodsPrice(),pcg.getListDetail().get(j).getGoodsCount()));
			}
			pcg.setPurListSum(sum);
			pcg.setPurListTotal(total);

			ndata.add(pcg);
		}
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(ndata);
		return sr;
	}
	/**
	 * 购物车商品信息查询（新版，根据供货商满赠信息，自动添加满赠商品，并返回信息；
	 * @param map
	 * @return
	 */
	public ShopsResult purCartGoodsSearchNew(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		sr.setRedundant(2);
		//查询供货商满赠优先级设置信息；
		List<Map<String,Object>> priorities= funDao.queryGiftPriority(map);
		//删除购物车中已有的赠品，防止赠品信息失效；
		int k=funDao.deleteCartPresent(map);
		k=funDao.deleteCartPurPresent(map);

		System.out.println(k);
		for(int i=0;i<priorities.size();i++){
			Map<String,Object> resource=priorities.get(i);
			resource.putAll(map);
			String presentType=resource.get("presentType").toString();
			if(presentType.equals("1")||presentType.equals("3")){//若有商品满赠
				//查询该商店购物车中已选中的商品的赠品；
				//将其添加到购物车中
				List<Map<String,Object>>  gifts=funDao.queryPresentFromSupGoods(resource);
				if(gifts==null||gifts.isEmpty()){
//					continue;
				}else{
					k=funDao.addGiftsToCart(gifts);//添加满赠的商品赠品至赠品列表
				}
			}
			if(presentType.equals("2")||presentType.equals("3")){
				//订单满赠
				//查询购物车中，该供货商相关的所有商品的金额，并依据此金额查询出需要赠品的商品信息
				List<Map<String,Object>> gifts=funDao.selectFullCartGoodsMessage(resource);
				if(gifts.isEmpty()||gifts==null){
				}else{
					k=funDao.addGiftToCartDetail(gifts);
				}
			}
		}


		//处理完成，查询购物车中的商品及赠品
		List<PurListCart> dataR=funDao.purCartGoodsSearchNew(map);
		//将购物车查询结果中无用数据删掉
		List<PurListCart> data=new ArrayList<PurListCart>();
		for(int i=0;i<dataR.size();i++){
			PurListCart p=dataR.get(i);
			if(p.getSupplierName()!=null){
				data.add(p);
			}
		}
		if(null==data||data.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("购物车为空！");
			return sr;
		}
		List<PurListCart> nData=new ArrayList<PurListCart>();

		for(int a=0;a<data.size();a++){
			PurListCart p=data.get(a);
			boolean flag=true;
			//某个供货商的商品列表为p.getListDetail
			for(int b=0;b<p.getListDetail().size();b++){
				if(p.getListDetail().get(b).getSupplierUnique()==null){
					continue;
				}
				if(p.getListDetail().get(b).getChecked().intValue()==2){
					flag=false;
					break;
				}
			}
			PurListCart cart=data.get(a);
			for(int c=0;c<cart.getListDetail().size();c++){
				PurListCartDetail detail=cart.getListDetail().get(c);
				cart.getListDetail().get(c).setDetailLong(detail.getDetail().size());
			}
			if(!flag){
				cart.setCheckaAll(2);
				List<PurListCartDetail> list=new ArrayList<PurListCartDetail>();
				for(int d=0;d<cart.getListDetail().size();d++){
					PurListCartDetail de=cart.getListDetail().get(d);
					de.setCheckAll(2);
					if(de.getSupplierName()!=null&&de.getSupplierUnique()!=null){
						list.add(de);
					}
				}
				cart.setListDetail(list);
			}
			List<PurListCartDetail> list=cart.getListDetail();
			for (int i = 0;list!=null&&i<list.size(); i++) {
					PurListCartDetail purListCartDetail=list.get(i);
					if(purListCartDetail.getSupplierName()==null&&purListCartDetail.getSupplierUnique()==null){
						list.remove(i);
					}else if(purListCartDetail.getGoodsBarcode()==null||"".equals(purListCartDetail.getGoodsBarcode())){
						list.remove(i);
					}
			}

			nData.add(cart);
		}
 		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(nData);
		return sr;
	}
	/**
	 * 供货商对账
	 */
	public PalmResult paySupOrder(Map<String, Object> map) {
		PalmResult result=new PalmResult();
		//查询供货商列表
		List<Map<String,Object>> supList=purDao.getSupList(map);
		//查询供货商订单
	 	String paystatus= (map.get("paystatus")).toString();
	 	if("1".equals(paystatus)){
	 		map.put("orderType", "ASC");
	 	}else if ("2".equals(paystatus)){
	 		map.put("orderType", "DESC");
	 	}
	 	result.setSupData(supList);//设置供货商列表信息
		List<Map<String,Object>> supOrderList=purDao.getSupOrderList(map);
		if(supOrderList!=null&&supOrderList.size()>0){
			Double sum=0.0;
			for (Map<String, Object> map2 : supOrderList) {
				Double purchaseListTotal=((BigDecimal) map2.get("purchaseListTotal")).doubleValue();
				sum+=purchaseListTotal;
			}
			result.setTotals(supOrderList.size());
			result.setHeji(sum);
		}
		//分页查询
		List<Map<String,Object>> supOrderListPage=purDao.getSupOrderListPage(map);
		if(supOrderListPage==null||supOrderListPage.isEmpty()){
			result.setStatus(2);
			result.setMsg("没有满足条件的信息！");
			return result;
		}
		result.setStatus(1);
		result.setData(supOrderListPage);
		return result;
	}

	/**
	 * 采购订单查询
	 */
	public PalmResult queryPurList(String shop_unique, Integer receipt_status, Integer pageNum, Integer pageSize,Long supplierUnique,String purMessage) {
		PalmResult ns=new PalmResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("receipt_status", receipt_status);
		map.put("purchase_list_status", 1);
		map.put("pageSize", pageSize);
		map.put("startNum", (pageNum-1)*pageSize);
		if(0==supplierUnique){
			supplierUnique=null;
		}
		if(null!=purMessage&&!purMessage.equals("")){
			map.put("purMessage", "%"+purMessage+"%");
		}
		map.put("supplierUnique", supplierUnique);
		System.err.println("进货订单查询：：："+map);
		List<PurCartDetail> result=purDao.queryPurList(map);
		if(result.size()==0){
			ns.setStatus(2);
			ns.setMsg("没有满足条件的订单");
			return ns;
		}
		ns.setStatus(1);
		ns.setMsg("查询成功！");
		ns.setData(result);
		return ns;
	}


	/**
	 * 订单确认后，提交订单
	 * @param shop_unique
	 * @param barcodes
	 * @return
	 */
	public NoteResult subCartGoods(HttpServletRequest request,String shop_unique,String[] barcodes,String remark){
		NoteResult ns=new NoteResult();
		Map<String,String[]> parmap=request.getParameterMap();//参数元素集合
//		String[] remarks=parmap.get("remarks[]");
//		String[] barcodes=parmap.get("barcodes[]");
		String[] remarks=remark.split(";");

		String purchase_list_parunique=parmap.get("purchase_list_parunique")[0];//购物车编号
		Map<String,Object> cmap=new HashMap<String, Object>();//主订单更新map
		Map<String,Object> pushMap;//用于订单信息的推送

		cmap.put("purchase_list_unique", purchase_list_parunique);
		cmap.put("purchase_list_status", 1);
//		JSONArray jsonArray=JSONArray.fromObject(remarks);//订单备注集合
		List<Map<String,Object>> purlists=new ArrayList<Map<String,Object>>();//子订单信息集合
		Random random=new Random();//随机函数，用户创建订单编号
		//商品信息查询（包含商品进价，商品售价，商品名称）
		List<String> goodsBarcodes=new ArrayList<String>();//商品条码集合，用于商品信息查询
		for(String bar:barcodes){
			goodsBarcodes.add(bar);
		}
		Map<String,Object> gmap=new HashMap<String, Object>();//商品条码map用于商品信息查询
		gmap.put("goodsBarcodes", goodsBarcodes);
		gmap.put("shop_unique", shop_unique);
		List<Map<String,Object>> goodsList=purDao.queryGoodsMessage(gmap);//购物车中所有商品的信息
		List<Map<String,Object>> ngoodsList=new ArrayList<Map<String,Object>>();//用于更新商品详情
//		System.out.println("获取的JSONARRAY参数为："+jsonArray);
		Double totalTotal=0.00;
		Double totalSum=0.0;

		for(int i=0;i<remarks.length;i++){//此处添加商品订单信息
			Map<String,Object> mp=new HashMap<String, Object>();//mp用来记录新的子订单信息
//			JSONObject object=JSONObject.fromObject(jsonArray.get(i));
//			mp.put("supplier_unique", object.get("supplier_unique"));//供货商编号
			mp.put("supplier_unique", remarks[i].split(":")[0]);
//			mp.put("purchase_list_remark", object.get("remark"));//订单备注
			if(!remarks[i].split(":")[1].equals("]|")){
				try {
				mp.put("purchase_list_remark", new String(remarks[i].split(":")[1].getBytes("ISO-8859-1"),"UTF-8"));
				} catch (Exception e) {
					log.error("订单备注转换异常",e);
				}
			}else{
				mp.put("purchase_list_remark", "");
			}
			Integer num=random.nextInt(89)+10;
			String purchase_list_unique=new Date().getTime()+""+num+i;
			mp.put("purchase_list_unique", purchase_list_unique);//子订单编码
			mp.put("shop_unique", shop_unique);//店铺编号
			//添加订单信息注册
			pushMap=purDao.queryPurPushMessageForSub(mp);
			mp.put("purchase_list_parunique", purchase_list_parunique);//父类编号
			Timestamp purchase_list_date=new Timestamp(new Date().getTime());
			mp.put("purchase_list_date", purchase_list_date);//订单生成时间
			cmap.put("purchase_list_date", purchase_list_date);//主订生成时间
			Double purchase_list_total=0.00;//子订单商品总金额
			Double purchase_list_saleTotal=0.00;//子订单商品销售预计总金额
			Double purchase_list_sum=0.0;//子订单商品总数量
//			System.out.println("新订单的供货商编号为："+object.get("supplier_unique"));
			for(int j=0;j<goodsList.size();j++){
				if(remarks[i].split(":")[0].equals(goodsList.get(j).get("supplier_unique"))){//比较已提交的商品的供货商与订单供货商编号是否相同，若相同，则添加到此订单中
//				if(object.get("supplier_unique").toString().equals(goodsList.get(j).get("supplier_unique"))){

//					purchase_list_total+=Double.parseDouble(goodsList.get(j).get("goods_price").toString());
//					System.out.println(goodsList.get(j));
					Double gprice=Double.parseDouble(goodsList.get(j).get("goods_price").toString());
					Double gcount=Double.parseDouble(goodsList.get(j).get("purchase_list_detail_count").toString());

					purchase_list_total=ShopsUtil.addDoubleSum(purchase_list_total, ShopsUtil.multiplicationDouble(gprice, gcount));//子订单商品的总进货价
//					System.out.println(gprice+"\\\\"+gcount);
//					System.out.println(ShopsUtil.multiplicationDouble(gprice, gcount));
//					System.out.println("总进价为："+purchase_list_total);
//					purchase_list_saleTotal+=Double.parseDouble(goodsList.get(j).get("goods_sale_price").toString());
					purchase_list_saleTotal=ShopsUtil.addDoubleSum(purchase_list_saleTotal, Double.parseDouble(goodsList.get(j).get("goods_sale_price").toString()));//子订单商品的总销售价
//					purchase_list_sum+=Double.parseDouble(goodsList.get(j).get("purchase_list_detail_count").toString());
					purchase_list_sum=ShopsUtil.addDoubleSum(purchase_list_sum, gcount);//子订单商品的总数量
					goodsList.get(j).put("purchase_list_unique", purchase_list_unique);
					goodsList.get(j).put("purchase_list_parunique", purchase_list_parunique);
					ngoodsList.add(goodsList.get(j));//用于更新子订单详情
				}
			}

			mp.put("purchase_list_sum", purchase_list_sum);
			totalSum+=purchase_list_sum;
			totalTotal+=purchase_list_total;
			mp.put("purchase_list_saleTotal", purchase_list_saleTotal);
			mp.put("purchase_list_total", purchase_list_total);
			mp.put("purchase_list_status", 1);
			pushMap.put("supplierMsg", "您有来自"+pushMap.get("shopName")+"价值¥"+purchase_list_total+"一笔新订单");
			pushMap.put("purListUnique",purchase_list_unique);
			//添加信息
			purDao.addNewPurOrderMessage(pushMap);
			purlists.add(mp);
		}
		cmap.put("totalSum", totalSum);
		cmap.put("totalTotal", totalTotal);

		int k=purDao.updateMainPurList(cmap);//更新主订单信息
		purDao.addSubPurLists(purlists);//创建新子订单
//		System.out.println("此处的商品信息将转化为订单信息："+ngoodsList);
		k= purDao.updateCartDetail(ngoodsList);//更新订单详情
//		System.out.println(k+"<<<<<<<<<<<<<<<<<<");
		//更新供货商商品库存信息
		k=purDao.updateSupGoodsCount(ngoodsList);//更新商品库存信息
		System.out.println(k);
//purDao.newSubOrders(purlists);
		String purchase_list_newparunique=new Date().getTime()+"";
		Map<String,Object> nmap=new HashMap<String, Object>();
		nmap.put("purchase_list_parunique", purchase_list_parunique);
		nmap.put("purchase_list_newparunique", purchase_list_newparunique);
		nmap.put("purchase_list_unique", purchase_list_newparunique);
		nmap.put("shop_unique", shop_unique);
		nmap.put("purchase_list_status", 2);
		purDao.newPurchase_list(nmap);//创建新购物车
		purDao.updateToCartDetail(nmap);//将未结算的购物车商品添加到新购物车中
		ns.setStatus(0);
		ns.setMsg("提交成功！");
		return ns;
	}

	/**
	 * 订单确认后，提交订单
	 * @param shop_unique
	 * @param barcodes
	 * @param remarks
	 * @return
	 */
	public NoteResult subCartGoodsNew(HttpServletRequest request,String shop_unique,String[] barcodes,String remark){
		NoteResult ns=new NoteResult();
		Map<String,String[]> parmap=request.getParameterMap();//参数元素集合
//		String[] remarks=parmap.get("remarks[]");
//		String[] barcodes=parmap.get("barcodes[]");
		String[] remarks=remark.split(";");

		String purchase_list_parunique=parmap.get("purchase_list_parunique")[0];//购物车编号
		Map<String,Object> cmap=new HashMap<String, Object>();//主订单更新map
		Map<String,Object> pushMap;//用于订单信息的推送

		cmap.put("purchase_list_unique", purchase_list_parunique);
		cmap.put("purchase_list_status", 1);
//		JSONArray jsonArray=JSONArray.fromObject(remarks);//订单备注集合
		List<Map<String,Object>> purlists=new ArrayList<Map<String,Object>>();//子订单信息集合
		Random random=new Random();//随机函数，用户创建订单编号
		//商品信息查询（包含商品进价，商品售价，商品名称）
		List<String> goodsBarcodes=new ArrayList<String>();//商品条码集合，用于商品信息查询
		for(String bar:barcodes){
			goodsBarcodes.add(bar);
		}
		Map<String,Object> gmap=new HashMap<String, Object>();//商品条码map用于商品信息查询
		gmap.put("goodsBarcodes", goodsBarcodes);
		gmap.put("shop_unique", shop_unique);
		List<Map<String,Object>> goodsList=purDao.queryGoodsMessageNew(gmap);//购物车中所有商品的信息（新版，促销商品采购时采用赠品的商品价格信息）
		List<Map<String,Object>> ngoodsList=new ArrayList<Map<String,Object>>();//用于更新商品详情
//		System.out.println("获取的JSONARRAY参数为："+jsonArray);
		Double totalTotal=0.00;
		Double totalSum=0.0;

		for(int i=0;i<remarks.length;i++){//此处添加商品订单信息
			Map<String,Object> mp=new HashMap<String, Object>();//mp用来记录新的子订单信息
//			JSONObject object=JSONObject.fromObject(jsonArray.get(i));
//			mp.put("supplier_unique", object.get("supplier_unique"));//供货商编号
			mp.put("supplier_unique", remarks[i].split(":")[0]);
//			mp.put("purchase_list_remark", object.get("remark"));//订单备注
			if(!remarks[i].split(":")[1].equals("]|")){
				try {
				mp.put("purchase_list_remark", new String(remarks[i].split(":")[1].getBytes("ISO-8859-1"),"UTF-8"));
				} catch (Exception e) {
					log.error("订单备注编码转换异常",e);
				}
			}else{
				mp.put("purchase_list_remark", "");
			}
			Integer num=random.nextInt(89)+10;
			String purchase_list_unique=new Date().getTime()+""+num+i;
			mp.put("purchase_list_unique", purchase_list_unique);//子订单编码
			mp.put("shop_unique", shop_unique);//店铺编号
			//添加订单信息注册
			pushMap=purDao.queryPurPushMessageForSub(mp);
			mp.put("purchase_list_parunique", purchase_list_parunique);//父类编号
			Timestamp purchase_list_date=new Timestamp(new Date().getTime());
			mp.put("purchase_list_date", purchase_list_date);//订单生成时间
			cmap.put("purchase_list_date", purchase_list_date);//主订生成时间
			Double purchase_list_total=0.00;//子订单商品总金额
			Double purchase_list_saleTotal=0.00;//子订单商品销售预计总金额
			Double purchase_list_sum=0.0;//子订单商品总数量
//			System.out.println("新订单的供货商编号为："+object.get("supplier_unique"));
			for(int j=0;j<goodsList.size();j++){
				if(remarks[i].split(":")[0].equals(goodsList.get(j).get("supplier_unique"))){//比较已提交的商品的供货商与订单供货商编号是否相同，若相同，则添加到此订单中
//				if(object.get("supplier_unique").toString().equals(goodsList.get(j).get("supplier_unique"))){

//					purchase_list_total+=Double.parseDouble(goodsList.get(j).get("goods_price").toString());
//					System.out.println(goodsList.get(j));
					Double gprice=Double.parseDouble(goodsList.get(j).get("goods_price").toString());
					Double gcount=Double.parseDouble(goodsList.get(j).get("purchase_list_detail_count").toString());
					purchase_list_total=ShopsUtil.addDoubleSum(purchase_list_total, ShopsUtil.multiplicationDouble(gprice, gcount));//子订单商品的总进货价
//					System.out.println(gprice+"\\\\"+gcount);
//					System.out.println(ShopsUtil.multiplicationDouble(gprice, gcount));
//					System.out.println("总进价为："+purchase_list_total);
//					purchase_list_saleTotal+=Double.parseDouble(goodsList.get(j).get("goods_sale_price").toString());

					purchase_list_saleTotal=ShopsUtil.addDoubleSum(purchase_list_saleTotal, Double.parseDouble(goodsList.get(j).get("goods_sale_price").toString()));//子订单商品的总销售价
//					purchase_list_sum+=Double.parseDouble(goodsList.get(j).get("purchase_list_detail_count").toString());
					purchase_list_sum=ShopsUtil.addDoubleSum(purchase_list_sum, gcount);//子订单商品的总数量
					goodsList.get(j).put("purchase_list_unique", purchase_list_unique);
					goodsList.get(j).put("purchase_list_parunique", purchase_list_parunique);
					ngoodsList.add(goodsList.get(j));//用于更新子订单详情
				}
			}

			mp.put("purchase_list_sum", purchase_list_sum);
			totalSum+=purchase_list_sum;
			totalTotal+=purchase_list_total;
			mp.put("purchase_list_saleTotal", purchase_list_saleTotal);
			mp.put("purchase_list_total", purchase_list_total);
			mp.put("purchase_list_status", 1);
			pushMap.put("supplierMsg", "您有来自"+pushMap.get("shopName")+"价值¥"+purchase_list_total+"一笔新订单");
			pushMap.put("purListUnique",purchase_list_unique);
			//添加信息
			purDao.addNewPurOrderMessage(pushMap);
			purlists.add(mp);
		}
		cmap.put("totalSum", totalSum);
		cmap.put("totalTotal", totalTotal);

		int k=purDao.updateMainPurList(cmap);//更新主订单信息
		System.out.println(cmap);
		System.out.println("注定的已更新！！！"+k);
		purDao.addSubPurLists(purlists);//创建新子订单
//		System.out.println("此处的商品信息将转化为订单信息："+ngoodsList);
		k= purDao.updateCartDetail(ngoodsList);//更新订单详情
		k=purDao.updateCartDetailNew(ngoodsList);//更新订单详情，赠品
		k=purDao.updateCartGiftDetail(ngoodsList);//更新赠品
		System.out.println(k+"<<<<<<<<<<<<<<<<<<");
		//更新供货商商品库存信息
		k=purDao.updateSupGoodsCount(ngoodsList);//更新商品库存信息
		System.out.println(k);
//purDao.newSubOrders(purlists);
		String purchase_list_newparunique=new Date().getTime()+"";
		Map<String,Object> nmap=new HashMap<String, Object>();
		nmap.put("purchase_list_parunique", purchase_list_parunique);
		nmap.put("purchase_list_newparunique", purchase_list_newparunique);
		nmap.put("purchase_list_unique", purchase_list_newparunique);
		nmap.put("shop_unique", shop_unique);
		nmap.put("purchase_list_status", 2);
		purDao.newPurchase_list(nmap);//创建新购物车
		purDao.updateToCartDetail(nmap);//将未结算的购物车商品添加到新购物车中
		ns.setStatus(0);
		ns.setMsg("提交成功！");
		return ns;
	}

	//批量确认支付接口
	public PalmResult payOrders(String purchaseListUniques) {
		//修改支付状态为3:商家已付款,供货商未确认
		PalmResult result=new PalmResult();
		result.setStatus(1);
		String[] purchaseListUniquess= purchaseListUniques.split(",");
		Map<String,Object> map=new HashMap<String, Object>();
		for (String purchaseListUnique : purchaseListUniquess) {
			map.put("purListUnique", purchaseListUnique);
			Map<String,Object> pushMap=purDao.queryPurPushMessage(map);
			if(null==pushMap){
				continue;
			}
			String message="[已支付]商家："+pushMap.get("shopName")+pushMap.get("purListTotal")+"元订单已支付";
//			System.out.println("查看支付状态"+map);
			if(purDao.updatePayStatus(map)<=0){
				result.setStatus(2);
			}
			//给供货商推送消息
			String deviceType=pushMap.get("cusSource").toString();
			String pushMessage="";
			deviceType=(Integer.parseInt(deviceType)-2)+"";
			if("1".equals(deviceType)||"3".equals(deviceType)){
				pushMessage = "{\"title\":\""+ message + "\","+
						"\"description\": \"" + message + "\"," + // 必选
						"\"notification_builder_id\": 0," + // 可选
						"\"notification_basic_style\": 7," + // 可选// 4响铃，2，震动，1，可清除；可累加；7=1+2+4
						"\"open_type\":2," + // 可选 //1：打开网址；2：打开应用
						"\"url\": \"http://developer.baidu.com\"," + // 可选，当open_type==1时起作用；
						"\"pkg_content\":\"\"," + // 可选 ，当open_type==2时起作用
						"\"custom_content\":{\"sale_list_unique\":\"" + pushMap.get("purListUnique") + "\"}" + // 用户自定义信息
						"}";

			}else if("2".equals(deviceType)||"4".equals(deviceType)){
				pushMessage = "{\"aps\": {\"alert\":\"" + message
						+ "\",\"sound\":\"default\", \"badge\":1,\"content-available\":1},\"sale_list_unique\":\"" + purchaseListUnique
						+ "\",\"message\":\"查看订单详情\"}";
			}
			pushMap.put("pushMessage", pushMessage);
			pushMap.put("appType", 3);
			pushMap.put("cusSource",Integer.parseInt(pushMap.get("cusSource").toString())-2);
			BadiDuPushUtil.pushMessageToBuy(pushMap);
		}
		return result;
	}

	/**
	 * 采购订单详情查询
	 * @param map
	 * @return
	 */
	public ShopsResult queryPurListDetail(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		if(null == map|| map.get("purListUnique") == null || map.get("purListUnique").toString().equals("")) {
			sr.setStatus(0);
			sr.setMsg("请输入订单号");
			return sr;
		}
		PurListDetailUpdate data=purDao.queryPurListDetail(map);
		if(null==data){
			sr.setStatus(2);
			sr.setMsg("没有该订单的相关信息");
			return sr;
		}
		sr.setData(data);
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		return sr;
	}


	/**
	 * @param map
	 * @return
	 */
	public ShopsResult queryPurListDetailNew(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		PurListDetailUpdate data=purDao.queryPurListDetailNew(map);
		if(null==data){
			sr.setStatus(2);
			sr.setMsg("没有该订单的相关信息");
			return sr;
		}
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}

	/**
	 * 更新订单处理状态，确认收货
	 * @param shopUnique
	 * @param purListUnique
	 * @param sameType
	 * @return
	 */
	public ShopsResult updateReceiptStatus(String shopUnique,String purListUnique,Integer sameType,Integer receiptStatus){
		ShopsResult sr=new ShopsResult();
		Map<String,Object> map=new HashMap<String, Object>();
		List<Map<String,Object>> stockList=new ArrayList<Map<String,Object>>();
		map.put("shopUnique", shopUnique);
		map.put("sameType", sameType);
		map.put("purListUnique", purListUnique);
		map.put("receiptStatus",receiptStatus);
		map.put("receiptDatetime", new Timestamp( new Date().getTime()));
		//查询该物流下是否还有其他订单，若有，查询是否均已完成配送，若均已完成配送，则更新配送状态
		Map<String,Object> mp=purDao.getPurListStatusSameLogistics(map);
		if(Integer.parseInt(mp.get("count").toString())<=1){
			purDao.completeLogistics(mp);//若所有订单信息都已完成，则更新物流订单
		}
		int k=purDao.updateReceiptStatus(map);//确认收货，修改订单状态
		if(k==0){
			sr.setStatus(2);
			sr.setMsg("确认收货失败，请检查订单编号或店铺编号是否正确！");
			return sr;
		}

		//添加订单收货时，修改库存数量信息
		List<Map<String,Object>> goodss=purDao.queryBottomGoodsMessage(map);
		for(int a=0;a<goodss.size();a++){
			Map<String,Object> smap=goodss.get(a);
			smap.put("goodsCount",goodss.get(a).get("contain"));
			smap.put("stockResource", 2);
			smap.put("shopUnique",shopUnique);
			smap.put("listUnique",purListUnique);
			smap.put("stockType", 1);
			smap.put("stockCount", goodss.get(a).get("goodsCount"));
			stockList.add(smap);
		}
//		System.out.println("入库商品信息！！！"+stockList);
//		System.out.println("进货订单信息！！！"+stockList);
		if(stockList!=null&&stockList.size()>0){
			k = stockDao.newStockRecords(stockList, null);
		}
//		System.out.println("<<<<<<>>>>>订单入库记录修改"+stockList);
		map.put("list", goodss);
		if(goodss!=null&&goodss.size()>0){
			k=purDao.sureGoodsReceipt(map);
		}

		Map<String,Object> pushMap=purDao.queryPurPushMessage(map);
		String message="[已收货]商家："+pushMap.get("shopName")+pushMap.get("purListTotal")+"元订单已确认收货";
		String deviceType=pushMap.get("cusSource").toString();
		String pushMessage="";
		if("1".equals(deviceType)){
			pushMessage = "{\"title\":\""+ message + "\","+
					"\"description\": \"" + message + "\"," + // 必选
					"\"notification_builder_id\": 0," + // 可选
					"\"notification_basic_style\": 7," + // 可选// 4响铃，2，震动，1，可清除；可累加；7=1+2+4
					"\"open_type\":2," + // 可选 //1：打开网址；2：打开应用
					"\"url\": \"http://developer.baidu.com\"," + // 可选，当open_type==1时起作用；
					"\"pkg_content\":\"\"," + // 可选 ，当open_type==2时起作用
					"\"custom_content\":{\"sale_list_unique\":\"" + pushMap.get("purListUnique") + "\"}" + // 用户自定义信息
					"}";

		}else if("2".equals(deviceType)){
			pushMessage = "{\"aps\": {\"alert\":\"" + message
					+ "\",\"sound\":\"default\", \"badge\":1,\"content-available\":1},\"sale_list_unique\":\"" + purListUnique
					+ "\",\"message\":\"查看订单详情\"}";
		}
		pushMap.put("pushMessage", pushMessage);
		pushMap.put("appType", 3);
		BadiDuPushUtil.pushMessageToBuy(pushMap);
		sr.setStatus(1);
		sr.setMsg("收货成功！");
		return sr;
	}

	/**
	 * 采购订单个状态数量查询
	 * @param map
	 * @return
	 */
	public ShopsResult shopPurListCount(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> data=purDao.shopPurListCount(map);
		if(null==data||data.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("没有该店相关的订单信息！");
			return sr;
		}
		sr.setData(data);
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		return sr;
	}


	/**
	 * 删除购物车商品（购物车商品编辑）
	 * @param map
	 * @return
	 */
	public ShopsResult purGoodsDelete(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		int k=purDao.purGoodsDelete(map);
		if(k==0){
			sr.setStatus(2);
			sr.setMsg("删除失败！请验证购物车中是否存在所删除商品！");
			return sr;
		}
		sr.setStatus(1);
		sr.setMsg("删除成功！");
		return sr;
	}

	/**
	 * 结算
	 * @param shop_unique
	 * @param goodsBarcodes
	 * @param purchase_list_parunique
	 * @return
	 */
	public ShopsResult toSubmitCart(Long purListUnique,String[] goodsBarcodes){
		ShopsResult sr=new ShopsResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("purListUnique", purListUnique);
		map.put("goodsBarcodes", goodsBarcodes);
		List<PurCartGoods> data=purDao.purCartGoodsSearch(map);
		List<PurCartGoods> dats=new ArrayList<PurCartGoods>();
		Double totalTotal=0.0;
		Double totalSum=0.0;
		for(int i=0;i<data.size();i++){
			PurCartGoods newCartGoods=data.get(i);
			Double purListTotal=0.0;
			Double purListSum=0.0;
			for(int j=0;j<newCartGoods.getListDetail().size();j++){
				PurCartDetail cartDetail=newCartGoods.getListDetail().get(j);
				purListSum=ShopsUtil.addDoubleSum(purListTotal, cartDetail.getGoodsCount());
				purListTotal=ShopsUtil.addDoubleSum(purListTotal, ShopsUtil.multiplicationDouble(cartDetail.getGoodsCount(), cartDetail.getGoodsPrice()));//累加商品
			}
			newCartGoods.setPurListSum(purListSum);
			newCartGoods.setPurListTotal(purListTotal);

			totalTotal=ShopsUtil.addDoubleSum(totalTotal, purListTotal);
			totalSum=ShopsUtil.addDoubleSum(totalSum, purListSum);
			dats.add(newCartGoods);
		}
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(dats);
		Map<String,Object> redundant=new HashMap<String, Object>();
		redundant.put("totalSum", totalSum);
		redundant.put("totalTotal", totalTotal);
		sr.setRedundant(redundant);
		return sr;
	}

	/**
	 * 结算
	 * @param shop_unique
	 * @param goodsBarcodes
	 * @param purchase_list_parunique
	 * @return
	 */
	public ShopsResult toSubmitCartNew(Long purListUnique,String[] goodsBarcodes){
		ShopsResult sr=new ShopsResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("purListUnique", purListUnique);
		map.put("goodsBarcodes", goodsBarcodes);
		List<PurCartGoods> data=purDao.purCartGoodsSearchNew(map);
		List<PurCartGoods> dats=new ArrayList<PurCartGoods>();
		Double totalTotal=0.0;
		Double totalSum=0.0;
		for(int i=0;i<data.size();i++){
			PurCartGoods newCartGoods=data.get(i);
			Double purListTotal=0.0;
			Double purListSum=0.0;
			for(int j=0;j<newCartGoods.getListDetail().size();j++){
				PurCartDetail cartDetail=newCartGoods.getListDetail().get(j);
				purListSum=ShopsUtil.addDoubleSum(purListTotal, cartDetail.getGoodsCount());
				purListTotal=ShopsUtil.addDoubleSum(purListTotal, ShopsUtil.multiplicationDouble(cartDetail.getGoodsCount(), cartDetail.getGoodsPrice()));//累加商品
			}
			newCartGoods.setPurListSum(purListSum);
			newCartGoods.setPurListTotal(purListTotal);

			totalTotal=ShopsUtil.addDoubleSum(totalTotal, purListTotal);
			totalSum=ShopsUtil.addDoubleSum(totalSum, purListSum);
			dats.add(newCartGoods);
		}
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(dats);
		Map<String,Object> redundant=new HashMap<String, Object>();
		redundant.put("totalSum", totalSum);
		redundant.put("totalTotal", totalTotal);
		sr.setRedundant(redundant);
		return sr;
	}
	/**
	 * 取消订单
	 * @param purListUnique
	 * @param receiptStatus
	 * @return
	 */
	public ShopsResult cancelPurList(Long purListUnique,Integer  receiptStatus){
		ShopsResult sr=new ShopsResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("purListUnique", purListUnique);
		map.put("receiptStatus", receiptStatus);
		map.put("cancelDatetime", new Timestamp(Calendar.getInstance().getTimeInMillis()));//设置当前时间为取消时间
		//取消订单后，将订单商品库存反向增加（1：获取订单商品信息，修改商品信息）
		List<Map<String,Object>> list=purDao.getCancelPurDetail(map);
		int k=0;
		k=purDao.rebackSupStock(list);
		//是否需要推送消息呢？若已接单，则推送消息？
		k=purDao.updateReceiptStatus(map);
		if(0==k){
			sr.setStatus(2);
			sr.setMsg("请检查订单信息是否正确");
			return sr;
		}
		sr.setStatus(1);
		sr.setMsg("取消成功!");
		return sr;
	}

	/**
	 * 商品购物车详情查询测试
	 * @param map
	 * @return
	 */
	public ShopsResult queryBottomGoodsMessage(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> data=purDao.queryBottomGoodsMessage(map);
		if(null==data||data.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("没有满足条件的商品");
			return sr;
		}
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}
	/**
	 * 更新购物车商品选中状态
	 * @param map
	 * @return
	 */
	public ShopsResult modifyCartChecked(Map<String,Object> map,String giftMessage){
		ShopsResult sr=new ShopsResult();
		sr.setRedundant(2);
		//初始化不需要刷新
		boolean flag=false;
		System.out.println("赠品信息修改"+map);
		int m=funDao.modifyCartChecked(map);
		System.out.println(m);
		//带赠品的商品，仅在进入购物车时刷新
		//查询订单总金额，判断是否达到满赠条件
		Double cartGoodsCheckedTotal=funDao.queryCartGoodsTotal(map);
		//根据总金额判断是否需要添加或删除赠品信息
		map.put("cartGoodsCheckedTotal", cartGoodsCheckedTotal);
		List<Map<String,Object>> goodsGift=funDao.queryGiftCountByGoodsChange(map);
		Boolean  goodsGoods=PurServiceImpl.checkHaveGift(giftMessage, goodsGift);
		if(goodsGoods){
			sr.setStatus(1);
		}
		List<Map<String,Object>> giftGoods=funDao.queryGiftDelete(map);
		String[] giftsMessage=null;
		if(null!=giftMessage){
			giftsMessage=giftMessage.split(";");
		}
		if(null==giftsMessage&&giftGoods.isEmpty()){

		}else if((null==giftsMessage&&!giftGoods.isEmpty())||(null!=giftsMessage&&giftGoods.isEmpty())||giftsMessage.length!=giftGoods.size()){//赠品信息不相同
			flag=true;
			sr.setRedundant(1);//需要刷新界面
		}
		if(null!=giftMessage&&!giftGoods.isEmpty()&&!giftMessage.equals("")){//若均不为空，则比较商品信息
			for(int k=0;k<giftsMessage.length;k++){
				boolean nflag=false;
				String barcode=giftsMessage[k].split(":")[0];
				Double count=Double.parseDouble(giftsMessage[k].split(":")[1]);
				int l=0;
				for(;l<giftGoods.size();l++){
					if(barcode.equals(giftGoods.get(l).get("goodsBarcode").toString())){
						nflag=true;
						if(count-Double.parseDouble(giftGoods.get(l).get("goodsCount").toString())!=0){
							flag=true;
						}
						giftGoods.remove(l);//若该元素已被使用，则移除；减少后期的比较次数
						break;
					}
				}
				if(!nflag){
					flag=true;
				}
			}
		}

		if(flag){//需要刷新界面
			sr.setRedundant(1);
		}
		sr.setStatus(1);
		sr.setMsg("更新成功");
		return sr;
	}

	/**
	 * 购物车某供货商商品全选
	 * @param map
	 * @param giftMessage
	 * @param checked
	 * @return
	 */
	public  ShopsResult modifyCartCheckedSup(Map<String,Object> map,String giftMessage,Integer checked){
		ShopsResult sr=new ShopsResult();
		sr.setRedundant(2);
		//初始化不需要刷新
		int m=funDao.modifyCartChecked(map);
		System.out.println(m);//更新商品的数量
		if(checked==1){
			sr.setRedundant(1);
		}else{
			sr.setRedundant(2);
		}
		sr.setStatus(1);
		sr.setMsg("更新成功！");
		return sr;
	}
	/**
	 * 供货商全部商品全选或反选
	 * @param map
	 * @return
	 */
	public ShopsResult modifyCartCheckedAll(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		int m=funDao.modifyCartChecked(map);
		System.out.println(m);
		sr.setRedundant(map.get("checked"));
		sr.setStatus(1);
		sr.setMsg("更新成功！");
		return sr;
	}
	/**
	 * 给出两个List，判断这两个List是否包含相同的内容
	 * @return
	 */
	public boolean checkGift(List<Map<String,Object>> resource,List<Map<String,Object>> gifts){
		//若两个均为空，则无需刷新
		if(null==resource&&gifts==null){
			return false;
		}
		if(resource.size()!=gifts.size()){
			return true;
		}
		for(int i=0;i<resource.size();i++){
			boolean flag=false;//判断是否有相同的商品
			String rbarcode=resource.get(i).get("goodsBarcode").toString();
			Double rcount=Double.parseDouble(resource.get(i).get("count").toString());
			int j=0;
			for(;j<gifts.size();j++){
				String barcode=gifts.get(j).toString();
				Double count=Double.parseDouble(gifts.get(j).get("count").toString());
				if(rbarcode.equals(barcode)){
					flag=true;
					if(count==rcount){
						gifts.remove(j);//若已有该商品信息，则删除已减少后期判定
						continue;
					}
					return true;
				}
			}
			if(!flag){//若没有相同的产品，
				return false;
			}
		}
		return false;
	}

	public static boolean checkHaveGift(String giftMessage,List<Map<String,Object>> gifts){
		if((null==giftMessage||giftMessage.equals(""))&&gifts.isEmpty()){
			return false;
		}
		if((null==giftMessage||giftMessage.equals(""))&&!gifts.isEmpty()){
			return true;
		}
		String[] giftsMessage=giftMessage.split(";");
		if(giftsMessage.length!=gifts.size()){
			return true;
		}
		for(int i=0;i<giftsMessage.length;i++){
			boolean flag=false;
			String goodsBarcode=giftsMessage[i].split(":")[0];
			System.out.println("曾【 想哦明显"+giftsMessage[i]);
			Double goodsCount=Double.parseDouble(giftsMessage[i].split(":")[1]);
			for(int j=0;j<gifts.size();j++){
				System.out.println(gifts.get(j).get("goodsBarcode"));
				String barcode=gifts.get(j).get("goodsBarcode").toString();
				if(goodsBarcode.equals(barcode)){
					flag=true;//有此商品
					Double count=Double.parseDouble(gifts.get(j).get("count").toString());
					if(count!=goodsCount){
						return true;
					}
					break;
				}
			}
			if(!flag){
				return true;
			}
		}
		return false;
	}

	public ShopsResult querySellList(Map<String, Object> map) {
		ShopsResult sr=new ShopsResult();
		List<SaleListTaskEntity> data=purDao.querySellList(map);
		if(data!=null&& !data.isEmpty()){
			sr.setData(data);
			sr.setStatus(1);
			sr.setMsg("查询成功！");
		}else{
			sr.setStatus(0);
			sr.setData(new ArrayList<Map<String,Object>>());
		}
		return sr;
	}

	public void sellListTask() {
		//查询昨天的商品记录
		Calendar cal2=Calendar.getInstance();
		cal2.add(Calendar.DATE,-1);
		Date time=cal2.getTime();
		String yestDay= new SimpleDateFormat("yyyy-MM-dd").format(time);
		Map<String,Object> dateMap=new HashMap<String,Object>();
		dateMap.put("yestDay", yestDay);
		List<Map<String,Object>> data=purDao.sellList(dateMap);

		List<String> orList=new ArrayList<String>();
		for (Map<String, Object> map2 : data) {
			String goods_barcode=(String) map2.get("goods_barcode");
			orList.add(goods_barcode);
		}
		Map<String,Object> map=new HashMap<String,Object>();
		map.put("list", orList);
		//查询前天的商品记录
		Calendar cal3=Calendar.getInstance();
		cal3.add(Calendar.DATE,-2);
		Date time3=cal3.getTime();
		String yestDay3= new SimpleDateFormat("yyyy-MM-dd").format(time3);
		map.put("yestDay", yestDay3);
		List<Map<String,Object>> data2=purDao.sellListBeforeYesterday(map);
		for (Map<String, Object> map2 : data) {
			Double saleCount=((BigDecimal)map2.get("saleCount")).doubleValue();
			String goods_barcode=(String) map2.get("goods_barcode");
			boolean flag=true;
			for (Map<String, Object> map3 : data2) {
				String goods_barcode_yestDay=(String) map3.get("goods_barcode");
				if(goods_barcode.equals(goods_barcode_yestDay)){
					flag=false;
					Double saleCount_yestDay=((BigDecimal)map3.get("saleCount")).doubleValue();
					Double pr=BigDecimal.valueOf(((saleCount-saleCount_yestDay)/saleCount_yestDay)*100).setScale(2,   BigDecimal.ROUND_DOWN).doubleValue();
					map2.put("proportion_no", pr+"%");
				}
			}
			if(flag){
				map2.put("proportion_no", "100%");
			}
		 }
		 //保存查询记录
		for (Map<String, Object> map2 : data) {
			purDao.saveSaleList(map2);
		}
		//查询需要推送的店铺
		List<Map<String,Object>> shops= purDao.queryShopsList();
		//插入一条推送消息
		for (Map<String, Object> map2 : shops) {
			Calendar   cal   =   Calendar.getInstance();
			cal.add(Calendar.DATE,   -1);
			String yesterday = new SimpleDateFormat( "M.dd ").format(cal.getTime());
			String title=yesterday+"日全网产品热销排行top20!";
			map2.put("shop_msg_type", 1);
			map2.put("shop_msg_info", title);
			map2.put("shop_msg_title", title);
			purDao.addNewTitle(map2);
		}
	}

	public void closeEndTimeOrder() {
		purDao.closeEndTimeOrder();
	}
}
