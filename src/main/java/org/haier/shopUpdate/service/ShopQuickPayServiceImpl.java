package org.haier.shopUpdate.service;

import cn.hutool.core.util.ObjectUtil;
import org.haier.shopUpdate.dao.ShopPayConfigDao;
import org.haier.shopUpdate.entity.ShopQuickPayConfig;
import org.haier.shopUpdate.enums.ValidStatus;
import org.haier.shopUpdate.params.shopQuickPay.ConfigListParams;
import org.haier.shopUpdate.util.ShopsResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ShopQuickPayServiceImpl implements ShopQuickPayService{

    @Autowired
    private ShopPayConfigDao shopPayConfigDao;


    /**
     * 查询配置列表
     * @param configListParams
     * @return
     */
    public ShopsResult configList(ConfigListParams configListParams) {
        ShopsResult shopsResult = new ShopsResult(ShopsResult.SUCCESS,"操作成功");

        configListParams.setValidStatus(1);

        List<ShopQuickPayConfig> shopQuickPayConfigList = shopPayConfigDao.configList(configListParams);
        shopsResult.setData(shopQuickPayConfigList);
        shopsResult.setCount(shopPayConfigDao.configCount(configListParams));

        return shopsResult;
    }


    /**
     * 添加配置
     * @param shopQuickPayConfig
     * @return
     */
    public ShopsResult addConfig(ShopQuickPayConfig shopQuickPayConfig) {
        ShopsResult shopsResult = new ShopsResult(ShopsResult.SUCCESS,"操作成功");

        //需要去重，防止添加相同金额
        ConfigListParams configListParams = new ConfigListParams();
        configListParams.setShopUnique(shopQuickPayConfig.getShopUnique());
        configListParams.setValidStatus(1);
        configListParams.setAmountMoney(shopQuickPayConfig.getAmountMoney());
        configListParams.setPage(1);
        configListParams.setPageSize(1000);


        List<ShopQuickPayConfig> shopQuickPayConfigList = shopPayConfigDao.configList(configListParams);
        if (ObjectUtil.isNotEmpty(shopQuickPayConfigList)) {
            shopsResult.setStatus(ShopsResult.FAIL);
            shopsResult.setMsg("添加失败，该金额已存在");
            return shopsResult;
        }

        shopQuickPayConfig.setValidStatus(ValidStatus.EFFECTIVE.getValue());

        if (shopPayConfigDao.addConfig(shopQuickPayConfig) > 0) {
            shopsResult.setStatus(ShopsResult.SUCCESS);
            shopsResult.setMsg("添加成功");
        } else {
            shopsResult.setStatus(ShopsResult.FAIL);
            shopsResult.setMsg("添加失败");
        }
        return shopsResult;
    }

    /**
     * 修改配置
     * @param shopQuickPayConfig
     * @return
     */
    public ShopsResult updateConfig(ShopQuickPayConfig shopQuickPayConfig) {
        ShopsResult shopsResult = new ShopsResult(ShopsResult.SUCCESS,"操作成功");
        //需要防止重复
        ConfigListParams configListParams = new ConfigListParams();
        configListParams.setShopUnique(shopQuickPayConfig.getShopUnique());
        configListParams.setValidStatus(1);
        configListParams.setAmountMoney(shopQuickPayConfig.getAmountMoney());
        configListParams.setPageSize(1000);
        configListParams.setPage(1);

        List<ShopQuickPayConfig> shopQuickPayConfigList = shopPayConfigDao.configList(configListParams);
        if (ObjectUtil.isNotEmpty(shopQuickPayConfigList)) {
            shopsResult.setStatus(ShopsResult.FAIL);
            shopsResult.setMsg("修改失败，该金额已存在");
            return shopsResult;
        }

        if (shopPayConfigDao.updateConfig(shopQuickPayConfig) > 0) {
            shopsResult.setStatus(ShopsResult.SUCCESS);
            shopsResult.setMsg("修改成功");
        } else {
            shopsResult.setStatus(ShopsResult.FAIL);
            shopsResult.setMsg("修改失败");
        }
        return shopsResult;
    }

    public ShopsResult deleteConfig(ShopQuickPayConfig shopQuickPayConfig) {
        ShopsResult shopsResult = new ShopsResult(ShopsResult.SUCCESS,"操作成功");
        shopQuickPayConfig.setValidStatus(ValidStatus.INVALID.getValue());
        if (shopPayConfigDao.updateConfig(shopQuickPayConfig) > 0) {
            shopsResult.setStatus(ShopsResult.SUCCESS);
            shopsResult.setMsg("删除成功");
        } else {
            shopsResult.setStatus(ShopsResult.FAIL);
            shopsResult.setMsg("删除失败");
        }
        return shopsResult;
    }
}
