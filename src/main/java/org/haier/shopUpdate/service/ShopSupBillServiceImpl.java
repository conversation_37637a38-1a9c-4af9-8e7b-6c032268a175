package org.haier.shopUpdate.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.haier.shopUpdate.dao.*;
import org.haier.shopUpdate.entity.*;
import org.haier.shopUpdate.entity.goods.goodsEnum.OperSourceEnum;
import org.haier.shopUpdate.entity.goods.goodsEnum.OperTypeEnum;
import org.haier.shopUpdate.entity.goods.goodsRecord.RecordGoods;
import org.haier.shopUpdate.entity.goods.goodsRecord.RecordGoodsOper;
import org.haier.shopUpdate.entity.goods.goodsRecord.RecordGoodsOperParams;
import org.haier.shopUpdate.enums.IsIoBoundInspectEnum;
import org.haier.shopUpdate.enums.shopStockDetail.StockKindEnum;
import org.haier.shopUpdate.enums.shopStockDetail.StockOriginEnum;
import org.haier.shopUpdate.enums.shopStockDetail.StockResourceEnum;
import org.haier.shopUpdate.enums.shopStockDetail.StockTypeEnum;
import org.haier.shopUpdate.log.enums.BusinessType;
import org.haier.shopUpdate.log.util.SendDingDingTalkUtils;
import org.haier.shopUpdate.params.shopStock.ShopStockAddParams;
import org.haier.shopUpdate.params.shopStockDetail.ShopStockDetailAddParams;
import org.haier.shopUpdate.params.shopSupBill.*;
import org.haier.shopUpdate.params.shopSupBill.externalCall.BillDetailInfoParams;
import org.haier.shopUpdate.params.shopSupBill.externalCall.BillInfoParams;
import org.haier.shopUpdate.params.shopSupBill.externalCall.CancelSupBillParams;
import org.haier.shopUpdate.params.shopSupBill.externalCall.UpdateSupBillStatusParams;
import org.haier.shopUpdate.params.shopSupBill.supExternalCall.BillEvidenceAddParams;
import org.haier.shopUpdate.params.shopSupBill.supExternalCall.ConfirmBillParams;
import org.haier.shopUpdate.params.shopSupBill.supExternalCall.StorageAllGoodsParams;
import org.haier.shopUpdate.redism.RedisCache;
import org.haier.shopUpdate.result.shopSupBill.QueryShopSupBillResult;
import org.haier.shopUpdate.result.shopSupBill.ShopSupBillDetailResult;
import org.haier.shopUpdate.result.shopSupBill.ShopSupBillPaymentResult;
import org.haier.shopUpdate.util.ShopsResult;
import org.haier.shopUpdate.util.helibao.HelibaoPayConfig;
import org.haier.shopUpdate.util.thread.SendMqttMsg;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
@Slf4j
@Service
@Transactional
public class ShopSupBillServiceImpl implements ShopSupBillService {
    @Autowired
    private ShopSupBillDao shopSupBillDao;
    @Autowired
    private ShopSupplierDao shopSupplierDao;
    @Autowired
    private GoodsDao goodsDao;
    @Autowired
    private StockDao stockDao;
    @Resource
    private ShopsConfigDao shopsConfigDao;
    @Autowired
    private RedisCache redis;
    @Resource
    private SendDingDingTalkUtils sendDingDingTalkUtils;
    private final static String URL = ResourceBundle.getBundle("config").getString("url");

    @Override
    public ShopsResult querySupBillList(QueryBillListParams params) {
        ShopsResult result = new ShopsResult();
        if (params.getPageIndex() != null && params.getPageSize() != null) {
            params.setPageIndex((params.getPageIndex() - 1) * params.getPageSize());
        }
        if (params.getGoodsName() != null && !"".equals(params.getGoodsName())) {
            params.setGoodsName("%" + params.getGoodsName() + "%");
        }

        List<QueryShopSupBillResult> list = shopSupBillDao.querySupBillList(params);
        result.setStatus(1);
        result.setMsg("查询成功！");
        result.setData(list);
        return result;
    }

    @Override
    public ShopsResult querySupBillGoodsList(QueryBillGoodsListParams params) {
        ShopsResult result = new ShopsResult();
        ShopSupBillDetailResult detailResult = shopSupBillDao.querySupBillGoodsList(params);
        if (detailResult != null) {
            List<String> evidenceList = shopSupBillDao.queryBillEvidence(String.valueOf(detailResult.getId()));
            detailResult.setVoucherPicturepath(evidenceList);
            result.setStatus(1);
            result.setMsg("查询成功！");
            result.setData(detailResult);
        } else {
            result.setStatus(0);
            result.setMsg("查询失败");
        }
        return result;
    }

    @Override
    @Transactional
    public ShopsResult storageAllGoods(StorageAllGoodsParams params) {
        ShopsResult result = new ShopsResult();
        QueryBillGoodsListParams queryBillGoodsListParams = new QueryBillGoodsListParams();
        BeanUtil.copyProperties(params, queryBillGoodsListParams);
        ShopSupBillEntity entity = shopSupBillDao.queryShopSupBillEntity(queryBillGoodsListParams);//查询出当前购销单所有信息
        if (null == entity) {
            result.setStatus(0);
            result.setMsg("当前购销单不存在");
            return result;
        }
        List<ShopSupBillDetailEntity> detail = entity.getDetail();
        for (int i = 0; null != detail && !detail.isEmpty() && i < detail.size(); i++) {
            ShopSupBillDetailEntity detailEntity = detail.get(i);//购销单明细信息
            if (detailEntity.getCheckStatus() != 1) {
                result.setStatus(0);
                result.setMsg("商品【" + detailEntity.getGoodsName() + "】未核对");
                return result;
            } else {
                StorageGoodsParams storageGoodsParams = new StorageGoodsParams();
                BeanUtil.copyProperties(detailEntity, storageGoodsParams);
                storageGoodsParams.setShopUnique(params.getShopUnique());
                storageGoodsParams.setSupplierUnique(entity.getSupplierUnique());
                storageGoodsParams.setGoodsSalePrice(detailEntity.getGoodsSalePriceStorage());
                storageGoodsParams.setGoodsWebSalePrice(detailEntity.getGoodsWebSalePriceStorage());
                storageGoodsParams.setGoodsCusPrice(detailEntity.getGoodsCusPriceStorage());
                storageGoodsParams.setCreateId(params.getCreateId());
                storageGoodsParams.setCreateBy(params.getCreateBy());
                storageGoodsParams.setId(params.getId());
                storageGoodsParams.setDetailId(detailEntity.getDetailId());
                Map<String, Object> goodsQuery = new HashMap<>();
                goodsQuery.put("shopUnique", params.getShopUnique());
                goodsQuery.put("goodsBarcode", detailEntity.getGoodsBarcode());
                GoodsEntity goodsEntity = goodsDao.queryOneByParam(goodsQuery);
                if (null != goodsEntity) {
                    ShopSupBillDetailEntity shopSupBillDetailEntity = shopSupBillDao.queryShopSupBillDetailEntity(storageGoodsParams);//购销单商品明细信息
                    if (shopSupBillDetailEntity == null) {
                        result.setStatus(0);
                        result.setMsg("未找到购销单明细，入库失败");
                        return result;
                    }
                } else {
                    result.setStatus(0);
                    result.setMsg("商品列表中未找到该商品信息，入库失败");
                    return result;
                }
            }
        }
        BigDecimal totalCount = BigDecimal.ZERO;
        BigDecimal totalAmount = BigDecimal.ZERO;
        String listUnique = StrUtil.concat(true, DateUtil.format(DateUtil.date(), "yyyyMMddHHmmssSSS"), RandomUtil.randomNumbers(3));
        for (int i = 0; null != detail && !detail.isEmpty() && i < detail.size(); i++) {
            ShopSupBillDetailEntity detailEntity = detail.get(i);//购销单明细信息
            StorageGoodsParams storageGoodsParams = new StorageGoodsParams();
            BeanUtil.copyProperties(detailEntity, storageGoodsParams);
            storageGoodsParams.setShopUnique(params.getShopUnique());
            storageGoodsParams.setSupplierUnique(entity.getSupplierUnique());
            storageGoodsParams.setGoodsInPrice(detailEntity.getGoodsPurchasePrice());
            storageGoodsParams.setGoodsSalePrice(detailEntity.getGoodsSalePriceStorage());
            storageGoodsParams.setGoodsWebSalePrice(detailEntity.getGoodsWebSalePriceStorage());
            storageGoodsParams.setGoodsCusPrice(detailEntity.getGoodsCusPriceStorage());
            storageGoodsParams.setCreateId(params.getCreateId());
            storageGoodsParams.setCreateBy(params.getCreateBy());
            storageGoodsParams.setId(params.getId());
            storageGoodsParams.setDetailId(detailEntity.getDetailId());
            storageGoodsParams.setListUnique(listUnique);
            Map<String, Object> goodsQuery = new HashMap<>();
            goodsQuery.put("shopUnique", params.getShopUnique());
            goodsQuery.put("goodsBarcode", detailEntity.getGoodsBarcode());
            GoodsEntity goodsEntity = goodsDao.queryOneByParam(goodsQuery);
            ShopSupBillDetailEntity shopSupBillDetailEntity = shopSupBillDao.queryShopSupBillDetailEntity(storageGoodsParams);//购销单商品明细信息
            Map<Integer, BigDecimal> goodsIdInventoryCountMap = new HashMap<>(1024);
            goodsIdInventoryCountMap.put(goodsEntity.getGoodsId(), NumberUtil.add(storageGoodsParams.getGoodsActualCount(), goodsEntity.getGoodsCount()));
            stockOneRecords(goodsEntity, storageGoodsParams, 1, goodsIdInventoryCountMap);//入库
            addStorageRecord(storageGoodsParams, goodsEntity);//保存购销单入库记录，与商品入库记录区别开
            shopSupBillDetailEntity.setCheckStatus(1);
            shopSupBillDetailEntity.setGoodsActualCount(storageGoodsParams.getGoodsActualCount());
            if (shopSupBillDetailEntity.getGoodsActualCount().compareTo(shopSupBillDetailEntity.getGoodsCount()) < 0) {
                shopSupBillDetailEntity.setStatus(1);
            } else if (shopSupBillDetailEntity.getGoodsActualCount().compareTo(shopSupBillDetailEntity.getGoodsCount()) == 0) {
                shopSupBillDetailEntity.setStatus(0);
            } else if (shopSupBillDetailEntity.getGoodsActualCount().compareTo(shopSupBillDetailEntity.getGoodsCount()) > 0) {
                shopSupBillDetailEntity.setStatus(2);
            }
            shopSupBillDetailEntity.setModifyId(params.getCreateId());
            shopSupBillDetailEntity.setModifyBy(params.getCreateBy());
            shopSupBillDao.updateShopSupBillDetailEntity(shopSupBillDetailEntity);
            totalCount = NumberUtil.add(totalCount, detailEntity.getGoodsCount());
            totalAmount = NumberUtil.add(totalAmount, NumberUtil.mul(detailEntity.getGoodsPurchasePrice(), detailEntity.getGoodsCount()));
        }
        ShopsConfig shopsConfig = shopsConfigDao.selectByShopUnique(String.valueOf(params.getShopUnique()));
        ShopStockDetailAddParams shopStockDetailAddParams = new ShopStockDetailAddParams();
        shopStockDetailAddParams.setShopUnique(String.valueOf(params.getShopUnique()));
        if (ObjectUtil.equal(IsIoBoundInspectEnum.NO.getCode(), shopsConfig.getIsIoBoundInspect())) {
            shopStockDetailAddParams.setAuditStatus(1);
        } else {
            shopStockDetailAddParams.setAuditStatus(0);
        }
        shopStockDetailAddParams.setShopUnique(String.valueOf(params.getShopUnique()));
        shopStockDetailAddParams.setListUnique(listUnique);
        shopStockDetailAddParams.setStockKind(StockKindEnum.INITIALIZED.getCode());
        shopStockDetailAddParams.setStockType(StockTypeEnum.INVENTORY_IN.getCode());
        shopStockDetailAddParams.setStockResource(StockResourceEnum.CLOUD_PURCHASE.getCode());
        shopStockDetailAddParams.setStockOrigin(StockOriginEnum.MOBILE.getCode());
        if (ObjectUtil.isNotEmpty(params.getCreateId())) {
            shopStockDetailAddParams.setStaffId(Long.valueOf(params.getCreateId()));
            shopStockDetailAddParams.setUpdateId(Long.valueOf(params.getCreateId()));
        }
        shopStockDetailAddParams.setUpdateTime(DateUtil.date());
        shopStockDetailAddParams.setStockTime(DateUtil.date());
        shopStockDetailAddParams.setTotalCount(totalCount);
        shopStockDetailAddParams.setTotalAmount(totalAmount);
        stockDao.addIntoStockDetailV2(shopStockDetailAddParams);

        UpdateBillStatusParams updateBillStatusParams = new UpdateBillStatusParams();
        updateBillStatusParams.setId(params.getId());
        updateBillStatusParams.setShopUnique(params.getShopUnique());
        updateBillStatusParams.setStatus(2);
        updateBillStatusParams.setCreateId(params.getCreateId());
        updateBillStatusParams.setCreateBy(params.getCreateBy());
        updateBillStatus(updateBillStatusParams);

        result.setStatus(1);
        result.setMsg("全部入库成功");
        return result;
    }

    @Override
    @Transactional
    public ShopsResult storageGoods(StorageGoodsParams params) {
        ShopsResult result = new ShopsResult();
        ShopSupBillDetailEntity entity = shopSupBillDao.queryShopSupBillDetailEntity(params);//购销单商品明细信息
        if (entity != null) {
            Map<String, Object> goodsQuery = new HashMap<>();
            goodsQuery.put("shopUnique", params.getShopUnique());
            goodsQuery.put("goodsBarcode", entity.getGoodsBarcode());
            GoodsEntity goodsEntity = goodsDao.queryOneByParam(goodsQuery);
            if (null == goodsEntity) {
                result.setStatus(0);
                result.setMsg("当前商品不存在");
                return result;
            }
            Map<Integer, BigDecimal> goodsIdInventoryCountMap = new HashMap<>(1024);
            String listUnique = StrUtil.concat(true, DateUtil.format(DateUtil.date(), "yyyyMMddHHmmssSSS"), RandomUtil.randomNumbers(3));
            params.setListUnique(listUnique);
            goodsIdInventoryCountMap.put(goodsEntity.getGoodsId(), NumberUtil.add(params.getGoodsActualCount(), goodsEntity.getGoodsCount()));
            stockOneRecords(goodsEntity, params, 1, goodsIdInventoryCountMap);//入库

            ShopStockDetailAddParams shopStockDetailAddParams = new ShopStockDetailAddParams();
            shopStockDetailAddParams.setShopUnique(String.valueOf(params.getShopUnique()));
            shopStockDetailAddParams.setAuditStatus(1);
            shopStockDetailAddParams.setShopUnique(String.valueOf(params.getShopUnique()));
            shopStockDetailAddParams.setListUnique(listUnique);
            shopStockDetailAddParams.setStockKind(StockKindEnum.INITIALIZED.getCode());
            shopStockDetailAddParams.setStockType(StockTypeEnum.INVENTORY_IN.getCode());
            shopStockDetailAddParams.setStockResource(StockResourceEnum.CLOUD_PURCHASE.getCode());
            shopStockDetailAddParams.setStockOrigin(StockOriginEnum.MOBILE.getCode());
            if (ObjectUtil.isNotEmpty(params.getCreateId())) {
                shopStockDetailAddParams.setStaffId(Long.valueOf(params.getCreateId()));
                shopStockDetailAddParams.setUpdateId(Long.valueOf(params.getCreateId()));
            }
            shopStockDetailAddParams.setUpdateTime(DateUtil.date());
            shopStockDetailAddParams.setStockTime(DateUtil.date());
            shopStockDetailAddParams.setTotalCount(params.getGoodsActualCount());
            shopStockDetailAddParams.setTotalAmount(params.getGoodsActualCount().multiply(params.getGoodsInPrice()));
            stockDao.addIntoStockDetailV2(shopStockDetailAddParams);

            addStorageRecord(params, goodsEntity);//保存购销单入库记录，与商品入库记录区别开
            entity.setCheckStatus(1);
            entity.setGoodsActualCount(params.getGoodsActualCount());
            if (entity.getGoodsActualCount().compareTo(entity.getGoodsCount()) < 0) {
                entity.setStatus(1);
            } else if (entity.getGoodsActualCount().compareTo(entity.getGoodsCount()) == 0) {
                entity.setStatus(0);
            } else if (entity.getGoodsActualCount().compareTo(entity.getGoodsCount()) > 0) {
                entity.setStatus(2);
            }
            entity.setModifyId(params.getCreateId());
            entity.setModifyBy(params.getCreateBy());
            shopSupBillDao.updateShopSupBillDetailEntity(entity);
            result.setStatus(1);
            result.setMsg("入库成功");
        } else {
            result.setStatus(0);
            result.setMsg("未找到购销单明细，入库失败");
        }

        return result;
    }

    @Override
    public ShopsResult addPaymentOrder(AddPaymentOrderParams params) {
        ShopsResult result = new ShopsResult();
        ShopSupPaymentOrderEntity entity = new ShopSupPaymentOrderEntity();
        BeanUtil.copyProperties(params, entity);
        BillIdParams billIdParams = new BillIdParams();
        BeanUtil.copyProperties(params, billIdParams);
        ShopSupSupplierExamineEntity shopSupSupplierExamineEntity = shopSupplierDao.queryShopSupplierExamineByUnique(billIdParams);
        if (shopSupSupplierExamineEntity != null) {
            if(params.getVoucherPicturepath() == null || params.getVoucherPicturepath().isEmpty()){
                result.setStatus(0);
                result.setMsg("请上传付款凭证");
            }
        }
        shopSupBillDao.addShopSupPaymentOrderEntity(entity);
        List<String> voucherPicturepath = params.getVoucherPicturepath();
        for (String s : voucherPicturepath) {
            ShopSupPaymentEvidenceEntity evidenceEntity = new ShopSupPaymentEvidenceEntity();
            evidenceEntity.setPaymentId(entity.getPaymentId());
            evidenceEntity.setVoucherPicturepath(s);
            evidenceEntity.setCreateId(params.getCreateId());
            evidenceEntity.setCreateBy(params.getCreateBy());
            shopSupBillDao.addShopSupPaymentEvidenceEntity(evidenceEntity);
        }

        if (shopSupSupplierExamineEntity != null) {
            UpdateBillStatusParams updateBillStatusParams = new UpdateBillStatusParams();
            updateBillStatusParams.setId(Long.valueOf(params.getBillId()));
            updateBillStatusParams.setShopUnique(params.getShopUnique());
            updateBillStatusParams.setStatus(3);
            updateBillStatusParams.setCreateId(params.getCreateId());
            updateBillStatusParams.setCreateBy(params.getCreateBy());
            updateBillStatus(updateBillStatusParams);
        } else {
            UpdateBillStatusParams statusParams = new UpdateBillStatusParams();
            statusParams.setId(Long.valueOf(params.getBillId()));
            statusParams.setStatus(4);
            statusParams.setShopUnique(params.getShopUnique());
            shopSupBillDao.updateBillStatus(statusParams);
        }

        result.setStatus(1);
        result.setMsg("付款成功");
        return result;
    }

    @Override
    public ShopsResult modifyPaymentOrder(ModifyPaymentOrderParams params) {
        ShopsResult result = new ShopsResult();
        ShopSupPaymentOrderEntity entity = new ShopSupPaymentOrderEntity();
        entity.setPaymentId(params.getPaymentId());
        if (params.getModifyType() == 1) {
            if (null != params.getShopUnique()) {
                entity.setShopUnique(params.getShopUnique());
            }
            if (null != params.getBillId()) {
                entity.setBillId(params.getBillId());
            }
            if (null != params.getPaymentMoney()) {
                entity.setPaymentMoney(params.getPaymentMoney());
            }
            if (null != params.getRemark()) {
                entity.setRemark(params.getRemark());
            }
            if (null != params.getCreateId()) {
                entity.setModifyId(params.getCreateId());
            }
            if (null != params.getCreateBy()) {
                entity.setModifyBy(params.getCreateBy());
            }
            if (null != params.getDelFlag()) {
                entity.setDelFlag(params.getDelFlag());
            }
            shopSupBillDao.updateShopSupPaymentOrderEntity(entity);
            result.setStatus(1);
            result.setMsg("修改成功");
        } else if (params.getModifyType() == 2) {
            shopSupBillDao.deleteShopSupPaymentOrderEntity(entity.getPaymentId());
            result.setStatus(1);
            result.setMsg("删除成功");
        }
        return result;
    }

    @Override
    @Transactional
    public ShopsResult cancelStorageGoods(CancelStorageGoodsParams params) {
        ShopsResult result = new ShopsResult();
        StorageGoodsParams storageGoodsParams = new StorageGoodsParams();
        BeanUtil.copyProperties(params, storageGoodsParams);
        ShopSupBillDetailEntity entity = shopSupBillDao.queryShopSupBillDetailEntity(storageGoodsParams);//购销单商品明细信息
        if (null == entity) {
            result.setStatus(0);
            result.setMsg("当前购销单已经不存在");
            return result;
        }
        //根据foreignKey查询商品
        Map<String, Object> goodsQuery = new HashMap<>();
        goodsQuery.put("shopUnique", params.getShopUnique());
        goodsQuery.put("goodsBarcode", entity.getGoodsBarcode());
        GoodsEntity goodsEntity = goodsDao.queryOneByParam(goodsQuery);
        if (null == goodsEntity) {
            result.setStatus(0);
            result.setMsg("当前商品已经不存在");
            return result;
        }
        String listUnique = StrUtil.concat(true, DateUtil.format(DateUtil.date(), "yyyyMMddHHmmssSSS"), RandomUtil.randomNumbers(3));
        ShopSupStorageGoodsEntity shopSupStorageGoodsEntity = updateStorageRecord(storageGoodsParams, goodsEntity);//更新购销单入库记录，逻辑删除
        storageGoodsParams.setGoodsInPrice(shopSupStorageGoodsEntity.getGoodsInPriceOld());
        storageGoodsParams.setGoodsSalePrice(shopSupStorageGoodsEntity.getGoodsSalePriceOld());
        storageGoodsParams.setGoodsWebSalePrice(shopSupStorageGoodsEntity.getGoodsWebSalePriceOld());
        storageGoodsParams.setGoodsCusPrice(new BigDecimal(shopSupStorageGoodsEntity.getGoodsCusPriceOld()));
        storageGoodsParams.setGoodsActualCount(shopSupStorageGoodsEntity.getStayStockCount().negate());
        storageGoodsParams.setListUnique(listUnique);
        Map<Integer, BigDecimal> goodsIdInventoryCountMap = new HashMap<>(1024);
        goodsIdInventoryCountMap.put(goodsEntity.getGoodsId(), NumberUtil.add(goodsEntity.getGoodsCount(), storageGoodsParams.getGoodsActualCount()));
        stockOneRecords(goodsEntity, storageGoodsParams, 2, goodsIdInventoryCountMap);//出库

        ShopStockDetailAddParams shopStockDetailAddParams = new ShopStockDetailAddParams();
        shopStockDetailAddParams.setShopUnique(String.valueOf(params.getShopUnique()));
        shopStockDetailAddParams.setAuditStatus(1);
        shopStockDetailAddParams.setShopUnique(String.valueOf(params.getShopUnique()));
        shopStockDetailAddParams.setListUnique(listUnique);
        shopStockDetailAddParams.setStockKind(StockKindEnum.INITIALIZED.getCode());
        shopStockDetailAddParams.setStockType(StockTypeEnum.INVENTORY_OUT.getCode());
        shopStockDetailAddParams.setStockResource(StockResourceEnum.CLOUD_PURCHASE.getCode());
        shopStockDetailAddParams.setStockOrigin(StockOriginEnum.MOBILE.getCode());
        if (ObjectUtil.isNotEmpty(params.getCreateId())) {
            shopStockDetailAddParams.setStaffId(Long.valueOf(params.getCreateId()));
            shopStockDetailAddParams.setUpdateId(Long.valueOf(params.getCreateId()));
        }
        shopStockDetailAddParams.setUpdateTime(DateUtil.date());
        shopStockDetailAddParams.setStockTime(DateUtil.date());
        shopStockDetailAddParams.setTotalCount(shopSupStorageGoodsEntity.getStayStockCount());
        shopStockDetailAddParams.setTotalAmount(shopSupStorageGoodsEntity.getStayStockCount().multiply(shopSupStorageGoodsEntity.getGoodsInPriceOld()));
        stockDao.addIntoStockDetailV2(shopStockDetailAddParams);

        entity.setCheckStatus(0);
        entity.setGoodsActualCount(null);
        entity.setStatus(null);
        entity.setModifyId(params.getCreateId());
        entity.setModifyBy(params.getCreateBy());
        shopSupBillDao.updateShopSupBillDetailEntity(entity);
        result.setStatus(1);
        result.setMsg("撤销入库成功");
        return result;
    }

    @Override
    public ShopsResult addShopSupBill(BillInfoParams params) {
        ShopsResult result = new ShopsResult();
        UpdateSupBillStatusParams queryParams = new UpdateSupBillStatusParams();
        BeanUtil.copyProperties(params, queryParams);
        ShopSupBillEntity shopSupBillEntity = shopSupBillDao.queryShopSupBillEntityByBillNo(queryParams);
        if (shopSupBillEntity != null) {//修改购销单信息
            if (!(shopSupBillEntity.getStatus() == 1 || shopSupBillEntity.getStatus() == 6)) {
                result.setStatus(0);
                result.setMsg("该购销单非待收货状态，禁止重复提交操作");
                return result;
            }
            if (params.getStatus() != 1) {
                result.setStatus(0);
                result.setMsg("该购销单非待收货状态，禁止修改");
                return result;
            }
            //校验购销单中商品是否建档
            List<BillDetailInfoParams> detail = params.getDetail();
            if (detail == null || detail.isEmpty()) {
                result.setStatus(0);
                result.setMsg("该购销单中无商品");
                return result;
            } else {
                StringBuilder msg = new StringBuilder();
                for (int i = 0; i < detail.size(); i++) {
                    BillDetailInfoParams detailEntity = detail.get(i);
                    Map<String, Object> map = new HashMap<>();
                    map.put("shopUnique", params.getCustomerUnique());
                    map.put("supplierUnique", params.getSupplierUnique());
                    map.put("goodsBarcode", detailEntity.getGoodsBarcode());
                    ShopSupGoodsEntity shopSupGoodsEntityNow = shopSupplierDao.queryShopSupGoodsByGoodsBarcode(map);
                    if (shopSupGoodsEntityNow == null || shopSupGoodsEntityNow.getRecordFlag() != 1) {
                        if (msg.length() > 0) {
                            msg.append("、").append(detailEntity.getGoodsBarcode());
                        } else {
                            msg.append(detailEntity.getGoodsBarcode());
                        }
                    }
                }
                if (msg.length() > 0) {
                    result.setStatus(0);
                    result.setMsg("该购销单商品条码为" + msg.toString() + "的商品没有建档，不能创建购销单");
                    return result;
                }
            }
            BeanUtil.copyProperties(params, shopSupBillEntity);
            shopSupBillDao.updateSupBill(shopSupBillEntity);
            List<ShopSupBillDetailEntity> detailEntityList = shopSupBillDao.querySupBillDetailByBillId(shopSupBillEntity.getId());
            if (detailEntityList != null && !detailEntityList.isEmpty()) {
                shopSupBillDao.deleteSupBillDetailByBillId(shopSupBillEntity.getId());
            }
            addSupBillDetail(params, shopSupBillEntity);
            List<String> billEvidenceList = shopSupBillDao.queryBillEvidence(String.valueOf(shopSupBillEntity.getId()));
            if (billEvidenceList != null && !billEvidenceList.isEmpty()) {
                shopSupBillDao.deleteSupBillEvidenceByBillId(shopSupBillEntity.getId());
            }
            addSupBillEvidence(params, result, shopSupBillEntity);
            result.setMsg("修改成功");
        } else {//新增购销单信息
            ShopSupBillEntity entity = new ShopSupBillEntity();
            BeanUtil.copyProperties(params, entity);
            if (params.getStatus() != 1) {
                result.setStatus(0);
                result.setMsg("该购销单非待收货状态，禁止添加");
                return result;
            }

            //校验购销单中商品是否建档
            List<ShopSupBillDetailEntity> detail = entity.getDetail();
            if (detail == null || detail.isEmpty()) {
                result.setStatus(0);
                result.setMsg("该购销单中无商品");
                return result;
            } else {
                StringBuilder msg = new StringBuilder();
                for (int i = 0; i < detail.size(); i++) {
                    ShopSupBillDetailEntity detailEntity = detail.get(i);
                    Map<String, Object> map = new HashMap<>();
                    map.put("shopUnique", params.getCustomerUnique());
                    map.put("supplierUnique", params.getSupplierUnique());
                    map.put("goodsBarcode", detailEntity.getGoodsBarcode());
                    ShopSupGoodsEntity shopSupGoodsEntityNow = shopSupplierDao.queryShopSupGoodsByGoodsBarcode(map);
                    if (shopSupGoodsEntityNow == null || shopSupGoodsEntityNow.getRecordFlag() != 1) {
                        if (msg.length() > 0) {
                            msg.append("、").append(detailEntity.getGoodsBarcode());
                        } else {
                            msg.append(detailEntity.getGoodsBarcode());
                        }
                    }
                }
                if (msg.length() > 0) {
                    result.setStatus(0);
                    result.setMsg("该购销单商品条码为" + msg.toString() + "的商品没有建档，不能创建购销单");
                    return result;
                }
            }
            shopSupBillDao.addSupBill(entity);
            addSupBillDetail(params, entity);
            addSupBillEvidence(params, result, entity);
            result.setMsg("新增成功");
        }
        return result;
    }

    /**
     * 新增购销单商品信息
     *
     * @param params            购销单及商品入参信息
     * @param shopSupBillEntity 购销单实体类信息
     */
    private void addSupBillDetail(BillInfoParams params, ShopSupBillEntity shopSupBillEntity) {
        List<BillDetailInfoParams> detailList = params.getDetail();
        for (int i = 0; null != detailList && !detailList.isEmpty() && i < detailList.size(); i++) {
            BillDetailInfoParams detailInfoParams = detailList.get(i);
            ShopSupBillDetailEntity detailEntity = new ShopSupBillDetailEntity();
            detailEntity.setOrderNo(detailInfoParams.getOrderNo());
            detailEntity.setGoodsName(detailInfoParams.getGoodsName());
            detailEntity.setGoodsBarcode(detailInfoParams.getGoodsBarcode());
            detailEntity.setGoodsImageUrl(detailInfoParams.getGoodsImageUrl());
            detailEntity.setGoodsPurchasePrice(new BigDecimal(detailInfoParams.getGoodsPurchasePrice()));
            detailEntity.setGoodsSalePrice(detailInfoParams.getGoodsSalePrice());
            detailEntity.setGoodsProduceDate(detailInfoParams.getGoodsProduceDate());
            detailEntity.setSubtotalMoney(detailInfoParams.getSubtotalMoney());
            detailEntity.setStatus(0);
            detailEntity.setGoodsPurchaseCount(detailInfoParams.getGoodsCount());
            detailEntity.setGoodsPurchaseUnit(detailInfoParams.getGoodsPurchaseUnit());
            detailEntity.setGoodsCount(detailInfoParams.getGoodsPurchaseCount());
            detailEntity.setGoodsUnit(detailInfoParams.getGoodsUnit());
            detailEntity.setBillId(String.valueOf(shopSupBillEntity.getId()));
            int detailId = shopSupBillDao.addSupBillDetail(detailEntity);
        }
    }

    /**
     * 新增购销单凭证
     *
     * @param params 购销单及商品入参信息
     * @param result 返回信息
     * @param entity 购销单实体类信息
     */
    private void addSupBillEvidence(BillInfoParams params, ShopsResult result, ShopSupBillEntity entity) {
        List<String> evidenceList = params.getImageUrlList();
        for (int i = 0; evidenceList != null && !evidenceList.isEmpty() && i < evidenceList.size(); i++) {
            ShopSupBillEvidenceEntity shopSupBillEvidenceEntity = new ShopSupBillEvidenceEntity();
            shopSupBillEvidenceEntity.setImgUrl(evidenceList.get(i));
            shopSupBillEvidenceEntity.setBillId(entity.getId());
            shopSupBillDao.addSupBillEvidence(shopSupBillEvidenceEntity);
        }
        result.setStatus(1);
    }


    @Override
    public ShopsResult updateBillStatus(UpdateBillStatusParams params) {
        ShopsResult result = new ShopsResult();
        int i = shopSupBillDao.updateBillStatus(params);
        BillIdParams billIdParams = new BillIdParams();
        billIdParams.setBillId(String.valueOf(params.getId()));
        billIdParams.setShopUnique(params.getShopUnique());
        ShopSupSupplierExamineEntity shopSupSupplierExamineEntity = shopSupplierDao.queryShopSupplierExamineByUnique(billIdParams);
        if (shopSupSupplierExamineEntity != null && i == 1) {
            result.setStatus(1);
            result.setMsg("更新状态成功");
            if (params.getStatus() == 2) {
                //确认收货后向供货商APP同步
                Map<String, Object> billMap = shopSupBillDao.querySupBillNo(params.getId());
                ConfirmBillParams confirmBillParams = new ConfirmBillParams();
                confirmBillParams.setBillNo(String.valueOf(billMap.get("billNo")));
                confirmBillParams.setCustomerUnique(String.valueOf(billMap.get("shopUnique")));
                confirmBillParams.setSupplierUnique(String.valueOf(billMap.get("supplierUnique")));
                confirmBillParams.setDeliveryUser(params.getCreateBy());
                String data = JSONUtil.toJsonStr(confirmBillParams);
                ShopsResult result1 = httpPost(URL + "/external/bill/confirmReceipt", data);
                if (result1.getStatus() == 0) {
                    result.setStatus(0);
                    result.setMsg(result1.getMsg());
                    throw new RuntimeException(result1.getMsg());
                }
            } else if (params.getStatus() == 3) {
                //付款后向供货商APP同步
                BillEvidenceAddParams billEvidenceAddParams = new BillEvidenceAddParams();
                Map<String, Object> paymentMap = new HashMap<>();
                paymentMap.put("id", params.getId());
                paymentMap.put("shopUnique", params.getShopUnique());
                Map<String, Object> paymentResultMap = shopSupBillDao.queryPayment(paymentMap);
                if (paymentResultMap != null && !paymentResultMap.isEmpty()) {
                    billEvidenceAddParams.setCustomerUnique(String.valueOf(paymentResultMap.get("customerUnique")));
                    billEvidenceAddParams.setSupplierUnique(String.valueOf(paymentResultMap.get("supplierUnique")));
                    billEvidenceAddParams.setBillNo(String.valueOf(paymentResultMap.get("billNo")));
                    billEvidenceAddParams.setPayUser(params.getCreateBy());
                    billEvidenceAddParams.setPayRemark(String.valueOf(paymentResultMap.get("payRemark")));
                    List<String> evidenceList = shopSupBillDao.queryVoucherPicturepath(String.valueOf(paymentResultMap.get("paymentId")));
                    billEvidenceAddParams.setImageUrlList(evidenceList);
                    String billEvidenceAddData = JSONUtil.toJsonStr(billEvidenceAddParams);
                    ShopsResult result1 = httpPost(URL + "/external/bill/uplaodPayEvidence", billEvidenceAddData);
                    if (result1.getStatus() == 0) {
                        result.setStatus(0);
                        result.setMsg(result1.getMsg());
                        throw new RuntimeException(result1.getMsg());
                    }
                }
            }
        } else if (i != 1) {
            result.setStatus(0);
            result.setMsg("更新状态失败");
        } else {
            result.setStatus(1);
            result.setMsg("更新状态成功");
        }
        return result;
    }

    public ShopsResult httpPost(String url, String data) {
        ShopsResult result = new ShopsResult();
        System.out.println("-------------------------同步, 地址: {" + url + "},参数: {" + data + "}---------------");
        String result1 = HttpUtil.post(url, data);
        System.out.println("-------------------------同步结果: {" + result1 + "}---------------");
        JSONObject jo1 = JSONUtil.parseObj(result1);
        if (jo1.get("status") == null) {
            result.setStatus(0);
            result.setMsg("访问同步信息接口超时");
        } else if ("1".equals(String.valueOf(jo1.get("status")))) {
            result.setStatus(1);
            result.setMsg(String.valueOf(jo1.get("message")));
        } else {
            result.setStatus(0);
            result.setMsg(String.valueOf(jo1.get("message")));
        }
        return result;
    }

    @Override
    public ShopsResult updateSupBillStatus(UpdateSupBillStatusParams params) {
        ShopsResult result = new ShopsResult();
        ShopSupBillEntity entity = shopSupBillDao.queryShopSupBillEntityByBillNo(params);
        if (entity != null) {
            if (params.getStatus().equals(5)) {
                if (entity.getStatus() == 2) {
                    result.setStatus(0);
                    result.setMsg("购销单已入库，不能作废");
                    return result;
                } else if (entity.getStatus() == 3) {
                    result.setStatus(0);
                    result.setMsg("购销单已付款，不能作废");
                    return result;
                } else if (entity.getStatus() == 4) {
                    result.setStatus(0);
                    result.setMsg("购销单已完成，不能作废");
                    return result;
                }
            } else if (params.getStatus().equals(4)) {
                if (entity.getStatus() != 3) {
                    result.setStatus(0);
                    result.setMsg("购销单处于未付款状态，不能完成");
                    return result;
                }
            }
            long id = entity.getId();
            UpdateBillStatusParams statusParams = new UpdateBillStatusParams();
            statusParams.setId(id);
            statusParams.setStatus(params.getStatus());
            statusParams.setShopUnique(Long.valueOf(params.getCustomerUnique()));
            shopSupBillDao.updateBillStatus(statusParams);
            result.setStatus(1);
            result.setMsg("更新成功");
        } else {
            result.setStatus(0);
            result.setMsg("该购销单不存在");
        }
        return result;
    }

    @Override
    public ShopsResult queryPayment(QueryBillGoodsListParams params) {
        ShopsResult result = new ShopsResult();
        ShopSupBillPaymentResult paymentResult = new ShopSupBillPaymentResult();
        Map<String, Object> paymentMap = new HashMap<>();
        paymentMap.put("id", params.getId());
        paymentMap.put("shopUnique", params.getShopUnique());
        Map<String, Object> paymentResultMap = shopSupBillDao.queryPayment(paymentMap);
        if (paymentResultMap != null && !paymentResultMap.isEmpty()) {
            paymentResult.setBillNo(String.valueOf(paymentResultMap.get("billNo")));
            if (paymentResultMap.get("payRemark") != null) {
                paymentResult.setPaymentRemark(String.valueOf(paymentResultMap.get("payRemark")));
            }
            if (paymentResultMap.get("paymentMoney") != null) {
                paymentResult.setPaymentMoney(new BigDecimal(String.valueOf(paymentResultMap.get("paymentMoney"))));
            }
            if (paymentResultMap.get("paymentId") != null) {
                List<String> evidenceList = shopSupBillDao.queryVoucherPicturepath(String.valueOf(paymentResultMap.get("paymentId")));
                paymentResult.setImageUrlList(evidenceList);
            }
        }
        result.setStatus(1);
        result.setMsg("查询成功！");
        result.setData(paymentResult);
        return result;
    }

    @Override
    public ShopsResult checkGoods(CheckGoodsParams params) {
        ShopsResult result = new ShopsResult();
        StorageGoodsParams storageGoodsParams = new StorageGoodsParams();
        BeanUtil.copyProperties(params, storageGoodsParams);
        ShopSupBillDetailEntity entity = shopSupBillDao.queryShopSupBillDetailEntity(storageGoodsParams);//购销单商品明细信息
        Map<String, Object> goodsQuery = new HashMap<>();
        goodsQuery.put("shopUnique", params.getShopUnique());
        goodsQuery.put("goodsBarcode", entity.getGoodsBarcode());
        GoodsEntity goodsEntity = goodsDao.queryOneByParam(goodsQuery);
        if (null == goodsEntity) {
            result.setStatus(0);
            result.setMsg("当前商品已经不存在");
            return result;
        }
        entity.setCheckStatus(1);
        entity.setGoodsActualCount(params.getGoodsActualCount());
        entity.setGoodsSalePriceStorage(params.getGoodsSalePrice());
        entity.setGoodsWebSalePriceStorage(params.getGoodsWebSalePrice());
        entity.setGoodsCusPriceStorage(params.getGoodsCusPrice());
        if (entity.getGoodsActualCount().compareTo(entity.getGoodsCount()) < 0) {
            entity.setStatus(1);
        } else if (entity.getGoodsActualCount().compareTo(entity.getGoodsCount()) == 0) {
            entity.setStatus(0);
        } else if (entity.getGoodsActualCount().compareTo(entity.getGoodsCount()) > 0) {
            entity.setStatus(2);
        }
        entity.setModifyId(params.getCreateId());
        entity.setModifyBy(params.getCreateBy());
        shopSupBillDao.updateShopSupBillDetailEntity(entity);
        result.setStatus(1);
        result.setMsg("核对成功");
        return result;
    }

    @Override
    public ShopsResult cancelSupBill(CancelSupBillParams params) {
        ShopsResult result = new ShopsResult();
        if (params.getDelFlag() != 0 && params.getDelFlag() != 1) {
            result.setStatus(0);
            result.setMsg("购销单作废标识不正确");
            return result;
        }
        UpdateSupBillStatusParams queryParams = new UpdateSupBillStatusParams();
        BeanUtil.copyProperties(params, queryParams);
        ShopSupBillEntity entity = shopSupBillDao.queryShopSupBillEntityByBillNo(queryParams);
        if (entity != null) {
            if (entity.getStatus() == 2) {
                result.setStatus(0);
                result.setMsg("购销单已经入库，不能作废");
                return result;
            } else if (entity.getStatus() == 3) {
                result.setStatus(0);
                result.setMsg("购销单已经付款，不能作废");
                return result;
            } else if (entity.getStatus() == 4) {
                result.setStatus(0);
                result.setMsg("购销单已经完成，不能作废");
                return result;
            }
        }
        int i = shopSupBillDao.cancelSupBill(params);
        if (params.getDelFlag() == 0) {
            result.setStatus(1);
            result.setMsg("作废成功");
        } else if (params.getDelFlag() == 1) {
            result.setStatus(1);
            result.setMsg("撤销作废成功");
        }
        return result;
    }

    @Override
    public ShopsResult cancelCheckGoods(CancelCheckGoodsParams params) {
        ShopsResult result = new ShopsResult();
        StorageGoodsParams storageGoodsParams = new StorageGoodsParams();
        BeanUtil.copyProperties(params, storageGoodsParams);
        ShopSupBillDetailEntity entity = shopSupBillDao.queryShopSupBillDetailEntity(storageGoodsParams);//购销单商品明细信息
        entity.setCheckStatus(0);
        entity.setModifyId(params.getCreateId());
        entity.setModifyBy(params.getCreateBy());
        shopSupBillDao.updateShopSupBillDetailEntity(entity);
        result.setStatus(1);
        result.setMsg("撤销核对成功");
        return result;
    }

    private void stockOneRecords(GoodsEntity goodsEntity, StorageGoodsParams params, int stockType, Map<Integer, BigDecimal> goodsIdInventoryCountMap) {
        Iterator<Map.Entry<Integer, BigDecimal>> iter = goodsIdInventoryCountMap.entrySet().iterator();
        Map<Long, BigDecimal> goodsForeignKeyGoodsCount = new HashMap<>(1024);
        Set<Long> foreignKeySet = new HashSet<>(1024);
        while (iter.hasNext()) {
            Map.Entry<Integer, BigDecimal> next = iter.next();
            BigDecimal inventoryCount = next.getValue(); //新库存
            BigDecimal goodsStock = NumberUtil.mul(inventoryCount, goodsEntity.getGoodsContain());
            /*//库存操作
            if (goodsForeignKeyGoodsCount.containsKey(goodsEntity.getForeignKey())){
                BigDecimal decimal = goodsForeignKeyGoodsCount.get(goodsEntity.getForeignKey());
                goodsStock = NumberUtil.add(decimal,goodsStock);
            }*/
            goodsForeignKeyGoodsCount.put(goodsEntity.getForeignKey(),goodsStock);
            foreignKeySet.add(goodsEntity.getForeignKey());
        }

        Map<String, Object> queryGoods = new HashMap<>();
        queryGoods.put("shopUnique", params.getShopUnique());
        queryGoods.put("foreignKeyList", foreignKeySet);
        List<GoodsEntity> goodsEntityList = goodsDao.queryGoodsByParam(queryGoods);

        //批量更新库存
        List<GoodsEntity> updGoodsList = new ArrayList<>(1024);
        List<Map<String, Object>> stockRecords = new ArrayList<>();
        List<String> goodsList = new ArrayList<>();
        for (GoodsEntity g : goodsEntityList) {
            BigDecimal goodsCount = goodsForeignKeyGoodsCount.get(g.getForeignKey());
            if (g.getGoodsContain().equals(BigDecimal.ONE)) {
                g.setGoodsCount(goodsCount);
            } else {
                g.setGoodsCount(BigDecimal.valueOf(NumberUtil.div(goodsCount, g.getGoodsContain()).doubleValue()));
            }
            ShopsConfig shopsConfig = shopsConfigDao.selectByShopUnique(String.valueOf(params.getShopUnique()));
            ShopStockAddParams shopStockAddParams = new ShopStockAddParams();
            shopStockAddParams.setShopUnique(String.valueOf(params.getShopUnique()));
            shopStockAddParams.setGoodsBarcode(g.getGoodsBarcode());
            shopStockAddParams.setListUnique(params.getListUnique());
            if (ObjectUtil.equal(IsIoBoundInspectEnum.NO.getCode(), shopsConfig.getIsIoBoundInspect())) {
                shopStockAddParams.setStockCount(g.getGoodsCount().add(params.getGoodsActualCount()));
            } else {
                shopStockAddParams.setStockCount(BigDecimal.ZERO);
            }
            shopStockAddParams.setGoodsCount(params.getGoodsActualCount());

            if (stockType == 2) {
                shopStockAddParams.setStockType(StockTypeEnum.INVENTORY_OUT.getCode());
            } else {
                shopStockAddParams.setStockType(StockTypeEnum.INVENTORY_IN.getCode());

            }
            shopStockAddParams.setStockTime(DateUtil.date());
            shopStockAddParams.setStockResource(StockResourceEnum.MANUAL.getCode());
            shopStockAddParams.setStockPrice(g.getGoodsInPrice());
            shopStockAddParams.setStockOrigin(StockOriginEnum.MOBILE.getCode());
            if (ObjectUtil.isNotEmpty(params.getCreateId())) {
                shopStockAddParams.setStaffId(Long.valueOf(params.getCreateId()));
            }
            stockDao.stockRecordV3(shopStockAddParams);

            updGoodsList.add(g);

            goodsList.add(g.getGoodsBarcode());
        }
        Map<String, Object> updGoodsCount = new HashMap<>();
        updGoodsCount.put("list", updGoodsList);
        stockDao.batchUpdateGoodsCount(updGoodsCount);
        updateGoodsPrice(params, goodsEntity);//更新商品价格

        sendMqtt(params.getShopUnique(), goodsList);//发消息同步商品

    }

    private void updateGoodsPrice(StorageGoodsParams params, GoodsEntity goodsEntity) {
        UpdatePriceParams updatePriceParams = new UpdatePriceParams();
        updatePriceParams.setGoodsBarcode(goodsEntity.getGoodsBarcode());
        updatePriceParams.setShopUnique(params.getShopUnique());
        updatePriceParams.setGoodsInPrice(params.getGoodsInPrice());
        updatePriceParams.setGoodsSalePrice(params.getGoodsSalePrice());
        updatePriceParams.setGoodsWebSalePrice(params.getGoodsWebSalePrice());
        updatePriceParams.setGoodsCusPrice(params.getGoodsCusPrice());
        shopSupBillDao.updatePrice(updatePriceParams);

    }

    private ShopSupStorageGoodsEntity updateStorageRecord(StorageGoodsParams params, GoodsEntity goodsEntity) {
        QueryShopSupStorageGoodsEntityParams queryShopSupStorageGoodsEntityParams = new QueryShopSupStorageGoodsEntityParams();
        queryShopSupStorageGoodsEntityParams.setId(params.getId());
        queryShopSupStorageGoodsEntityParams.setDetailId(params.getDetailId());
        queryShopSupStorageGoodsEntityParams.setShopUnique(params.getShopUnique());
        queryShopSupStorageGoodsEntityParams.setGoodsBarcode(goodsEntity.getGoodsBarcode());
        ShopSupStorageGoodsEntity shopSupStorageGoodsEntity = shopSupBillDao.queryShopSupStorageGoodsEntity(queryShopSupStorageGoodsEntityParams);

        UpdateStorageGoodsParams updateStorageGoodsParams = new UpdateStorageGoodsParams();
        updateStorageGoodsParams.setSssdId(shopSupStorageGoodsEntity.getSssdId());
        updateStorageGoodsParams.setModifyId(params.getCreateId());
        updateStorageGoodsParams.setModifyBy(params.getCreateBy());
        updateStorageGoodsParams.setDelFlag(1);
        shopSupBillDao.updateStorageRecord(updateStorageGoodsParams);
        return shopSupStorageGoodsEntity;
    }

    private void addStorageRecord(StorageGoodsParams params, GoodsEntity goodsEntity) {
        ShopSupStorageGoodsEntity shopSupStorageGoodsEntity = new ShopSupStorageGoodsEntity();
        shopSupStorageGoodsEntity.setBillId(params.getId());
        shopSupStorageGoodsEntity.setBillDetailId(params.getDetailId());
        shopSupStorageGoodsEntity.setGoodsBarcode(goodsEntity.getGoodsBarcode());
        shopSupStorageGoodsEntity.setShopUnique(params.getShopUnique());
        shopSupStorageGoodsEntity.setGoodsInPrice(params.getGoodsInPrice());
        shopSupStorageGoodsEntity.setGoodsSalePrice(params.getGoodsSalePrice());
        shopSupStorageGoodsEntity.setGoodsWebSalePrice(params.getGoodsWebSalePrice());
        shopSupStorageGoodsEntity.setGoodsCusPrice(String.valueOf(params.getGoodsCusPrice()));
        shopSupStorageGoodsEntity.setGoodsInPriceOld(goodsEntity.getGoodsInPrice());
        shopSupStorageGoodsEntity.setGoodsSalePriceOld(goodsEntity.getGoodsSalePrice());
        shopSupStorageGoodsEntity.setGoodsWebSalePriceOld(goodsEntity.getGoodsWebSalePrice());
        shopSupStorageGoodsEntity.setGoodsCusPriceOld(goodsEntity.getGoodsCusPrice());
        shopSupStorageGoodsEntity.setStayStockCount(params.getGoodsActualCount());
        shopSupStorageGoodsEntity.setCreateId(params.getCreateId());
        shopSupStorageGoodsEntity.setCreateBy(params.getCreateBy());
        shopSupBillDao.addStorageRecord(shopSupStorageGoodsEntity);

        //修改完成后保存记录
        try{
            RecordGoodsOperParams recordGoodsOperParams = new RecordGoodsOperParams();
            Map<String, Object> map = new HashMap<>();
            map.put("shopUnique" , params.getShopUnique());
            map.put("goodsBarcode" , goodsEntity.getGoodsBarcode());
            RecordGoods newRecordGoods = goodsDao.querySourceGoods(map);
            recordGoodsOperParams.setResultGoods(newRecordGoods);

            RecordGoods sourceGoods = new RecordGoods();
            BeanUtil.copyProperties(newRecordGoods, sourceGoods);
            sourceGoods.setGoodsCount(goodsEntity.getGoodsCount());
            sourceGoods.setGoodsInPrice(goodsEntity.getGoodsInPrice());
            sourceGoods.setGoodsSalePrice(goodsEntity.getGoodsSalePrice());
            sourceGoods.setGoodsWebSalePrice(goodsEntity.getGoodsWebSalePrice());
            sourceGoods.setGoodsCusPrice(String.valueOf(goodsEntity.getGoodsCusPrice()));
            recordGoodsOperParams.setSourceGoods(sourceGoods);
            RecordGoodsOper recordGoodsOper = new RecordGoodsOper();
            recordGoodsOper.setGoodsId(sourceGoods.getGoodsId() + 0l);
            recordGoodsOper.setOperType(OperTypeEnum.OPER_TYPE_UPDATE.getValue());
            recordGoodsOper.setGoodsBarcode(goodsEntity.getGoodsBarcode());
            recordGoodsOper.setShopUnique(params.getShopUnique());
            recordGoodsOper.setDeviceSource(1);
            recordGoodsOper.setDeviceSourceMsg("");
            if (ObjectUtil.isNotEmpty(params.getCreateId())) {
                recordGoodsOper.setUserId(params.getCreateId() == null ? "" : params.getCreateId() + "");
            }
            recordGoodsOper.setUserType(2);
            recordGoodsOper.setOperSource(OperSourceEnum.GOODS_CHANGE.getValue());
            recordGoodsOperParams.setRecordGoodsOper(recordGoodsOper);

            Map<String,Object> headerMap = new HashMap<>();
            headerMap.put("Content-Type", "application/json; charset=" + org.haier.shopUpdate.util.unionpay.HttpUtil.DEFAULT_CHARSET);
            String jsonStr = com.alibaba.fastjson.JSONObject.toJSONString(recordGoodsOperParams);
            String result = org.haier.shopUpdate.util.unionpay.HttpUtil.doPostStr(HelibaoPayConfig.RECORDGOODSOPER, headerMap, jsonStr);
        } catch (Exception e){
            log.error("保存操作记录信息失败", e);
            sendDingDingTalkUtils.sendDingDingTalkMsg(((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest(), "保存操作记录信息", e, JSONUtil.toJsonStr(params) + JSONUtil.toJsonStr(goodsEntity), "购销单-商品入库", BusinessType.UPDATE.ordinal());
        }
    }

    /*private void sendMqtt(StorageGoodsParams params, List<Map<String, Object>> returnList) {
        //mqtt 同步商品
        try {
            Object mac = redis.getObject("topic_" + params.getShopUnique());
            if (mac != null) {
                @SuppressWarnings("unchecked")
                List<String> macIdList = (List<String>) mac;
                //2 MQTT 发送消息
                for (String macid : macIdList) {
                    Map<String, Object> data = new HashMap<>();
                    data.put("ctrl", "msg_goods_update_v2.0");
                    data.put("ID", macid);
                    data.put("status", 200);
                    data.put("data", returnList);
                    data.put("count", 1);
                    MqttxUtil.sendMapMsg(data, macid);
                }
            }
            //清除缓存
            String rcId = "pcGoods" + params.getShopUnique() + 1 + 3 + null;
            Object res = redis.getObject(rcId);
            if (null != res) {
                redis.removeObject(rcId);
            }
            String rcId2 = "pcGoods" + params.getShopUnique() + 0 + 3 + null;
            Object res2 = redis.getObject(rcId2);
            if (null != res2) {
                redis.removeObject(rcId2);
            }
        } catch (Exception e) {

        }
    }*/

    private void sendMqtt(Long shopUnique, List<String> returnList) {
        //mqtt 同步商品
        try {
            for (int i = 0; returnList != null && i < returnList.size(); i++) {
                SendMqttMsg sendMqttMsg = new SendMqttMsg(shopUnique + "", returnList.get(i));
                sendMqttMsg.start();
            }
        } catch (Exception e) {
            log.error("发送mqtt消息失败", e);
        }
    }
}
