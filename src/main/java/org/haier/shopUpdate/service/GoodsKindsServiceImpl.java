package org.haier.shopUpdate.service;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.haier.shopUpdate.dao.GoodsDao;
import org.haier.shopUpdate.dao.GoodsKindsDao;
import org.haier.shopUpdate.dao.ShopsStaffDao;
import org.haier.shopUpdate.dto.GoodsKindByShopUniqueDto;
import org.haier.shopUpdate.entity.GoodsKind;
import org.haier.shopUpdate.entity.ShopsGroupGoodsKinds;
import org.haier.shopUpdate.entity.ShopsKindGoodsKinds;
import org.haier.shopUpdate.redism.RedisCache;
import org.haier.shopUpdate.util.*;
import org.haier.shopUpdate.util.common.StringUtils;
import org.haier.shopUpdate.util.mqtt.MqttxUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GoodsKindsServiceImpl implements GoodsKindsService {
    @Resource
    private GoodsKindsDao kindDao;
    @Resource
    private ShopsStaffDao staffDao;
    @Resource
    private I18nLanguageStaticReturnParamsUtil i18nRtUtil;
    @Resource
    private GoodsDao goodsDao;

    /**
     * 商品分类查询
     *
     * @param map
     * @return
     */
    public ShopsResult queryGoodsKindsByShop(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        //map.putAll(kindDao.queryShopKindType(map));//添加当前店铺分类类型
        //List<ShopsGroupGoodsKinds> data = kindDao.queryGoodsKindsByShop(map);
        Map<String, Object> stringObjectMap = kindDao.queryShopKindType(map);
        map.put("kindType", stringObjectMap.get("kindType"));
        //根据店铺unique和类型查询商品分类信息
        System.out.println("map -----" + map);
        List<Map<String, Object>> kindList = kindDao.queryGoodsKind(map);
        log.error("kindList --- :" + kindList);
        List<ShopsGroupGoodsKinds> shopsGroupGoodsKindList = new ArrayList<>();
        //查询头像 和 头像id
        for (Map<String, Object> result : kindList) {
            ShopsGroupGoodsKinds shopsGroupGoodsKinds = new ShopsGroupGoodsKinds();
            if (ObjectUtil.isNotEmpty(result.get("goods_kind_icon_id"))) {
                shopsGroupGoodsKinds.setKindIconId(result.get("goods_kind_icon_id").toString());
            }
            if (ObjectUtil.isNotEmpty(result.get("goods_kind_icon_picture"))) {
                shopsGroupGoodsKinds.setKindIcon(result.get("goods_kind_icon_picture").toString());
            }
            shopsGroupGoodsKinds.setShopUnique(result.get("shop_unique").toString());
            shopsGroupGoodsKinds.setGroupId(result.get("goods_kind_id").toString());
            shopsGroupGoodsKinds.setGroupName(result.get("goods_kind_name").toString());
            shopsGroupGoodsKinds.setGroupUnique(result.get("goods_kind_unique").toString());
            shopsGroupGoodsKinds.setParentUnique(result.get("goods_kind_parunique").toString());
            shopsGroupGoodsKinds.setEditType(Integer.valueOf(result.get("edit_type").toString()));
            //1有效 2无效
            shopsGroupGoodsKinds.setValid_type(Integer.valueOf(result.get("valid_type").toString()));
            shopsGroupGoodsKindList.add(shopsGroupGoodsKinds);
        }
        List<ShopsGroupGoodsKinds> parentList = shopsGroupGoodsKindList.stream().filter(e -> e.getParentUnique().equals("0")).collect(Collectors.toList());
        List<ShopsGroupGoodsKinds> childrenList = shopsGroupGoodsKindList.stream().filter(e -> !e.getParentUnique().equals("0")).collect(Collectors.toList());
        //把子集构造成kindDetail
        List<ShopsKindGoodsKinds> childrenListGoods = new ArrayList<>();
        for (ShopsGroupGoodsKinds shopsGroupGoodsKinds : childrenList) {
            ShopsKindGoodsKinds subKind = new ShopsKindGoodsKinds();
            subKind.setGroupUnique(shopsGroupGoodsKinds.getParentUnique());
            subKind.setKindName(shopsGroupGoodsKinds.getGroupName());
            subKind.setKindId(Integer.valueOf(shopsGroupGoodsKinds.getGroupId()));
            subKind.setKindUnique(shopsGroupGoodsKinds.getGroupUnique());
            subKind.setEditType(shopsGroupGoodsKinds.getEditType().toString());
            subKind.setValid_type(shopsGroupGoodsKinds.getValid_type());
            childrenListGoods.add(subKind);
        }


        List<ShopsGroupGoodsKinds> shopsGroupGoodsKinds = GoodsCategoryTreeBuilder.buildCategoryTree(parentList, childrenListGoods);


       /* if (null == data || data.isEmpty()) {
            sr.setData(ShopsUtil.map);
        } else {
            for (ShopsGroupGoodsKinds group : data) {
                if (group.getKindDetail().size() == 1 && ObjectUtil.isEmpty(group.getKindDetail().get(0).getKindId())) {
                    group.setKindDetail(Collections.EMPTY_LIST);
                }
            }
        }*/
        sr.setStatus(1);
        sr.setMsg("查询成功！");
        sr.setData(shopsGroupGoodsKinds);
        return sr;
    }

    /**
     * 店铺商品分类信息查询
     */
    public ShopsResult appQueryGoodsKinds(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        List<Map<String, Object>> data = kindDao.appQueryGoodsKinds(map);
        if (null == map || map.isEmpty()) {
            sr.setStatus(2);
            sr.setMsg("没有满足条件的信息！");
            return sr;
        }
        sr.setStatus(1);
        sr.setMsg("查询成功！");
        sr.setData(data);
        return sr;
    }

    /**
     * 商品分类信息查询
     */
    public ShopsResult queryGoodsBigKindsByShop(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        //查询店铺分类信息
        map.putAll(kindDao.queryShopKindType(map));
        List<Map<String, Object>> data = kindDao.queryGoodsBigKindsByShop(map);
        if (ObjectUtil.isNotEmpty(data)) {
            for (Map<String, Object> map2 : data) {
                if (ObjectUtil.isNotEmpty(map2.get("groupName"))) {
                    map2.put("groupName", i18nRtUtil.getMessage(String.valueOf(map2.get("groupName"))));
                }
            }
        }
        for (Map<String, Object> map2 : data) {
            map.put("groupUnique", map2.get("groupUnique"));
            map.put("shopUnique", map.get("shop_unique"));
            List<Map<String, Object>> childList = kindDao.appQueryGoodsKinds(map);
            if (ObjectUtil.isNotEmpty(childList)) {
                for (Map<String, Object> childMap : childList) {
                    if (ObjectUtil.isNotEmpty(childMap.get("kindName"))) {
                        childMap.put("kindName", i18nRtUtil.getMessage(String.valueOf(childMap.get("kindName"))));
                    }
                }
            }

            for (Map<String, Object> map3 : childList) {
                map.put("groupUnique", map3.get("kindUnique"));
                log.error("groupUnique---" + map3.get("groupUnique"));
                map.put("shopUnique", map.get("shop_unique"));
                List<Map<String, Object>> sonList = kindDao.appQueryGoodsKinds(map);
                map3.put("kindDetail", sonList);
            }
            map2.put("kindDetail", childList);

        }
        if (data != null && data.size() > 0) {
            sr.setData(data);
            sr.setStatus(1);
        } else {
            sr.setStatus(0);
            sr.setData(new ArrayList<Map<String, Object>>());
        }
        sr.setMsg("查询成功！");

        return sr;
    }

    /**
     *
     */
    public ShopsResult queryMoreGoodsBigKinds(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        map.putAll(kindDao.queryShopKindType(map));
        List<Map<String, Object>> data = kindDao.queryMoreGoodsBigKinds(map);
        if (data != null && data.size() > 0) {
            sr.setData(data);
            sr.setStatus(1);
        } else {
            sr.setStatus(0);
            sr.setData(new ArrayList<Map<String, Object>>());
        }
        sr.setMsg("查询成功！");
        return sr;
    }

    @Transactional
    public ShopsResult saveGoodsBigKindsByShop(String shop_unique, String goods_kind_list) {
        ShopsResult sr = new ShopsResult();
//		System.out.println(goods_kind_list);
        List<String> goods_kind_unique_list = new ArrayList<String>();
        if (goods_kind_list != null && !"".equals(goods_kind_list)) {
            JSONArray array = JSONArray.fromObject(goods_kind_list);
            for (int i = 0; i < array.size(); i++) {
                JSONObject temp = (JSONObject) array.get(i);
                String goods_kind_name = temp.getString("goods_kind_name");
                String goods_kind_unique = temp.getString("goods_kind_unique");
                if (goods_kind_unique.equals("-100") || goods_kind_unique.equals("-101")) {//自定义大类跳过
                    continue;
                }
                ;//
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("shop_unique", shop_unique);
                map.put("goods_kind_name", goods_kind_name);
                map.put("goods_kind_unique", goods_kind_unique);
                map.put("goods_kind_sort", i);
                //是否有此类,此类是否为本店大类
                Map<String, Object> result = kindDao.queryGoodsKinds(map);
                if (result == null) {
                    //没有获取默认图片并添加
                    map.put("shop_unique", 0);
                    Map<String, Object> result2 = kindDao.queryGoodsKinds(map);
                    result2.put("shop_unique", shop_unique);
                    result2.put("goods_kind_sort", i);
                    kindDao.saveGoodsBigKinds(result2);
                    //获取小类并添加
                    Map<String, Object> params = new HashMap<String, Object>();
                    params.put("groupUnique", goods_kind_unique);
                    List<Map<String, Object>> smallList = kindDao.appQueryGoodsKinds(params);
                    for (Map<String, Object> map2 : smallList) {
                        map2.put("shop_unique", shop_unique);
                        map2.put("goods_kind_parunique", goods_kind_unique);
                        kindDao.saveSmallGoodsKind(map2);
                    }

                } else {
                    //有则更新为显示状态并排序
                    kindDao.updateBigGoodsKind(map);
                }
                goods_kind_unique_list.add(goods_kind_unique);
            }
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("shop_unique", shop_unique);
            int i = 0;
            if (goods_kind_unique_list.size() > 0) {
                params.put("list", goods_kind_unique_list);
                //批量更新没有显示的分类为隐藏状态
                i = kindDao.updateBatchBigGoodsKind(params);
            } else {
                i = kindDao.updateBatchBigGoodsKindAll(params);
            }
            System.out.println(i);
        }
        sr.setStatus(1);
        sr.setMsg("查询成功！");
        return sr;
    }

    /**
     * 修改店铺使用的商品分类类型
     */
    @Transactional
    public ShopsResult useCustomeKind(Integer kindType, String shopUnique) {
        ShopsResult sr = new ShopsResult(1, "修改成功！");
        //修改店铺分类使用信息
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", shopUnique);
        map.put("kindType", kindType);
        //检测店铺当前分类是否更改，若不更改，直接返回
        Integer c = staffDao.getShopNowKindType(shopUnique);
        if (kindType == c) {
            return sr;
        }
        int k = staffDao.updateShopsMessage(map);

        if (k > 0) {
            if (kindType == 1) {//使用系统分类
                /*
                 * 步骤1、查询所有商品在goods_dict中的分类信息，
                 * 步骤2、将店铺内所有商品分类信息更新
                 */
                List<Map<String, Object>> list = kindDao.queryGoodsDictKind(map);
                if (null != list && !list.isEmpty()) {
                    kindDao.rebackSystemKind(list);
                }
            } else if (kindType == 2) {
                /*
                 * 将店内所有商品分类信息更新为自定义默认分类
                 */
                map.put("kindUnique", ConfigForShopUpdate.DEFAULTKINDUNIQUE);
                kindDao.useCustomeKind(map);
            }
        } else {
            sr.setStatus(2);
            sr.setMsg("操作失败");
        }
        return sr;
    }

    /**
     * 删除商品分类前，查询该分类下的商品数量
     *
     * @param goodsKind
     * @return
     */
    public ShopsResult getGoodsCountByKindUnique(GoodsKind goodsKind) {
        ShopsResult sr = new ShopsResult(1, "操作成功！");
        if (goodsKind.getGoodsKindUnique() == null || goodsKind.getGoodsKindUnique().equals("null")) {
            sr.setStatus(2);
            sr.setMsg("未传入商品分类信息");
            return sr;
        }
        Integer k = kindDao.getGoodsCountByKindUnique(goodsKind);
        sr.setData(i18nRtUtil.getMessage("该分类下商品个数为") + (k == null ? "0" : k) + i18nRtUtil.getMessage(", 停用后，商品分类更新为未分类，是否继续？"));
        return sr;
    }

    /**
     * 添加新的商品自定义分类信息
     *
     * @param kind
     * @return
     */
    @Transactional
    public ShopsResult modifyCustomKinds(GoodsKind kind) {
        ShopsResult sr = new ShopsResult(1, "操作成功");
        int k = 0;
        if (kind.getKindType() == null) {//自定义分类
            kind.setKindType(2);
        }
        if (kind.getEditType() == null) {//可编辑
            kind.setEditType(2);
        }
        String ctrl = null;

        if (kind.getGoodsKindParunique() == null) {//更新或删除
            List<Long> kindUniques = new ArrayList<>();
            Map<String, Object> mapValidMap = new HashMap<>();
            mapValidMap.put("shopUnique", kind.getShopUnique());
            mapValidMap.put("kindUnique", kind.getKindUnique());

            //判断当前商品是启用还是禁用
            Integer i = 0;
            if (kind.getValidType() != null) {
                i = kindDao.queryGoodsKindValidType(mapValidMap);
            }
            ctrl = "msg_goods_kind_update";
            if (kind.getValidType() != null && i == 1) {
                kind.setValidType(2);
                //不修改名字
                kind.setGoodsKindName(null);
                kindUniques = kindDao.queryKindMsg(mapValidMap);
                log.error("禁用分类 ", kindUniques);
            } else if (kind.getValidType() != null && i == 2) {
                kind.setValidType(1);
                //不修改名字
                kind.setGoodsKindName(null);
                kindUniques = kindDao.queryGroupMsg(mapValidMap);
                log.error("启用分类 ", kindUniques);
            } else {
                kindUniques.add(Long.valueOf(kind.getKindUnique()));
            }
            log.error("最终分类 ", kindUniques);
            kind.setKindUniques(kindUniques);
            k = kindDao.updateCustomKind(kind);


            //此处是否考虑将子分类一并删除
            //若更新操作为删除商品分类，则应将该分类下的商品更新为默认分类
            if (kind.getValidType() != null && kind.getValidType() == 2) {
                ctrl = "msg_goods_kind_delete";
//				kind.setGoodsKindUnique(ConfigForShopUpdate.DEFAULTKINDUNIQUE);
                Map<String, Object> map = new HashMap<>();
                map.put("shopUnique", kind.getShopUnique());
                map.put("kindUnique", ConfigForShopUpdate.DEFAULTKINDUNIQUE);
                map.put("oldKindUnique", kind.getKindUnique());
                map.put("kindUniques", kindUniques);
                kindDao.useCustomeKind(map);
            }


        } else {//新增
            if (kind.getGoodsKindParunique() == null) {
                sr.setStatus(2);
                sr.setMsg("父级分类不能为空");
                return sr;
            }
            if (kind.getGoodsKindName() == null || kind.getGoodsKindName().trim().equals("")) {
                sr.setStatus(2);
                sr.setMsg("分类名称不能为空");
                return sr;
            }
            if ("0".equals(kind.getGoodsKindParunique())) {
                //只有网页和APP限制上传，收银机不需要上传图标
                if (ObjectUtil.isNotNull(kind.getSourceType()) && (kind.getSourceType() == 2 || kind.getSourceType() == 3)) {
                    if (kind.getKindIconId() == null || Objects.equals(String.valueOf(kind.getKindIconId()), "")) {
                        sr.setStatus(2);
                        sr.setMsg("一级商品分类的图标不能为空");
                        return sr;
                    }
                }
            }
            kind.setGoodsKindUnique(SnowIdUtils.getNextId().toString());//新的分类编号
            //新增分类之前，若大类为自定义分类的常用，则限制小类数量
            if (kind.getGoodsKindParunique().equals(ConfigForShopUpdate.DEFAULTPARUNIQUE)) {
                if (kindDao.queryGoodsKindCount(kind) >= 12) {
                    sr.setStatus(2);
                    sr.setMsg("常用分类的子分类数量最多为12个");
                    return sr;
                }
            }
            ctrl = "msg_goods_kind_add";
            k = kindDao.addNewCustomKinds(kind);
        }
        if (k == 0) {
            sr.setStatus(2);
            sr.setMsg("添加失败！");
        }
        //20220909 新增MQTT -通知收银机有商品分类更新----start
        try {
            if (sr.getStatus() == 1) {
                RedisCache rc = new RedisCache("");
                Object mac = rc.getObject("topic_" + kind.getShopUnique());
                @SuppressWarnings("unchecked")
                List<String> macIdList = (List<String>) mac;
                List<Map<String, Object>> mqttData = new ArrayList<>();
                mqttData = kindDao.queryMqttKind(kind);
                //2 MQTT 发送消息
                for (String macid : macIdList) {
                    Map<String, Object> data = new HashMap<String, Object>();
                    data.put("ctrl", ctrl);//商品分类更新
                    data.put("ID", macid);
                    data.put("status", 200);
                    data.put("data", mqttData);
                    data.put("count", 1);
                    MqttxUtil.sendMapMsg(data, macid);
                }

            }

        } catch (Exception e) {
            log.error("MQTT通知收银机商品分类更新异常：", e);
        }
        //20220909 新增MQTT -通知收银机有商品分类更新----end
        return sr;
    }

    /**
     * 获取当前店铺的分类使用状态
     *
     * @param goodsKind
     * @return
     */
    public ShopsResult getNowKindStatus(GoodsKind goodsKind) {
        ShopsResult sr = new ShopsResult(1, "操作成功！");
        Integer k = kindDao.getNowKindStatus(goodsKind);
        sr.setData(k);
        return sr;
    }

    @Override
    public ShopsResult queryGoodsKindsByShop2(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        map.putAll(kindDao.queryShopKindType(map));//添加当前店铺分类类型
        List<ShopsGroupGoodsKinds> data = kindDao.queryGoodsKindsByShop2(map);
        if (null == data || data.isEmpty()) {
            sr.setData(ShopsUtil.map);
        }
        sr.setStatus(1);
        sr.setMsg("查询成功！");
        sr.setData(data);
        return sr;
    }

    @Override
    public List<GoodsKindByShopUniqueDto> queryGoodsKindByShopService(String shopUnique) {
        return kindDao.queryGoodsKindByShop(shopUnique);
    }

    @Override
    public ShopsResult queryListByIconType(Integer icon_type, String shop_unique) {
        ShopsResult sr = new ShopsResult(1, "查询成功！");
        if (icon_type != 2 && icon_type != 1) {
            return new ShopsResult(0, "图标类型不正确");
        }
        if (icon_type == 2 && (shop_unique == null || shop_unique.isEmpty())) {
            return new ShopsResult(0, "店铺信息不能为空");
        }
        List<Map<String, Object>> list;
        Map<String, Object> map = new HashMap<String, Object>();
        if (icon_type == 2) {
            map.put("shopUnique", shop_unique);
            map.put("iconType", icon_type);
            list = kindDao.queryListByIconTypeAndShopUnique(map);
        } else {
            map.put("iconType", icon_type);
            list = kindDao.queryListByIconType(map);
        }
        for (Map<String, Object> m : list) {
            String pic = (String) m.get("goods_kind_icon_picture");
            m.put("goods_kind_icon_picture", pic == null || pic.isEmpty() ? "" : pic.replaceAll("\\\\", "/"));
        }
        sr.setData(list);
        return sr;
    }

    @Override
    public ShopsResult deleteKind(Long kindUnique, String shopUnique) {
        ShopsResult sr = new ShopsResult();
        List<Long> longs = goodKindUniqueList(shopUnique, kindUnique);
        //查询有无商品属于该分类
        List<Map<String, Object>> maps = goodsDao.queryGoodsByKindAndShopUnique(shopUnique, longs);
        //有则不删
        if (CollectionUtils.isNotEmpty(maps)) {
            sr.setStatus(0);
            sr.setMsg("当前分类已有商品,禁止删除");
            return sr;
        } else {
            //无则删
            int i = kindDao.enableKind(kindUnique, shopUnique);
            if (i > 0) {
                sr.setStatus(1);
                sr.setMsg("操作成功");
            }
            return sr;
        }
    }

    @Override
    public List<Long> goodKindUniqueList(String shopUnique, Long kindUnique) {
        //根据店铺id 和当前传来的分类查询当前分类和所有子集
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", shopUnique);
        map.put("kindUnique", kindUnique);
        return kindDao.selectCommonLevelKind(map);
    }
}
