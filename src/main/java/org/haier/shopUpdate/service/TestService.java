package org.haier.shopUpdate.service;

import java.util.Map;

import org.haier.shopUpdate.util.ShopsResult;

public interface TestService {
	
	/**
	 * 创建临时表
	 * @return
	 */
	public ShopsResult test();
	
	/**
	 * 更新店铺的注册时间
	 * @return
	 */
	public ShopsResult modifyShopTime();
	
	/**
	 * 
	 * @return
	 */
	public ShopsResult modifyGoodsCount();
	
	public ShopsResult modifyShopLong();
	
	public ShopsResult goodsKind();
	
	public ShopsResult addGoods(Map<String,Object> map);
	public ShopsResult querySaleListUnique();
	
	
	/**
	 * 将goods_new中的商品数量添加到goods中
	 * @param shopUnique
	 * @return
	 */
	public ShopsResult modifyShopGoods(String shopUnique);
	
	/**
	 * 
	 * @param path:需要修改的图片路径或文件夹路径
	 * @param size：修改后图片的宽度
	 * @return
	 */
	public ShopsResult modifyGoodsSize(String path,Integer size , Integer type) ;
}
