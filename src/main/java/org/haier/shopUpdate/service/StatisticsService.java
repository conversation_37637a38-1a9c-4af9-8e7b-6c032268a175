package org.haier.shopUpdate.service;

import java.util.Map;

import org.haier.shopUpdate.params.QuerySaleListByPayMethodParams;
import org.haier.shopUpdate.params.QuerySaleListPayMethodBySaleListUniqueParams;
import org.haier.shopUpdate.params.QueryStatisticsByShopParams;
import org.haier.shopUpdate.util.ShopsResult;

public interface StatisticsService {

	/**
	 * 查询订单或退款订单的支付详情
	 * @param params
	 * @return
	 */
	public ShopsResult querySaleListPayMethodBySaleListUnique(QuerySaleListPayMethodBySaleListUniqueParams params);
	/**
	 * 查询各支付方式下的订单列表
	 * @param params
	 * @return
	 */
	public ShopsResult querySaleListByPaymethod(QuerySaleListByPayMethodParams params);
	/**
	 * 统计信息
	 * @return
	 */
	public ShopsResult queryStatisticsByShop(QueryStatisticsByShopParams params);
	/**
	 * 主界面店铺信息统计
	 * @param map
	 * @return
	 */
	public ShopsResult statisticsShopsMessage(Map<String,Object> map);
	
	/**
	 * 流水统计二十四小时图
	 * @param map
	 * @return
	 */
	public ShopsResult turnOverBytime(Map<String,Object> map,Integer showType);
	/**
	 * 新版：主界面信息统计
	 * @param map
	 * @return
	 */
	public ShopsResult queryStatisticsMessageInMain(Map<String,Object> map,String datetime);
	
	/**
	 * 新版：统计界面店铺总览
	 * @param map
	 * @return
	 */
	public ShopsResult businessOverview(Map<String,Object> map);
	
	/**
	 * 新版：分类销量占比
	 * @param map
	 * @return
	 */
	public ShopsResult querySaleSumByKind(Map<String,Object> map);
	

	/**
	 * 新版：营业额走势
	 * @param map
	 * @return
	 */
	public ShopsResult querySaleSumTrend(Map<String,Object> map,Integer checkType);
	
	/**
	 * 销量前五
	 * @param map
	 * @return
	 */
	public ShopsResult queryPreSaleFiveGoods(Map<String,Object> map);
}
