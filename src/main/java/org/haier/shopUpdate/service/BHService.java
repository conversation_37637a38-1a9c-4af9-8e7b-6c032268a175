package org.haier.shopUpdate.service;

import org.haier.shopUpdate.entity.electronic.CreateOrUpdateGoodsListParams;
import org.haier.shopUpdate.entity.electronic.CreateOrUpdateGoodsParams;
import org.haier.shopUpdate.entity.electronic.DeleteGoodsParams;

public interface BHService {
    /**
     * 创建或修改单个商品
     * @param createOrUpdateGoodsParams
     */
    public void createOrUpdateGoods(CreateOrUpdateGoodsParams createOrUpdateGoodsParams);

    /**
     * 批量同步商品信息到电子价签
     * @param createOrUpdateGoodsListParams
     */
    public void createOrUpdateGoodsList(CreateOrUpdateGoodsListParams createOrUpdateGoodsListParams);

    /**
     * 删除商品同步到电子价签
     * @param deleteGoodsParams
     */
    public void deleteGoods(DeleteGoodsParams deleteGoodsParams);
}
