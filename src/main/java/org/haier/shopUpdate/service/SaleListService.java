package org.haier.shopUpdate.service;

import java.util.Map;

import org.haier.shopUpdate.entity.SaleListInvalidReq;
import org.haier.shopUpdate.entity.goods.goodsRecord.GoodsOperParam;
import org.haier.shopUpdate.util.ShopsResult;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

public interface SaleListService {
	
	/**
	 * 
	 * @param shopUnique 店铺编号
	 * @param cusUnique 会员编号
	 * @param saleListPayment 1、现金；2、支付宝；3、微信；4、银行卡
	 * @param goodsMsg
	 * @return
	 */
	@RequestMapping("/saveFarmOrder.do")
	@ResponseBody
	public ShopsResult saveFarmOrder(String shopUnique, String cusUnique,String saleListPayment,String goodsMsg,String staffId,String macId,Double saleListActuallyReceived);
	
	/**
	 * 查询退款订单详情
	 * @param retListUnique
	 * @return
	 */
	public ShopsResult queryReturnDetail(String retListUnique);
	/**
	 * 修改退款订单状态
	 * @param retListUnique 退款单号
	 * @param retListHandlestate 退款审核状态
	 * @param retListRemarks 如果拒绝退款，需要填写拒绝原因
 	 * @param staffId 操作员工ID
	 * @param macId 操作设备的macId或浏览器型号
	 * @return
	 */
	@Transactional
	public ShopsResult modifyReturnMsg(String retListUnique, Integer retListHandlestate, String retListRemarks, String staffId, String macId);
	/**
	 * 查询退款订单的申请信息
	 * @param shopUnique 店铺编号
	 * @param page 页码
	 * @param pageSize 单页查询数量
	 * @param startTime 开始查询时间
	 * @param endTime 结束查询时间
	 * @return
	 */
	public ShopsResult queryRetLists(String shopUnique, Integer page, Integer pageSize, String startTime, String endTime, Integer retListHandlestate);
	/**
	 * 修改话术内容或状态
	 * @param id id
	 * @param delFlag 1、正常；2、删除
	 * @param msg 话术内容
	 * @return
	 */
	public ShopsResult modifyReturnListMsg(String id,Integer delFlag,String msg);
	/**
	 * 添加新的拒绝退款订单话术信息
	 * @param shopUnique
	 * @param staffId
	 * @param msg
	 * @return
	 */
	public ShopsResult addNewReturnListMsg(String shopUnique,String staffId,String msg);
	/**
	 * 查询常用话术
	 * @param shopUnique
	 * @return
	 */
	public ShopsResult queryReturnListMsg(String shopUnique);
	/**
	 * 查询店铺各类型订单的数量
	 * @param map
	 * @return
	 */
	public ShopsResult shopsSaleListCount(Map<String,Object> map);  
	
	
	/**
	 * 网络订单查询
	 * @param map
	 * @return
	 */
	public ShopsResult  querySaleList(Map<String,Object> map);
	
	/**
	 * 订单详情查询
	 * @param map
	 * @return
	 */
	public ShopsResult querySaleListDetail(Map<String,Object> map);
	
	/**
	 * 修改订单支付状态或处理状态
	 * 发货和取消功能，确认订单支付功能
	 * @param map
	 * @return
	 */
	public ShopsResult modifySaleListState(Map<String,Object> map);
	
	/**
	 * 平账
	 */
	public ShopsResult flatOrderAccount(Map<String,Object> map);
	
	
	/**
	 * 经营助手（商品销量分时间段查询）
	 * @param map
	 * @return
	 */
	public ShopsResult querySaleGoodsCount(Map<String,Object> map);
	
	/**
	 * 经营助手
	 * @param map
	 * @return
	 */
	public ShopsResult queryGroupsGoodsSaleMessage(Map<String,Object> map,Integer queryType);

	/**
	 * 百货商家端农批首页统计
	 */
	public ShopsResult indexStatisticsNP(Map<String,Object> map);

	/**
	 * 农批订单作废
	 * @param req
	 * @return
	 */
	public ShopsResult saleListInvalidNP(SaleListInvalidReq req, GoodsOperParam goodsOperParam);

}
