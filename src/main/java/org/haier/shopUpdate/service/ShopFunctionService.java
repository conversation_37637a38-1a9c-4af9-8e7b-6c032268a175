package org.haier.shopUpdate.service;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.haier.shopUpdate.util.ShopsResult;

public interface ShopFunctionService {
	/**
	 * 查询店铺功能列表
	 * @param map
	 * @return
	 */
	public ShopsResult queryShopFunction(Map<String,Object> map);
	
	/**
	 * 更显店铺功能
	 * @param map
	 * @return
	 */
	public ShopsResult modifyShopFunction(Map<String,Object> map);
	
	/**
	 * 查询店铺付款码信息
	 * @param map
	 * @return
	 */
	public ShopsResult queryShopPayCode(Map<String,Object> map);
	

	/**
	 * 修改店铺付款码信息
	 * @param request
	 * @param map
	 * @return
	 * @throws Exception 
	 */
	public ShopsResult modifyPayPic(HttpServletRequest request,Map<String,Object> map) throws Exception;
}
