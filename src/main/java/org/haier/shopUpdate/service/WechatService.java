package org.haier.shopUpdate.service;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.haier.shopUpdate.dao.WechatDao;
import org.haier.shopUpdate.redism.RedisCache;
import org.haier.shopUpdate.util.MyException;
import org.haier.shopUpdate.util.ShopsResult;
import org.haier.shopUpdate.util.UtilForJAVA;
import org.haier.shopUpdate.util.wechat.wxpush.WXPush;
import org.haier.shopUpdate.wechat.AuthUtil;
import org.haier.shopUpdate.wechat.SendMsgAfterLottery;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
* @author: 作者:王恩龙
* @version: 2020年11月10日 下午4:11:23
*
*/
@Slf4j
@Service
public class WechatService {
	@Resource
	private WechatDao wechatDao;

	/**
	 * 1\创建一个聚合支付订单信息
	 * @param shopUnique
	 * @param orderNo
	 * @param payMoney
	 * @param staffId
	 */
	public void createNewOrder(String shopUnique,String orderNo,Double payMoney,String staffId) {
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("sale_list_unique", orderNo);
		map.put("shop_unique", shopUnique);
		map.put("sale_list_total", payMoney);
		map.put("sale_list_pur", payMoney * 0.8);
		map.put("sale_list_totalCount", 1);
		map.put("sale_list_state", 2);
		map.put("sale_list_handlestate", 8);
		map.put("sale_list_actually_received", payMoney);

		wechatDao.addNewOrder(map);

		Map<String,Object> detailMap = new HashMap<String,Object>();
		detailMap.put("sale_list_unique", orderNo);
		detailMap.put("goods_barcode", "999999999");
		detailMap.put("goods_name", "聚合支付收款");
		detailMap.put("sale_list_detail_count", "1");
		detailMap.put("sale_list_detail_price", payMoney);
		detailMap.put("sale_list_detail_subtotal", payMoney);
		detailMap.put("goods_id", "0");
		detailMap.put("goods_purprice", payMoney * 0.8);
		detailMap.put("commission_total", 0.010);
		detailMap.put("goods_old_price", payMoney);
		wechatDao.addSaleListDetail(detailMap);

		detailMap.put("sale_list_detail_total", payMoney);
		wechatDao.addSaleListDetailTotal(detailMap);

	}

	/**
	 * 查询店铺的支付信息和名称
	 * @param shopUnique
	 * @return
	 */
	public Map<String,Object> queryShopMsg(String shopUnique){
		Map<String,Object> map = wechatDao.queryShopMsg(shopUnique);

		return map;
	}
	/**
	 * 查询宁宇会员消费记录
	 * @param cusId 会员ID
	 * @param page 页码
	 * @param rows 每页查询数量
	 * @return
	 */
	public ShopsResult searchRechargeRecord(String cusId,Integer page,Integer rows ) {
		ShopsResult sr = new ShopsResult();
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("startNum", (page-1)*rows);
		map.put("pageSize", rows);
		map.put("cusId", cusId);


		List<Map<String,Object>> list = wechatDao.searchRechargeRecord(map);
		sr.setRows(list);
		sr.setTotal(wechatDao.searchRechargeRecordCount(cusId));

		return sr;
	}

	/**
	 * 查询宁宇会员消费记录
	 * @param cusId 会员ID
	 * @param page 页码
	 * @param rows 每页查询数量
	 * @return
	 */
	public ShopsResult queryCusConsumptionRecord(String cusId,Integer page,Integer rows ) {
		ShopsResult sr = new ShopsResult();

		Map<String,Object> map = new HashMap<String,Object>();
		map.put("startNum", (page-1)*rows);
		map.put("pageSize", rows);
		map.put("cusId", cusId);

		List<Map<String,Object>> list = wechatDao.queryCusConsumptionRecord(map);
		sr.setRows(list);
		sr.setTotal(wechatDao.queryCusConsumptionRecordCount(cusId));

		return sr;
	}

	/**
	 * 查询是否有优惠
	 * @return
	 */
	public Map<String,Object> queryRechargeActive(){
		return wechatDao.queryRechargeActiveMsg();
	}


	@Transactional
	public ShopsResult luckDraw(String saleListUnique,String openId) {
		ShopsResult sr = new ShopsResult();
		Map<String,Object> resMap = new HashMap<String,Object>();
		Integer c = 0;
		RedisCache rc = new RedisCache();
		Object l = rc.getObject(saleListUnique + openId);
		String lot = l == null ? "0" : l.toString();
		if(lot.startsWith("NO")) {
			resMap.put("index", -1);
			resMap.put("msg", "充值结果等待中，请稍后");
			return sr;
		}

		c = Integer.parseInt(lot);
		if(c >= 1) {
			rc.putObject(saleListUnique+openId, c -1);
			/*
			 * 1、查询会员信息
			 * 2、查询充值列表信息，概率配置信息
			 * 3、计算抽奖结果
			 * 4、创建新的充值记录；
			 * 5、创建新的充值使用记录；
			 * 6、修改会员的余额信息；
			 * 7、向会员发送充值通知；
			 */

			//查询会员信息
			Map<String,Object> qMap = new HashMap<String,Object>();
			qMap.put("shopUnique", AuthUtil.SHOPUNIQUE);
			qMap.put("cusWeixin", openId);

			Map<String,Object> cusMap = wechatDao.queryCusMsg(qMap);

			//查询充值信息
			List<Map<String,Object>> rechargeList = wechatDao.queryNingYuRechargeGive();
			BigDecimal b = new BigDecimal(0);
			for(Map<String,Object> rm : rechargeList) {
				b = b.add(new BigDecimal(rm.get("ratio").toString()));
				rm.put("ratio", b.intValue());
			}
			Random r = new Random();

			Float ran = r.nextFloat();
			System.out.println("当前随机数" + ran);
			System.out.println("当前B值" + b);
			b = b.multiply(new BigDecimal(ran));

			Integer index = 1;
			BigDecimal rechargeMoney = null;
			for(Integer j = 1 ;j < rechargeList.size() ; j++) {
				Map<String,Object> rm = rechargeList.get(rechargeList.size() - 1 - j);
				System.out.println(rm.toString() + ":::当前随机数=== " + b);
				if(Integer.parseInt(rm.get("ratio").toString()) < b.doubleValue()) {
					index = Integer.parseInt(rechargeList.get(rechargeList.size() - j).get("id").toString()) ;
					rechargeMoney = new BigDecimal(rechargeList.get(rechargeList.size() - j).get("money").toString());
					break;
				}
			}
			System.out.println("充值金额" + rechargeMoney);
			if(rechargeMoney == null) {
				rechargeMoney = new BigDecimal(rechargeList.get(0).get("money").toString());
				index = Integer.parseInt(rechargeList.get(0).get("id").toString());
			}

			resMap.put("index", index);
			resMap.put("msg", rechargeMoney);

			if(null != cusMap && !cusMap.isEmpty()) {
				Map<String,Object> rechargeMap = new HashMap<String,Object>();
				rechargeMap.put("cusId", cusMap.get("cus_id"));
				rechargeMap.put("rechargeMoney", rechargeMoney);
				rechargeMap.put("recharge_money", rechargeMoney);
				rechargeMap.put("cusBalance", rechargeMoney.add(new BigDecimal(cusMap.get("cus_balance").toString())));
				rechargeMap.put("cusAmount", rechargeMoney.add(new BigDecimal(cusMap.get("cus_amount").toString())));
				rechargeMap.put("saleListUnique", saleListUnique);
				rechargeMap.put("recharge_method", 8);
				rechargeMap.put("give_money", 0);
				rechargeMap.put("shop_unique", AuthUtil.SHOPUNIQUE);
				rechargeMap.put("recharge_status", 1);
				rechargeMap.put("cusType", "1");

				wechatDao.addRecharge(rechargeMap);

				wechatDao.addRechargeUse(rechargeMap);

				rechargeMap.put("sale_points", 0);

				wechatDao.updateCusMsg(rechargeMap);


				//获取最新的会员充值信息
				Map<String,Object> queryMap = new HashMap<String,Object>();
				queryMap.put("shopUnique", rechargeMap.get("shop_unique"));
				queryMap.put("cusWeixin", openId);

				Map<String,Object> newCusMap = wechatDao.queryCusMsg(queryMap);

				//充值成功，向会员推送消息
				String template_id = WXPush.RECHARGETEMPLATEID;
				String token = AuthUtil.ACCESSTOKEN;

				if(null == token) {
					token = WXPush.getToken();
				}

				Map<String,String> pushMap = new HashMap<String,String>();
				pushMap.put("first", "宁宇会员抽奖到帐");
				pushMap.put("keyword1", newCusMap.get("cus_phone").toString());
				pushMap.put("keyword2", rechargeMap.get("recharge_money").toString());
				pushMap.put("keyword3", rechargeMap.get("give_money").toString());
				pushMap.put("keyword4", newCusMap.get("cus_balance").toString());
				pushMap.put("keyword5", UtilForJAVA.getSystemTimeNow());
				pushMap.put("remark", "恭喜您获得" + rechargeMap.get("recharge_money").toString() +"元赠送红包");


				SendMsgAfterLottery s = new SendMsgAfterLottery();
				s.setToken(token);
				s.setOpenId(openId);
				s.setTemplate_id(template_id);
				s.setPushMap(pushMap);
				s.setCusPhone(cusMap.get("cus_phone").toString());
				s.start();
			}
		}else {
			resMap.put("index", -1);
			resMap.put("msg", "您的抽奖次数已用尽!");
		}
		sr.setData(resMap);
		return sr;
	}

	@Transactional
	public boolean completeCusRecharge(String saleListUnique,Integer rechargeMoney) {
		Map<String,Object> rechargeMap = wechatDao.queryCustomerRechargeMsg(saleListUnique);
		rechargeMap.put("saleListUnique", saleListUnique);
		Double rm = Double.parseDouble(rechargeMap.get("recharge_money").toString())*100;
		if(rm.intValue() != rechargeMoney) {
//			return false;
		}
		if(null == rechargeMap || rechargeMap.isEmpty() || rechargeMap.get("recharge_status") == null) {
			return false;
		}else if(rechargeMap.get("recharge_status").toString().equals("1")){
			// recgarge_status : 2、待充值；1、充值成功；0、充值失败
			return true;
		}else if(!rechargeMap.get("recharge_status").toString().equals("1")){
			Integer c = 0;
			//更新会员充值记录状态和信息
			rechargeMap.put("recharge_status", 1);
			//cus_balance，修改为充值后账户余额；cus_amount：宁宇会员专用，修改为充值后，赠额余额（前端收银充值要一起改）
			//此处会员的余额必须重新查询，防止充值过程中有消费或其他操作  cus_rebate
			c = wechatDao.updateCustomerRechargeMsg(rechargeMap);
			if(c == 0) {
				return false;
			}else {
				//更新会员信息
				rechargeMap.put("cusId", rechargeMap.get("cus_id"));
				c = wechatDao.updateCusMsg(rechargeMap);
				if(c == 0) {
					throw new MyException(0,"更新会员信息失败");
				}
				c = wechatDao.addNewCustomerRechargeUse(rechargeMap);
				if(c == 0) {
					throw new MyException(0, "添加会员充值使用记录失败!");
				}else {
					//充值成功，向会员推送消息
					String userId = rechargeMap.get("cus_weixin") == null ? null : rechargeMap.get("cus_weixin").toString();
					String template_id = WXPush.RECHARGETEMPLATEID;
					String token = AuthUtil.ACCESSTOKEN;

					if(null != userId) {
						try {
							//获取最新的会员充值信息
							Map<String,Object> queryMap = new HashMap<String,Object>();
							queryMap.put("shopUnique", rechargeMap.get("shop_unique"));
							queryMap.put("cusWeixin", rechargeMap.get("cus_weixin"));

							Map<String,Object> cusMap = wechatDao.queryCusMsg(queryMap);


							Map<String,String> pushMap = new HashMap<String,String>();
							pushMap.put("first", "宁宇会员充值到帐信息");
							pushMap.put("keyword1", rechargeMap.get("cus_phone").toString());
							pushMap.put("keyword2", rechargeMap.get("recharge_money").toString());
							pushMap.put("keyword3", rechargeMap.get("give_money").toString());
							pushMap.put("keyword4", cusMap.get("cus_balance").toString());
							pushMap.put("keyword5", UtilForJAVA.getSystemTimeNow());
							pushMap.put("remark", "感谢您使用宁宇会员");

							if(null == token) {
								token = WXPush.getToken();
							}
							WXPush.SendWeChatMsg(token, userId, template_id, pushMap);
						}catch (Exception e) {
							System.out.println("发送充值信息失败！");
							log.error("发送充值信息失败！",e);
						}
					}
					return true;
				}
			}
		}
		return false;
	}

	/**
	 * 添加新的会员充值记录
	 * @param map
	 * @return
	 */
	public Map<String,Object> addRecharge(Map<String,Object> map,Double rechargeMoney,String cus_id,String openId) {
		wechatDao.addRecharge(map);

		Map<String,Object> atMap = wechatDao.queryRechargeActive();
		map.put("lotteryCount", 0);
		if(null == atMap || atMap.isEmpty()) {
			return map;
		}
		Double ratio = Double.parseDouble(atMap.get("ratio").toString());
		if(null == ratio || ratio <= 0) {
			return map;
		}
		if(rechargeMoney.compareTo(ratio) >= 0) {
			//充值金额
			System.out.println("充值金额足够");
			Integer type = Integer.parseInt(atMap.get("type").toString());
			RedisCache rc = new RedisCache();
			String id = map.get("saleListUnique").toString() + openId;
			System.out.println("充值时的redisId" + id);
			if(type == 1) {
				map.put("lotteryCount", "1");
				rc.putObject(id,"NO1");
			}else {
				System.out.println("按金额分配次数");
				BigDecimal r = new BigDecimal(ratio);
				BigDecimal rm = new BigDecimal(rechargeMoney);
				System.out.println(r);
				System.out.println(rm);
				rm = rm.divide(r,0,BigDecimal.ROUND_DOWN);
				map.put("lotteryCount", rm.intValue());
				map.put("saleListUnique", map.get("saleListUnique"));
				rc.putObject(id,"NO" + map.get("lotteryCount"));
			}
		}else {
			System.out.println("充值金额不足");
			map.put("lotteryCount", 0);
		}

		return map;
	}

	/**
	 * 添加会员充值使用记录
	 * @param map
	 * @return
	 */
	public ShopsResult addRechargeUse(Map<String,Object> map) {
		ShopsResult sr = new ShopsResult(1,"添加充值使用记录成功");
		wechatDao.addRechargeUse(map);
		return sr;
	}

	/**
	 * 添加会员信息至数据库
	 * @param cus_phone
	 * @param cus_weixin
	 * @param cus_name
	 * @param cus_birthday
	 * @param cus_sex
	 * @return
	 */
	public ShopsResult addNewCusMsg (
			String cus_phone,String cus_weixin,String cus_name,String cus_birthday,String cus_sex
			) {
		ShopsResult sr = new ShopsResult();
		//验证相同手机号和相同微信号的用户是否存在
		Map<String,Object> paramMap = new HashMap<String,Object>();
		paramMap.put("cusWeixin", cus_weixin);
		paramMap.put("shopUnique", AuthUtil.SHOPUNIQUE);

		//获取商户信息是否存在
		Map<String,Object> cusMap = wechatDao.queryCusMsg(paramMap);
		if(null == cusMap || cusMap.isEmpty()) {
			paramMap.remove("cusWeixin");
			paramMap.put("cusPhone", cus_phone);
			cusMap = wechatDao.queryCusMsg(paramMap);
			if(null == cusMap || cusMap.isEmpty()) {
				//会员信息不存在，添加新的会员信息
				//添加新的会员信息
				Calendar cal = Calendar.getInstance();
				paramMap.put("cus_unique", cal.getTimeInMillis());
				paramMap.put("shop_unique", AuthUtil.SHOPUNIQUE);;
				paramMap.put("cus_name", cus_name);
				paramMap.put("cus_phone", cus_phone);
				paramMap.put("cus_total", 0);
				paramMap.put("total_points", 0);
				paramMap.put("cus_points", 0);
				paramMap.put("cus_birthday", cus_birthday);
				paramMap.put("cus_count", 0);
				paramMap.put("cus_amount", 0);
				paramMap.put("cus_use", 0);
				paramMap.put("cus_balance", 0);
				paramMap.put("cus_weixin", cus_weixin);
				paramMap.put("cus_sex", 0);
				paramMap.put("cus_head_path", "");
				//获取会员等级ID信息
				paramMap.put("cus_level_id", wechatDao.getCusLevelId(paramMap));

				System.out.println("会员信息"+paramMap.toString());

				paramMap.put("cus_type", "会");
				wechatDao.addNewCusMsg(paramMap);
				paramMap.put("cus_type", "储");
				wechatDao.addNewCusMsg(paramMap);
				paramMap.put("status", 1);
				sr.setStatus(1);
				sr.setMsg("创建会员信息成功！");
			}else {
				if(null == cusMap.get("cus_weixin") || cusMap.get("cus_weixin").toString().equals("")) {
					//相同手机号，但是微信信息未绑定的用户，更新用户信息
					paramMap.put("cus_weixin", cus_weixin);
					paramMap.put("cus_name", cus_name);
					paramMap.put("cus_birthday", cus_birthday);
					paramMap.put("cus_sex", cus_sex);
					wechatDao.modifyCusMsg(paramMap);
					sr.setStatus(1);
					sr.setMsg("创建会员信息成功！");
				}else {
					sr.setStatus(0);
					sr.setMsg("该手机号已绑定其他会员，请勿重复绑定");
				}
			}
		}else {
			String phoneNum = cusMap.get("cus_phone") == null ? "":cusMap.get("cus_phone").toString();
			if(phoneNum.equals(cus_phone)) {
				//更会会员信息
				paramMap.put("cusPhone", cus_phone);
				paramMap.put("cus_birthday", cus_birthday);
				paramMap.put("cus_sex", cus_sex);
				paramMap.put("cus_name", cus_name);

				wechatDao.modifyCusMsg(paramMap);
				sr.setStatus(1);
				sr.setMsg("创建会员信息成功！");
			}else {
				sr.setMsg("该会员已绑定其他");
				sr.setStatus(0);
			}
		}

		return sr;
	}

	public Map<String,Object> getCusMsg(String cus_weixin){
		String shopUnique = AuthUtil.SHOPUNIQUE;
		Map<String,Object> result = new HashMap<String,Object>();
		Map<String,Object> param = new HashMap<String,Object>();
		param.put("shopUnique", shopUnique);
		param.put("cusWeixin", cus_weixin);

		result = wechatDao.queryCusMsg(param);

		return result;
	}

	public Map<String,Object> checkCusMsg(
			String cus_phone,String cus_weixin,String cus_name,String cus_birthday,String cus_sex
			) {

		Map<String,Object> paramMap = new HashMap<String,Object>();
		paramMap.put("cusWeixin", cus_weixin);
		paramMap.put("shopUnique", AuthUtil.SHOPUNIQUE);


		System.out.println("当前校验的用户数据为：：："+cus_weixin);

		//获取商户信息是否存在
		Map<String,Object> cusMap = wechatDao.queryCusMsg(paramMap);
		//如果没有会员信息，新增会员信息，如果有会员信息，判断是否需要更新
		if(null == cusMap || cusMap.isEmpty()) {
			cusMap = cusMap == null ? new HashMap<String,Object>():cusMap;
			cusMap.put("cus_weixin", cus_weixin);
			cusMap.put("cus_phone", cus_phone);
			cusMap.put("status", 0);
		}else {
			cusMap.put("status", 1);
		}
		return cusMap;
	}
}
