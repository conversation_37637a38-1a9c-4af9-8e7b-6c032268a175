package org.haier.shopUpdate.service;

import org.haier.shopUpdate.util.ShopsResult;

public interface AppPayService {


	ShopsResult addGoodsToCar(String shop_unique, String sale_list_unique, String goods_barcode,
			Double sale_list_detail_count, Double sale_list_detail_price, String goods_type, Integer sale_list_cashier, Integer table_id);

	ShopsResult editGoodsToCar(String shop_unique, String sale_list_unique, String goods_barcode,
			Double sale_list_detail_count, Double sale_list_detail_price, Double goods_cus_price);

	ShopsResult querySaleListWaitList(String shop_unique, Integer sale_list_cashier);

	ShopsResult cancelSaleListWait(String shop_unique, String sale_list_unique);

	ShopsResult querySaleListDetailWait(String shop_unique, String sale_list_unique);

	ShopsResult queryLatelySaleListDetailWait(String shop_unique);

	ShopsResult addCusJoinSaleList(String shop_unique, String sale_list_unique, String cus_unique, String sale_list_remarks);

	ShopsResult searchGoods(String shop_unique, String goods_message);

	ShopsResult deleteGoods(String sale_list_detail_id);

	ShopsResult shop_pay(String shop_unique, String sale_list_unique, Integer sale_list_payment,
			Double sale_list_discount, Double sale_list_total, Double sale_list_actually_received,
			String sale_list_remarks, String spbill_create_ip, String auth_code);

	ShopsResult queryFoodTableList(String shop_unique);

	ShopsResult addFoodTable(String shop_unique, String table_name);

	ShopsResult deleteFoodTable(String id);

	ShopsResult upadatePrintCount(String shop_unique, String sale_list_unique, String goods_barcode,
			String print_count);
	
}
