package org.haier.shopUpdate.service;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.haier.shopUpdate.dao.CollectionPageDao;
import org.haier.shopUpdate.dao.SaleListDao;
import org.haier.shopUpdate.dao.ShopsConfigDao;
import org.haier.shopUpdate.entity.ShopsConfig;
import org.haier.shopUpdate.enums.PayMethodIconsEnums;
import org.haier.shopUpdate.params.QuerySaleListByPayMethodParams;
import org.haier.shopUpdate.params.QuerySaleListPayMethodBySaleListUniqueParams;
import org.haier.shopUpdate.params.QueryStatisticsByShopParams;
import org.haier.shopUpdate.project.ProjectConfig;
import org.haier.shopUpdate.result.statistics.*;
import org.haier.shopUpdate.util.ShopsResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
@Slf4j
@Service
@Transactional
public class StatisticsServiceImpl implements StatisticsService{
	@Resource
	private CollectionPageDao pageDao;
	@Resource
	private SaleListDao saleListDao;
	@Resource
	private ShopsConfigDao shopsConfigDao;



	/**
	 * 查询订单或退款订单的支付详情
	 * @param params
	 * @return
	 */
	public ShopsResult querySaleListPayMethodBySaleListUnique(QuerySaleListPayMethodBySaleListUniqueParams params) {
		List<QuerySaleListPayMethodBySaleListUniqueResilt> resilt = saleListDao.querySaleListPayMethodBySaleListUnique(params);
		for (QuerySaleListPayMethodBySaleListUniqueResilt r : resilt) {
			r.setPayMethodIcon(PayMethodIconsEnums.getIconByMethod(r.getPayMethod()));
		}
		return ShopsResult.ok(resilt);
	}

	/**
	 * 查询各支付方式下的订单列表
	 * @param params
	 * @return
	 */
	public ShopsResult querySaleListByPaymethod(QuerySaleListByPayMethodParams params) {
		System.out.println("查询各支付方式的订单" + params);

		QuerySaleListByPayMethodResult result = new QuerySaleListByPayMethodResult();

		QueryStatisticsStatis queryStatisticsStatis = new QueryStatisticsStatis();
		QueryStatisticsStatis queryStatisticsStatisShop = saleListDao.queryStatisticsByShop(params);
//		QueryStatisticsStatis queryStatisticsStatisCanyin = saleListDao.queryStatisticsByShopCanyin(params);
		if (ObjectUtil.isNull(params.getShopType())) {
			queryStatisticsStatis = hebingQueryStatisticsStatis(new QueryStatisticsStatis(),queryStatisticsStatisShop);
		} else if (ObjectUtil.isNotNull(params.getShopType())) {
			switch (params.getShopType()) {
				case 1:
				case 2:
				case 3:
				case 4:
				case 5:
				case 6:
				case 7:
				case 8:
				case 9:
				case 10:
				case 11:
				case 12:
					queryStatisticsStatis = queryStatisticsStatisShop;
					break;
				case 13:
//					queryStatisticsStatis = queryStatisticsStatisCanyin;
					break;
				default:
					break;
			}
		}
 		List<QuerySaleListByPayMethodEntity> list = null;
		Integer count = null;
		Integer shopType = params.getShopType();
		if (ObjectUtil.isNull(params.getOrderType()) || params.getOrderType() == 1) {
			list = saleListDao.querySaleListByPaymethodSale(params);
			count = saleListDao.querySaleListByPaymethodCountSale(params);

		} else if (params.getOrderType() == 2) {
			list = saleListDao.querySaleListByPaymethodRet(params);
			count = saleListDao.querySaleListByPaymethodCountRet(params);
		}
		//合并相同字段数据
		result.setQueryStatisticsStatis(queryStatisticsStatis);
		result.setOrderCount(count);
		result.setList(list);

		return ShopsResult.ok(result);
	}

	/**
	 * 合并相同的数据
	 * @param a
	 * @param b
	 * @return
	 */
	public QueryStatisticsStatis hebingQueryStatisticsStatis(QueryStatisticsStatis a, QueryStatisticsStatis b) {
		QueryStatisticsStatis result = new QueryStatisticsStatis();
		BigDecimal saleListTotalA = a.getSaleListTotal();
		BigDecimal saleListTotalB = b.getSaleListTotal();
		result.setSaleListTotal(addBigDecimal(saleListTotalA,saleListTotalB));

		BigDecimal saleListTotalMoneyA = a.getSaleListTotalMoney();
		BigDecimal saleListTotalMoneyB = b.getSaleListTotalMoney();
		result.setSaleListTotalMoney(addBigDecimal(saleListTotalMoneyA,saleListTotalMoneyB));

		BigDecimal saleListRefundMoneyA = a.getSaleListRefundMoney();
		BigDecimal saleListRefundMoneyB = b.getSaleListRefundMoney();
		result.setSaleListRefundMoney(addBigDecimal(saleListRefundMoneyA,saleListRefundMoneyB));

		Integer saleListCountA = a.getSaleListCount();
		Integer saleListCountB = b.getSaleListCount();
		result.setSaleListCount(addInt(saleListCountA,saleListCountB));

		return result;
	}
	/**
	 * 统计信息
	 * @return
	 */
	public ShopsResult queryStatisticsByShop(QueryStatisticsByShopParams params) {
		System.out.println("查询统计信息" + params);
		if (ObjectUtil.isNull(params.getShopUnique())) {
			return ShopsResult.fail("店铺唯一码不能为空");
		}
		if (ObjectUtil.isNull(params.getStartTime())||ObjectUtil.isNull(params.getEndTime())) {
			return ShopsResult.fail("时间不能为空");
		}
		//查看店铺是否查询餐饮订单
		ShopsConfig shopsConfig = shopsConfigDao.selectByShopUnique(params.getShopUnique());
		QueryStatisticsByShopResult result = new QueryStatisticsByShopResult();
		//总的统计部分，由线上和线下两部分组成
		QueryStatisticsStatis statis = new QueryStatisticsStatis();
		QueryStatisticsStatis resultOnline = new QueryStatisticsStatis();
		QueryStatisticsStatis resultUnline = new QueryStatisticsStatis();

		//统计线上部分
		QueryStatisticsByShopEntity onlineStatis = new QueryStatisticsByShopEntity();
		//统计线下部分
		QueryStatisticsByShopEntity unlineStatis = new QueryStatisticsByShopEntity();
		//店铺统计线上部分
		QueryStatisticsByShopEntity onlineStatisShop = new QueryStatisticsByShopEntity();
		//店铺统计线下部分
		QueryStatisticsByShopEntity unlineStatisShop = new QueryStatisticsByShopEntity();
		//餐饮统计线上部分
		QueryStatisticsByShopEntity onlineStatisCanYin = new QueryStatisticsByShopEntity();
		//餐饮统计线下部分
		QueryStatisticsByShopEntity unlineStatisCanYin = new QueryStatisticsByShopEntity();

		//查询saleList订单
		//查询总的统计数据
		resultOnline = saleListDao.queryStatisticsByShop(params);

		params.setSaleType(2);
		QueryStatisticsStatis queryStatisticsStatisOn = saleListDao.queryStatisticsByShop(params);
		onlineStatisShop.setQueryStatisticsStatis(queryStatisticsStatisOn);

		List<QueryStatisticsSubEntity> onList = saleListDao.queryPayMethodStatis(params);
		for (QueryStatisticsSubEntity entity : onList) {
			entity.setSaleType(1);
			entity.setPayMethodIcon(PayMethodIconsEnums.getIconByMethod(entity.getPayMethod()));
		}
		onlineStatisShop.setList(onList);


		params.setSaleType(0);
		QueryStatisticsStatis queryStatisticsStatisUn = saleListDao.queryStatisticsByShop(params);
		unlineStatisShop.setQueryStatisticsStatis(queryStatisticsStatisUn);

		List<QueryStatisticsSubEntity> unList = saleListDao.queryPayMethodStatis(params);
		for (QueryStatisticsSubEntity entity : unList) {
			entity.setSaleType(0);
			entity.setPayMethodIcon(PayMethodIconsEnums.getIconByMethod(entity.getPayMethod()));
		}
		unlineStatisShop.setList(unList);

		//将餐饮的和非餐饮的数据合并
		QueryStatisticsStatis queryStatisticsStatis = new QueryStatisticsStatis();
		List<QueryStatisticsSubEntity> list = new ArrayList<>();
		QueryStatisticsStatis onlineStatisData = onlineStatisShop.getQueryStatisticsStatis();
		QueryStatisticsStatis onlineStatisDataCanYin = onlineStatisCanYin.getQueryStatisticsStatis();
		if (ObjectUtil.isNull(onlineStatisData)) {
			onlineStatisData = new QueryStatisticsStatis();
		}
		if (ObjectUtil.isNull(onlineStatisDataCanYin)) {
			onlineStatisDataCanYin = new QueryStatisticsStatis();
		}
		BigDecimal saleListTotal = addBigDecimal(onlineStatisData.getSaleListTotal(),onlineStatisDataCanYin.getSaleListTotal());
		BigDecimal saleListTotalMoney = addBigDecimal(onlineStatisData.getSaleListTotalMoney(),onlineStatisDataCanYin.getSaleListTotalMoney());
		BigDecimal saleListRefundMoney = addBigDecimal(onlineStatisData.getSaleListRefundMoney(),onlineStatisDataCanYin.getSaleListRefundMoney());
		Integer saleListCount = addInt(onlineStatisData.getSaleListCount() , onlineStatisDataCanYin.getSaleListCount());
		queryStatisticsStatis.setSaleListCount(saleListCount);
		queryStatisticsStatis.setSaleListTotal(saleListTotal);
		queryStatisticsStatis.setSaleListTotalMoney(saleListTotalMoney);
		queryStatisticsStatis.setSaleListRefundMoney(saleListRefundMoney);
		if (ObjectUtil.isNotEmpty(onlineStatisShop.getList())) {
			list.addAll(onlineStatisShop.getList());
		}
		if (ObjectUtil.isNotEmpty(onlineStatisCanYin.getList())) {
			list.addAll(onlineStatisCanYin.getList());
		}
		onlineStatis.setQueryStatisticsStatis(queryStatisticsStatis);
		onlineStatis.setList(list);

		//线下部分合并
		QueryStatisticsStatis unlineStatisData = unlineStatisShop.getQueryStatisticsStatis();
		QueryStatisticsStatis unlineStatisDataCanYin = unlineStatisCanYin.getQueryStatisticsStatis();
		if (ObjectUtil.isNull(unlineStatisData)) {
			unlineStatisData = new QueryStatisticsStatis();
		}
		if (ObjectUtil.isNull(unlineStatisDataCanYin)) {
			unlineStatisDataCanYin = new QueryStatisticsStatis();
		}
		QueryStatisticsStatis queryStatisticsStatisUnline = saleListDao.queryStatisticsByShop(params);
		List<QueryStatisticsSubEntity> listUnline = new ArrayList<>();
		BigDecimal saleListTotalUnile = addBigDecimal(unlineStatisData.getSaleListTotal(),unlineStatisDataCanYin.getSaleListTotal());
		BigDecimal saleListTotalMoneyUnile = addBigDecimal(unlineStatisData.getSaleListTotalMoney(),unlineStatisDataCanYin.getSaleListTotalMoney());
		BigDecimal saleListRefundMoneyUnile = addBigDecimal(unlineStatisData.getSaleListRefundMoney(),unlineStatisDataCanYin.getSaleListRefundMoney());
		Integer saleListCountUnile = addInt(unlineStatisData.getSaleListCount() , unlineStatisDataCanYin.getSaleListCount());
		queryStatisticsStatisUnline.setSaleListCount(saleListCountUnile);
		queryStatisticsStatisUnline.setSaleListTotal(saleListTotalUnile);
		queryStatisticsStatisUnline.setSaleListTotalMoney(saleListTotalMoneyUnile);
		queryStatisticsStatisUnline.setSaleListRefundMoney(saleListRefundMoneyUnile);
		if (ObjectUtil.isNotEmpty(unlineStatisShop.getList())) {
			listUnline.addAll(unlineStatisShop.getList());
		}
		if (ObjectUtil.isNotEmpty(unlineStatisCanYin.getList())) {
			listUnline.addAll(unlineStatisCanYin.getList());
		}
		unlineStatis.setQueryStatisticsStatis(queryStatisticsStatisUnline);
		unlineStatis.setList(listUnline);

		//将线上和线下部分合并
		if (ObjectUtil.isNull(resultOnline)) {
			resultOnline = new QueryStatisticsStatis();
		}
		if (ObjectUtil.isNull(resultUnline)) {
			resultUnline = new QueryStatisticsStatis();
		}
		statis.setSaleListTotal(addBigDecimal(resultOnline.getSaleListTotal(),resultUnline.getSaleListTotal()));
		statis.setSaleListCount(addInt(resultOnline.getSaleListCount(),resultUnline.getSaleListCount()));
		statis.setSaleListRefundMoney(addBigDecimal(resultOnline.getSaleListRefundMoney(),resultUnline.getSaleListRefundMoney()));
		statis.setSaleListTotalMoney(addBigDecimal(resultOnline.getSaleListTotalMoney(),resultUnline.getSaleListTotalMoney()));

		result.setQueryStatisticsStatis(statis);
		result.setOnlineStatis(onlineStatis);
		result.setUnlineStatis(unlineStatis);
		return ShopsResult.ok(result);
	}

	public static BigDecimal addBigDecimal(BigDecimal a,BigDecimal b) {
		if (ObjectUtil.isNull(a)) {
			a = BigDecimal.ZERO;
		}
		if (ObjectUtil.isNull(b)) {
			b = BigDecimal.ZERO;
		}
		return a.add(b).setScale(2, BigDecimal.ROUND_HALF_UP);
	}

	public static Integer addInt(Integer a,Integer b) {
		if (ObjectUtil.isNull(a)) {
			a = 0;
		}
		if (ObjectUtil.isNull(b)) {
			b = 0;
		}
		return a + b;
	}
	/**
	 * 主界面店铺信息统计
	 * @param map
	 * @return
	 */
	public ShopsResult statisticsShopsMessage(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		System.out.println("aaa");
		//分别统计当日浏览量和总浏览量
		List<Map<String,Object>> data1;
//		System.out.println(map);
		data1=pageDao.queryBrowserCount(map);
		Map<String,Object> data;
		if(null==data1||data1.isEmpty()){
			data=new HashMap<String, Object>();
		}else{
			data=data1.get(0);
		}

		if(null==data||data.isEmpty()){
			data=new HashMap<String, Object>();
			data.put("lastWeekAccount", 0);
			data.put("listProfit",0);
			data.put("weekRunAccount",0);
			data.put("monthRunAccount", 0);
			data.put("count", 0);
			data.put("lastDayAccount", 0);
			data.put("listTotal", 0);
			data.put("stockTotal", 0);
			data.put("allCount", 0);
			data.put("listCount", 0);
			data.put("todayAccount", 0);
			data.put("lastMonthAccount",0);
			sr.setStatus(1);
			sr.setData(data);
			return sr;
		}
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}
	/**
	 * 流水统计二十四小时图
	 * @param map
	 * @return
	 */
	public ShopsResult turnOverBytime(Map<String,Object> map,Integer showType){
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> data=null;
		if(showType==1){
			data= pageDao.turnOverBytime(map);
		}
		if(showType==2){
			data=pageDao.turnOverByDay(map);
		}
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}
	/**
	 * 新版：主界面信息统计
	 * @param map
	 * @return
	 */
	public ShopsResult queryStatisticsMessageInMain(Map<String,Object> map,String datetime){
		ShopsResult sr=new ShopsResult();
		Map<String,Object> data = pageDao.queryStatisticsMessageInMain(map);
		//Map<String,Object> cyData = pageDao.queryStatisticsMessageInMainCanyin(map);
		System.out.println(map);

		Map<String,Object> d1 = pageDao.signOutStatisticsAll(map);

		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");

		Long time = 0l;
		try {
			time = format.parse(datetime).getTime() - 24*60*60*1000;
		} catch (ParseException e) {
			log.error("时间转换异常：",e);
		}

		String datebefor = format.format(new Date(time));
		map.put("startTime", datebefor);
		map.put("endTime", datetime);

		Map<String,Object> d2 = pageDao.signOutStatisticsAll(map);

		if(null == d1 || d1.isEmpty()) {
			sr.setStatus(1);
			data=new HashMap<String, Object>();
			sr.setMsg("今日无相关信息！");
			data.put("unitPrice", 0);
			data.put("saleSum", 0);
			data.put("listCount", 0);
			data.put("percent", 0);
			data.put("profit", 0);
			data.put("pur", 0);
			sr.setData(data);
			return sr;
		}else {
			if(null == d2 || d2.isEmpty()) {
				d1.put("percent", 100);
			}else {
				BigDecimal b1 = new BigDecimal(d1.get("saleSum").toString());
				BigDecimal b2 = new BigDecimal(d2.get("saleSum").toString());
				if(b1.compareTo(new BigDecimal(0)) == 0) {
					d1.put("percent", 0);
				}else if(b2.compareTo(new BigDecimal(0.0)) == 0) {
					d1.put("percent", 100);
				}else {
					d1.put("percent", b1.multiply(new BigDecimal(100)).divide(b2,2,BigDecimal.ROUND_HALF_UP));
				}
			}
			BigDecimal unitPrice = new BigDecimal(String.valueOf(d1.get("unitPrice")));
			unitPrice = unitPrice.setScale(2, RoundingMode.HALF_UP);
			d1.put("unitPrice",unitPrice);
			sr.setData(d1);
		}


//		if(null==data||data.isEmpty()){
//			sr.setStatus(1);
//			data=new HashMap<String, Object>();
//			sr.setMsg("今日无相关信息！");
//			data.put("unitPrice", 0);
//			data.put("saleSum", 0);
//			data.put("listCount", 0);
//			data.put("percent", 0);
//			data.put("profit", 0);
//			sr.setData(data);
//			return sr;
//		}
		BigDecimal unitPrice = new BigDecimal(String.valueOf(data.get("unitPrice")));
		unitPrice = unitPrice.setScale(2, RoundingMode.HALF_UP);
		data.put("unitPrice",unitPrice);
		sr.setStatus(1);
		sr.setMsg("查询成功！");
//		sr.setData(data);
		sr.setObject(data);
		return sr;
	}
	/**
	 * 新版：统计界面店铺总览
	 * @param map
	 * @return
	 */
	public ShopsResult businessOverview(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		Map<String,Object> data=pageDao.businessOverview(map);

		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}
	/**
	 * 新版：分类销量占比
	 * @param map
	 * @return
	 */
	public ShopsResult querySaleSumByKind(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> data=pageDao.querySaleSumByKind(map);
//		for(Map<String,Object> m:data){
//			System.out.println(m.get("kindName").toString()+"////"+m.get("sum").toString());
//		}
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}


	/**
	 * 新版：营业额走势
	 * @param map
	 * @return
	 */
	public ShopsResult querySaleSumTrend(Map<String,Object> map,Integer checkType){
		ShopsResult sr=new ShopsResult();

		List<Map<String,Object>> data=null;
		if(checkType==1){
			data=pageDao.querySaleSumTrend(map);
		}
		if(checkType==2||checkType==3){
			data=pageDao.querySaleSumTrendDay(map);
		}
		System.out.println(map);
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}
	/**
	 * 销量前五
	 * @param map
	 * @return
	 */
	public ShopsResult queryPreSaleFiveGoods(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> data=pageDao.queryPreSaleFiveGoods(map);

		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}
}
