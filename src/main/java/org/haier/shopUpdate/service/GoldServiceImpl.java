package org.haier.shopUpdate.service;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.shopUpdate.dao.GoldDao;
import org.haier.shopUpdate.util.ShopsResult;
import org.springframework.stereotype.Service;

/**
* @author: 作者:王恩龙
* @version: 2020年7月4日 上午11:23:51
*
*/
@Service
public class GoldServiceImpl implements GoldService{
	@Resource
	private GoldDao goldDao;
	
	/**
	 * 获取当前店铺的金圈币余额
	 * @param shopUnique
	 * @return
	 */
	public ShopsResult queryShopJQB(String shopUnique) {
		ShopsResult sr = new ShopsResult();
		Map<String,Object> map = new HashMap<>();
		map.put("shopUnique", shopUnique);
		//获取店铺的金圈币信息
		Double jqbCount = goldDao.queryShopJQB(map); 
		if(null == jqbCount) {
			jqbCount = 0.0;
		}
		//获取未领取的金圈币信息
		Map<String,Object> jqbMap = goldDao.queryGoldGrant(map);
		sr.setData(jqbCount);
		sr.setObject(jqbMap);
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		
		return sr;
	}
	
	
	public ShopsResult modifyShopGold(String shopUnique,String goldGrantId) {
		ShopsResult sr = new ShopsResult();
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		map.put("goldGrantId", goldGrantId);
		
		//查询领取状态，防止重复领取
		Map<String,Object> jqbMap = goldDao.queryGoldGrant(map);
		if(null == jqbMap || !jqbMap.get("receive_status").toString().equals("1")) {
			sr.setStatus(0);
			sr.setMsg("该优惠已领取或已过期");
			return sr;
		}
		
		//修改为已领取状态
		map.put("receiveStatus", 2);
		goldDao.grantGoldById(map);
		//需要增加的金圈币数量
		map.put("jqbCount", jqbMap.get("jqb_count"));
		
		//查询店铺金圈币信息，如果没有则新增，如果有则更新
		Integer c = goldDao.modifyShopGold(map);
		if(c == 0 ) {
			goldDao.addNewShopGold(map);
		}
		sr.setStatus(1);
		sr.setMsg("领取成功");
		sr.setData(goldDao.queryShopJQB(map));
		return sr;
	}
}
