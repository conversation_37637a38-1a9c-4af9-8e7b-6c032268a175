package org.haier.shopUpdate.service;

import org.haier.shopUpdate.params.shopSupBill.AddSupRelParams;
import org.haier.shopUpdate.params.shopSupBill.externalCall.GoodsInfoParams;
import org.haier.shopUpdate.params.shopSupBill.externalCall.ShopGoodsInfoParams;
import org.haier.shopUpdate.params.shopSupplier.*;
import org.haier.shopUpdate.util.ShopsResult;

import java.util.Map;

public interface ShopSupplierService {
    /**
     * 添加供应商分类信息
     *
     * @param params
     * @return
     */
    ShopsResult addSupKind(SupKindAddParams params);

    /**
     * 修改或删除供应商分类信息
     *
     * @param params
     * @return
     */
    ShopsResult modifySupKind(SupKindModifyParams params);

    /**
     * 更新供货商分类排序
     * @param params
     * @return
     */
    ShopsResult updateSupKindSort(SupKindSortUpdateParams params);

    /**
     * 查询供货商分类信息列表
     * @param params
     * @return
     */
    ShopsResult querySupKindList(ShopUniqueParams params);

    /**
     * 供货商端调用接口-添加供货商
     * @param params
     * @return
     */
    ShopsResult addShopSupRel(AddSupRelParams params);
    /**
     * 添加供应商信息
     *
     * @param params
     * @return
     */
    ShopsResult addSupInfo(SupInfoAddParams params);
    /**
     * 修改供应商信息
     *
     * @param params
     * @return
     */
    ShopsResult updateSupInfo(SupInfoUpdateParams params);

    /**
     * 删除供货商信息
     * @param params
     * @return
     */
    ShopsResult deleteSupInfo(SupplierIdParams params);
    /**
     * 查询供应商列表信息
     *
     * @param params
     * @return
     */
    ShopsResult querySupList(QuerySupListParams params);

    /**
     * 查询供应商信息
     *
     * @param params
     * @return
     */
    ShopsResult querySupInfo(SupplierUniqueParams params);

    /**
     * 查询供应商详细信息-业务信息
     * @param params
     * @return
     */
    ShopsResult querySupBusinessInfo(SupplierUniqueParams params);
    /**
     * 查询供应商详细信息-购销订单信息
     * @param params
     * @return
     */
    ShopsResult querySupBillInfo(QueryBillListParams params);
    /**
     * 查询供应商详细信息-付款记录信息
     * @param params
     * @return
     */
    ShopsResult querySupPaymentInfo(SupplierUniqueWithPageParams params);

    /**
     * 添加单个店铺多商品信息
     * @param params
     * @return
     */
    ShopsResult addGoods(GoodsInfoParams params);

    /**
     * 添加多个店铺单个商品信息
     * @param params
     * @return
     */
    ShopsResult addShopsGood(ShopGoodsInfoParams params);

    /**
     * 查询供应商给店铺所供商品列表（包括未建档和已建档）
     * @param params
     * @return
     */
    ShopsResult querySupRecordGoodList(QueryRecordGoodsListParams params);

    /**
     * 查询店铺与供应商还款信息
     * @param params
     * @return
     */
    ShopsResult queryQepaymentInfo(QueryRepayHisInfoParams params);
    /**
     * 查询店铺与供应商未还款购销单
     */
    ShopsResult queryUnpaidBillList(SupplierUniqueParams params);
    /**
     * 多选购销单进行还款
     */
    ShopsResult repaymentBills(RepaymentBillsParams params);

    ShopsResult updateSupRecordGood(SupRecordGoodsUpdateParams params);

    /**
     * 绑定供货商
     * @param params
     * @return
     */
    ShopsResult bindSupplier(BindSupParams params);

    /**
     * 根据查询条件查询供货商申请列表
     * @param params
     * @return
     */
    ShopsResult querySupExamineList(QuerySupListParams params);

    /**
     * 根据查询条件查询供货商申请列表
     * @param params
     * @return
     */
    ShopsResult deleteShopSupGoodsEntity(ShopSupGoodsDeleteParams params);
    /**
     * 商品编辑页面添加供货商
     * @param params（shopUnique：店铺编号；goodsBarcode：商品条码；supplierUnique：供货商编号）
     * @return
     */
    ShopsResult addSupGood(Map<String,Object> params);
}
