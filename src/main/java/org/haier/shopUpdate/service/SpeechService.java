package org.haier.shopUpdate.service;

import org.haier.shopUpdate.entity.SpeechCmdEntity;
import org.haier.shopUpdate.entity.SpeechListEntity;
import org.haier.shopUpdate.params.speech.AddNewSpeechEntityParams;
import org.haier.shopUpdate.params.speech.QueryCommonSpeechListParams;
import org.haier.shopUpdate.util.NewResultObject;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

public interface SpeechService {
        /**
     * 上传一条新的语音记录，创建并向服务器发送请求
     *
     * @param addNewSpeechEntityParams
     */
    public NewResultObject<SpeechListEntity> addNewSpeechEntity(AddNewSpeechEntityParams addNewSpeechEntityParams, HttpServletRequest request) throws Exception;

    /**
     * 查询常用语音列表
     * @param queryCommonSpeechListParams
     * @return
     */
    public NewResultObject<List<SpeechCmdEntity>> queryCommonSpeechList(QueryCommonSpeechListParams queryCommonSpeechListParams);
}
