package org.haier.shopUpdate.service.goods;

import org.apache.commons.beanutils.BeanUtils;
import org.haier.shopUpdate.dao.GoodsDao;
import org.haier.shopUpdate.dao.ShopsDao;
import org.haier.shopUpdate.dao.dojo.QueryGoodsInfoDo;
import org.haier.shopUpdate.dao.goods.AllocationShopDao;
import org.haier.shopUpdate.dao.goods.AllocationShopDetailDao;
import org.haier.shopUpdate.dao.pojo.QueryAllocationPo;
import org.haier.shopUpdate.dto.QueryGoodsByGoodsBarcodeDto;
import org.haier.shopUpdate.entity.ShopsEntity;
import org.haier.shopUpdate.entity.goods.AllocationShopDetailEntity;
import org.haier.shopUpdate.entity.goods.AllocationShopEntity;
import org.haier.shopUpdate.enums.AllocationShopStatus;
import org.haier.shopUpdate.params.goods.allocation.AddAllocateShopsDetail;
import org.haier.shopUpdate.params.goods.allocation.AddAllocateShopsParams;
import org.haier.shopUpdate.params.goods.allocation.FinishAllocateParams;
import org.haier.shopUpdate.params.goods.allocation.QueryAllocationParams;
import org.haier.shopUpdate.result.goods.allocation.AllocationShopDetailInfo;
import org.haier.shopUpdate.result.goods.allocation.AllocationShopInfo;
import org.haier.shopUpdate.result.goods.allocation.QueryAllocationListResult;
import org.haier.shopUpdate.service.GoodsService;
import org.haier.shopUpdate.util.I18nLanguageMsgUtil;
import org.haier.shopUpdate.util.ShopsResult;
import org.haier.shopUpdate.util.UUIDUtil;
import org.haier.shopUpdate.util.common.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class AllocateService {
    @Resource
    AllocationShopDao allocationShopDao;
    @Resource
    AllocationShopDetailDao allocationShopDetailDao;
    @Resource
    GoodsService goodsService;
    @Resource
    ShopsDao shopsDao;

    @Resource
    GoodsDao goodsDao;
    @Resource
    private I18nLanguageMsgUtil i18nLanguageMsgUtil;


    public ShopsResult queryList(QueryAllocationParams params) throws InvocationTargetException, IllegalAccessException {
        if (!StringUtils.isEmpty(params.getStartTime())
                && !StringUtils.isEmpty(params.getEndTime())) {
            params.setStartTime(params.getStartTime() + " 00:00:00");
            params.setEndTime(params.getEndTime() + " 23:59:59");
        }
        QueryAllocationListResult result = new QueryAllocationListResult();


        QueryAllocationPo po = new QueryAllocationPo();
        BeanUtils.copyProperties(po, params);
        po.setAllocationStatus(null);

        /*
         * 待发货、待调出 状态查询
         */
        if (AllocationShopStatus.WAIT_SHIPMENTS.getStatus().equals(params.getAllocationStatus())) {
            po.setOutStoreOfId(params.getPullStoreOfId());
        }
        if (AllocationShopStatus.WAIT_RECEIVED.getStatus().equals(params.getAllocationStatus())) {
            po.setInStoreOfId(params.getPullStoreOfId());
        }
        if (AllocationShopStatus.FINISH.getStatus().equals(params.getAllocationStatus())
                || AllocationShopStatus.WAIT_ALLOCATE.getStatus().equals(params.getAllocationStatus())
                || AllocationShopStatus.QUASH.getStatus().equals(params.getAllocationStatus())
        ) {
            po.setStoreOfId(params.getPullStoreOfId());
            po.setAllocationStatus(params.getAllocationStatus());
        }
        if (null == params.getAllocationStatus()) {
            po.setStoreOfId(params.getPullStoreOfId());
        }
        if (!StringUtils.isEmpty(po.getSearchKey())) {
            po.setSearchKey("%" + po.getSearchKey() + "%");
        }
        List<AllocationShopEntity> list = allocationShopDao.queryListWhere(po);

        if (list.isEmpty()) {
            return ShopsResult.ok(result);
        }
        List<AllocationShopInfo> resultInfoList = new ArrayList<>();
        for (AllocationShopEntity allocationShopEntity : list) {
            Long purchaseListUnique = allocationShopEntity.getPurchaseListUnique();
            // 调拨单详情
            List<AllocationShopDetailEntity> allocationShopDetailEntities = allocationShopDetailDao.queryListByPurchaseListUnique(purchaseListUnique);

            if (StringUtils.isEmpty(allocationShopDetailEntities)) {
                continue;
            }
            // 拼装调拨单下的商品
            List<AllocationShopDetailInfo> allocationShopDetailInfos = assembleGoodsInfo(allocationShopEntity.getStorehouseOutId(), allocationShopDetailEntities);

            // 查询商店名称
            ShopsEntity inShops = shopsDao.getByShopUnique(allocationShopEntity.getStorehouseInId());
            ShopsEntity outShops = shopsDao.getByShopUnique(allocationShopEntity.getStorehouseOutId());

            AllocationShopInfo allocationShopInfo = new AllocationShopInfo();
            BeanUtils.copyProperties(allocationShopInfo, allocationShopEntity);
            allocationShopInfo.setDetailInfoList(allocationShopDetailInfos);
            // 状态重置
            if (AllocationShopStatus.FINISH.getStatus().equals(allocationShopEntity.getAllocationStatus())
                    || AllocationShopStatus.WAIT_ALLOCATE.getStatus().equals(allocationShopEntity.getAllocationStatus())
                    || AllocationShopStatus.QUASH.getStatus().equals(allocationShopEntity.getAllocationStatus())
            ) {
                // 已完成 已撤销 不进行状态翻转
                AllocationShopStatus byStatus = AllocationShopStatus.findByStatus(allocationShopEntity.getAllocationStatus());
                if (null != byStatus) {
                    allocationShopInfo.setAllocationStatus(byStatus.getStatus());
                    allocationShopInfo.setAllocationStatusName(byStatus.getDesc());
                }
            } else {
                // 调入方查询
                if (params.getPullStoreOfId().equals(allocationShopEntity.getStorehouseInId())) {
                    allocationShopInfo.setAllocationStatus(AllocationShopStatus.WAIT_RECEIVED.getStatus());
                    allocationShopInfo.setAllocationStatusName(AllocationShopStatus.WAIT_RECEIVED.getDesc());
                }
                // 调出方查询
                if (params.getPullStoreOfId().equals(allocationShopEntity.getStorehouseOutId())) {
                    allocationShopInfo.setAllocationStatus(AllocationShopStatus.WAIT_SHIPMENTS.getStatus());
                    allocationShopInfo.setAllocationStatusName(AllocationShopStatus.WAIT_SHIPMENTS.getDesc());
                }
            }

            if (null != inShops) {
                allocationShopInfo.setStorehouseInName(inShops.getShopName());
            }
            if (null != outShops) {
                allocationShopInfo.setStorehouseOutName(outShops.getShopName());
            }

            resultInfoList.add(allocationShopInfo);
        }

        result.setRows(resultInfoList);
        return ShopsResult.ok(result);
    }

    public ShopsResult addAllocate(AddAllocateShopsParams params) {
        if (null == params.getDetailList()) {
            return ShopsResult.fail("商品列表为空");
        }

        //获取所有的barcode
        List<String> barcodeList = new ArrayList<>();
        for (AddAllocateShopsDetail d : params.getDetailList() ){
            barcodeList.add(d.getGoodsBarcode());
        }

        //查询库存与传入的数量对比
        List<QueryGoodsByGoodsBarcodeDto> dtos = goodsDao.queryGoodsAndClassByGoodsCode(barcodeList, String.valueOf(params.getPullStoreOfId()));
        for (QueryGoodsByGoodsBarcodeDto dto : dtos){
            for (AddAllocateShopsDetail d : params.getDetailList() ){
                if (dto.getGoodsBarcode().equals(d.getGoodsBarcode()) && new BigDecimal(dto.getGoodsCount()).compareTo(d.getPurchaseListDetailCount()) == -1){
                    return ShopsResult.fail(StringUtils.join("\"",dto.getGoodsName(),"\"" + i18nLanguageMsgUtil.getMessage("库存不足")));
                }
            }
        }


        AllocationShopEntity allocationShop = new AllocationShopEntity();
        Long purchaseListId = UUIDUtil.createID();
        allocationShop.setPurchaseListUnique(purchaseListId);
        allocationShop.setAllocationStatus(AllocationShopStatus.WAIT_SHIPMENTS.getStatus());

        allocationShop.setStorehouseInId(params.getInboundStoreOfId());
        allocationShop.setStorehouseOutId(params.getPullStoreOfId());

        allocationShop.setUserId(params.getUserId());
        allocationShop.setUserName(params.getUserName());
        // 存储 调拨单
        allocationShopDao.addAllocationShop(allocationShop);


        List<AllocationShopDetailEntity> detailEntities = new ArrayList<>();
        AllocationShopDetailEntity detailEntity;
        for (AddAllocateShopsDetail add : params.getDetailList()) {
            detailEntity = new AllocationShopDetailEntity();
            detailEntity.setPurchaseListUnique(purchaseListId);
            detailEntity.setGoodsBarcode(add.getGoodsBarcode());
            detailEntity.setGoodsName(add.getGoodsName());
            detailEntity.setPurchaseListDetailPrice(add.getPurchaseListDetailPrice());
            detailEntity.setPurchaseListDetailCount(add.getPurchaseListDetailCount());

            detailEntities.add(detailEntity);
        }
        // 存储 调拨单 详情
        allocationShopDetailDao.batchAddAllocationShopDetail(detailEntities);
        return ShopsResult.ok();
    }

    /**
     * 修改 调拨单状态
     *
     * @param id
     * @return
     */
    public ShopsResult updateAllocationStatus(Integer id, AllocationShopStatus status) {
        AllocationShopEntity allocationShop = new AllocationShopEntity();
        allocationShop.setPurchaseListId(id);
        allocationShop.setAllocationStatus(status.getStatus());
        allocationShopDao.updateAllocation(allocationShop);
        return ShopsResult.ok();
    }

    /**
     * 完成调拨单
     *
     * @param
     * @return
     */
    public ShopsResult recipientsAllocation(FinishAllocateParams params) {
        AllocationShopEntity allocationShop = new AllocationShopEntity();
        allocationShop.setPurchaseListId(params.getId());
        allocationShop.setAllocationStatus(AllocationShopStatus.FINISH.getStatus());

        allocationShop.setRecipientsTime(new Date());
        allocationShop.setRecipientsUserId(params.getRecipientsUserId());
        allocationShop.setRecipientsUserIdName(params.getRecipientsUserIdName());

        allocationShopDao.updateAllocation(allocationShop);
        return ShopsResult.ok();
    }

    public AllocationShopEntity queryOneById(Integer id) {

        return allocationShopDao.queryOneById(id);
    }

    public AllocationShopInfo queryOneAndDetailById(Integer id) throws InvocationTargetException, IllegalAccessException {
        AllocationShopEntity allocationShopEntity = allocationShopDao.queryOneById(id);

        List<AllocationShopDetailEntity> allocationShopDetailEntities = allocationShopDetailDao.queryListByPurchaseListUnique(allocationShopEntity.getPurchaseListUnique());

        AllocationShopInfo result = new AllocationShopInfo();

        BeanUtils.copyProperties(result, allocationShopEntity);

        List<AllocationShopDetailInfo> detailInfoList = new ArrayList<>();
        AllocationShopDetailInfo detailInfo;
        for (AllocationShopDetailEntity detail : allocationShopDetailEntities) {
            detailInfo = new AllocationShopDetailInfo();
            detailInfo.setPurchaseListDetailId(detail.getPurchaseListDetailId());
            detailInfo.setPurchaseListUnique(detail.getPurchaseListUnique());
            detailInfo.setGoodsName(detail.getGoodsName());
            detailInfo.setGoodsBarcode(detail.getGoodsBarcode());
            detailInfo.setPurchaseListDetailCount(detail.getPurchaseListDetailCount());
            detailInfo.setGoodsInPrice(detail.getPurchaseListDetailPrice());
            detailInfoList.add(detailInfo);
        }
        result.setDetailInfoList(detailInfoList);

        return result;
    }

    private List<AllocationShopDetailInfo> assembleGoodsInfo(Long shopUnique, List<AllocationShopDetailEntity> allocationShopDetailEntities) throws InvocationTargetException, IllegalAccessException {
        List<String> goodsBarcodes = new ArrayList<>();
        for (AllocationShopDetailEntity allocationShopDetail : allocationShopDetailEntities) {
            String goodsBarcode = allocationShopDetail.getGoodsBarcode();
            goodsBarcodes.add(goodsBarcode);
        }

        // 商品详情
        Map<String, QueryGoodsInfoDo> infoDoMap = goodsService.queryListByShopUniqueAndBarcode(shopUnique, goodsBarcodes);

        List<AllocationShopDetailInfo> result = new ArrayList<>();
        for (AllocationShopDetailEntity allocationShopDetail : allocationShopDetailEntities) {
            AllocationShopDetailInfo detailInfo = new AllocationShopDetailInfo();
            QueryGoodsInfoDo infoDo = infoDoMap.get(allocationShopDetail.getGoodsBarcode());
            if (null != infoDo) {
                detailInfo.setPurchaseListDetailId(allocationShopDetail.getPurchaseListDetailId());
                detailInfo.setPurchaseListUnique(allocationShopDetail.getPurchaseListUnique());
                detailInfo.setPurchaseListDetailCount(allocationShopDetail.getPurchaseListDetailCount());

                detailInfo.setGoodsBarcode(infoDo.getGoodsBarcode());
                detailInfo.setGoodsName(infoDo.getGoodsName());
                detailInfo.setGoodsInPrice(infoDo.getGoodsInPrice());
                detailInfo.setGoodsSalePrice(infoDo.getGoodsSalePrice());
                detailInfo.setGoodsSpec(infoDo.getGoodsStandard());
                detailInfo.setGoodsUnit(infoDo.getGoodsUnit());
                detailInfo.setGoodsPicturePath(infoDo.getGoodsPicturePath());
                result.add(detailInfo);
            }
        }
        return result;
    }

}
