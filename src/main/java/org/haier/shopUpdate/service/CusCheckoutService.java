package org.haier.shopUpdate.service;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.haier.shopUpdate.entity.CusCheckout;
import org.haier.shopUpdate.entity.PalmResult;
import org.haier.shopUpdate.util.EvaluateResult;
import org.haier.shopUpdate.util.ShopsResult;

/**
 * 类名：com.palmshop.online.entity.CusCheckoutService;
 * 描述：收银端会员 信息相关的service
 * 包括注册会员信息、修改会员信息、查询会员信息等
 * 方法顺序：增、删、改、查
 * <AUTHOR>
 *
 */
public interface CusCheckoutService {
	/**
	 * 1、添加新的会员信息
	 * @param cusUnique 会员编号
	 * @param shopUnique 店铺编号
	 * @param cusName 会员名称
	 * @param cusPhone 会员电话
	 * @return
	 */
	public PalmResult addCusNP(String cusUnique,String shopUnique,String cusName,String cusPhone);
	/**
	 * 添加会员信息
	 * @param request 
	 * @param cus：用户实体类
	 * @return
	 */
	public PalmResult addCus(CusCheckout cus, HttpServletRequest request);
	
	public PalmResult getCustList(String searchKey, Long shopUnique, int pages, int perpage);

	public PalmResult findCusById(String cus_unique, Long shopUnique,Integer searchType);

	public PalmResult editCus(CusCheckout cus, HttpServletRequest request);

	public PalmResult getMsgList(Long shopUnique, Integer shopMsgType, Integer pages, Integer perpage);

	public PalmResult editMsgById(Long shopMsgId);

	public EvaluateResult userEvaluate(int evaluateUserId, int evaluateScore, int goodsId, String evaluateContent,
			Long evaluateId, int userType, HttpServletRequest request);

	public EvaluateResult getEvaluateList(Long goodsId, int pages, int perpage);

	public PalmResult getImages(Map<String,Object> map);

	
	public PalmResult getPromotionList(Long supplierUnique, Long shopUnique, int pages, int perpage);

	public PalmResult getBusinessHead(Long pages, Long perpage);

	public PalmResult getBusinessHeadGoods(Long businessHeadId, int pages, int perpage);

	public PalmResult updateClickCount(Long businessHeadId);

	/**
	 * 促销商品的采购
	 * @param map
	 * @return
	 */
	public ShopsResult  promotionGoodsPurchase(Map<String,Object> map);
	/**
	 * 促销商品采购，若店铺无此商品，则先添加该商品，再添加商品至购物车
	 * @param map
	 * @return
	 */
	public ShopsResult newPromotionGoodsPurchase(Map<String,Object> map);
	
	/**
	 * 会员充值消费记录（缺少会员积分兑换记录）
	 * @param map
	 * @return
	 */
	public ShopsResult queryCusConRecord(Map<String,Object> map);
	/**
	 * 会员消费方式查询
	 * @return
	 */
	public ShopsResult  queryConType();

	public PalmResult queryCusDetailDown(String cus_unique, Long shopUnique);

	public PalmResult editCusStatus(String cus_unique, Long shopUnique, String cus_status);

	public PalmResult editCusPassword(String cus_unique, Long shopUnique, String cus_password);

	public PalmResult editCusLevel(String cus_unique, Long shopUnique, Integer cus_level_id);

	public PalmResult findCusByCusPhone(String cusPhone, Long shopUnique);

	public Map<String, Object> queryCusSynchroStatus(String string);

	public ShopsResult getMemberLevel(Map<String, Object> map);

	public ShopsResult updateMemberLevel(Map<String, Object> map);

	public ShopsResult queryCusCheckOut(Map<String, Object> map);

	public ShopsResult queryCusRechargeLog(Map<String, Object> map);
}
