package org.haier.shopUpdate.service;

import org.haier.shopUpdate.dao.dojo.QueryGoodsInfoDo;
import org.haier.shopUpdate.dto.AddGoodsDto;
import org.haier.shopUpdate.params.QueryPurchaseOrderGoodsParams;
import org.haier.shopUpdate.params.UpdateReplenishmentGoodsParams;
import org.haier.shopUpdate.params.goods.AddGoodsBaseParam;
import org.haier.shopUpdate.params.goods.AddGoodsParam;
import org.haier.shopUpdate.params.goods.QueryPromotionActivityListParams;
import org.haier.shopUpdate.params.goods.UpdateGoodsBaseParam;
import org.haier.shopUpdate.util.ShopsResult;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.Map;

public interface GoodsService {

	/**
	 * 查询店铺的促销商品列表
	 * @param params
	 * @return
	 */
	public ShopsResult queryPromotionActivityList(QueryPromotionActivityListParams params);
    /**
     * 1、添加或更新商品信息
     * @param shopUnique
     * @param goodsBarcode
     * @param goodsName
     * @param goodsPrice
     * @param kindUnique
     * @param goodsChengType
     * @return
     */
    public ShopsResult addNewGoodsNP(String shopUnique,String goodsBarcode,String goodsName,String goodsPrice,String kindUnique,String goodsChengType);
	/**
	 * 
	 * @param shopUnique 店铺编号
	 * @param pages 页码
	 * @param pageSize 单页查询数量
	 * @param classUnique 大分类编号
	 * @param supplierUnique 供货商编号
	 * @param goodsMsg 商品输入框信息
	 * @return
	 */
	public ShopsResult queryFarmGoodsList(String shopUnique,Integer pages,Integer pageSize,String classUnique,String supplierUnique,String goodsMsg,Integer goodsType );
	/**
	 * 查询商品信息
	 * @param map
	 * @return
	 */
	public ShopsResult queryGoodsMessage(Map<String,Object> map);

	ShopsResult queryGoodsMessageInvented(Map<String,Object> map);
	
	/**
	 * 删除店铺商品
	 * @param map
	 * @return
	 */
	public ShopsResult deleteShopsGoods(Map<String,Object> map);
	
	
	/**
	 * 更新商品信息
	 * @param map
	 * @return
	 */
	public ShopsResult modifyGoods(Map<String,Object> map,HttpServletRequest request);
	
	/**
	 * 商品详情查询
	 * @param map
	 * @return
	 */
	public ShopsResult goodsDetail(Map<String,String> map);
	
	/**
	 * 查询商品供货商详情
	 * @param map
	 * @return
	 */
	public ShopsResult goodsSupplierQuery(Map<String,Object> map);
	/**
	 * 查询商品供货商详情
	 * @param map
	 * @return
	 */
	public ShopsResult goodsSupplierQueryNew(Map<String,Object> map);
	/**
	 * 更新商品信息
	 * @param map
	 * @return
	 * @throws Exception 
	 */
	public ShopsResult updateListGoodsMessage(Map<String,Object> map,String goodsMessage,HttpServletRequest request,Double goodsCount,String goodsBarcode
			,Long shopUnique,Long foreignKey,Integer sameType,String supplierUnique,String goodsPicturePath,Integer tableType,Integer goodsChengType) throws Exception;
	
	/**
	 * 商品基本信息查询
	 * 1：获取
	 * @param map
	 * @return
	 */
	public ShopsResult searchBaseGoods(Map<String,Object> map,String goodsBarcode,Integer type);
	/**
	 * 商品出入库记录查询
	 * @param map
	 * @return
	 */
	public ShopsResult stockRecord(Map<String,Object> map);
	
	/**
	 * 商品销量排行
	 * @param map
	 * @return
	 */
	public ShopsResult goodsSaleStatistics(Map<String,Object> map);
	

	/**
	 * 输入商品后六位，查询商品信息
	 * @param map
	 * @return
	 */
	public ShopsResult queryBaseGoodsMessageByCode(Map<String,Object> map,Integer kindType);

	public ShopsResult queryGoodsCount(Map<String, Object> map);

	public ShopsResult queryGoodsCountWarning(Map<String, Object> map);

	/**
	 * 商品信息更新
	 * @param map
	 * @return
	 */
	ShopsResult modifyGoodsInfoTrx(Map<String,Object> map);

	ShopsResult queryPurchaseOrderGoods(QueryPurchaseOrderGoodsParams params);

	ShopsResult updateReplenishmentGoodsService(UpdateReplenishmentGoodsParams params);

	Map<String, QueryGoodsInfoDo> queryListByShopUniqueAndBarcode(Long shopUnique, String goodsBarcode);
	Map<String, QueryGoodsInfoDo> queryListByShopUniqueAndBarcode(Long shopUnique, List<String> goodsBarcodeList);

	ShopsResult addGoodsInfo(AddGoodsDto addGoodsDto) throws InvocationTargetException, IllegalAccessException;

	ShopsResult addGoods(AddGoodsBaseParam addGoodsBaseParam);

	ShopsResult updateGoods(UpdateGoodsBaseParam updateGoodsBaseParam);

	/**
	 * 创建新的条码
	 * @return
	 */
	ShopsResult queryGoodsBarcodeSameForeignkey(String shopUnique);

	ShopsResult queryOneByParam(String shopUnique, String goodsBarcode);
}
