package org.haier.shopUpdate.service;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import javax.annotation.Resource;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.haier.shopUpdate.dao.DiamondsDao;
import org.haier.shopUpdate.entity.BeanTixianVO;
import org.haier.shopUpdate.entity.BeansExchange;
import org.haier.shopUpdate.entity.BeansGetRule;
import org.haier.shopUpdate.entity.PageQuery;
import org.haier.shopUpdate.entity.ShopBeansVO;
import org.haier.shopUpdate.entity.ShopCard;
import org.haier.shopUpdate.util.ShopsResult;
import org.haier.shopUpdate.util.ShopsUtil;
import org.haier.shopUpdate.util.WechatPayUtil;
import org.haier.shopUpdate.util.common.BeanResult;
import org.haier.shopUpdate.util.common.CommonResult;
import org.haier.shopUpdate.util.cxb.BankCardValidate;
import org.haier.shopUpdate.util.AlipayPayUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;


@Service
@Transactional
@Slf4j
public class DiamondsServiceImpl implements DiamondsService{
	@Resource
	private DiamondsDao diamondsDao;
//	@Resource
//	private PurchaseListDao purtestDao;

	/**
	 * 周期时间段内钻石的营收统计及余额显示
	 * @param map
	 * @return
	 */
	public ShopsResult statisticsShopsDiamondsByTime(Map<String,Object> map){
		ShopsResult sr=new ShopsResult(1, "查询成功！");
		List<Map<String,Object>> data=diamondsDao.statisticsShopsDiamondsByTime(map);
		List<Map<String,Object>> inList=new ArrayList<>();
		List<Map<String,Object>> outList=new ArrayList<>();
		Map<String,Object> res=diamondsDao.queryShopDiamondsTotal(map);
		Double inTotal=0.0,outTotal=0.0;
		if(null!=data&&!data.isEmpty()){
			for(int i=0;i<data.size();i++){
				Map<String,Object> m=data.get(i);
				if(m.get("value")==null){
					m.put("value", 0);
				}
				if(m.get("type").toString().equals("1")){
					inList.add(m);
					//double类型加法，返回两位小数
					inTotal=ShopsUtil.addDoubleSum(inTotal, Double.parseDouble(m.get("value").toString()));
				}else if(m.get("type").toString().equals("2")){
					outList.add(m);
					outTotal=ShopsUtil.addDoubleSum(outTotal, Double.parseDouble(m.get("value").toString()));
				}
			}
		}
		res.put("inList", inList);
		res.put("outList",outList);
		res.put("outTotal",outTotal);
		res.put("inTotal",inTotal);
		res.put("profit",ShopsUtil.subtractionDouble(inTotal, outTotal));
		sr.setData(res);
		return sr;
	}

	/**
	 * 获取店铺的银行卡列表信息
	 * @param shopCard
	 * @return
	 */
	public CommonResult getShopCardList(ShopCard shopCard){
		CommonResult sr=new CommonResult(1,"查询成功！");
		if(shopCard.getShopUnique()==null||shopCard.getShopUnique().equals(""))
		{
			sr.setStatus(0);
			sr.setMsg("参数异常！");
			return sr;
		}
		List<ShopCard> list=diamondsDao.getShopCardList(shopCard);
		if(null==list||list.isEmpty()){
			sr.setData(new ArrayList<>());
		}else{
			for(int i=0;i<list.size();i++){
				String subNum=new String();
				String cardNum=list.get(i).getBankCard().toString();
				if(cardNum.length()>4){
//					System.out.println(cardNum);
					for(int j=0;j<cardNum.length()-4;j++){
						if(j>0&&j%4==0){
							subNum+=" ";
						}
						subNum+="*";
					}
					for(int k=cardNum.length()-4;k<cardNum.length();k++){
						if(k>0&&k%4==0){
							subNum+=" ";
						}
						subNum+=cardNum.substring(k, k+1);
					}
//					System.out.println(subNum);
				}else{
					subNum=cardNum;
				}
				list.get(i).setBankCard(subNum);
			}
			sr.setData(list);
		}
		return sr;
	}

	/**
	 * 删除银行卡信息
	 * @param shopCard
	 * @return
	 */
	public CommonResult removeShopCard(ShopCard shopCard){
		CommonResult sr=new CommonResult(1,"删除成功");
		try {
			if(shopCard.getCardId()==null||shopCard.getShopUnique()==null||shopCard.getShopUnique().equals(""))
			{
				sr.setStatus(0);
				sr.setMsg("参数异常!");
				return sr;
			}

			shopCard.setValidType(2);
			Integer k=diamondsDao.modifyShopCard(shopCard);
			if(k>0){
				return sr;
			}
			sr.setStatus(0);
			sr.setMsg("删除失败!");
		}catch(Exception e){
			log.error("删除银行卡信息失败,异常信息：",e);
			sr.setStatus(0);
			sr.setMsg("删除失败，请联系管理员!");
		}

		return sr;
	}

	/**
	 * 添加或保存银行卡信息
	 * @param card
	 * @return
	 */
	public CommonResult addNewShopCard(ShopCard card){
		CommonResult sr=new CommonResult(1,"添加成功");
		card.setBankCard(card.getBankCard().replace(" ", ""));

		if(ObjectUtil.isEmpty(card.getBankCard()))
		{
			sr.setStatus(2);
			sr.setMsg("银行卡号不能为空！");
			return sr;
		}

		//String cardInfo=BankCardValidate.requestGETData(card.getBankCard());
//		if(cardInfo!=null) {
//			try {
//			//JSONObject jsonObject =  JSON.parseObject(cardInfo);
////			if(!(boolean) jsonObject.get("validated"))
////			{
////				sr.setStatus(2);
////				sr.setMsg("请输入正确银行卡号！");
////				return sr;
////			}else
////			{
////				//card.setCardType(jsonObject.get("cardType").toString());
////			}
////			} catch (Exception e) {
////
////				System.out.print("---------------------支付宝验证银行卡问题 接口失效---------------------");
////			}
//
//		}else
//		{
//			System.out.print("---------------------支付宝验证银行卡问题 接口失效---------------------");
//		}
//
//		}
		Integer  k=0;
		if(card.getCardId()!=null){
			//更新银行卡信息
			k=diamondsDao.modifyShopCard(card);
			sr.setMsg("更新成功！");
		}else {
			if(card.getBankCard().equals(""))
			{
				sr.setStatus(2);
				sr.setMsg("卡号不能为空！");
				return sr;
			}else if(card.getCardName().equals(""))
			{
				sr.setStatus(2);
				sr.setMsg("所属人不能为空！");
				return sr;
			}else if(card.getBankPhone().equals(""))
			{
				sr.setStatus(2);
				sr.setMsg("电话不能为空！");
				return sr;
			}else if(ObjectUtil.isEmpty(card.getBankPhone()))
			{
				sr.setStatus(2);
				sr.setMsg("请输入正确手机号！");
				return sr;
			}else if(card.getBank()==null)
			{
				sr.setStatus(2);
				sr.setMsg("电话不能为空！");
				return sr;
			}if(diamondsDao.queryBankById(card)<1)
			{
				sr.setStatus(2);
				sr.setMsg("银行不存在！");
				return sr;
			}
			//添加新的银行卡信息
			//添加前查询，防止重复插入

			List<ShopCard> list=diamondsDao.getShopCardList(card);
			if(list!=null&&!list.isEmpty()){
				sr.setStatus(2);
				sr.setMsg("该卡号已添加");
				return sr;
			}
			k=diamondsDao.addNewShopCard(card);
		}
		if(k>0){
			return sr;
		}
		sr.setStatus(0);
		sr.setMsg("更新失败");
		return sr;
	}

    /**
             * 验证手机号 由于号码段不断的更新，只需要判断手机号有11位，并且全是数字以及1开头
     * @param phoneNumber 手机号码
     * @return
     */
    private  boolean matchPhoneNumber(String phoneNumber) {
        String regex = "^1\\d{10}$";
        if(phoneNumber==null||phoneNumber.length()<=0){
            return false;
        }
        return Pattern.matches(regex, phoneNumber);
    }


	/**
	 * 获取支持的银行卡列表
	 */
	public ShopsResult getBankListMsg(){
		ShopsResult sr=new ShopsResult(1,"查询成功！");
		List<Map<String,Object>> list=diamondsDao.getBankListMsg();
		sr.setData(list);
		return sr;
	}


	/**
	 * 获取店铺的当前钻石数量
	 * @return
	 */
	public ShopsResult getShopBeansAndRule(ShopCard card){
		ShopsResult sr=new ShopsResult(1, "查询成功！");
		Map<String,Object> res=diamondsDao.getShopBeansAndRule(card);//获取现有的钻石数量及相应的规则

		//根据规则，查询已提现次数，判断是否可以提现
		Integer count=diamondsDao.getCashCount(res);
		if(count<=0){
			sr.setStatus(2);
			sr.setMsg("今日提现次数已用尽");
			return sr;
		}

		sr.setData(res);
		return sr;
	}


	/**
	 * 百货豆提现检测
	 * 1、提现次数限制
	 * 2、提现余额限制
	 * 3、提现规则验证（手续费是否足够）
	 * 4、提现
	 * @param beansExchange
	 * @return
	 */
	public ShopsResult addNewCashRecord(BeansExchange beansExchange){
		ShopsResult sr=new ShopsResult(2, "提现成功！");
		Map<String,Object> res=diamondsDao.getShopBeansAndRule(beansExchange);//获取现有的钻石数量及相应的规则
		System.out.println(res);
		Integer count=diamondsDao.getCashCount(res);//剩余的可提现次数
		Integer serviceCharge=beansExchange.getServiceCharge();//手续费
		Integer shopBeans=Integer.parseInt(res.get("shopBeans").toString());
		Integer receiveCount=beansExchange.getReceiveCount();//实际取现数量
		Double payMoney=beansExchange.getPayMoney();//取现金额
		Integer ptTax=Integer.parseInt(res.get("ptTax").toString());
		String msg="提交成功";
		if(count<=0){
			msg="今日提现次数已用尽";
		}else if((receiveCount+serviceCharge)>=shopBeans){
			msg="账户余额不足,请刷新界面获取最新余额";
		}else if(payMoney<Double.parseDouble(res.get("sMoney").toString())){
			msg="取现金额小于最小取现金额";
		}else if(payMoney>Double.parseDouble(res.get("mMoney").toString())){
			msg="取现金额超过最大金额";
		}else if(receiveCount<=0){
			msg="取现数量必须大于零";
		}else if((payMoney*100)!=receiveCount){
			msg="取现金额兑换比率错误";
		}else if((receiveCount*ptTax/100-serviceCharge)>1||(receiveCount*ptTax/100-serviceCharge)<0){
			System.out.println((receiveCount*ptTax/100-serviceCharge));
			msg="取现手续费率错误";
		}else{
			sr.setStatus(1);
		}
		//提交取现订单
		Integer k=diamondsDao.addNewCashRecord(beansExchange);
		if(k==0){
			sr.setStatus(2);
			sr.setMsg("提交失败");
			return sr;
		}
		sr.setMsg(msg);
		return sr;
	}

	/**
	 * 获取银行卡详情
	 * @param card
	 * @return
	 */
	public ShopsResult getCardDetail(ShopCard card){
		ShopsResult sr=new ShopsResult(1, "查询成功！");
		List<ShopCard> list=diamondsDao.getShopCardList(card);
		if(list!=null && !list.isEmpty()){
			sr.setData(list.get(0));
		}else{
			sr.setMsg("没有该卡信息");
		}
		return sr;
	}

	/**
	 * 查询店铺的规则设置信息
	 * @param card
	 * @return
	 */

	public ShopsResult getDiamondsRule(ShopCard card){
		ShopsResult sr=new ShopsResult(1,"查询成功！");
		Map<String,Object> map=diamondsDao.getDiamondsRule(card);
		sr.setData(map);
		return sr;
	}

	/**
	 * 修改店铺的规则信息
	 * @param beansGetRule
	 * @return
	 */
	public ShopsResult addNewGetRule(BeansGetRule beansGetRule){
		ShopsResult sr=new ShopsResult(1,"操作成功");
		Integer c=diamondsDao.addNewGetRule(beansGetRule);//创建新的记录
		c=diamondsDao.modifyShopUseRule(beansGetRule);
		if(c==0){
			diamondsDao.addNewShopUseRule(beansGetRule);
		}
		c=diamondsDao.modifyBeansRule(beansGetRule);
		sr.setData(c);
		return sr;
	}


	/**
	 * 购买钻石数量统计
	 * @param map
	 * @return
	 */
	public ShopsResult beansBuyRecordTotal(PageQuery pageQuery){
		ShopsResult sr=new ShopsResult(1,"查询成功！");
		if(pageQuery.getHandleStatus()==null){//默认只查询已完成
			pageQuery.setHandleStatus(1);
		}
		Map<String,Object> map=diamondsDao.beansBuyRecordTotal(pageQuery);

		if(map==null||map.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("无相关信息！");
		}
		sr.setData(map);
		return sr;
	}

	/**
	 * 取现记录列表
	 * @param pageQuery
	 * @return
	 */
	public ShopsResult beansBuyRecordList(PageQuery pageQuery){
		ShopsResult sr=new ShopsResult(1,"查询成功！");
		List<Map<String,Object>> list=diamondsDao.beansBuyRecordList(pageQuery);
		sr.setData(list);
		return sr;
	}

	/**
	 * 抵扣记录信息统计
	 * @param pageQuery
	 * @return
	 */
	public ShopsResult dikouTotal(PageQuery pageQuery){
		ShopsResult sr=new ShopsResult(1,"查询成功！");
		List<Map<String,Object>> list=diamondsDao.dikouTotal(pageQuery);
		Map<String,Object> map=new HashMap<>();
		if(null==list||list.isEmpty()){
			map.put("shopBeans", 0);
			map.put("listCount", 0);
			map.put("cusCount", 0);
			map.put("averCount", 0);
		}else{
			Integer listCount=0,shopBeans=0,cusCount=list.size();
			for(int i=0;i<list.size();i++){
				listCount+=Integer.parseInt(list.get(i).get("listCount").toString());
				shopBeans+=new Double(list.get(i).get("shopBeans").toString()).intValue();
			}
			map.put("shopBeans", shopBeans);
			map.put("listCount", listCount);
			map.put("cusCount", cusCount);
			map.put("averCount", shopBeans/listCount);
			map.put("cusAver", shopBeans/cusCount);
		}
		sr.setData(map);
		return sr;
	}

	/**
	 * 抵扣信息分页查询
	 * @param pageQuery
	 * @return
	 */
	public ShopsResult dikouList(PageQuery pageQuery){
		ShopsResult sr=new ShopsResult(1,"查询成功！");
		List<Map<String,Object>> list=diamondsDao.dikouList(pageQuery);
		sr.setData(list);
		return sr;
	}

	/**
	 * 免密赠送商家百货豆统计
	 * @param pageQuery
	 * @return
	 */
	public ShopsResult mmGiveBeansTotal(PageQuery pageQuery){
		ShopsResult sr=new ShopsResult(1, "查询成功！");
		Map<String,Object> map=diamondsDao.mmGiveBeansTotal(pageQuery);
		if(null==map||map.isEmpty()){
			map=new HashMap<>();
			map.put("listCount", 0);
			map.put("shopBeans", 0);
			map.put("listTotal", 0);
			map.put("averGive", 0);
		}
		sr.setData(map);
		return sr;
	}


	/**
	 * 免密赠送分页查询
	 * @param pageQuery
	 * @return
	 */
	public ShopsResult mmGiveBeansList(PageQuery pageQuery){
		ShopsResult sr=new ShopsResult(1, "查询成功！");
		List<Map<String,Object>> list=diamondsDao.mmGiveBeansList(pageQuery);
		if(null==list||list.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("没有相关信息！");
		}else{
			sr.setData(list);
		}
		return sr;
	}

	/**
	 * 提现记录统计
	 * @param pageQuery
	 * @return
	 */
	public ShopsResult takeCashTotal(PageQuery pageQuery){
		ShopsResult sr=new ShopsResult(1, "查询成功！");
		Map<String,Object> map=diamondsDao.takeCashTotal(pageQuery);
		if(null==map||map.isEmpty()){
			map=new HashMap<>();
			map.put("shopBeans", 0);
			map.put("payMoney", 0);
			map.put("giveCount", 0);
			map.put("lastShopBeans", 0);
		}
		sr.setData(map);
		return sr;
	}
	/**
	 * 提现分页查询
	 * @param pageQuery
	 * @return
	 */
	public ShopsResult takeCashList(PageQuery pageQuery){
		ShopsResult sr=new ShopsResult(1,"查询成功！");
		List<Map<String,Object>> list=diamondsDao.takeCashList(pageQuery);
		if(list==null||list.isEmpty()){
			sr.setData(new ArrayList<Object>());
		}else{
			sr.setData(list);
		}
		return sr;
	}

	/**
	 * 赠送信息统计
	 * @param pageQuery
	 * @return
	 */
	public ShopsResult giveTotal(PageQuery pageQuery){
		ShopsResult sr=new ShopsResult(1,"查询成功！");
		List<Map<String,Object>> list=diamondsDao.giveTotal(pageQuery);
		Map<String,Object> map=new HashMap<>();
		Integer listCount=0,beansGet=0;
		System.out.println(list);
		if(null==list||list.isEmpty()){
		}else{
			for(int i=0;i<list.size();i++){
				listCount+=Integer.parseInt(list.get(i).get("listCount").toString());
				beansGet+=new Double(list.get(i).get("beansGet").toString()).intValue();
			}
		}
		map.put("listCount", listCount);
		map.put("beansGet", beansGet);
		map.put("cusCount", list.size());
		if(listCount==0){
			map.put("averCount",0 );
		}else{
			map.put("averCount", beansGet/listCount);
		}
		sr.setData(map);
		return sr;
	}


	/**
	 * 钻石赠送记录分页查询
	 * @param pageQuery
	 * @return
	 */
	public ShopsResult giveList(PageQuery pageQuery){
		ShopsResult sr=new ShopsResult(1,"查询成功！");
		List<Map<String,Object>> list=diamondsDao.giveList(pageQuery);
		if(null==list||list.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("没有满足条件的订单信息");
		}else{
			sr.setData(list);
		}
		return sr;
	}
	/**
	 * 钻石购买规则查询
	 * @return
	 */
	public ShopsResult beansBuyRule(){
		ShopsResult sr=new ShopsResult(1, "查询成功！");
		List<Map<String,Object>> map=diamondsDao.beansBuyRule();
		if(null==map||map.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("该功能暂未开通");
			return sr;
		}
		sr.setData(map);
		return sr;
	}

//	public static void main(String[] args) {
//		String orderId=UUIDUtil.getUUID32();
//		System.out.println(orderId);
//	}
	/**
	 * 充值
	 * @param beansExchange
	 * @return
	 */
	public ShopsResult diamondsBuyNew(BeansExchange beansExchange){
		ShopsResult sr=new ShopsResult(1, "查询成功！");
		Map<String,Object> data=new HashMap<>();
		String orderId=Calendar.getInstance().getTimeInMillis()+"";
		try{
			beansExchange.setExchangeType(1);//买豆
			beansExchange.setReceiveState(3);//未支付
			beansExchange.setOrderId(orderId);
			Integer count=diamondsDao.addNewCashRecord(beansExchange);
			Map<String,Object> map=new HashMap<>();//用来存储调用支付宝所需的信息
			if(count==1&&beansExchange.getPayType()==0){
				map.put("subject", "百货平台钻石购买");
				map.put("out_trade_no", orderId);
        		map.put("total_amount", beansExchange.getPayMoney());
        		String orderString = AlipayPayUtil.alipayPay(map);
        		sr.setData(orderString);
        		data.put("orderString", orderString);
			}else if(count==1&&beansExchange.getPayType()==1){
				//微信支付
				Map<String ,Object> paramsMap = new HashMap<String, Object>();
        		paramsMap.put("body", "buyDiamonds");
        		paramsMap.put("out_trade_no", orderId);
        		paramsMap.put("total_fee", beansExchange.getPayMoney().toString());
        		paramsMap.put("spbill_create_ip", beansExchange.getSpbillCreateIp().toString());
        		data=WechatPayUtil.weixinPay(paramsMap);
			}else{
				sr.setStatus(2);
				sr.setMsg("创建记录失败!");
			}
		}catch(Exception e){
			log.error("异常信息：",e);
			sr.setStatus(2);
			sr.setMsg("充值失败!");
		}
		sr.setData(data);
		return sr;
	}

	/**
	 * 支付宝成功回调函数
	 * @param beansExchange
	 * @return
	 */
	public boolean modifyBeansExchangeRecord(BeansExchange beansExchange){
		boolean flag=false;
		Integer c=diamondsDao.modifyBeansExchangeRecord(beansExchange);
		if(c==1){
			flag=true;
			//完成后，增加数量
		}
		return flag;
	}

	public ShopsResult queryRulrForDiamonds(Map<String,Object> map){
		ShopsResult sr=new ShopsResult(1, "查询成功！");
		Map<String,Object> res=diamondsDao.queryRulrForDiamonds(map);
		sr.setData(res);
		return sr;
	}

	public Map<String,Object> queryExchangeMsgByOrderId(BeansExchange b){
		return diamondsDao.queryExchangeMsgByOrderId(b);
	}

	public Integer modifyShopDiamonds(Map<String,Object> map){
		return diamondsDao.modifyShopDiamonds(map);
	}

	/**
	 * 周期时间段内钻石的营收统计及余额显示
	 * @param map
	 * @return
	 */
	public BeanResult queryBeanList(Map<String,Object> map){
		BeanResult sr=new BeanResult(1, "查询成功！");
		Map<String,Object> res=diamondsDao.queryShopDiamondsTotal(map);
		List<Map<String,Object>> data=diamondsDao.queryBeanList(map);

		sr.setCount(Integer.parseInt(res.get("shopBeans").toString()));
		sr.setData(data);
		return sr;
	}

	/**
	   * 提现按钮（返回可提现金额、提现说明）
	 * @param map
	 * @return
	 */
	public CommonResult queryBeanMoney(Map<String,Object> map){
		CommonResult sr=new CommonResult(1, "查询成功！");

		BeanTixianVO data=new BeanTixianVO();
		ShopBeansVO beanDetai=diamondsDao.getShopBeans(map);
		if(beanDetai!=null)
		{
			double a=beanDetai.getBeans_old_count()*(100-beanDetai.getPt_tax())/100;
			double moneyTop=a/100;

			if(moneyTop<beanDetai.getS_moeny())
			{
				moneyTop=0;
			}else if(moneyTop>beanDetai.getM_moeny())
			{
				moneyTop=beanDetai.getM_moeny();
			}

			int tx_dtae=beanDetai.getTx_dtae();
			int tx_times=beanDetai.getTx_times();
			//查询最近tx_dtae日的交易次数
			map.put("tx_dtae", tx_dtae);
			int tixianCount=diamondsDao.queryTransactionCount(map);
			int tiXianCount=tx_times-tixianCount;
			if(tiXianCount<1)
			{
				sr.setStatus(0);
				sr.setMsg("今天已经不能提现！");
			}
			data.setMoneyTop(moneyTop);
			data.setTiXianCount(tiXianCount);
			data.setM_moeny(beanDetai.getM_moeny());
			data.setS_moeny(beanDetai.getS_moeny());



			String content="";
			content="<p>1.平台服务费百分之"+beanDetai.getPt_tax()+",扣除相应的百货豆（百货豆不满一个按一个计算）</p>"
					+"<p>2."+beanDetai.getS_cl_date()+"至"+beanDetai.getM_cl_date()+"个工作日</p>"
					+"<p>3."+beanDetai.getTx_dtae()+"日限制提现"+beanDetai.getTx_times()+"次</p>"
					+"<p>4.每次最小提现金额为"+beanDetai.getS_moeny()+"元</p>"
					+"<p>5.每次最大提现金额为"+beanDetai.getM_moeny()+"元</p>";
			data.setContent(content);

		}else
		{
			data.setMoneyTop(0.0);
			data.setTiXianCount(0);
		}

		sr.setData(data);
		return sr;
	}

	/**
	   * 百货豆提现
	 * 1、提现次数限制
	 * 2、提现余额限制
	 * 3、提现规则验证（手续费是否足够）
	 * 4、提现
	 * @param beansExchange
	 * @return
	 */
	public CommonResult addBeanMoney(BeansExchange beansExchange){
		CommonResult sr=new CommonResult(1, "提现成功！");

		//1查询银行卡是否存在
		ShopCard shopCard=new ShopCard();
		shopCard.setShopUnique(beansExchange.getShopUnique());
		shopCard.setCardId(beansExchange.getCardId());
		 List<ShopCard> cardList=diamondsDao.getShopCardList(shopCard);
		 if(cardList==null || cardList.size()<1)
		 {
			 	sr.setMsg("请核对银行卡信息！");
				sr.setStatus(0);
				return sr;
		 }
		//获取现有的钻石数量及相应的规则
		Map<String,Object> res=diamondsDao.getShopBeansAndRule(beansExchange);
		//剩余的可提现次数
		Integer count=diamondsDao.getCashCount(res);
		//费率
		Integer ptTax=Integer.parseInt(res.get("ptTax").toString());
		//取现金额
		double payMoney=beansExchange.getPayMoney();
		//当前百货豆余额
		Integer shopBeans=Integer.parseInt(res.get("shopBeans").toString());
		//实际取现数量+费率
		Integer receiveCount=(int) payMoney*100/(100-ptTax)*100;
		beansExchange.setReceiveCount(receiveCount);
		//店铺兑换前百货豆数量
		beansExchange.setBeansOldCount(shopBeans);
		//订单号
		String order_id = "tx" + System.currentTimeMillis();
		beansExchange.setOrderId(order_id);
		//1:买豆，2：提现
		beansExchange.setExchangeType(2);
		beansExchange.setServiceCharge(receiveCount-(int) payMoney*100);
		if(count<=0){
			sr.setMsg("今日提现次数已用尽！");
			sr.setStatus(0);
			return sr;
		}else if(receiveCount>shopBeans){
			sr.setMsg("账户余额不足,请刷新界面获取最新余额！");
			sr.setStatus(0);
			return sr;
		}else if(payMoney<Double.parseDouble(res.get("sMoney").toString())){
			sr.setMsg("取现金额小于最小取现金额！");
			sr.setStatus(0);
			return sr;
		}else if(payMoney>Double.parseDouble(res.get("mMoney").toString())){
			sr.setMsg("取现金额超过最大金额！");
			sr.setStatus(0);
			return sr;
		}else if(receiveCount<=0){
			sr.setMsg("取现数量必须大于零！");
			sr.setStatus(0);
			return sr;
		}
		//提交取现订单
		Integer k=diamondsDao.addNewCashRecord(beansExchange);
		if(k==0){
			sr.setStatus(0);
			sr.setMsg("提交失败!");
			return sr;
		}else
		{
			Map<String,Object> data=new HashMap<>();
			data.put("shopBeans", beansExchange.getReceiveCount()*-1);
			data.put("shopUnique", beansExchange.getShopUnique());
			//扣除百货豆
			diamondsDao.modifyShopDiamonds(data);
		}
		return sr;
	}
}
