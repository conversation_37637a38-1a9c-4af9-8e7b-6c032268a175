package org.haier.shopUpdate.service;

import org.haier.shopUpdate.dto.AddGoodsStockDto;
import org.haier.shopUpdate.entity.goods.goodsRecord.GoodsOperParam;
import org.haier.shopUpdate.util.ShopsResult;
import org.haier.shopUpdate.util.common.CommonResult;

import java.util.Map;


public interface StockService {
	
	/**
	 * 获取指定数据字典的字典数据
	 * @param code
	 * @return
	 */
	public CommonResult querySystemDict(String code);

	/**
	 * 添加新的入库 优先调用该方法
	 * @param dto
	 * @return
	 */
	public ShopsResult addGoodsStock(AddGoodsStockDto dto, GoodsOperParam goodsOperParam);
	/**
	 * 添加新的入库记录
	 * @deprecated 对外弃用 改用 to {@link #addGoodsStock(AddGoodsStockDto)}
	 * @param map
	 * @return
	 */
	@Deprecated
	public ShopsResult newStockRecord(Map<String,Object> map , GoodsOperParam goodsOperParam, Integer stockType);
	/**
	 * 查询店铺的出入库记录
	 * @param map
	 * @return
	 */
	public ShopsResult queryShopStockRecord(Map<String, Object> map);
	public ShopsResult addBatchStockRecord(Long shopUnique, Integer stockType, String supplier_unique, Integer staffId,
			String fail_id, String goods_stock_list, Integer stockOrigin, String stock_kind);
	public ShopsResult queryShopStockRecordList(Map<String, Object> map);
	public ShopsResult queryShopStockRecordDetail(Long shopUnique, String fail_id);
	public ShopsResult queryGoodsStockLast(Long shopUnique, String goods_barcode, String stock_type);
	public ShopsResult addAuditStock(String list_unique, String shop_unique, String audit_status,
			String stock_type_code);
	public ShopsResult editIntoStock(String shop_unique, String detailJson, String list_unique, String startTime,
			String user_id, String supplier_unique, String stock_kind);
	public ShopsResult qeryGoodsStockLog(String shop_unique, String goods_barcode, String stock_type);
	
	public CommonResult reason(Map<String,Object> map);
}
