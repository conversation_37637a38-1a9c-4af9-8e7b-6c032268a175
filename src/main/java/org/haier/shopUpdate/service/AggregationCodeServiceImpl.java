package org.haier.shopUpdate.service;

import lombok.extern.slf4j.Slf4j;
import org.haier.shopUpdate.dao.*;
import org.haier.shopUpdate.entity.*;
import org.haier.shopUpdate.params.GetAuditStatusParams;
import org.haier.shopUpdate.params.GetQualificationsParams;
import org.haier.shopUpdate.params.SaveAggregationCodeParams;
import org.haier.shopUpdate.project.ProjectConfig;
import org.haier.shopUpdate.result.getAggregatePayStatus.GetAuditStatusResult;
import org.haier.shopUpdate.result.getQualifications.GetQualificationsResult;
import org.haier.shopUpdate.util.ShopsResult;
import org.haier.shopUpdate.util.common.utils.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class AggregationCodeServiceImpl implements AggregationCodeService{

    @Autowired
    private ShopAggregationCodeUnionDao shopAggregationCodeUnionDao;

    @Autowired
    private ShopQualificationSettingsDao settingsDao;

    @Autowired
    private ShopsDao shopsDao;

    @Autowired
    private ShopAggregationCodeDao shopAggregationCodeDao;

    @Autowired
    private ShopQualificationInfoDao shopQualificationInfoDao;

    /**
     * 查询聚合码审核状态
     * @param params
     * @return
     */
    @Override
    public ShopsResult getAuditStatus(GetAuditStatusParams params) {
        ShopsResult sr = new ShopsResult(1, "查询成功！");
        if (StringUtils.isEmpty(params.getShopUnique())){
            sr.setStatus(0);
            sr.setMsg("请输入店铺编号");
            return sr;
        }

        //校验商户是否存在
        ShopsEntity shopsEntity = shopsDao.getByShopUnique(Long.parseLong(params.getShopUnique()));
        if (shopsEntity == null){
            sr.setStatus(0);
            sr.setMsg("未获取到商户信息");
            return sr;
        }
        try {
            GetAuditStatusResult result = new GetAuditStatusResult();

            //查询资质信息设置返回值
            doQueryShopQualificationInfo(params,result,shopsEntity);

            //查询聚合码设置项设置返回值
            doQueryShopQualificationSettings(result);

            sr.setData(result);
        }catch (Exception e){
            log.error("异常信息：",e);
            sr.setStatus(0);
            sr.setMsg("数据异常");
            return sr;
        }

        return sr;
    }

    /**
     * 查询资质信息
     * @param params
     * @return
     */
    @Override
    public ShopsResult getQualifications(GetQualificationsParams params) {
        ShopsResult sr = new ShopsResult(1, "查询成功！");
        if (StringUtils.isEmpty(params.getShopUnique())){
            sr.setStatus(0);
            sr.setMsg("请输入店铺编号");
            return sr;
        }

        //校验商户是否存在
        ShopsEntity shopsEntity = shopsDao.getByShopUnique(Long.parseLong(params.getShopUnique()));
        if (shopsEntity == null){
            sr.setStatus(0);
            sr.setMsg("未获取到商户信息");
            return sr;
        }

        try {
            GetQualificationsResult result = new GetQualificationsResult();
            //查询资质信息
            doQueryShopAggregationCode(params,result);

            sr.setData(result);
        }catch (Exception e){
            log.error("异常信息：",e);
            sr.setStatus(0);
            sr.setMsg("数据异常");
            return sr;
        }
        return sr;
    }

    /**
     * 保存资质信息
     * @param params
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShopsResult saveAggregationCode(SaveAggregationCodeParams params) {
        ShopsResult sr = new ShopsResult(1, "保存成功");
        //暂不先用框架校验了，手动简单校验
        if (StringUtils.isEmpty(params.getShopUnique())){
            sr.setStatus(0);
            sr.setMsg("请输入店铺编号");
            return sr;
        }
        //校验商户是否存在
        ShopsEntity shopsEntity = shopsDao.getByShopUnique(Long.parseLong(params.getShopUnique()));
        if (shopsEntity == null){
            sr.setStatus(0);
            sr.setMsg("未获取到商户信息");
            return sr;
        }
        if (params.getStafferId() == null){
            sr.setStatus(0);
            sr.setMsg("请输入员工编号");
            return sr;
        }
        if (params.getSaveType() == null || (params.getSaveType() != 1 && params.getSaveType() != 2)){
            sr.setStatus(0);
            sr.setMsg("保存类型错误");
            return sr;
        }

        try {
            String resp = doSaveAggregationCode(params);
            if (!"success".equals(resp)){
                sr.setStatus(0);
                sr.setMsg(resp);
                return sr;
            }
        }catch (Exception e){
            log.error("异常信息：",e);
            sr.setStatus(0);
            sr.setMsg("数据异常");
            return sr;
        }

        return sr;
    }

    /**
     * 查询资质信息构建返回参数
     * @param params
     * @param result
     */
    private void doQueryShopQualificationInfo(GetAuditStatusParams params,GetAuditStatusResult result,ShopsEntity shopsEntity){
        //查询是否存在店铺资质
        ShopAggregationCodeUnionEntity union = new ShopAggregationCodeUnionEntity();
        union.setShopUnique(Long.parseLong(params.getShopUnique()));
        ShopAggregationCodeUnionEntity unionEntity = shopAggregationCodeUnionDao.queryByShopUnique(union);

        //还没有申请过聚合码
        if (unionEntity == null){
            result.setAggregateAuditStatus(0);
            result.setAggregatePayImage("");
            result.setAggregateRefuseReason("");
            result.setAggregateApplyType(0);

            //如果设置过草稿，优先取草稿的申请类型
            ShopAggregationCodeEntity queryDraft = new ShopAggregationCodeEntity();
            queryDraft.setAggregateCodeType(1);
            queryDraft.setShopUnique(Long.parseLong(params.getShopUnique()));
            ShopAggregationCodeEntity draftEntity = shopAggregationCodeDao.queryByEntity(queryDraft);
            if (draftEntity != null){
                result.setAggregateApplyType(draftEntity.getAggregateApplyType() == null ? 0 : draftEntity.getAggregateApplyType());
            }

            return;
        }

        //查询聚合单
        ShopAggregationCodeEntity codeQueryEntity = new ShopAggregationCodeEntity();
        codeQueryEntity.setId(unionEntity.getAggregationCodeId());
        ShopAggregationCodeEntity codeEntity = shopAggregationCodeDao.queryByEntity(codeQueryEntity);

        //根据审核状态设置数据
        if (unionEntity.getAggregateAuditStatus() == 2){ //审核成功
            //将二维码图片的内容返回给前端，自己生成二维码
            result.setAggregatePayImage(StringUtils.join(ProjectConfig.PROJECT_URL + "/buyhooPay?type=aggregatedPay&shopUnique=",params.getShopUnique(), "&mqttId=&staffId=&shopName=",shopsEntity.getShopName()));
        }else if (unionEntity.getAggregateAuditStatus() == 3){ //审核失败
            //查询具体的失败原因
            result.setAggregateRefuseReason(codeEntity == null ? "" : codeEntity.getAggregateRefuseReason());
        }

        //聚合码申请状态
        result.setAggregateAuditStatus(unionEntity.getAggregateAuditStatus());

        //申请人类型
        result.setAggregateApplyType(codeEntity.getAggregateApplyType() == null ? 0 : codeEntity.getAggregateApplyType());

    }

    /**
     * 查询聚合码设置并设置返回参数
     * @param result
     */
    private void doQueryShopQualificationSettings(GetAuditStatusResult result){
        ShopQualificationSettingsEntity entity = settingsDao.queryOneSetting();
        if (entity == null){
            result.setNotLegalGuideImage("");
            result.setAggregateIndexImage("");
            result.setHelibaoAuthBookUrl("");
            result.setRuiyinxinAuthBookUrl("");
            return;
        }

        result.setNotLegalGuideImage(entity.getNotLegalGuideImage());
        result.setAggregateIndexImage(entity.getAggregateIndexImage());
        result.setHelibaoAuthBookUrl(entity.getHelibaoAuthBookUrl());
        result.setRuiyinxinAuthBookUrl(entity.getRuiyinxinAuthBookUrl());
    }

    /**
     * 查询资质信息
     * @param result
     */
    private void doQueryShopAggregationCode(GetQualificationsParams params, GetQualificationsResult result){
        //查询是否存在店铺资质
        ShopAggregationCodeUnionEntity union = new ShopAggregationCodeUnionEntity();
        long shopUnique = Long.parseLong(params.getShopUnique());
        union.setShopUnique(shopUnique);
        ShopAggregationCodeUnionEntity unionEntity = shopAggregationCodeUnionDao.queryByShopUnique(union);

        if (unionEntity == null){ //还没有提交过资质
            //查询草稿数据
            ShopAggregationCodeEntity queryDraft = new ShopAggregationCodeEntity();
            queryDraft.setAggregateCodeType(1);
            queryDraft.setShopUnique(shopUnique);
            ShopAggregationCodeEntity draftEntity = shopAggregationCodeDao.queryByEntity(queryDraft);

            //查询资质表看其他地方是否有上传过
            ShopQualificationInfoEntity infoEntity = shopQualificationInfoDao.queryByShopUnique(shopUnique);

            //如果有草稿优先使用草稿，没有草稿取其他地方上传的
            handleDraftAndQualificationInfo(result,draftEntity,infoEntity);
        }else {
            //已经存在店铺资质，则直接查询当前的资质
            ShopAggregationCodeEntity queryEntity = new ShopAggregationCodeEntity();
            queryEntity.setId(unionEntity.getAggregationCodeId());
            ShopAggregationCodeEntity codeEntity = shopAggregationCodeDao.queryByEntity(queryEntity);
            BeanUtils.copyProperties(codeEntity,result);
        }
    }

    /**
     * 处理草稿资质和资质信息
     * @param result
     * @param draftEntity
     * @param infoEntity
     */
    private void handleDraftAndQualificationInfo(GetQualificationsResult result,ShopAggregationCodeEntity draftEntity,ShopQualificationInfoEntity infoEntity){
        if (draftEntity == null){
            draftEntity = new ShopAggregationCodeEntity();
        }
        if (infoEntity == null){
            infoEntity = new ShopQualificationInfoEntity();
        }
        result.setLicense(handleDraftAndInfoData(draftEntity.getLicense(),infoEntity.getLicense()));
        result.setLicenseHand(handleDraftAndInfoData(draftEntity.getLicenseHand(),infoEntity.getLicenseHand()));
        result.setLicenseMerchantName(handleDraftAndInfoData(draftEntity.getLicenseMerchantName(),infoEntity.getLicenseMerchantName()));
        result.setLicenseCode(handleDraftAndInfoData(draftEntity.getLicenseCode(),infoEntity.getLicenseCode()));
        result.setLegalName(handleDraftAndInfoData(draftEntity.getLegalName(),infoEntity.getLegalName()));
        result.setLicenseAddress(handleDraftAndInfoData(draftEntity.getLicenseAddress(),infoEntity.getLicenseAddress()));
        result.setLegalIdCardPortrait(handleDraftAndInfoData(draftEntity.getLegalIdCardPortrait(),infoEntity.getLegalIdCardPortrait()));
        result.setLegalIdCardEmblem(handleDraftAndInfoData(draftEntity.getLegalIdCardEmblem(),infoEntity.getLegalIdCardEmblem()));
        result.setLegalIdCardHand(handleDraftAndInfoData(draftEntity.getLegalIdCardHand(),infoEntity.getLegalIdCardHand()));
        result.setLegalIdCardCode(handleDraftAndInfoData(draftEntity.getLegalIdCardCode(),infoEntity.getLegalIdCardCode()));
        result.setBankCardFront(handleDraftAndInfoData(draftEntity.getBankCardFront(),infoEntity.getBankCardFront()));
        result.setBankCardBack(handleDraftAndInfoData(draftEntity.getBankCardBack(),infoEntity.getBankCardBack()));
        result.setBankCode(handleDraftAndInfoData(draftEntity.getBankCode(),infoEntity.getBankCode()));
        result.setOpenBank(handleDraftAndInfoData(draftEntity.getOpenBank(),infoEntity.getOpenBank()));
        result.setShopDoorhead(handleDraftAndInfoData(draftEntity.getShopDoorhead(),infoEntity.getShopDoorhead()));
        result.setShopInside(handleDraftAndInfoData(draftEntity.getShopInside(),infoEntity.getShopInside()));
        result.setShopCashier(handleDraftAndInfoData(draftEntity.getShopCashier(),infoEntity.getShopCashier()));
        result.setCardholderIdCardPortrait(handleDraftAndInfoData(draftEntity.getCardholderIdCardPortrait(),infoEntity.getCardholderIdCardPortrait()));
        result.setCardholderIdCardEmblem(handleDraftAndInfoData(draftEntity.getCardholderIdCardEmblem(),infoEntity.getCardholderIdCardEmblem()));
        result.setCardholderName(handleDraftAndInfoData(draftEntity.getCardholderName(),infoEntity.getCardholderName()));
        result.setCardholderIdCardCode(handleDraftAndInfoData(draftEntity.getCardholderIdCardCode(),infoEntity.getCardholderIdCardCode()));
        result.setAggregateApplyType(draftEntity.getAggregateApplyType() == null ? 0 : draftEntity.getAggregateApplyType());
        result.setHelibaoAuth(StringUtils.isEmpty(draftEntity.getHelibaoAuth()) ? "" : draftEntity.getHelibaoAuth());
        result.setRuiyinxinAuth(StringUtils.isEmpty(draftEntity.getRuiyinxinAuth()) ? "" : draftEntity.getRuiyinxinAuth());
    }

    /**
     * 处理草稿资质和资质信息
     * @param draft
     * @param info
     * @return
     */
    private String handleDraftAndInfoData(String draft,String info){
        //草稿数据存在，优先使用草稿，草稿不存在，使用资质，都不存在返回空串
        return StringUtils.isEmpty(draft) ? (StringUtils.isEmpty(info) ? "" : info) : draft;
    }

    /**
     * 保存聚合码资质
     * @param params
     */
    private String doSaveAggregationCode(SaveAggregationCodeParams params){
        ShopAggregationCodeEntity newAggCode = new ShopAggregationCodeEntity();
        BeanUtils.copyProperties(params,newAggCode);

        long shopUnique = Long.parseLong(params.getShopUnique());
        newAggCode.setShopUnique(shopUnique);
        //查询草稿数据
        ShopAggregationCodeEntity queryDraft = new ShopAggregationCodeEntity();
        queryDraft.setAggregateCodeType(1);
        queryDraft.setShopUnique(shopUnique);
        ShopAggregationCodeEntity draftEntity = shopAggregationCodeDao.queryByEntity(queryDraft);

        if (params.getSaveType() == 1){ //数据保存
            if (draftEntity == null){ //没有草稿数据
                newAggCode.setAggregateCodeType(1);
                shopAggregationCodeDao.insert(newAggCode);
            }else {
                newAggCode.setId(draftEntity.getId());
                shopAggregationCodeDao.updateById(newAggCode);
            }
        }else if (params.getSaveType() == 2){ //数据提交
            //查询是否存在店铺资质
            ShopAggregationCodeUnionEntity union = new ShopAggregationCodeUnionEntity();
            union.setShopUnique(shopUnique);
            ShopAggregationCodeUnionEntity unionEntity = shopAggregationCodeUnionDao.queryByShopUnique(union);

            if (draftEntity == null){ //没有草稿
                //保存聚合码详情
                newAggCode.setAggregateCodeType(2);
                shopAggregationCodeDao.insert(newAggCode);

                if (unionEntity == null){ //没有保存过数据
                    //保存关联关系
                    ShopAggregationCodeUnionEntity insertEntity = new ShopAggregationCodeUnionEntity();
                    insertEntity.setShopUnique(shopUnique);
                    insertEntity.setAggregationCodeId(newAggCode.getId());
                    insertEntity.setAggregateAuditStatus(1);
                    shopAggregationCodeUnionDao.insert(insertEntity);
                }else {
                    //更新关联关系
                    unionEntity.setAggregationCodeId(newAggCode.getId());
                    unionEntity.setAggregateAuditStatus(1);
                    shopAggregationCodeUnionDao.updateById(unionEntity);
                }

            }else { //存在草稿

                //更新草稿为非草稿
                newAggCode.setAggregateCodeType(2);
                newAggCode.setId(draftEntity.getId());
                shopAggregationCodeDao.updateById(newAggCode);

                if (unionEntity == null) { //没有保存过数据
                    ShopAggregationCodeUnionEntity insertEntity = new ShopAggregationCodeUnionEntity();
                    insertEntity.setShopUnique(shopUnique);
                    insertEntity.setAggregationCodeId(draftEntity.getId());
                    insertEntity.setAggregateAuditStatus(1);
                    shopAggregationCodeUnionDao.insert(insertEntity);
                }else { //提交过修改数据
                    unionEntity.setAggregationCodeId(draftEntity.getId());
                    unionEntity.setAggregateAuditStatus(1);
                    shopAggregationCodeUnionDao.updateById(unionEntity);
                }
            }

        }

        return "success";
    }

}
