package org.haier.shopUpdate.service;

import org.haier.shopUpdate.params.RecordGoodsOper.QueryRecordGoodsOperDetailParams;
import org.haier.shopUpdate.params.RecordGoodsOper.QueryRecordGoodsOperListParams;
import org.haier.shopUpdate.util.ShopsResult;

public interface RecordGoodsOperService {
	public ShopsResult queryRecordGoodsOper(QueryRecordGoodsOperListParams params);

	public ShopsResult queryRecordGoodsOperDetail(QueryRecordGoodsOperDetailParams params);
}
