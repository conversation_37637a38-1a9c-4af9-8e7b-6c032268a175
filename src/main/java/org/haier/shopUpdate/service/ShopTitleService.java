package org.haier.shopUpdate.service;

import java.util.List;

import org.haier.shopUpdate.entity.ShopTitle;
import org.haier.shopUpdate.util.ShopsResult;
import org.haier.shopUpdate.util.common.CommonResult;

public interface ShopTitleService {
	/**
	 * 查询商家端APP主界面的模块信息
	 * @param shopTitle
	 * @return
	 */
	public ShopsResult queryMainPageTitle(ShopTitle shopTitle);
	
	/**
	 * 查询商家端APP主界面的模块信息
	 * @param shopTitle
	 * @return
	 */
	public CommonResult queryMainPageTitle_v2(ShopTitle shopTitle);
	
	/**
	 * 更新店铺模块的排序信息
	 * @param list
	 * @return
	 */
	public ShopsResult modifyTitle(List<ShopTitle> list);
}
