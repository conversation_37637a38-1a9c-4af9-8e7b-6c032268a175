package org.haier.shopUpdate.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.tools.ant.util.DateUtils;
import org.haier.shopUpdate.dao.*;
import org.haier.shopUpdate.dao.dojo.QueryGoodsInfoDo;
import org.haier.shopUpdate.dao.pojo.AddGoodsPo;
import org.haier.shopUpdate.dao.pojo.GoodsBatchAddPo;
import org.haier.shopUpdate.dto.*;
import org.haier.shopUpdate.entity.*;
import org.haier.shopUpdate.entity.electronic.BaseGoodsMsg;
import org.haier.shopUpdate.entity.electronic.CreateOrUpdateGoodsListParams;
import org.haier.shopUpdate.entity.goods.goodsEnum.OperTypeEnum;
import org.haier.shopUpdate.entity.goods.goodsEnum.DeviceSourceEnum;
import org.haier.shopUpdate.entity.goods.goodsEnum.OperSourceEnum;
import org.haier.shopUpdate.entity.goods.goodsEnum.UserTypeEnum;
import org.haier.shopUpdate.entity.goods.goodsRecord.RecordGoods;
import org.haier.shopUpdate.entity.goods.goodsRecord.RecordGoodsOper;
import org.haier.shopUpdate.entity.goods.goodsRecord.RecordGoodsOperParams;
import org.haier.shopUpdate.entity.promotion.PromotionActivity;
import org.haier.shopUpdate.enums.GoodsInPriceTypeEnums;
import org.haier.shopUpdate.enums.IsIoBoundInspectEnum;
import org.haier.shopUpdate.enums.shopStockDetail.StockKindEnum;
import org.haier.shopUpdate.enums.shopStockDetail.StockOriginEnum;
import org.haier.shopUpdate.enums.shopStockDetail.StockResourceEnum;
import org.haier.shopUpdate.enums.shopStockDetail.StockTypeEnum;
import org.haier.shopUpdate.log.enums.BusinessType;
import org.haier.shopUpdate.log.util.SendDingDingTalkUtils;
import org.haier.shopUpdate.oss.OssConstant;
import org.haier.shopUpdate.oss.UploadResult;
import org.haier.shopUpdate.params.QueryPurchaseOrderGoodsParams;
import org.haier.shopUpdate.params.UpdateReplenishmentGoodsParams;
import org.haier.shopUpdate.params.goods.*;
import org.haier.shopUpdate.params.goodsOnlineSetting.MinSaleCountAddParams;
import org.haier.shopUpdate.params.goodsOnlineSetting.MinSaleCountUpdateParams;
import org.haier.shopUpdate.params.shopStock.ShopStockAddParams;
import org.haier.shopUpdate.params.shopStockDetail.ShopStockDetailAddParams;
import org.haier.shopUpdate.redism.RedisCache;
import org.haier.shopUpdate.result.goods.allocation.QueryGoodsLastInPriceResult;
import org.haier.shopUpdate.result.goodsOnlineSetting.GoodsOnlineSettingDto;
import org.haier.shopUpdate.upload.FileUploadService;
import org.haier.shopUpdate.util.*;
import org.haier.shopUpdate.util.common.StringUtils;
import org.haier.shopUpdate.util.helibao.HelibaoPayConfig;
import org.haier.shopUpdate.util.thread.SendMqttMsg;
import org.haier.shopUpdate.util.unionpay.HttpUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.sql.Timestamp;
import java.util.*;
@Slf4j
@Service
@Transactional
public class GoodsServiceImpl implements GoodsService {
    @Resource
    private GoodsDao goodsDao;
    @Resource
    private GoodsBatchDao goodsBatchDao;
    @Resource
    private GoodsOnlineSettingDao goodsOnlineSettingDao;
    @Resource
    private StockDao stockDao;
    @Resource
    private GoodsDictDao dictDao;
    @Resource
    private ShopsConfigDao shopsConfigDao;
    @Resource
    private PurDao purDao;
    @Resource
    private GoodsKindsDao kindDao;
    @Resource
    private GoodsKindsService kindsService;
    @Resource
    private StockService stockService;
    @Resource
    private ShopSupplierService shopSupplierService;
    @Resource
    private RedisCache redis;
    @Resource
    private ShopStaffDao shopStaffDao;
    @Resource
    private SendDingDingTalkUtils sendDingDingTalkUtils;
    @Resource
    private GoodsPositionServiceImpl goodsPositionService;
    @Resource
    private FileUploadService fileUploadService;
    @Resource
    private BHService bhService;
    @Resource
    private I18nLanguageStaticReturnParamsUtil i18nRtUtil;
    @Resource
    private GoodsWholesaleMapper goodsWholesaleMapper;
    /**
     * 查询店铺的促销商品列表
     * @param params
     * @return
     */
    public ShopsResult queryPromotionActivityList(QueryPromotionActivityListParams params) {
        List<PromotionActivity> list = goodsDao.queryPromotionActivityList(params);
        List<PromotionActivity> resList = new ArrayList<>();

        //依次筛选出各种活动类型，查询各种类型的详情，并返回结果
        List<PromotionActivity> type1List = new ArrayList<>();
        List<PromotionActivity> type2List = new ArrayList<>();
        List<PromotionActivity> type3List = new ArrayList<>();
        List<PromotionActivity> type4List = new ArrayList<>();

        for(PromotionActivity activity : list) {
            switch (activity.getType()) {
                case 1:
                    type1List.add(activity);
                    break;
                case 2:
                    type2List.add(activity);
                    break;
                case 3:
                    type3List.add(activity);
                    break;
                case 4:
                    type4List.add(activity);
                    break;
            }
        }

        //依次查询各情况的信息列表
        List<Integer> ids = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(type1List)) {
            for(PromotionActivity activity : type1List) {
                ids.add(activity.getPromotionActivityId());
            }
            type1List.clear();
            type1List = goodsDao.queryPromotionActivityGoodsMarkdown(ids);
            resList.addAll(type1List);
        }
        ids.clear();
        if (CollectionUtils.isNotEmpty(type2List)) {
            for(PromotionActivity activity : type2List) {
                ids.add(activity.getPromotionActivityId());
            }
            type2List.clear();
            type2List = goodsDao.queryPromotionActivityGiftlList(ids);
            resList.addAll(type2List);
        }
        if (CollectionUtils.isNotEmpty(type3List)) {
            for(PromotionActivity activity : type3List) {
                ids.add(activity.getPromotionActivityId());
            }
            type3List.clear();
            type3List = goodsDao.queryPromotionActivityOrderMarkdown(ids);
            resList.addAll(type3List);
        }
        if (CollectionUtils.isNotEmpty(type4List)) {
            for(PromotionActivity activity : type4List) {
                ids.add(activity.getPromotionActivityId());
            }
            type4List.clear();
            type4List = goodsDao.queryPromotionActivitySingle(ids);
            resList.addAll(type4List);
        }

        return ShopsResult.ok(resList);
    }
    /**
     * 1、添加或更新商品信息
     * @param shopUnique
     * @param goodsBarcode
     * @param goodsName
     * @param goodsPrice
     * @param kindUnique
     * @param goodsChengType
     * @return
     */
    public ShopsResult addNewGoodsNP(String shopUnique,String goodsBarcode,String goodsName,String goodsPrice,String kindUnique,String goodsChengType) {
        ShopsResult sr = new ShopsResult(1,"添加成功");
        Map<String,Object> map = new HashMap<String,Object>();
        map.put("goodsName", goodsName);
        map.put("kindUnique", kindUnique);
        map.put("goodsInPrice", "0");
        map.put("goodsSalePrice", goodsPrice);
        map.put("goodsContain", 1);
        map.put("goodsUnit", "斤");
        map.put("goodsCusPrice", goodsPrice);
        map.put("goodsChengType", goodsChengType);
        map.put("goodsWebSalePrice", goodsPrice);
        map.put("shopUnique", shopUnique);
        if(null == goodsBarcode) {
            Long barcode = goodsDao.queryMaxGoodsBarcode(map);
            goodsBarcode = (barcode + 1) + "";
            map.put("goodsBarcode", goodsBarcode);
            map.put("foreignKey", goodsBarcode);

            goodsDao.newGoodsMessage(map);
        }else {
            map.put("goodsBarcode", goodsBarcode);
            System.out.println("需要更新的商品信息");
            goodsDao.modifyGoods(map);
        }
        sr.setData(map);
        return sr;
    }

    /**
     *
     * @param shopUnique 店铺编号
     * @param pages 页码
     * @param pageSize 单页查询数量
     * @param classUnique 大分类编号
     * @param supplierUnique 供货商编号
     * @param goodsMsg 商品输入框信息
     * @return
     */
    public ShopsResult queryFarmGoodsList(String shopUnique,Integer pages,Integer pageSize,String classUnique,String supplierUnique,String goodsMsg,Integer goodsType ) {
        ShopsResult sr = new ShopsResult(1,"查询成功！");
        Map<String,Object> map = new HashMap<String,Object>();
        map.put("shopUnique", shopUnique);
        map.put("startNum", (pages - 1) * pageSize);
        map.put("pageSize", pageSize);
        map.put("classUnique", classUnique);
        map.put("supplierUnique", supplierUnique);
        map.put("goodsMsg", goodsMsg);
        map.put("goodsType", goodsType);

        List<Map<String,Object>> goodsList = goodsDao.queryFarmGoodsList(map);

        Integer goodsTotal = goodsDao.queryFarmGoodsCount(map);

        map.clear();
        map.put("goodsList", goodsList);
        map.put("goodsTotal", goodsTotal);

        sr.setData(map);

        return sr;
    }
    /**
     * 查询商品信息
     *
     * @param map
     * @return
     */
    public ShopsResult queryGoodsMessage(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult(1, "查询成功！");
        List<Map<String, Object>> data = goodsDao.queryGoodsMessage(map);
        List<QueryGoodsLastInPriceResult> priceList ;
        if (ObjectUtil.isNotEmpty(data)) {
            QueryGoodsLastInPriceParams params = new QueryGoodsLastInPriceParams();
            params.setShopUnique(map.get("shopUnique").toString());
            List<String> barcodes = new ArrayList<>();
            for (Integer i = 0; i < data.size(); i++) {
                barcodes.add(data.get(i).get("goodsBarcode").toString());
            }
            params.setList(barcodes);
            priceList = goodsDao.queryGoodsLastInPrice(params);
        } else {
            priceList = new ArrayList<>();
        }

        for (Integer i = 0; i < data.size(); i++) {
            Map<String,Object> goods = data.get(i);
            for (Integer j = 0; j < priceList.size(); j++) {
                if (goods.get("goodsBarcode").toString().equals(priceList.get(j).getGoodsBarcode())) {
                    goods.put("goodsInPrice", priceList.get(j).getStockPrice());
                    break;
                }
            }
        }
        sr.setStatus(1);
        sr.setPageIndex(Integer.parseInt(map.get("pageIndex").toString()));
        sr.setPageSize(data.size());
        sr.setData(data);
        return sr;
    }

    /**
     * 查询商品信息
     *
     * @param map
     * @return
     */
    public ShopsResult queryGoodsMessageInvented(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult(1, "查询成功！");
        List<Map<String, Object>> data = goodsDao.queryGoodsMessageInvented(map);

        List<QueryGoodsLastInPriceResult> priceList ;
        if (ObjectUtil.isNotEmpty(data)) {
            QueryGoodsLastInPriceParams params = new QueryGoodsLastInPriceParams();
            params.setShopUnique(map.get("shopUnique").toString());
            List<String> barcodes = new ArrayList<>();
            for (Integer i = 0; i < data.size(); i++) {
                barcodes.add(data.get(i).get("goodsBarcode").toString());
            }
            params.setList(barcodes);
            priceList = goodsDao.queryGoodsLastInPrice(params);
        } else {
            priceList = new ArrayList<>();
        }

        for (Integer i = 0; i < data.size(); i++) {
            Map<String,Object> goods = data.get(i);
            for (Integer j = 0; j < priceList.size(); j++) {
                if (goods.get("goodsBarcode").toString().equals(priceList.get(j).getGoodsBarcode())) {
                    goods.put("goodsInPrice", priceList.get(j).getStockPrice());
                    break;
                }
            }
        }
        sr.setStatus(1);
        sr.setPageIndex(Integer.parseInt(map.get("pageIndex").toString()));
        sr.setPageSize(data.size());
        sr.setData(data);
        return sr;
    }

    public static String isImagesTrue(String posturl) {
        try {
            posturl = posturl.replaceAll("\\\\", "/");
            URL url = new URL(posturl);
            HttpURLConnection urlcon = (HttpURLConnection) url.openConnection();
            urlcon.setRequestMethod("GET");
            urlcon.setRequestProperty("Content-type", "application/x-www-form-urlencoded");
            if (urlcon.getResponseCode() == HttpURLConnection.HTTP_OK) {
                //System.out.println(HttpURLConnection.HTTP_OK + posturl + ":posted ok!");
                return "200";
            } else {
                //System.out.println(urlcon.getResponseCode() + posturl + ":Bad post...");
                return "404";
            }
        } catch (Exception e) {
            return "404";
        }

    }

    /**
     * 删除店铺商品
     *
     * @param map
     * @return
     */
    public ShopsResult deleteShopsGoods(Map<String, Object> map) {
        int k = goodsDao.deleteShopsGoods(map);
        if (k < 1) {
            return ShopsResult.fail(map.get("goodsBarcode") + "该商品不存在！");
        }

        //添加商品删除记录
        k = goodsDao.addNewGoodsDeleteRecord(map);
        if (k < 1) {
            return ShopsResult.fail("新增删除记录失败！");
        }


        return ShopsResult.ok("删除成功");
    }

    /**
     * 更新商品信息
     *
     * @param map
     * @return
     */
    public ShopsResult modifyGoods(Map<String, Object> map, HttpServletRequest request) {
        ShopsResult sr = new ShopsResult();

        MultipartFile file = ShopsUtil.testMulRequest(request, "goodsPicture");
        if (file != null) {//检测商品图片是否存在,若存在，则保存，并添加新路径
            String shopUnique = map.get("shopUnique").toString();
            String orName = file.getOriginalFilename();//获取文件原名称
            String lastName = orName.substring(orName.lastIndexOf("."));
            String newGoodsName = map.get("goodsBarcode").toString() + Math.round(Math.random() * 100) + lastName;//新的保存文件名称
            String filePath = OssConstant.IMAGE_SOURCE + shopUnique + "/" + newGoodsName;
            UploadResult fileResult = null;
            try {
                fileResult = fileUploadService.uploadFileByPath(file.getInputStream(), filePath, file.getContentType());
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            map.put("goodsPicturepath", ObjectUtil.isNull(fileResult) ? "" : fileResult.getUrl());
        }
        int k = goodsDao.modifyGoods(map);
        if (k == 0) {
            sr.setStatus(2);
            sr.setMsg("更新失败");
            return sr;
        }
        sr.setStatus(1);
        sr.setMsg("更新成功！");
        return sr;
    }

    /**
     * 商品详情查询
     *
     * @param map
     * @return
     */
    public ShopsResult goodsDetail(Map<String, String> map) {
        ShopsResult sr = new ShopsResult();
        System.out.println(map);
        BaseGoods data = goodsDao.goodsDetail(map);
        System.out.println("商品三级测试 ----- " + data);
//		List<Map<String,Object>> data=goodsDao.goodsDetail(map);
        if (null == data) {
            sr.setStatus(2);
            sr.setMsg("商品信息不存在！");
            return sr;
        }
        List<GoodsPacking> listDetail = data.getListDetail();
        if (CollectionUtils.isNotEmpty(listDetail)) {
            List<String> goodsPositions = new ArrayList<>();
            for (GoodsPacking goodsPacking : listDetail) {
                if (StrUtil.isNotEmpty(goodsPacking.getGoodsPosition())) {
                    goodsPositions.add(goodsPacking.getGoodsPosition());
                }
                Map<String, Object> wholesaleMap = new HashMap<>();
                wholesaleMap.put("goodsBarcode", goodsPacking.getGoodsBarcode());
                wholesaleMap.put("shopUnique", map.get("shopUnique"));
                List<GoodsWholesaleEntity> listWholesale = goodsWholesaleMapper.queryByGoodsBarcode(wholesaleMap);

                if (ObjectUtil.isNotEmpty(listWholesale)) {
                    List<GoodsWholesaleInfo> goodsWholesaleInfoList = new ArrayList<>();
                    for (GoodsWholesaleEntity goodsWholesaleEntity : listWholesale) {
                        GoodsWholesaleInfo goodsWholesaleInfo = new GoodsWholesaleInfo();
                        goodsWholesaleInfo.setId(goodsWholesaleEntity.getId());
                        goodsWholesaleInfo.setWholesaleCount(goodsWholesaleEntity.getWholesaleCount());
                        goodsWholesaleInfo.setWholesalePrice(goodsWholesaleEntity.getWholesalePrice());
                        goodsWholesaleInfoList.add(goodsWholesaleInfo);
                    }
                    goodsWholesaleInfoList.sort(Comparator.comparing(GoodsWholesaleInfo::getId));
                    goodsPacking.setWholesaleList(goodsWholesaleInfoList);
                }
            }
            Map<String, String> longStringMap = goodsPositionService.queryDetailByPositionId(goodsPositions);
            for (GoodsPacking goodsPacking : listDetail) {
                goodsPacking.setCompletePositionName(longStringMap.get(goodsPacking.getGoodsPosition()));
            }
        }
        sr.setStatus(1);
        sr.setMsg("查询成功！");
        sr.setData(data);
        return sr;
    }

    /**
     * 查询商品供货商详情
     *
     * @param map
     * @return
     */
    public ShopsResult goodsSupplierQuery(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        List<Map<String, Object>> data = goodsDao.goodsSupplierQuery(map);
        if (null == data) {
            sr.setData(ShopsUtil.map);
        }
        sr.setStatus(1);
        sr.setMsg("查询成功！");
        sr.setData(data);
        return sr;
    }

    /**
     * 查询商品供货商详情
     *
     * @param map
     * @return
     */
    public ShopsResult goodsSupplierQueryNew(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        System.out.println("商品供货商信息查询" + map);
        List<Map<String, Object>> data = goodsDao.goodsSupplierQueryNew(map);
        if (null == data) {
            sr.setData(ShopsUtil.map);
        }
        sr.setStatus(1);
        sr.setMsg("查询成功！");
        sr.setData(data);
        return sr;
    }

    /**
     * 更新商品信息
     *
     * @param map 缺失信息：商品图片，商品的foreign_key外键不对
     * @return
     * @throws Exception
     * @throws Exception
     */
    public ShopsResult updateListGoodsMessage(Map<String, Object> map, String goodsMessage, HttpServletRequest request, Double goodsCount,
                                              String goodsBarcode, Long shopUnique, Long foreignKey, Integer sameType, String supplierUnique, String goodsPicturePath, Integer tableType, Integer goodsChengType) throws Exception {
        ShopsResult sr = new ShopsResult();
//		List<Map<String,Object>> ordata=new ArrayList<Map<String,Object>>();//存放需要保存的数据
//		System.out.println(goodsMessage);
        String[] listGoods = goodsMessage.split("]@");
        MultipartFile file = ShopsUtil.testMulRequest(request, "goodsPicture");
        Map<String, Object> nmap = new HashMap<String, Object>();//存放需要新添加的商品
        List<String> barcodes = new ArrayList<String>();//
        String newKey = null;
        if (null == goodsBarcode || goodsBarcode.equals("")) {
            goodsBarcode = listGoods[0].split(":;:")[0];
        }
        if (null != file) {//图片信息处理
            String orName = file.getOriginalFilename();
            String lastName = orName.substring(orName.lastIndexOf("."));
            String ngoods = goodsBarcode + Math.round(Math.random() * 100) + lastName;
            String catPath = request.getServletContext().getRealPath("");//项目所在绝对路径
            catPath = new File(catPath).getParent();
            String filePath = catPath + File.separator + "image" + File.separator + shopUnique;

            String filePathDetail = "/" + OssConstant.IMAGE_SOURCE + shopUnique;

            InputStream is = file.getInputStream();
            boolean flag = ShopsUtil.savePicture(file, filePath, ngoods);//图片保存本地
            ShopsUtil.targetZoomOut(filePath + File.separator + ngoods, filePath + File.separator + ngoods, filePath, 500, 500);
            is = Files.newInputStream(new File(filePath + File.separator + ngoods).toPath());

            fileUploadService.uploadFileByPath(is, filePathDetail + "/" + ngoods, file.getContentType());
            //中图
            String filePath2 = Objects.requireNonNull(this.getClass().getClassLoader().getResource("../../")).getPath();
            filePath2 = filePath2.substring(0, filePath2.length() - request.getContextPath().length()) + File.separator + "middle" + filePathDetail;
            ShopsUtil.targetZoomOut(filePath + File.separator + ngoods, filePath2 + File.separator + ngoods, filePath2, 150, 150);
            is = Files.newInputStream(new File(filePath2 + File.separator + ngoods).toPath());
            fileUploadService.uploadFileByPath(is, OssConstant.IMAGE_MIDDLE + shopUnique + "/" + ngoods, file.getContentType());
            //小图
            String filePath3 = Objects.requireNonNull(this.getClass().getClassLoader().getResource("../../")).getPath();
            filePath3 = filePath3.substring(0, filePath3.length() - request.getContextPath().length()) + File.separator + "small" + filePathDetail;
            ShopsUtil.targetZoomOut(filePath + File.separator + ngoods, filePath3 + File.separator + ngoods, filePath3, 100, 100);
            is = Files.newInputStream(new File(filePath3 + File.separator + ngoods).toPath());
            fileUploadService.uploadFileByPath(is, OssConstant.IMAGE_SMALL + shopUnique + "/" + ngoods, file.getContentType());
            if (flag) {
                map.put("goodsPicturepath", filePathDetail + "/" + ngoods);
            }

        } else {
            map.put("goodsPicturepath", goodsPicturePath);
        }
        try {
            List<Map<String, Object>> returnList = new ArrayList<>();
            for (int i = 0; i < listGoods.length; i++) {//商品信息处理
//			System.out.println("<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<"+listGoods[i]);
                String[] goods = listGoods[i].split(":;:");
                for (int j = 0; j < goods.length; j++) {
                    if (goods[j].equals("@$@")) {
                        goods[j] = null;
                    }
                }

                if (i == 0 && (null == goods[0] || null == goods[4])) {
                    sr.setStatus(2);
                    sr.setMsg("商品条码不能为空");
                    return sr;
                } else if (null == goods[0]) {
//				System.out.println("其他规格");
                    break;
                }
                String tbarcode = goods[0].trim();
                if (goods[0].trim().startsWith("0")) {
                    tbarcode = ShopsUtil.removeZeroHead(tbarcode);//根据此值测试是否有该商品相关的以0开头的商品
                }
//			if(shopUnique.equals("1553299966652")){
//				tbarcode=tbarcode.replaceAll("[^\\d]+", "");
//			}
                nmap.put("tbarcode", "0%" + tbarcode);
                nmap.put("shopUnique", shopUnique);
//			System.out.println("商品重复查询！！！！"+nmap);
                List<String> list = goodsDao.queryGoodsStartWithZero(nmap);
                boolean flag = true;
//			System.out.println("商品重复查询"+list);
                if (list.size() > 0) {
                    for (String s : list) {
                        String barcode = ShopsUtil.removeZeroHead(s);
                        if (barcode.equals(goods[0].trim())) {
                            nmap.put("goodsBarcode", s);
                            flag = false;
                            break;
                        }
                    }
                }
                if (flag) {
                    nmap.put("goodsBarcode", goods[0].trim());
                }
//			System.out.println(nmap);
                if (i == 0 && (null == foreignKey || foreignKey == 0 || foreignKey == 1)) {
                    newKey = goods[0].replaceAll("[^\\d]+", "");
                    nmap.put("foreignKey", newKey);
                    foreignKey = Long.parseLong(newKey);
                } else {
                    nmap.put("foreignKey", foreignKey);
                }
                barcodes.add(goods[0]);
                nmap.put("goodsName", ChineseCharToEn.replaceLetter(goods[1]));

                nmap.put("goodsAlias", ChineseCharToEn.getAllFirstLetter(goods[1]));
                if (goods[2] == null || "".equals(goods[2].trim())) {
                    nmap.put("goodsInPrice", "0");
                } else {
                    nmap.put("goodsInPrice", goods[2]);
                }
                nmap.put("goodsSalePrice", goods[3]);
                Double contain = new Double(goods[4]);
                if (i == 0) {
                    if (contain != 1) {
                        sr.setStatus(2);
                        sr.setMsg("最小规格商品的最小单位数量必须为1");
                        return sr;
                    }
                } else {
                    if (contain <= 1) {
                        sr.setStatus(2);
                        sr.setMsg("非最小规格商品的最小单位数量必须 大于1");
                        return sr;
                    }
                }
                nmap.put("goodsContain", contain);
                nmap.put("goodsUnit", goods[5]);
                nmap.put("goodsDiscount", goods[7]);
                nmap.put("goodsCusPrice", goods[6]);
                nmap.put("goodsStandard", goods[8]);
                nmap.put("goodsChengType", goodsChengType);
                if (goods.length >= 13) {
                    nmap.put("goodsWebSalePrice", goods[12]);
                } else {
                    nmap.put("goodsWebSalePrice", goods[3]);
                }
                nmap.put("sameType", sameType);
                if (null == goodsCount) {
                    goodsCount = 0.0;
                }
                double goodsCountBd = 0;
                if (null != contain) {
                    goodsCountBd = ShopsUtil.div(goodsCount, contain);
                    nmap.put("goodsCount", goodsCountBd);
                }
                nmap.put("updateTime", new Timestamp(new Date().getTime()));
                nmap.putAll(map);

                //查询本店是否有该商品，若有则更新，若无则添加
                Map<String, Object> baseGoods = goodsDao.baseGoodsMessage(nmap);
                if (i == 0) {
                    //最小规格添加库存预警设置
                    if (goods.length > 9) {
                        nmap.put("stock_warning_status", goods[9]);
                        nmap.put("out_stock_waring_count", goods[10]);
                        nmap.put("unsalable_count", goods[11]);
                    }
                }
                if ((null == baseGoods || baseGoods.isEmpty())) {//需要添加入库记录
                    //检测商品是否为以0开头的商品；

                    goodsDao.newGoodsMessage(nmap);//若更新数据失败，则添加店铺商品信息
                    if (i == 0) {
                        if (BigDecimal.valueOf(goodsCountBd).compareTo(BigDecimal.ZERO) > 0) {
                            nmap.put("list_unique", System.currentTimeMillis());
                            //添加入库记录
                            goodsDao.addNewShopStock(nmap);
                        }
                    }
                } else if (tableType == 2) {//更新数据并且不添加入库
                    goodsDao.updateGoodsMessage(nmap);//测试更新商品信息
                } else {//提示商品信息不存在，但实际上商品已存在，则返回商品信息库存不运行修改
                    nmap.put("goodsCount", null);
                    goodsDao.updateGoodsMessage(nmap);//测试更新商品信息
                    System.out.println("仅仅更新商品信息！！！");
                }
                //验证分类信息，若分类长度不为7，则不更新分类信息
                if (nmap.get("kindUnique").toString().length() != 7) {
                    nmap.remove("kindUnique");
                }
//			int l=dictDao.moifyGoodsDictMessage(nmap);//测试更新大库信息
////			System.out.println("次数就用于大库更新！！！！！！——————————————————————"+nmap);
//			if(l==0){
//				dictDao.newGoodsDict(nmap);//若更新数据失败，则添加大库信息
//			}
                //将采购购物车中商品相关的采购供货商变为修改后的供货商信息
                purDao.updateCartGoodsSupplier(nmap);//依次将订单中商品的信息供货商信息修
                //mqtt
                Map<String, Object> map2 = new HashMap<String, Object>();
                if(nmap.containsKey("goods_id"))
                {
                    map2.put("goods_id", nmap.get("goods_id"));
                }
                map2.put("goods_barcode", nmap.get("goodsBarcode"));
                map2.put("shop_unique", shopUnique);
                map2.put("goods_name", ChineseCharToEn.replaceLetter(goods[1]));
                map2.put("goods_in_price", goods[2]);
                map2.put("goods_sale_price", goods[3]);
                map2.put("goods_unit", goods[5]);
                map2.put("goods_standard", goods[8]);
                map2.put("goods_web_sale_price", nmap.get("goodsWebSalePrice"));
                map2.put("update_time", DateCommon.getStringDate());
                map2.put("goodsCusPrice", goods[6]);
                map2.put("goods_kind_unique", map.get("kindUnique"));
                map2.put("pc_shelf_state",1);
                returnList.add(map2);
            }
            //mqtt 同步商品 ----------start 20220901
            try{
                SendMqttMsg sendMqttMsg = new SendMqttMsg(shopUnique + "", goodsBarcode);
                sendMqttMsg.start();
            }catch (Exception e){
                log.error("MQTT通知收银机商品更新异常：", e);
            }

            //mqtt 同步商品 ----------end 20220901


            Map<String, Object> bar = new HashMap<String, Object>();
            bar.put("list", barcodes);
            if (foreignKey == null) {
                bar.put("foreignKey", newKey);
            } else {
                bar.put("foreignKey", foreignKey);
            }
            bar.put("shopUnique", shopUnique);
            goodsDao.updateGoodsForeignKey(bar);//更新商品的包装外键关键词
            goodsDao.updateSgoodsForeignKey(bar);
            sr.setStatus(1);
            sr.setMsg("更新成功！");
        } catch (Exception e) {
            log.error("更新商品信息异常：", e);
            sr.setStatus(2);
            sr.setMsg("系统错误！");
        }
        return sr;
    }

    /**
     * 添加新的商品信息
     * 1：验证商品是否已存在
     * 若存在，获取其最小单位foreign_key,并依次更新或添加其他商品
     * 若不存在，则从大库中获取
     * 若大库也不存在，则创建新的商品信息
     *
     * @return
     */
    public ShopsResult addNewGoods(Map<String, Object> map, String goodsMessage,
                                   HttpServletRequest request, Double goodsCount, String goodsBarcode, Long shopUnique, Long foreignKey) {
        ShopsResult sr = new ShopsResult();
        map.put("goodsBarcode", goodsBarcode);
        //存放商品信息
        Map<String, Object> reMap = new HashMap<String, Object>();
        reMap.put("goodsBarcode", goodsBarcode);
        if (null == foreignKey) {
            reMap = dictDao.queryDictGoodsMessage(reMap);
            if (null == reMap || reMap.isEmpty()) {
                foreignKey = new Date().getTime();
            } else {
                foreignKey = Long.parseLong(map.get("foreignKey").toString());
            }
        }
        String[] listGoods = goodsMessage.split("]@");
        MultipartFile file = ShopsUtil.testMulRequest(request, "goodsPicture");
        Map<String, Object> nmap = new HashMap<String, Object>();
        List<String> barcodes = new ArrayList<String>();
        if (null != file) {//图片处理
            String orName = file.getOriginalFilename();
            String lastName = orName.substring(orName.lastIndexOf("."));
            String ngoods = goodsBarcode + Math.round(Math.random() * 100) + lastName;
            String filePath = File.separator + "home" + File.separator + "apache-tomcat-7.0.42"
                    + File.separator + "webapps" + File.separator + "image" + File.separator + shopUnique;
            String ngoodsPath = "image" + File.separator + shopUnique + File.separator + ngoods;
            boolean flag = ShopsUtil.savePicture(file, filePath, ngoods);
            if (flag) {
                map.put("goodsPicturepath", ngoodsPath);
            }
        }
        for (int i = 0; i < listGoods.length; i++) {
            String[] goods = listGoods[i].split(":;:");
            for (int j = 0; j < goods.length; j++) {
                if (goods[j].equals("@$@")) {
                    goods[j] = null;
                }
            }
            if (null == goods[0]) {
                break;
            }
            barcodes.add(goods[0]);
            Double contain = 0.0;
            contain = new Double(goods[4]);
            nmap.put("goodsBarcode", goods[0]);
            nmap.put("goodsName", goods[1]);
            nmap.put("goodsAlias", ChineseCharToEn.getAllFirstLetter(goods[1]));
            nmap.put("goodsInPrice", goods[2]);
            nmap.put("goodsSalePrice", goods[3]);
            nmap.put("goodsContain", contain);
            nmap.put("goodsUnit", goods[5]);
            nmap.put("goodsDiscount", goods[7]);
            nmap.put("goodsCusPrice", goods[6]);
            nmap.put("goodsStandard", goods[8]);

            if (null != contain) {
                nmap.put("goodsCount", ShopsUtil.div(goodsCount, contain));
            }
            nmap.put("updateTime", new Timestamp(new Date().getTime()));
            nmap.putAll(map);

            String hbarcode = dictDao.queryGoodsMessageByBarcode(goodsBarcode);

            if (null == hbarcode) {
                dictDao.newGoodsDict(nmap);
            } else {
//				dictDao.moifyGoodsDictMessage(nmap);
            }

            hbarcode = goodsDao.queryGoodsMessageByBarcode(nmap);
            if (null == hbarcode) {
                goodsDao.newGoodsMessage(nmap);
            } else {
                goodsDao.updateGoodsMessage(nmap);
            }
        }
        Map<String, Object> bar = new HashMap<String, Object>();
        bar.put("list", barcodes);
        bar.put("foreignKey", foreignKey);
        bar.put("shopUnique", nmap.get("shopUnique"));
        System.out.println("///////////////////////" + bar);
        goodsDao.updateGoodsForeignKey(bar);
        goodsDao.updateSgoodsForeignKey(bar);
        sr.setStatus(1);
        sr.setMsg("添加成功！");
        return sr;
    }

    /**
     * 商品基本信息查询
     * 1：获取
     *
     * @param type:1 只查询中规格商品（自身商品信息）
     * @param map
     * @return
     */
    public ShopsResult searchBaseGoods(Map<String, Object> map, String goodsBarcode, Integer type) {
        ShopsResult sr = new ShopsResult(1, "查询成功！");
        BaseGoods data = new BaseGoods();
        @SuppressWarnings("unused")
        Double goodsContain = 1.0;
        boolean flag = false;
        //添加模糊搜索功能，先查询本店是否有相同产品，再查询其他店是否有相同商品
        if (null == goodsBarcode || goodsBarcode.equals("")) {
            flag = true;
        } else {
            Map<String, Object> smap = new HashMap<>();//searchMap
            smap.put("goodsBarcode", "%" + goodsBarcode + "%");
            smap.put("shopUnique", map.get("shopUnique"));
            smap.put("goodsBarcodeLike","goodsBarcodeLike"); //随便传，标识
            smap.put("yxlLimit",10); //防止limit有使用
            List<Map<String, Object>> glist = dictDao.queryBaseMessageF(smap);
            if (null == glist || glist.isEmpty()) {
                if (goodsBarcode.length() > 10 || goodsBarcode.length() == 8) {
                    smap.put("barcodeEquals", goodsBarcode);
                    smap.remove("goodsBarcodeLike");
                }
                glist = dictDao.queryBaseMessageDict(smap);
                if (null == glist || glist.isEmpty()) {
                    flag = true;
                }
            }
            if (null != glist && glist.size() > 0) {
                goodsBarcode = glist.get(0).get("goodsBarcode").toString();
                goodsContain = Double.parseDouble(glist.get(0).get("goodsContain").toString());
            }
        }

        if (flag) {
            data = new BaseGoods();
            data.setBaseBarcode(goodsBarcode);
            data.setForeignKey("");
            data.setGoodsAddress("");
            data.setGoodsCount(0.0);
            data.setGoodsId(0);
            data.setGoodsPicturepath("");
            data.setGoodsPrice(0.0);
            data.setGoodsRemarks("");
            data.setGoodsUnit("");
            data.setGoodsSold(0.0);
            data.setGroupsName("");
            data.setGroupsUnique("");
            data.setGoodsBrand("");
            data.setKindName("");
            data.setKindUnique("");
            data.setThreeName("");
            data.setKindUnique("");
            data.setSupplierUnique("");
            data.setSupplierName("");
            data.setTableType(1);
            GoodsPacking goods = new GoodsPacking();
            goods.setForeign_key("");
            goods.setGoodsBarcode(goodsBarcode);
            goods.setGoodsCusPrice(0.0);
            goods.setGoodsDiscount(1.0);
            goods.setGoodsId(0l);
            goods.setGoodsInPrice(0.0);
            goods.setGoodsName("");
            goods.setGoodsPromotion(1.0);
            goods.setGoodsSalePrice(0.0);
            goods.setGoodsStandard("");
            goods.setGoodsUnit("");
            goods.setContainCount(1);
            goods.setGoodsContainCount(1.0);
            goods.setShelfState(1);
            List<GoodsPacking> data1 = new ArrayList<GoodsPacking>();
            data1.add(goods);
            data.setListDetail(data1);
            sr.setStatus(1);
            sr.setMsg("没有满足条件的商品信息！");
            sr.setData(data);
            return sr;
        }
        map.put("goodsBarcode", goodsBarcode);
        data = dictDao.queryBaseGoodsMessageSelf(map);
//		if(null != type && type == 1){
//			data = dictDao.queryBaseGoodsMessageSelf(map);
//			if(null == data){
//				sr.setStatus(0);
//				sr.setMsg("没有满足条件的商品信息");
//				return sr;
//			}
//			sr.setData(data);
//			return sr;
//		}else{
//			data=dictDao.queryBaseGoodsMessage(map);
//		}
        if (null == data) {
            data = new BaseGoods();
            data.setBaseBarcode(goodsBarcode);
            data.setForeignKey("");
            data.setGoodsAddress("");
            data.setGoodsCount(0.0);
            data.setGoodsId(0);
            data.setGoodsPicturepath("");
            data.setGoodsPrice(0.0);
            data.setGoodsRemarks("");
            data.setGoodsUnit("");
            data.setGoodsSold(0.0);
            data.setGroupsName("");
            data.setGroupsUnique("");
            data.setGoodsBrand("");
            data.setKindName("");
            data.setKindUnique("");
            data.setThreeName("");
            data.setKindUnique("");
            data.setSupplierUnique("");
            data.setSupplierName("");
            data.setTableType(1);
            GoodsPacking goods = new GoodsPacking();
            goods.setForeign_key("");
            goods.setGoodsBarcode(goodsBarcode);
            goods.setGoodsCusPrice(0.0);
            goods.setGoodsDiscount(1.0);
            goods.setGoodsId(0l);
            goods.setGoodsInPrice(0.0);
            goods.setGoodsName("");
            goods.setGoodsPromotion(1.0);
            goods.setGoodsSalePrice(0.0);
            goods.setGoodsStandard("");
            goods.setGoodsUnit("");
            goods.setContainCount(1);
            goods.setGoodsContainCount(1.0);
            goods.setShelfState(1);
            List<GoodsPacking> data1 = new ArrayList<GoodsPacking>();
            data1.add(goods);
            data.setListDetail(data1);
            sr.setStatus(1);
            sr.setMsg("没有满足条件的商品信息！");
            sr.setData(data);
            return sr;
        }
        List<GoodsPacking> list = data.getListDetail();
        Integer tableType = list.get(0).getTableType();
        data.setStockTableType(tableType);
        //新版功能，若同一个商品具有不同的规格，则在本店为新增
//		for(int i=0;i<list.size();i++){
//			GoodsPacking g=list.get(i);
//			if(g.getTableType()==1){
//				tableType=1;
//				break;
//			}
//		}
//		data.setTableType(tableType);
        if (data.getTableType() == 1) {
            sr.setMsg("云库商品");
        } else {
            sr.setMsg("本店商品");
        }
        //查询该店铺是否为自定义分类
        GoodsKind goodsKind = new GoodsKind();
        goodsKind.setShopUnique(map.get("shopUnique").toString());
        Integer s = kindDao.getNowKindStatus(goodsKind);
        if (data.getTableType() == 1) {
            if (s == 2) {//自定义分类信息
                data.setGroupsName("条码");
                data.setKindName("条码");
                data.setGroupsUnique("98000");
                data.setKindUnique("98001");
            } else {//

            }
        }
        sr.setStatus(1);
        sr.setData(data);
        return sr;
    }

    /**
     * 商品出入库记录查询
     *
     * @param map
     * @return
     */
    public ShopsResult stockRecord(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        List<Map<String, Object>> data = goodsDao.stockRecord(map);
        if (null == data || data.isEmpty()) {
            sr.setStatus(2);
            sr.setMsg("没有相关信息！");
            return sr;
        } else {
            for (Map<String, Object> m : data) {
                if (ObjectUtil.isNotEmpty(m.get("goodsName"))) {
                    m.put("goodsName", i18nRtUtil.getMessage(String.valueOf(m.get("goodsName"))));
                }
                if (ObjectUtil.isNotEmpty(m.get("stockResource"))) {
                    m.put("stockResource", i18nRtUtil.getMessage(String.valueOf(m.get("stockResource"))));
                }
            }
        }
        for (int i = 0; i < data.size(); i++) {
            Map<String, Object> m = data.get(i);
            if (ObjectUtil.isNotEmpty(m.get("stockPicture"))) {
                m.put("stockPicture", Arrays.asList(String.valueOf(m.get("stockPicture")).split(",")));
            } else {
                m.put("stockPicture", Collections.EMPTY_LIST);
            }
        }
        sr.setStatus(1);
        sr.setMsg("查询成功！");
        sr.setData(data);
        return sr;
    }


    /**
     * 商品销量排行
     *
     * @param map
     * @return
     */
    public ShopsResult goodsSaleStatistics(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        System.out.println(map);
        List<Map<String, Object>> data = goodsDao.goodsSaleStatistics(map);
        if (null == data || data.isEmpty()) {
            return ShopsResult.fail("没有相关信息！");
        }

        List<Object> goodsBarcodeList = new ArrayList<>();
        for (Map<String, Object> m : data) {
            goodsBarcodeList.add(m.get("goodsBarcode"));
        }
        Map<String, BigDecimal> goodsMap = new HashMap<>();
        Map<String, String> goodsUnitMap = new HashMap<>();
        if (null != goodsBarcodeList && goodsBarcodeList.size() > 0) {
            List<Map<String, Object>> goodsList = goodsDao.queryGoodsByGoodsBarcodes((Long) map.get("shopUnique"), goodsBarcodeList);
            if (null != goodsList && goodsList.size() > 0) {
                for (Map<String, Object> m : goodsList) {
                    goodsMap.put((String) m.get("goods_barcode"), (BigDecimal) m.get("goods_in_price"));
                    goodsUnitMap.put((String) m.get("goods_barcode"), null == m.get("goods_unit") ? "" : (String) m.get("goods_unit"));
                }
            }
        }
        List<Map<String, Object>> resultData = new ArrayList<>();
        for (Map<String, Object> m : data) {
            String goodsBarcode = (String) m.get("goodsBarcode");
            BigDecimal count = (BigDecimal) m.get("count");
            BigDecimal sum = (BigDecimal) m.get("sum");
            BigDecimal profit = BigDecimal.ZERO;
            if (StrUtil.isNotBlank(goodsBarcode) && null != count && null != sum && null != goodsMap.get(goodsBarcode)) {
                profit = sum.subtract(count.multiply(goodsMap.get(goodsBarcode))).setScale(2, BigDecimal.ROUND_HALF_UP);
            }
            m.put("profit", profit);
            m.put("goodsUnit", goodsUnitMap.get(goodsBarcode));
            resultData.add(m);
        }

        //统计总数量和金额


        Map<String, Object> statisMap = goodsDao.goodsSaleStatisticsTotal(map);
        sr.setStatus(1);
        sr.setMsg("查询成功！");
        sr.setData(resultData);
        sr.setObject(statisMap);
        return sr;
    }

    /**
     * 根据商品名称或者条码搜索
     * @param map
     * @param kindType
     * @return
     */
    public ShopsResult queryBaseGoodsMessageByCode(Map<String, Object> map, Integer kindType) {
        Long shopUnique = MapUtils.getLong(map,"shopUnique");
        String goodsBarcodeLike = MapUtils.getString(map, "goodsBarcode");
        String goodsBarcode = goodsBarcodeLike.substring(1,goodsBarcodeLike.length()-1); //去除左右%
        //纯数字认为是条码，其他认为是商品名称
        boolean barcodeFlag = goodsBarcode.matches("[0-9]+");

        //查询本店是否有该商品
        map.put("goodsBarcodeLike","goodsBarcodeLike"); //随便传有值就行
        map.put("limit",10);
        List<Map<String, Object>> localList = dictDao.queryBaseMessageLimit(map);
        if (ObjectUtil.isEmpty(localList)){ //本店没有该商品，走云库的逻辑
            return handleYunDictGoods(shopUnique,barcodeFlag,goodsBarcode);
        }else { //本店搜索
            return handleLocalGoods(localList,shopUnique,barcodeFlag,goodsBarcode,kindType);
        }
    }

    public ShopsResult queryGoodsCount(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        //查询总库存和库存成本
        Map<String, Object> data = goodsDao.queryGoodsCount(map);
        if (data != null) {
            //查询库存预警数量
            Map<String, Object> warningNumMap = goodsDao.queryGoodsCountWarningNum(map);
            if (warningNumMap != null) {
                data.put("stock_warning_num", warningNumMap.get("stock_warning_num"));
            } else {
                data.put("stock_warning_num", 0);
            }
            //查询商品库存
            List<Map<String, Object>> goodsCountList = goodsDao.queryGoodsCountList(map);
            for (Map<String, Object> goods : goodsCountList) {
                BigDecimal goods_contain = new BigDecimal(goods.get("goods_contain").toString());
                if (goods_contain.compareTo(new BigDecimal(1.0)) != 0) {
                    //不是最小规格要查询最小规格的库存量然后转换
                    Map<String, Object> params = new HashMap<String, Object>();
                    params.put("shopUnique", map.get("shopUnique"));
                    params.put("goods_barcode", goods.get("foreign_key"));
                    Map<String, Object> smallGoods = goodsDao.querySmallGoods(params);
                    BigDecimal smallGoodsCount = (BigDecimal) smallGoods.get("goods_count");
                    BigDecimal bigGoodsCount = smallGoodsCount.divide(goods_contain, 2, BigDecimal.ROUND_HALF_UP);
                    goods.put("goods", bigGoodsCount);
                }
            }
            data.put("goods_list", goodsCountList);
            sr.setStatus(1);
            sr.setData(data);
        } else {
            sr.setStatus(0);
        }
        return sr;
    }

    public ShopsResult queryGoodsCountWarning(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        //查询商品库存
        List<Map<String, Object>> goodsCountList = goodsDao.queryGoodsCountList(map);
        for (int j = 0; goodsCountList != null && j < goodsCountList.size(); j++) {
            Map<String, Object> goods = goodsCountList.get(0);
            BigDecimal goods_contain = new BigDecimal(goods.get("goods_contain").toString());
            if (goods_contain.compareTo(new BigDecimal(1)) != 0) {
                //不是最小规格要查询最小规格的库存量然后转换
                Map<String, Object> params = new HashMap<String, Object>();
                params.put("shopUnique", map.get("shopUnique"));
                params.put("goods_barcode", goods.get("foreign_key"));
                params.put("stock_warning_status", 1);
                Map<String, Object> smallGoods = goodsDao.querySmallGoods(params);
                if (smallGoods == null) {
                    //小规格没设置库存预警
                    goodsCountList.remove(goods);
                    j--;
                } else {
                    BigDecimal smallGoodsCount = (BigDecimal) smallGoods.get("goods_count");
                    BigDecimal out_stock_waring_count = (BigDecimal) smallGoods.get("out_stock_waring_count");
                    BigDecimal unsalable_count = (BigDecimal) smallGoods.get("unsalable_count");

                    BigDecimal bigGoodsCount = smallGoodsCount.divide(goods_contain, 2, BigDecimal.ROUND_HALF_UP);
                    BigDecimal out_stock_waring_count2 = out_stock_waring_count.divide(goods_contain, 2, BigDecimal.ROUND_HALF_UP);
                    BigDecimal unsalable_count2 = unsalable_count.divide(goods_contain, 2, BigDecimal.ROUND_HALF_UP);

                    Integer warningType = (Integer) map.get("warningType");
                    if (warningType == 1) {
                        //不足
                        if (bigGoodsCount.doubleValue() < out_stock_waring_count2.doubleValue()) {
                            goods.put("out_stock_waring_count", out_stock_waring_count2);
                            goods.put("unsalable_count2", unsalable_count2);
                            goods.put("bigGoodsCount", bigGoodsCount);
                        } else {
                            goodsCountList.remove(goods);
                            j--;
                        }
                    } else if (warningType == 2) {
                        //超额
                        if (bigGoodsCount.doubleValue() > unsalable_count2.doubleValue()) {
                            goods.put("out_stock_waring_count", out_stock_waring_count2);
                            goods.put("unsalable_count2", unsalable_count2);
                            goods.put("bigGoodsCount", bigGoodsCount);
                        } else {
                            goodsCountList.remove(goods);
                            j--;
                        }
                    }

                }

            }
        }
        if (goodsCountList != null && goodsCountList.size() > 0) {
            sr.setStatus(1);
            sr.setData(goodsCountList);
        } else {
            sr.setStatus(1);
            sr.setData(Collections.EMPTY_LIST);
        }
        return sr;
    }

    @Override
    public ShopsResult modifyGoodsInfoTrx(Map<String, Object> map) {
        Map<String, Object> goodsParams = new HashMap<>();
        goodsParams.put("shopUnique" , map.get("shopUnique").toString());
        goodsParams.put("goodsBarcode" , map.get("goodsBarcode").toString());
        RecordGoods recordGoods = goodsDao.querySourceGoods(goodsParams);
        int k = goodsDao.modifyGoods(map);
        if (k == 0) {
            return ShopsResult.fail(map.get("goodsBarcode") + "更新失败");
        }
        //想收银设备发送通知
        try {
        	SendMqttMsg sendMqttMsg = new SendMqttMsg(map.get("shopUnique").toString(), map.get("goodsBarcode").toString());
            sendMqttMsg.start();
        }catch (Exception e) {
        	log.error("MQTT通知收银机商品更新异常：", e);
		}

        //商品修改记录
        RecordGoods sourceGoods = new RecordGoods();
        //操作信息
        RecordGoodsOper recordGoodsOper = new RecordGoodsOper();
        RecordGoods resultGoods = new RecordGoods();
        if (ObjectUtil.isNotEmpty(recordGoods)) {
            sourceGoods.setGoodsId(recordGoods.getGoodsId());
            sourceGoods.setShopUnique(recordGoods.getShopUnique());
            sourceGoods.setGoodsBarcode(recordGoods.getGoodsBarcode());
            sourceGoods.setGoodsKindUnique(recordGoods.getGoodsKindUnique());
            sourceGoods.setPcShelfState(recordGoods.getPcShelfState());
            sourceGoods.setShelfState(recordGoods.getShelfState());
            cn.hutool.core.bean.BeanUtil.copyProperties(sourceGoods, resultGoods);
            if (ObjectUtil.isNotEmpty(map.get("kindUnique"))) {
                resultGoods.setGoodsKindUnique(Long.parseLong(map.get("kindUnique").toString()));
            } else {
                resultGoods.setGoodsKindUnique(sourceGoods.getGoodsKindUnique());
            }

            if (ObjectUtil.isNotEmpty(map.get("pcShelfState"))) {
                resultGoods.setPcShelfState(Integer.parseInt(map.get("pcShelfState").toString()));
            } else {
                resultGoods.setPcShelfState(sourceGoods.getPcShelfState());
            }

            if (ObjectUtil.isNotEmpty(map.get("shelfState"))) {
                resultGoods.setShelfState(Integer.parseInt(map.get("shelfState").toString()));
            } else {
                resultGoods.setShelfState(sourceGoods.getShelfState());
            }
            if (ObjectUtil.isNotEmpty(map.get("staffId"))) {
                recordGoodsOper.setUserId(String.valueOf(map.get("staffId")));
            }

            recordGoodsOper.setGoodsId(Long.valueOf(recordGoods.getGoodsId()));
            recordGoodsOper.setGoodsBarcode(recordGoods.getGoodsBarcode());
            recordGoodsOper.setShopUnique(recordGoods.getShopUnique());
            recordGoodsOper.setOperType(OperTypeEnum.OPER_TYPE_UPDATE.getValue());
            recordGoodsOper.setDeviceSource(DeviceSourceEnum.PHONE_APP.getValue());
            recordGoodsOper.setUserType(UserTypeEnum.SHOP_STAFF.getValue());
            recordGoodsOper.setCreateTtime(DateUtil.formatDateTime(DateUtil.date()));
            recordGoodsOper.setOperSource(OperSourceEnum.CHANGE_GOODS_KIND.getValue());
            recordGoodsOper.setDeviceSourceMsg(DeviceSourceEnum.PHONE_APP.getLabel());
            RecordGoodsOperParams recordGoodsOperParams = new RecordGoodsOperParams();
            recordGoodsOperParams.setSourceGoods(sourceGoods);
            recordGoodsOperParams.setResultGoods(resultGoods);
            recordGoodsOperParams.setRecordGoodsOper(recordGoodsOper);
            try{
                Map<String,Object> headerMap = new HashMap<>();
                headerMap.put("Content-Type", "application/json; charset=" + HttpUtil.DEFAULT_CHARSET);
                String jsonStr = com.alibaba.fastjson.JSONObject.toJSONString(recordGoodsOperParams);
                HttpUtil.doPostStr(HelibaoPayConfig.RECORDGOODSOPER, headerMap, jsonStr);
            } catch (Exception e){
                log.error("调用海利宝接口异常：",e);
            }
        }
        return ShopsResult.ok("更新成功！");
    }

    @Override
    public ShopsResult queryPurchaseOrderGoods(QueryPurchaseOrderGoodsParams params) {

        // 补货单中的商品信息
        ShopsResult forward = HttpsUtil.forward("/purchase-app/replenishment/replenishmentSuggestedPriceService.do", BeanUtil.obj2Map(params));
        if (forward.getStatus().equals(ShopsResult.FAIL)) {
            return forward;
        }
        List<QueryReplenishmentGoodsResult> list = JSONObject.parseArray(String.valueOf(forward.getData()), QueryReplenishmentGoodsResult.class);
        List<String> goodsBarcode = Lists.newArrayList();
        // 商品建议价集合
        Map<String, QueryReplenishmentGoodsResult> goodsResultMap = Maps.newHashMap();
        for (QueryReplenishmentGoodsResult goods : list) {
            goodsBarcode.add(goods.getGoodsCode());
            goodsResultMap.put(goods.getGoodsCode(), goods);
        }
        if (CollectionUtils.isEmpty(goodsBarcode)) {
            return ShopsResult.fail("暂无商品");
        }
        // 匹配的商品
        List<QueryGoodsByGoodsBarcodeDto> queryGoodsDtos = goodsDao.queryGoodsAndClassByGoodsCode(goodsBarcode, params.getShopUnique());
        String start = DateUtils.format(new Date(), "YYYY-MM-dd 00:00:00");
        String end = DateUtils.format(new Date(), "YYYY-MM-dd 23:59:59");
        // 查询商品调整次数
        List<QueryAdjustmentRecodingCountDto> recodingCountDtos = goodsDao.queryAdjustmentRecoding(goodsBarcode, params.getShopUnique(), start, end);
        Map<String, Integer> recodingMap = Maps.newHashMap();
        for (QueryAdjustmentRecodingCountDto recoding : recodingCountDtos) {
            recodingMap.put(recoding.getGoodsBarcode(), recoding.getAdjustmentCount());
        }
        // 所有的分类
        List<GoodsKindByShopUniqueDto> goodsKindByShopUniqueDtos = kindsService.queryGoodsKindByShopService(params.getShopUnique());

        // 初始化 分类数据 set empty list
        Map<Long, GoodsKindByShopUniqueDto> newMap = Maps.newHashMap();
        for (GoodsKindByShopUniqueDto dto : goodsKindByShopUniqueDtos) {
            dto.setGoodsKindList(Lists.<GoodsKindByShopUniqueDto>newArrayList());
            dto.setGoodsList(Lists.<QueryGoodsByGoodsBarcodeDto>newArrayList());
            newMap.put(dto.getGoodsKindUnique(), dto);
        }
        // 遍历 商品 设置建议价 并 存入分类中
        for (QueryGoodsByGoodsBarcodeDto dto : queryGoodsDtos) {
            // 商品所属 分类 数据
            GoodsKindByShopUniqueDto goodsKindByShopUniqueDto = newMap.get(dto.getGoodsKindUnique());
            // 查找商品对应的分类
            if (null != goodsKindByShopUniqueDto) {
                // 设置建议价
                dto.setGoodsCostPrice(goodsResultMap.get(dto.getGoodsBarcode()).getGoodsCost());
                // 设置 今日 调整次数
                dto.setAdjustmentCount(recodingMap.get(dto.getGoodsBarcode()) == null ? 0 : recodingMap.get(dto.getGoodsBarcode()));
                // 存入分类
                goodsKindByShopUniqueDto.getGoodsList().add(dto);
            }
        }

        // 分类分级
        for (GoodsKindByShopUniqueDto dto : goodsKindByShopUniqueDtos) {
            if (!Objects.equals(dto.getGoodsKindParunique(), 0L)
                    && !CollectionUtils.isEmpty(dto.getGoodsList())) {
                // 一级以下分类
                newMap.get(dto.getGoodsKindParunique()).getGoodsKindList().add(dto);
            }
        }
        // 过滤 不存在商品的分类
        List<GoodsKindByShopUniqueDto> resultList = Lists.newArrayList();
        for (GoodsKindByShopUniqueDto dto : goodsKindByShopUniqueDtos) {
            if (Objects.equals(dto.getGoodsKindParunique(), 0L)
                    && !CollectionUtils.isEmpty(dto.getGoodsKindList())) {
                resultList.add(dto);
            }
        }
        // 将二级分类数据 移至 一级分类
        for (GoodsKindByShopUniqueDto dto : resultList) {
            if (CollectionUtils.isNotEmpty(dto.getGoodsKindList())) {
                for (GoodsKindByShopUniqueDto kindDto : dto.getGoodsKindList()) {
                    dto.getGoodsList().addAll(kindDto.getGoodsList());
                    dto.setGoodsKindList(null);
                }
            }
        }
        return ShopsResult.ok(resultList);
    }

    @Override
    public ShopsResult updateReplenishmentGoodsService(UpdateReplenishmentGoodsParams params) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("shopUnique", params.getShopUnique());
        map.put("goodsBarcode", params.getGoodsBarcode());
        map.put("goodsSalePrice", params.getGoodsSalePrice());
        map.put("goodsWebSalePrice", params.getGoodsWebSalePrice());
        map.put("goodsCusPrice", params.getGoodsCusPrice());
        int i = goodsDao.modifyGoods(map);

        if (i < 1) {
            return ShopsResult.fail("商品修改异常");
        }

        GoodsPriceAdjustmentEntity entity = new GoodsPriceAdjustmentEntity();
        entity.setShopUnique(params.getShopUnique());
        entity.setGoodsBarcode(params.getGoodsBarcode());
        entity.setGoodsSalePrice(params.getGoodsSalePrice());
        entity.setGoodsWebSalePrice(params.getGoodsWebSalePrice());
        entity.setGoodsCusPrice(params.getGoodsCusPrice());
        i = goodsDao.addAdjustmentRecoding(entity);
        if (i < 1) {
            return ShopsResult.fail("新增修改记录异常");
        }
        return ShopsResult.ok("操作成功");
    }

    @Override
    public Map<String, QueryGoodsInfoDo> queryListByShopUniqueAndBarcode(Long shopUnique, String goodsBarcode) {
        List<String> list = new ArrayList<>();
        list.add(goodsBarcode);
        return queryListByShopUniqueAndBarcode(shopUnique, list);
    }

    @Override
    public Map<String, QueryGoodsInfoDo> queryListByShopUniqueAndBarcode(Long shopUnique, List<String> goodsBarcodeList) {
        List<QueryGoodsInfoDo> goodsInfoDos = goodsDao.queryListByGoodsBarcode(shopUnique, goodsBarcodeList);
        if (StringUtils.isEmpty(goodsInfoDos)) {
            return new HashMap<>();
        }
        Map<String, QueryGoodsInfoDo> infoDoMap = new HashMap<>();
        for (QueryGoodsInfoDo infoDo : goodsInfoDos) {
            infoDoMap.put(infoDo.getGoodsBarcode(), infoDo);
        }
        return infoDoMap;
    }

    /**
     * 新增商品
     */
    @Override
    public ShopsResult addGoodsInfo(AddGoodsDto addGoodsDto) throws InvocationTargetException, IllegalAccessException {
        if (StringUtils.isEmpty(addGoodsDto.getGoodsBarcode())) {
            return ShopsResult.fail("条形码不能为空");
        }
        Map<String, Object> nmap = new HashMap<>();
        nmap.put("shopUnique", addGoodsDto.getShopsUnique());
        nmap.put("goodsBarcode", addGoodsDto.getGoodsBarcode());
        // 查询是否存在
        Map<String, Object> baseGoods = goodsDao.baseGoodsMessage(nmap);
        if (baseGoods != null) {
            return ShopsResult.fail("商品已存在无法新增");
        }

        AddGoodsPo goodsPo = new AddGoodsPo();
        BeanUtils.copyProperties(goodsPo,addGoodsDto);

        if (StringUtils.isEmpty(addGoodsDto.getGoodsAlias())) {
            goodsPo.setGoodsAlias(ChineseCharToEn.getAllFirstLetter(addGoodsDto.getGoodsName()));
        }
        goodsPo.setShopUnique(String.valueOf(addGoodsDto.getShopsUnique()));
        goodsPo.setForeignKey(Long.parseLong(addGoodsDto.getGoodsBarcode()));
        goodsPo.setGoodsWebSalePrice(addGoodsDto.getGoodsWebSalePrice());
        goodsDao.addGoodsInfo(goodsPo);
        return ShopsResult.ok();
    }



    /**
     * 将精确匹配的条码放在第一条数据
     * @param data
     * @return
     */
    private List<BaseGoods> handleQueryBaseGoodsMessageByCodeData(boolean barcodeFlag,String goodsBarcode,List<BaseGoods> data){
        if (ObjectUtil.isEmpty(data)) return data;
        //没有条码直接返回
        if (ObjectUtil.isEmpty(goodsBarcode)) return data;

        List<BaseGoods> resp = new ArrayList<>();
        Iterator<BaseGoods> iter = data.iterator();
        while (iter.hasNext()){
            BaseGoods next = iter.next();
            List<GoodsPacking> listDetail = next.getListDetail();
            for (GoodsPacking gp : listDetail){
                if (barcodeFlag){ //条码搜索
                    if (goodsBarcode.equals(gp.getGoodsBarcode())){
                        resp.add(next);
                        iter.remove();
                        break;
                    }
                }else { //商品名称搜索
                    if (goodsBarcode.equals(gp.getGoodsName())){
                        resp.add(next);
                        iter.remove();
                        break;
                    }
                }
            }
        }
        resp.addAll(data);

        //第一条数据要是匹配则将精准排序放在第一位
        BaseGoods baseGoods = resp.get(0);
        List<GoodsPacking> sortPacking = new ArrayList<>();
        List<GoodsPacking> listDetail = baseGoods.getListDetail();
        Iterator<GoodsPacking> diter = listDetail.iterator();
        while (diter.hasNext()){
            GoodsPacking gp = diter.next();
            if (barcodeFlag){ //条码搜索
                if (goodsBarcode.equals(gp.getGoodsBarcode())){
                    sortPacking.add(gp);
                    diter.remove();
                }
            }else { //商品名称搜索
                if (goodsBarcode.equals(gp.getGoodsName())){
                    sortPacking.add(gp);
                    diter.remove();
                }
            }
        }
        sortPacking.addAll(listDetail);
        baseGoods.setListDetail(sortPacking);

        return resp;
    }

    /**
     * 云库商品处理
     * @param shopUnique 店铺编号
     * @param barcodeFlag true代表条码搜索，false代表名称搜索
     * @param goodsBarcode 商品条码或商品名称
     * @return
     */
    private ShopsResult handleYunDictGoods(Long shopUnique,boolean barcodeFlag,String goodsBarcode){
        //精确查找云库有没有该商品
        Map<String,Object> queryYun = new HashMap<>();
        queryYun.put("shopUnique",shopUnique);
        queryYun.put(barcodeFlag ? "barcodeEquals" : "nameEquals",goodsBarcode);
        queryYun.put("yxlLimit",barcodeFlag ? 1 : 10);
        List<Map<String, Object>> accurateList = dictDao.queryBaseMessageDict(queryYun);
        if (accurateList.size() < 10){ //精确查找少于10条
            //模糊查询云库
            queryYun.remove(barcodeFlag ? "barcodeEquals" : "nameEquals");
            queryYun.put("goodsBarcodeLike","goodsBarcodeLike"); //随便传，有值就行
            queryYun.put("goodsBarcode",StringUtils.join("%",goodsBarcode,"%"));
            List<Map<String, Object>> likeList = dictDao.queryBaseMessageDict(queryYun);
            //去除两次查询重复的数据
            Iterator<Map<String, Object>> iter = likeList.iterator();
            while (iter.hasNext()){
                Map<String, Object> next = iter.next();
                String nextBarcode = MapUtils.getString(next, "goodsBarcode");
                for (Map<String,Object> accu : accurateList){
                    String accuBarcode = MapUtils.getString(accu, "goodsBarcode");
                    if (nextBarcode.equals(accuBarcode)){
                        iter.remove();
                        break;
                    }
                }
            }
            //追加到十条数据
            for (Map<String,Object> like : likeList){
                if (accurateList.size() < 10){
                    accurateList.add(like);
                }else {
                    break;
                }
            }
        }


        if (ObjectUtil.isEmpty(accurateList)) {
            return ShopsResult.fail("没有满足条件的商品信息！");
        }
        List<BaseGoods> data = dictDao.queryBaseMessageDictS(accurateList);
        for (int i = 0; i < data.size(); i++) {
            data.get(i).setKindName(ConfigForShopUpdate.DEFAULTKINDNAME);
            data.get(i).setKindUnique(ConfigForShopUpdate.DEFAULTKINDUNIQUE);
            data.get(i).setGroupsName(ConfigForShopUpdate.DEFAULTPARNAME);
            data.get(i).setGroupsUnique(ConfigForShopUpdate.DEFAULTPARUNIQUE);
        }
        return ShopsResult.ok("查询成功！", handleQueryBaseGoodsMessageByCodeData(barcodeFlag,goodsBarcode,data));
    }

    /**
     * 本地商品处理
     * @param likeList 模糊查询的商品信息
     * @param shopUnique 店铺编号
     * @param barcodeFlag true代表条码搜索，false代表名称搜索
     * @param goodsBarcode 商品条码或商品名称
     * @param kindType 店铺类型
     * @return
     */
    private ShopsResult handleLocalGoods(List<Map<String, Object>> likeList,Long shopUnique,boolean barcodeFlag,String goodsBarcode, int kindType){
        Map<String,Object> accuMap = new HashMap<>();
        accuMap.put("shopUnique",shopUnique);
        accuMap.put(barcodeFlag ? "barcodeEquals" : "nameEquals",goodsBarcode);
        accuMap.put("limit",barcodeFlag ? 1 : 10);
        List<Map<String, Object>> accuList = dictDao.queryBaseMessageLimit(accuMap);
        if (accuList.size() < 10){
            //去除两次查询重复的数据
            Iterator<Map<String, Object>> iter = likeList.iterator();
            while (iter.hasNext()){
                Map<String, Object> next = iter.next();
                String nextBarcode = MapUtils.getString(next, "goodsBarcode");
                for (Map<String, Object> accu : accuList){
                    String accuBarcode = MapUtils.getString(accu, "goodsBarcode");
                    if (nextBarcode.equals(accuBarcode)){
                        iter.remove();
                    }
                }
            }
            //追加到十条数据
            for (Map<String,Object> like : likeList){
                if (accuList.size() < 10){
                    accuList.add(like);
                }else {
                    break;
                }
            }
        }

        List<BaseGoods> data = dictDao.queryBaseGoodsMessageByCode(accuList);
        //若店铺使用的自定义分类，且查出的商品为云库商品，将商品分类修改为默认分类；
        if (kindType == 2) {//使用自定义分类
            for (int i = 0; i < data.size(); i++) {
                if (data.get(i).getTableType() == 1) {//查出来的商品为云库商品
                    data.get(i).setKindName(ConfigForShopUpdate.DEFAULTKINDNAME);
                    data.get(i).setKindUnique(ConfigForShopUpdate.DEFAULTKINDUNIQUE);
                    data.get(i).setGroupsName(ConfigForShopUpdate.DEFAULTPARNAME);
                    data.get(i).setGroupsUnique(ConfigForShopUpdate.DEFAULTPARUNIQUE);
                }
            }
        }

        return ShopsResult.ok("查询成功！", handleQueryBaseGoodsMessageByCodeData(barcodeFlag,goodsBarcode,data));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShopsResult addGoods(AddGoodsBaseParam addGoodsBaseParam) {
        List<AddGoodsParam> goodsMessage=addGoodsBaseParam.getGoodsMessage();

        CreateOrUpdateGoodsListParams createOrUpdateGoodsListParams = new CreateOrUpdateGoodsListParams();
        createOrUpdateGoodsListParams.setShopUnique(addGoodsBaseParam.getShopUnique().toString());

        List<BaseGoodsMsg> f1 = new ArrayList<>();
        for (int i = 0; i < goodsMessage.size(); i++) {
            AddGoodsParam goodsParam = goodsMessage.get(i);
            BaseGoodsMsg baseGoodsMsg = new BaseGoodsMsg();
            baseGoodsMsg.setGoodsBarcode(goodsParam.getGoodsBarcode());
            baseGoodsMsg.setGoodsName(goodsParam.getGoodsName());
            baseGoodsMsg.setGoodsStandard(goodsParam.getGoodsStandard());
            baseGoodsMsg.setGoodsUnit(goodsParam.getGoodsUnit());
            baseGoodsMsg.setGoodsSalesPrice(goodsParam.getGoodsSalePrice());
            baseGoodsMsg.setGoodsCusPrice(goodsParam.getGoodsCusPrice());
            baseGoodsMsg.setGoodsBrand(goodsParam.getGoodsUnit());
            f1.add(baseGoodsMsg);
        }

        createOrUpdateGoodsListParams.setF1(f1);


        //大库商品
        List<Map<String,Object>> dictGoodsList = new ArrayList<>();
        //同时添加多个商品信息,所以需要多条记录
        List< RecordGoodsOperParams> operParamsList = new ArrayList<>();
        //此处需要注意有新增的情况
        for(Integer i = 0; i < goodsMessage.size(); i++){
            RecordGoodsOperParams recordGoodsOperParams = new RecordGoodsOperParams();
            AddGoodsParam goods = goodsMessage.get(i);

            Map<String, Object> dictMap = new HashMap<>();
            dictMap.put("goodsBarcode", goods.getGoodsBarcode());
            dictMap.put("goodsName", goods.getGoodsName());
            dictMap.put("goodsAlias", goods.getGoodsAlias() == null ? "" : goods.getGoodsAlias());
            dictMap.put("goodsInPrice", goods.getGoodsInPrice());
            dictMap.put("goodsSalePrice", goods.getGoodsSalePrice());
            dictMap.put("goodsContain", goods.getGoodsContain());
            dictMap.put("kindUnique", addGoodsBaseParam.getGoodsKindUnique());
            dictMap.put("goodsPicturepath", goods.getGoodsPicturePath());
            dictMap.put("goodsStandard", goods.getGoodsStandard());
            dictMap.put("goodsUnit", goods.getGoodsUnit());
            dictMap.put("goodsBrand", null);
            dictMap.put("goodsAddress", null);
            dictMap.put("foreignKey", addGoodsBaseParam.getForeignKey());

            dictGoodsList.add(dictMap);
            Map<String, Object> map = new HashMap<>();
            map.put("shopUnique" , addGoodsBaseParam.getShopUnique());
            map.put("goodsBarcode" , goodsMessage.get(i).getGoodsBarcode());
            RecordGoods sourceGoods = goodsDao.querySourceGoods(map);
            recordGoodsOperParams.setSourceGoods(sourceGoods);
            RecordGoodsOper recordGoodsOper = new RecordGoodsOper();
            if(null != sourceGoods){
                recordGoodsOper.setGoodsId(sourceGoods.getGoodsId() + 0l);
                recordGoodsOper.setOperType(OperTypeEnum.OPER_TYPE_UPDATE.getValue());
            }else {
                recordGoodsOper.setOperType(OperTypeEnum.OPER_TYPE_ADD.getValue());
            }

            recordGoodsOper.setGoodsBarcode(goodsMessage.get(i).getGoodsBarcode());
            recordGoodsOper.setShopUnique(addGoodsBaseParam.getShopUnique());
            recordGoodsOper.setDeviceSource(addGoodsBaseParam.getDeviceSource());
            recordGoodsOper.setDeviceSourceMsg(addGoodsBaseParam.getDevicesourcemsg());
            if (ObjectUtil.isNotEmpty(addGoodsBaseParam.getStaffId())) {
                recordGoodsOper.setUserId(addGoodsBaseParam.getUserId() == null ? "" : addGoodsBaseParam.getUserId() + "");
            }
            recordGoodsOper.setUserType(addGoodsBaseParam.getUserType());
            recordGoodsOper.setOperSource(OperSourceEnum.GOODS_CHANGE.getValue());

            recordGoodsOperParams.setRecordGoodsOper(recordGoodsOper);

            operParamsList.add(recordGoodsOperParams);
        }

        if(goodsMessage==null||goodsMessage.size()<1)
        {
           return ShopsResult.fail("商品信息缺失！");
        }
        boolean flag=false;
        String msg="";
        List<AddGoodsPo> mqttList=new ArrayList<>();

        List<MinSaleCountAddParams> minSaleCountAddParamsList = new ArrayList<>();
        List<MinSaleCountUpdateParams> minSaleCountUpdateParamsList = new ArrayList<>();

        List<Map<String, Object>> wholeShopList = new ArrayList<>();
        for(AddGoodsParam data:goodsMessage)
        {

            AddGoodsPo addGoodsPo =new AddGoodsPo();
            addGoodsPo.setShopUnique(String.valueOf(addGoodsBaseParam.getShopUnique()));
            addGoodsPo.setGoodsBarcode(data.getGoodsBarcode());
            addGoodsPo.setUnderlinedPrice(data.getUnderlinedPrice());
            if(data.getGoodsBarcode().startsWith("0"))
            {
                flag=true;
                msg="商品条码不能0开头！";
            }else if(data.getGoodsBarcode().equals(addGoodsBaseParam.getForeignKey())&&data.getGoodsContain()!=1)
            {
                flag=true;
                msg="基本包装,单位换算必须等于1！";
            }else if(!data.getGoodsBarcode().equals(addGoodsBaseParam.getForeignKey())&&data.getGoodsContain()<1)
            {
                flag=true;
                msg="非最小包装商品的 换算单位必须大于1！";
            }else if(goodsDao.queryGoodsExists(addGoodsPo)>0)//去重
            {
                flag=true;
                msg="商品条码名称不能重复:"+data.getGoodsName();
            }
            if(data.getGoodsContain()==1)
            {
                if (addGoodsBaseParam.getGoodsCount().compareTo(BigDecimal.ZERO) > 0) {
                    //添加批次记录
                    addGoodBatch(data, addGoodsBaseParam, addGoodsPo);
                } else {
                    addGoodsPo.setGoodsCount(addGoodsBaseParam.getGoodsCount());
                }

            }
            addGoodsPo.setGoodsContain(new BigDecimal(data.getGoodsContain()));
            addGoodsPo.setGoodsName(data.getGoodsName());
            addGoodsPo.setGoodsKindUnique(addGoodsBaseParam.getGoodsKindUnique());
            addGoodsPo.setGoodsBrand(addGoodsBaseParam.getGoodsBrand());
            addGoodsPo.setGoodsAddress(addGoodsBaseParam.getGoodsAddress());
            if (ObjectUtil.isNotEmpty(addGoodsBaseParam.getGoodsLife())) {
                addGoodsPo.setGoodsLife(String.valueOf(addGoodsBaseParam.getGoodsLife()));
            }
            //创建别名
            if(StringUtils.isEmpty(data.getGoodsAlias()))
            {
                addGoodsPo.setGoodsAlias(ChineseCharToEn.getAllFirstLetter(data.getGoodsName()));
            }else {
                addGoodsPo.setGoodsAlias(data.getGoodsAlias());
            }
            if(StrUtil.isNotBlank(addGoodsBaseParam.getSupplierUnique()))
            {
                addGoodsPo.setSupplierUnique(addGoodsBaseParam.getSupplierUnique());
            }
            addGoodsPo.setForeignKey(addGoodsBaseParam.getForeignKey());
            addGoodsPo.setGoodsPicturePath(data.getGoodsPicturePath());
            addGoodsPo.setGoodsRemarks(addGoodsBaseParam.getGoodsRemarks());
            addGoodsPo.setGoodsSalePrice(data.getGoodsSalePrice());
            addGoodsPo.setGoodsInPrice(data.getGoodsInPrice());
            if (ObjectUtil.isEmpty(data.getGoodsCusPrice()) || data.getGoodsCusPrice().compareTo(BigDecimal.ZERO) == 0) {
                addGoodsPo.setGoodsCusPrice(data.getGoodsSalePrice());
            } else {
                addGoodsPo.setGoodsCusPrice(data.getGoodsCusPrice());
            }
            if (ObjectUtil.isEmpty(data.getGoodsWebSalePrice()) || data.getGoodsWebSalePrice().compareTo(BigDecimal.ZERO) == 0) {
                addGoodsPo.setGoodsWebSalePrice(data.getGoodsSalePrice());
            } else {
                addGoodsPo.setGoodsWebSalePrice(data.getGoodsWebSalePrice());
            }
            addGoodsPo.setGoodsScaleType(addGoodsBaseParam.getGoodsChengType());
            addGoodsPo.setGoodsUnit(data.getGoodsUnit());
            addGoodsPo.setGoodsContain(new BigDecimal(data.getGoodsContain()));
            addGoodsPo.setGoodsStandard(data.getGoodsStandard());
            if(addGoodsBaseParam.getStockWarningStatus()!=null&&addGoodsBaseParam.getOutStockWaringCount()!=null)
            {
                addGoodsPo.setStockWarningStatus(String.valueOf(addGoodsBaseParam.getStockWarningStatus()));
                if(data.getGoodsContain()==1)
                {
                    addGoodsPo.setOutStockWaringCount(String.valueOf(addGoodsBaseParam.getOutStockWaringCount()));
                    if(addGoodsBaseParam.getUnsalableCount()!=null)
                    {
                        addGoodsPo.setUnsalableCount(String.valueOf(addGoodsBaseParam.getUnsalableCount()));
                    }

                }else
                {
                    addGoodsPo.setOutStockWaringCount(String.valueOf(addGoodsBaseParam.getOutStockWaringCount().multiply(new BigDecimal(data.getGoodsContain()))));
                    if(addGoodsBaseParam.getUnsalableCount()!=null) {
                        addGoodsPo.setUnsalableCount(String.valueOf(addGoodsBaseParam.getUnsalableCount().multiply(new BigDecimal(data.getGoodsContain()))));
                    }

                }
            }


            if(data.getPcShelfState()==null)
            {
                data.setPcShelfState(1);
            }
            if(data.getShelfState()==null)
            {
                data.setShelfState(2);
            }
            addGoodsPo.setPcShelfState(new BigDecimal(data.getPcShelfState()));
            addGoodsPo.setShelfState(new BigDecimal(data.getShelfState()));
            addGoodsPo.setMinSaleCount(data.getMinSaleCount());
            if (ObjectUtil.isNotEmpty(data.getWholesalePriceFlg())) {
                if (data.getWholesalePriceFlg() ==1){
                    if (ObjectUtil.isNotEmpty(data.getWholesaleList())) {
                        Set<BigDecimal> wholesaleSet = new HashSet<>();
                        if (data.getWholesaleList().size() > 5) {
                            flag=true;
                            msg="批发信息不能超过3条！";
                        }
                        for (AddGoodsParam.WholesaleInfo wholesaleInfo : data.getWholesaleList()) {
                            if (ObjectUtil.isEmpty(wholesaleInfo.getWholesaleCount())) {
                                flag=true;
                                msg="批发数量不能为空！";
                            }
                            if (ObjectUtil.isEmpty(wholesaleInfo.getWholesalePrice())  || wholesaleInfo.getWholesalePrice().compareTo(BigDecimal.ZERO) <= 0) {
                                flag=true;
                                msg="批发价不能为空或0！";
                            }
                            wholesaleSet.add(wholesaleInfo.getWholesaleCount());
                        }
                        if (wholesaleSet.size() != data.getWholesaleList().size()) {
                            flag=true;
                            msg="起批数量不能重复！";
                        }
                        List<GoodsWholesaleEntity> goodsWholesaleEntityList = new ArrayList<>();
                        for (AddGoodsParam.WholesaleInfo wholesaleInfo : data.getWholesaleList()) {
                            GoodsWholesaleEntity wholesaleEntity = new GoodsWholesaleEntity();
                            wholesaleEntity.setGoodsBarcode(data.getGoodsBarcode());
                            wholesaleEntity.setShopUnique(addGoodsBaseParam.getShopUnique());
                            wholesaleEntity.setWholesalePrice(wholesaleInfo.getWholesalePrice());
                            wholesaleEntity.setWholesaleCount(wholesaleInfo.getWholesaleCount());
                            goodsWholesaleEntityList.add(wholesaleEntity);
                        }
                        Map<String, Object> wholeShopMap = new HashMap<>();
                        wholeShopMap.put("goodsBarcode",data.getGoodsBarcode());
                        wholeShopMap.put("shopUnique",addGoodsBaseParam.getShopUnique());
                        wholeShopMap.put("wholesaleList",goodsWholesaleEntityList);
                        wholeShopList.add(wholeShopMap);

                    } else {
                        flag=true;
                        msg="批发信息不能为空！";
                    }

                }
            }
            addGoodsPo.setWholesalePriceFlg(data.getWholesalePriceFlg());
            addGoodsPo.setGoodsPosition(data.getGoodsPosition());
            mqttList.add(addGoodsPo);
        }

        if(flag)
        {
            return ShopsResult.fail(msg);
        }

        if (ObjectUtil.isNotEmpty(wholeShopList)) {
            for (Map<String, Object> wholeShopMap : wholeShopList) {
                Map<String, Object> map = new HashMap<>();
                map.put("shopUnique", wholeShopMap.get("shopUnique"));
                map.put("goodsBarcode", wholeShopMap.get("goodsBarcode"));
                if (ObjectUtil.isNotEmpty(goodsWholesaleMapper.queryByGoodsBarcode(map))) {
                    goodsWholesaleMapper.deleteByGoodsBarcode(map);
                }
                for (GoodsWholesaleEntity goodsWholesaleEntity : (List<GoodsWholesaleEntity>) wholeShopMap.get("wholesaleList")) {
                    goodsWholesaleMapper.insert(goodsWholesaleEntity);
                }
            }
        }

        for(AddGoodsPo data:mqttList)
        {
            goodsDao.addGoodsInfo(data);

            if (ObjectUtil.isNotEmpty(data.getMinSaleCount())) {
                GoodsOnlineSettingDto goodsOnlineSettingDto = goodsOnlineSettingDao.queryGoodsOnlineSettingByGoodsId(data.getGoodsId());
                if (ObjectUtil.isNotEmpty(goodsOnlineSettingDto)) {
                    MinSaleCountUpdateParams minSaleCountUpdateParams = new MinSaleCountUpdateParams();
                    minSaleCountUpdateParams.setGoodsId(data.getGoodsId());
                    minSaleCountUpdateParams.setMinSaleCount(data.getMinSaleCount());
                    minSaleCountUpdateParamsList.add(minSaleCountUpdateParams);
                } else {
                    MinSaleCountAddParams minSaleCountAddParams = new MinSaleCountAddParams();
                    minSaleCountAddParams.setGoodsId(data.getGoodsId());
                    minSaleCountAddParams.setMinSaleCount(data.getMinSaleCount());
                    minSaleCountAddParamsList.add(minSaleCountAddParams);
                }
            }
            try {
                SendMqttMsg sendMqttMsg = new SendMqttMsg(addGoodsBaseParam.getShopUnique() + "", data.getGoodsBarcode());
                sendMqttMsg.start();
            } catch (Exception e) {
                log.error("MQTT通知收银机商品信息更新异常：", e);
                sendDingDingTalkUtils.sendDingDingTalkMsg(((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest(), "发送MQTT消息同步收银机商品信息", e, JSONUtil.toJsonStr(data), "新增商品", BusinessType.INSERT.ordinal());
            }
        }


        try {
            if (ObjectUtil.isNotEmpty(minSaleCountAddParamsList)) {
                goodsOnlineSettingDao.saveGoodsOnlineSettingList(minSaleCountAddParamsList);
            }
            if (ObjectUtil.isNotEmpty(minSaleCountUpdateParamsList)) {
                goodsOnlineSettingDao.updateGoodsOnlineSettingList(minSaleCountUpdateParamsList);
            }

            final List<AddGoodsPo> threadMqttList = mqttList;
            final String supplierUnique = addGoodsBaseParam.getSupplierUnique();
            ThreadUtil.execute(new Runnable() {
                public void run() {
                    for (AddGoodsPo data : threadMqttList) {
                        if (StrUtil.isNotBlank(supplierUnique)) {
                            Map<String, Object> params = new HashMap<>();
                            params.put("shopUnique", data.getShopUnique());
                            params.put("goodsBarcode", data.getGoodsBarcode());
                            params.put("supplierUnique", data.getSupplierUnique());
                            params.put("createId", "1");
                            params.put("createBy", "SYSTEM");
                            shopSupplierService.addSupGood(params);
                        }

                    }
                }
            });
        } catch (Exception e) {
            log.error("新增商品时，保存商品在线设置异常：", e);
            sendDingDingTalkUtils.sendDingDingTalkMsg(((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest(), "商品绑定供货商", e, JSONUtil.toJsonStr(mqttList), "新增商品", BusinessType.INSERT.ordinal());
        }

        //修改完成后保存记录
        try{
            for(Integer i = 0; i < goodsMessage.size(); i++){
                AddGoodsParam addGoodsParam = goodsMessage.get(i);
                Map<String, Object> map = new HashMap<>();
                map.put("shopUnique" , addGoodsBaseParam.getShopUnique());
                map.put("goodsBarcode" , goodsMessage.get(i).getGoodsBarcode());
                RecordGoods recordGoods = goodsDao.querySourceGoods(map);
                for(RecordGoodsOperParams params : operParamsList){
                    //根据操作类型是新增或修改，修改对应的resourGoods
                    RecordGoodsOper recordGoodsOper = params.getRecordGoodsOper();
                    if(addGoodsParam.getGoodsBarcode().equals(recordGoodsOper.getGoodsBarcode())){
                        if(recordGoodsOper.getOperType() == OperTypeEnum.OPER_TYPE_ADD.getValue()){
                            //如果是新增
                            params.setSourceGoods(recordGoods);
                        }else {
                            params.setResultGoods(recordGoods);
                        }
                    }
                }
            }

            for(RecordGoodsOperParams params : operParamsList){
                Map<String,Object> headerMap = new HashMap<>();
                headerMap.put("Content-Type", "application/json; charset=" + HttpUtil.DEFAULT_CHARSET);
                String jsonStr = JSONObject.toJSONString(params);
                String result = HttpUtil.doPostStr(HelibaoPayConfig.RECORDGOODSOPER, headerMap, jsonStr);
            }
        } catch (Exception e){
            log.error("保存操作记录信息异常：", e);
            sendDingDingTalkUtils.sendDingDingTalkMsg(((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest(), "保存操作记录信息", e, JSONUtil.toJsonStr(operParamsList), "新增商品", BusinessType.INSERT.ordinal());
        }
        try {
            if(null != dictGoodsList && !dictGoodsList.isEmpty()){
                for (Map<String, Object> dictMap : dictGoodsList){
                    sameMsgToGoodsDict(dictMap);
                }
            }
        } catch (Exception e){
            log.error("新增商品时，保存商品在线设置异常：", e);
        }
        if (ObjectUtil.isNotEmpty(f1)) {
            bhService.createOrUpdateGoodsList(createOrUpdateGoodsListParams);
        }
        return ShopsResult.ok();
    }

    private void addGoodBatch(AddGoodsParam goodsMessage,AddGoodsBaseParam addGoodsBaseParam,AddGoodsPo addGoodsPo) {

        if (ObjectUtil.isNotEmpty(addGoodsBaseParam.getGoodsProd())) {
            addGoodsBaseParam.setGoodsProd(!addGoodsBaseParam.getGoodsProd().contains(" ")?addGoodsBaseParam.getGoodsProd() + " 00:00:00":addGoodsBaseParam.getGoodsProd());
        }

        ShopsConfig shopsConfig = shopsConfigDao.selectByShopUnique(String.valueOf(addGoodsBaseParam.getShopUnique()));
        ShopStockDetailAddParams shopStockDetailAddParams = new ShopStockDetailAddParams();
        shopStockDetailAddParams.setShopUnique(String.valueOf(addGoodsBaseParam.getShopUnique()));
        String listUnique = StrUtil.concat(true, DateUtil.format(DateUtil.date(), "yyyyMMddHHmmssSSS"), RandomUtil.randomNumbers(3));
        if (ObjectUtil.equal(IsIoBoundInspectEnum.NO.getCode(), shopsConfig.getIsIoBoundInspect())) {
            shopStockDetailAddParams.setAuditStatus(1);
            addGoodsPo.setGoodsCount(addGoodsBaseParam.getGoodsCount());
        } else {
            shopStockDetailAddParams.setAuditStatus(0);
        }
        shopStockDetailAddParams.setShopUnique(String.valueOf(addGoodsBaseParam.getShopUnique()));
        shopStockDetailAddParams.setListUnique(listUnique);
        shopStockDetailAddParams.setStockKind(StockKindEnum.INITIALIZED.getCode());
        shopStockDetailAddParams.setStockType(StockTypeEnum.INVENTORY_IN.getCode());
        shopStockDetailAddParams.setStockResource(StockResourceEnum.MANUAL.getCode());
        shopStockDetailAddParams.setStockOrigin(StockOriginEnum.MOBILE.getCode());
        if (ObjectUtil.isNotEmpty(addGoodsBaseParam.getStaffId())) {
            shopStockDetailAddParams.setStaffId(Long.valueOf(addGoodsBaseParam.getStaffId()));
            shopStockDetailAddParams.setUpdateId(Long.valueOf(addGoodsBaseParam.getStaffId()));
        }
        shopStockDetailAddParams.setUpdateTime(DateUtil.date());
        shopStockDetailAddParams.setStockTime(DateUtil.date());
        shopStockDetailAddParams.setTotalCount(addGoodsBaseParam.getGoodsCount());
        shopStockDetailAddParams.setTotalAmount(addGoodsBaseParam.getGoodsCount().multiply(goodsMessage.getGoodsInPrice()));
        stockDao.addIntoStockDetailV2(shopStockDetailAddParams);

        ShopStockAddParams shopStockAddParams = new ShopStockAddParams();
        shopStockAddParams.setShopUnique(String.valueOf(addGoodsBaseParam.getShopUnique()));
        shopStockAddParams.setGoodsBarcode(goodsMessage.getGoodsBarcode());
        shopStockAddParams.setListUnique(listUnique);
        shopStockAddParams.setGoodsCount(addGoodsBaseParam.getGoodsCount());
        shopStockAddParams.setStockCount(addGoodsBaseParam.getGoodsCount());
        shopStockAddParams.setStockType(StockTypeEnum.INVENTORY_IN.getCode());
        shopStockAddParams.setStockTime(DateUtil.date());
        shopStockAddParams.setStockResource(StockResourceEnum.MANUAL.getCode());
        shopStockAddParams.setStockPrice(goodsMessage.getGoodsInPrice());
        shopStockAddParams.setStockOrigin(StockOriginEnum.MOBILE.getCode());
        if (ObjectUtil.isNotEmpty(addGoodsBaseParam.getStaffId())) {
            shopStockAddParams.setStaffId(Long.valueOf(addGoodsBaseParam.getStaffId()));
        }
        if (ObjectUtil.isNotEmpty(addGoodsBaseParam.getGoodsProd())) {
            shopStockAddParams.setGoodsProd(DateUtil.parse(addGoodsBaseParam.getGoodsProd(), "yyyy-MM-dd HH:mm:ss"));
        }
        if (ObjectUtil.isNotEmpty(addGoodsBaseParam.getGoodsLife())) {
            shopStockAddParams.setGoodsLife(addGoodsBaseParam.getGoodsLife());
        }
        if (ObjectUtil.isNotEmpty(addGoodsBaseParam.getGoodsLife()) && ObjectUtil.isNotEmpty(addGoodsBaseParam.getGoodsProd())) {
            shopStockAddParams.setGoodsExp(DateUtil.offsetDay(shopStockAddParams.getGoodsProd(), addGoodsBaseParam.getGoodsLife()));
        }
        stockDao.stockRecordV3(shopStockAddParams);
        if (ObjectUtil.isNotNull(shopsConfig)) {
            if (ObjectUtil.equals(GoodsInPriceTypeEnums.BATCH.getCode(), shopsConfig.getGoodsInPriceType())) {
                if (ObjectUtil.equal(IsIoBoundInspectEnum.NO.getCode(), shopsConfig.getIsIoBoundInspect())) {
                    if (ObjectUtil.isNotEmpty(addGoodsBaseParam.getGoodsProd())) {
                        addGoodsBaseParam.setGoodsProd(!addGoodsBaseParam.getGoodsProd().contains(" ") ? addGoodsBaseParam.getGoodsProd() + " 00:00:00" : addGoodsBaseParam.getGoodsProd());
                    }
                    GoodsBatchAddPo goodsBatchAddPo = new GoodsBatchAddPo();
                    goodsBatchAddPo.setShopUnique(addGoodsBaseParam.getShopUnique());
                    goodsBatchAddPo.setBatchUnique(StrUtil.concat(true, String.valueOf(System.currentTimeMillis()), RandomUtil.randomNumbers(3)));
                    goodsBatchAddPo.setStockListUnique(listUnique);
                    goodsBatchAddPo.setGoodsBarcode(goodsMessage.getGoodsBarcode());
                    goodsBatchAddPo.setGoodsInPrice(goodsMessage.getGoodsInPrice());
                    goodsBatchAddPo.setGoodsCount(addGoodsBaseParam.getGoodsCount());
                    goodsBatchAddPo.setGoodsInCount(addGoodsBaseParam.getGoodsCount());
                    if (ObjectUtil.isNotEmpty(addGoodsBaseParam.getGoodsProd())) {
                        goodsBatchAddPo.setGoodsProd(DateUtil.parse(addGoodsBaseParam.getGoodsProd(), "yyyy-MM-dd HH:mm:ss"));
                    }
                    if (ObjectUtil.isNotEmpty(addGoodsBaseParam.getGoodsLife())) {
                        goodsBatchAddPo.setGoodsLife(addGoodsBaseParam.getGoodsLife());
                    }
                    if (ObjectUtil.isNotEmpty(addGoodsBaseParam.getGoodsLife()) && ObjectUtil.isNotEmpty(addGoodsBaseParam.getGoodsProd())) {
                        goodsBatchAddPo.setGoodsExp(DateUtil.offsetDay(goodsBatchAddPo.getGoodsProd(), addGoodsBaseParam.getGoodsLife()));
                    }
                    goodsBatchAddPo.setSourceBarcode(goodsMessage.getGoodsBarcode());
                    if (ObjectUtil.isNotEmpty(addGoodsBaseParam.getStaffId())) {
                        goodsBatchAddPo.setCreateId(Long.valueOf(addGoodsBaseParam.getStaffId()));
                        goodsBatchAddPo.setUpdateId(Long.valueOf(addGoodsBaseParam.getStaffId()));
                    }
                    goodsBatchAddPo.setCreateTime(DateUtil.date());
                    goodsBatchAddPo.setUpdateTime(DateUtil.date());
                    goodsBatchDao.saveGoodsBatch(goodsBatchAddPo);
                }
            }
        }
    }

    /**
     * 需要将以前管理的数据解除关联信息，并存储对应的修改记录
     * @param updateGoodsBaseParam
     * @return
     */
    @Override
    public ShopsResult updateGoods(UpdateGoodsBaseParam updateGoodsBaseParam) {
        List<UpdateGoodsParam> goodsMessage=updateGoodsBaseParam.getGoodsMessage();

        CreateOrUpdateGoodsListParams createOrUpdateGoodsListParams = new CreateOrUpdateGoodsListParams();
        createOrUpdateGoodsListParams.setShopUnique(String.valueOf(updateGoodsBaseParam.getShopUnique()));
        List<BaseGoodsMsg> f1 = new ArrayList<>();
        for (UpdateGoodsParam goodsParam : goodsMessage) {
            BaseGoodsMsg baseGoodsMsg = new BaseGoodsMsg();
            baseGoodsMsg.setGoodsBarcode(goodsParam.getGoodsBarcode());
            baseGoodsMsg.setGoodsName(goodsParam.getGoodsName());
            baseGoodsMsg.setGoodsStandard(goodsParam.getGoodsStandard());
            baseGoodsMsg.setGoodsUnit(goodsParam.getGoodsUnit());
            baseGoodsMsg.setGoodsSalesPrice(goodsParam.getGoodsSalePrice());
            baseGoodsMsg.setGoodsCusPrice(goodsParam.getGoodsCusPrice());
            baseGoodsMsg.setGoodsBrand(goodsParam.getGoodsUnit());

            f1.add(baseGoodsMsg);
        }
        createOrUpdateGoodsListParams.setF1(f1);

        //查询之前关联的商品信息，并与当前上传的商品对比，如果没在本次列表中，删除对应关联关系
        GoodsEntity goods = new GoodsEntity();
        goods.setShopUnique(updateGoodsBaseParam.getShopUnique());
        goods.setForeignKey(updateGoodsBaseParam.getForeignKey());
        List<GoodsEntity> goodsEntities = goodsDao.selectGoodsByParam(goods);
        //待取消关联关系的商品列表
        List<GoodsEntity> restoreList = new ArrayList<>();
        if(null != goodsEntities && !goodsEntities.isEmpty()){
            //新旧产品做比较，筛选出需要取消关联的产品
            for (GoodsEntity goodsEntity : goodsEntities) {
                //是否需要取消关联
                boolean flag = true;
                GoodsEntity g = null;
                for (UpdateGoodsParam goodsParam : goodsMessage) {
                    System.out.println("新商品" + goodsParam.getGoodsBarcode() + "====" + goodsEntity.getGoodsBarcode());

                    if (goodsParam.getGoodsBarcode().equals(goodsEntity.getGoodsBarcode())) {
                        //存在相同条码的商品，不需要取消关联
                        flag = false;
                        break;
                    }
                }
                //需要取消关联
                if (flag) {
                    restoreList.add(goodsEntity);
                }
            }
        }

        //大库商品
        List<Map<String,Object>> dictGoodsList = new ArrayList<>();

        if(goodsMessage==null||goodsMessage.size()<1)
        {
            return ShopsResult.fail("商品信息缺失！");
        }
        boolean flag=false;
        String msg="";
        List<AddGoodsPo> mqttList=new ArrayList<>();

        List<RecordGoodsOperParams> goodsUpdateRecords = new ArrayList<>();

        List<MinSaleCountAddParams> minSaleCountAddParamsList = new ArrayList<>();
        List<MinSaleCountUpdateParams> minSaleCountUpdateParamsList = new ArrayList<>();
        List<Map<String, Object>> wholeShopList = new ArrayList<>();
        for(UpdateGoodsParam data:goodsMessage)
        {
            Map<String, Object> dictMap = new HashMap<>();

            dictMap.put("goodsBarcode", data.getGoodsBarcode());
            dictMap.put("goodsName", data.getGoodsName());
            dictMap.put("goodsAlias", data.getGoodsAlias() == null ? "" : data.getGoodsAlias());
            if (ObjectUtil.isNotEmpty(data.getGoodsInPrice())) {
                dictMap.put("goodsInPrice", data.getGoodsInPrice());
            }
            dictMap.put("goodsSalePrice", data.getGoodsSalePrice());
            dictMap.put("goodsContain", data.getGoodsContain());
            dictMap.put("kindUnique", updateGoodsBaseParam.getGoodsKindUnique());
            dictMap.put("goodsRemarks", "");
            dictMap.put("goodsPicturepath", data.getGoodsPicturePath());
            dictMap.put("goodsStandard", data.getGoodsStandard());
            dictMap.put("goodsUnit", data.getGoodsUnit());
            dictMap.put("goodsBrand", null);
            dictMap.put("goodsAddress", null);
            dictMap.put("foreignKey", updateGoodsBaseParam.getForeignKey());

            dictGoodsList.add(dictMap);

            AddGoodsPo addGoodsPo =new AddGoodsPo();
            addGoodsPo.setGoodsId(data.getGoodsId());
            addGoodsPo.setShopUnique(String.valueOf(updateGoodsBaseParam.getShopUnique()));
            addGoodsPo.setGoodsBarcode(data.getGoodsBarcode());
            addGoodsPo.setUnderlinedPrice(data.getUnderlinedPrice());
            if(data.getGoodsBarcode().startsWith("0"))
            {
                flag=true;
                msg="商品条码不能0开头！";
                break;
            }else if(data.getGoodsBarcode().equals(updateGoodsBaseParam.getForeignKey())&&data.getGoodsContain()!=1)
            {
                flag=true;
                msg="基本包装,单位换算必须等于1！";
                break;
            }else if(!data.getGoodsBarcode().equals(updateGoodsBaseParam.getForeignKey())&&data.getGoodsContain()<1)
            {
                flag=true;
                msg="非最小包装商品的 换算单位必须大于1！";
                break;
            }
            //商品更新就是要重复的
//            else if(goodsDao.queryGoodsExists(addGoodsPo)>0)//去重
//            {
//                flag=true;
//                msg="商品条码名称不能重复:"+data.getGoodsName();
//                break;
//            }
            addGoodsPo.setGoodsContain(new BigDecimal(data.getGoodsContain()));
            addGoodsPo.setGoodsName(data.getGoodsName());
            addGoodsPo.setGoodsKindUnique(updateGoodsBaseParam.getGoodsKindUnique());
            addGoodsPo.setGoodsBrand(updateGoodsBaseParam.getGoodsBrand());
            addGoodsPo.setGoodsAddress(updateGoodsBaseParam.getGoodsAddress());
            addGoodsPo.setGoodsAlias(data.getGoodsAlias());
            if(StrUtil.isNotBlank(updateGoodsBaseParam.getSupplierUnique()))
            {
                addGoodsPo.setSupplierUnique(updateGoodsBaseParam.getSupplierUnique());
            }
            addGoodsPo.setForeignKey(updateGoodsBaseParam.getForeignKey());
            addGoodsPo.setGoodsPicturePath(data.getGoodsPicturePath());
            addGoodsPo.setGoodsRemarks(updateGoodsBaseParam.getGoodsRemarks());
            addGoodsPo.setGoodsSalePrice(data.getGoodsSalePrice());
            addGoodsPo.setGoodsInPrice(data.getGoodsInPrice());
            if (ObjectUtil.isEmpty(data.getGoodsCusPrice()) || data.getGoodsCusPrice().compareTo(BigDecimal.ZERO) == 0) {
                addGoodsPo.setGoodsCusPrice(data.getGoodsSalePrice());
            } else {
                addGoodsPo.setGoodsCusPrice(data.getGoodsCusPrice());
            }
            if (ObjectUtil.isEmpty(data.getGoodsWebSalePrice()) || data.getGoodsWebSalePrice().compareTo(BigDecimal.ZERO) == 0) {
                addGoodsPo.setGoodsWebSalePrice(data.getGoodsSalePrice());
            } else {
                addGoodsPo.setGoodsWebSalePrice(data.getGoodsWebSalePrice());
            }
            addGoodsPo.setGoodsScaleType(updateGoodsBaseParam.getGoodsChengType());
            addGoodsPo.setGoodsUnit(data.getGoodsUnit());
            addGoodsPo.setGoodsStandard(data.getGoodsStandard());
            if (ObjectUtil.isNotEmpty(updateGoodsBaseParam.getGoodsLife())) {
                addGoodsPo.setGoodsLife(updateGoodsBaseParam.getGoodsLife());
            }
            if(updateGoodsBaseParam.getStockWarningStatus()!=null&&updateGoodsBaseParam.getOutStockWaringCount()!=null)
            {
                addGoodsPo.setStockWarningStatus(String.valueOf(updateGoodsBaseParam.getStockWarningStatus()));
                if(data.getGoodsContain()==1)
                {
                    addGoodsPo.setOutStockWaringCount(String.valueOf(updateGoodsBaseParam.getOutStockWaringCount()));
                    if(updateGoodsBaseParam.getUnsalableCount()!=null)
                    {
                        addGoodsPo.setUnsalableCount(String.valueOf(updateGoodsBaseParam.getUnsalableCount()));
                    }

                }else
                {
                    addGoodsPo.setOutStockWaringCount(String.valueOf(updateGoodsBaseParam.getOutStockWaringCount().multiply(new BigDecimal(data.getGoodsContain()))));
                    if(updateGoodsBaseParam.getUnsalableCount()!=null) {
                        addGoodsPo.setUnsalableCount(String.valueOf(updateGoodsBaseParam.getUnsalableCount().multiply(new BigDecimal(data.getGoodsContain()))));
                    }
                }
            }


            if(data.getPcShelfState()==null)
            {
                data.setPcShelfState(1);
            }
            if(data.getShelfState()==null)
            {
                data.setShelfState(2);
            }
            addGoodsPo.setPcShelfState(new BigDecimal(data.getPcShelfState()));
            addGoodsPo.setShelfState(new BigDecimal(data.getShelfState()));
            addGoodsPo.setMinSaleCount(data.getMinSaleCount());
            if (data.getWholesalePriceFlg() ==1){
                if (ObjectUtil.isNotEmpty(data.getWholesaleList())) {
                    Set<BigDecimal> wholesaleSet = new HashSet<>();
                    if (data.getWholesaleList().size() > 5) {
                        flag=true;
                        msg="批发信息不能超过3条！";
                    }
                    for (UpdateGoodsParam.WholesaleInfo wholesaleInfo : data.getWholesaleList()) {
                        if (ObjectUtil.isEmpty(wholesaleInfo.getWholesaleCount())) {
                            flag=true;
                            msg="批发数量不能为空！";
                        }
                        if (ObjectUtil.isEmpty(wholesaleInfo.getWholesalePrice()) || wholesaleInfo.getWholesalePrice().compareTo(BigDecimal.ZERO) <= 0) {
                            flag=true;
                            msg="批发价不能为空或0！";
                        }
                        wholesaleSet.add(wholesaleInfo.getWholesaleCount());
                    }
                    if (wholesaleSet.size() != data.getWholesaleList().size()) {
                        flag=true;
                        msg="起批数量不能重复！";
                    }
                    List<GoodsWholesaleEntity> goodsWholesaleEntityList = new ArrayList<>();
                    for (UpdateGoodsParam.WholesaleInfo wholesaleInfo : data.getWholesaleList()) {
                        GoodsWholesaleEntity wholesaleEntity = new GoodsWholesaleEntity();
                        wholesaleEntity.setGoodsBarcode(data.getGoodsBarcode());
                        wholesaleEntity.setShopUnique(updateGoodsBaseParam.getShopUnique());
                        wholesaleEntity.setWholesalePrice(wholesaleInfo.getWholesalePrice());
                        wholesaleEntity.setWholesaleCount(wholesaleInfo.getWholesaleCount());
                        goodsWholesaleEntityList.add(wholesaleEntity);
                    }
                    Map<String, Object> wholeShopMap = new HashMap<>();
                    wholeShopMap.put("goodsBarcode",data.getGoodsBarcode());
                    wholeShopMap.put("shopUnique",updateGoodsBaseParam.getShopUnique());
                    wholeShopMap.put("wholesaleList",goodsWholesaleEntityList);
                    wholeShopList.add(wholeShopMap);
                } else {
                    flag=true;
                    msg="批发信息不能为空！";
                }
            }
            addGoodsPo.setWholesalePriceFlg(data.getWholesalePriceFlg());
            addGoodsPo.setGoodsPosition(data.getGoodsPosition());
            mqttList.add(addGoodsPo);


            //修改前的商品信息
            RecordGoods sourceGoods = new RecordGoods();
            //操作信息
            RecordGoodsOper recordGoodsOper = new RecordGoodsOper();
            Map<String, Object> map = new HashMap<>();
            map.put("shopUnique" , addGoodsPo.getShopUnique());
            map.put("goodsBarcode" , addGoodsPo.getGoodsBarcode());
            RecordGoods recordGoods = goodsDao.querySourceGoods(map);
            cn.hutool.core.bean.BeanUtil.copyProperties(recordGoods, sourceGoods);
            if(null != addGoodsPo.getGoodsId()){
                System.out.println(addGoodsPo.getGoodsId());
                recordGoodsOper.setGoodsId(Long.valueOf(addGoodsPo.getGoodsId()));
            }
            recordGoodsOper.setGoodsBarcode(addGoodsPo.getGoodsBarcode());
            recordGoodsOper.setShopUnique(updateGoodsBaseParam.getShopUnique());
            recordGoodsOper.setOperType(OperTypeEnum.OPER_TYPE_UPDATE.getValue());
            recordGoodsOper.setDeviceSource(updateGoodsBaseParam.getDeviceSource());
            recordGoodsOper.setUserId(updateGoodsBaseParam.getUserId() == null ? "" : StrUtil.toString(updateGoodsBaseParam.getUserId()));
            recordGoodsOper.setUserName(updateGoodsBaseParam.getUserName());
            recordGoodsOper.setUserType(updateGoodsBaseParam.getUserType());
            recordGoodsOper.setCreateTtime(DateUtil.formatDateTime(DateUtil.date()));
            recordGoodsOper.setOperSource(OperSourceEnum.GOODS_CHANGE.getValue());
            recordGoodsOper.setDeviceSourceMsg(updateGoodsBaseParam.getDevicesourcemsg());
            RecordGoodsOperParams recordGoodsOperParams = new RecordGoodsOperParams();
            recordGoodsOperParams.setSourceGoods(sourceGoods);
            recordGoodsOperParams.setRecordGoodsOper(recordGoodsOper);
            goodsUpdateRecords.add(recordGoodsOperParams);
        }

        //处理需要取消关联的商品信息
        for (GoodsEntity goodsEntity : restoreList){
            /**
             * 1、添加信息到商品记录
             * 2、添加mqtt同步消息通知
             * 3、添加商品修改列表
             */
            goodsEntity.setForeignKey(Long.parseLong(goodsEntity.getGoodsBarcode()));
            goodsEntity.setGoodsCount(BigDecimal.valueOf(1));

            AddGoodsPo addGoodsPo =new AddGoodsPo();
            addGoodsPo.setGoodsId(goodsEntity.getGoodsId());
            addGoodsPo.setShopUnique(String.valueOf(updateGoodsBaseParam.getShopUnique()));
            addGoodsPo.setUnderlinedPrice(goodsEntity.getUnderlinedPrice());
            addGoodsPo.setGoodsBarcode(goodsEntity.getGoodsBarcode());
            addGoodsPo.setGoodsContain(BigDecimal.valueOf(1));
            addGoodsPo.setGoodsName(goodsEntity.getGoodsName());
            addGoodsPo.setGoodsKindUnique(updateGoodsBaseParam.getGoodsKindUnique());
            addGoodsPo.setGoodsBrand(updateGoodsBaseParam.getGoodsBrand());
            addGoodsPo.setGoodsAddress(updateGoodsBaseParam.getGoodsAddress());
            addGoodsPo.setGoodsAlias(goodsEntity.getGoodsAlias());
            addGoodsPo.setForeignKey(goodsEntity.getForeignKey());
            addGoodsPo.setGoodsPicturePath(goodsEntity.getGoodsPicturepath());
            addGoodsPo.setGoodsRemarks(updateGoodsBaseParam.getGoodsRemarks());
            addGoodsPo.setGoodsSalePrice(goodsEntity.getGoodsSalePrice());
            addGoodsPo.setGoodsInPrice(goodsEntity.getGoodsInPrice());
            addGoodsPo.setGoodsCusPrice(new BigDecimal(goodsEntity.getGoodsCusPrice()).setScale(2, BigDecimal.ROUND_HALF_UP));
            addGoodsPo.setGoodsWebSalePrice(goodsEntity.getGoodsWebSalePrice());
            addGoodsPo.setGoodsScaleType(updateGoodsBaseParam.getGoodsChengType());
            addGoodsPo.setGoodsUnit(goodsEntity.getGoodsUnit());
            addGoodsPo.setGoodsStandard(goodsEntity.getGoodsStandard());
            addGoodsPo.setPcShelfState(new BigDecimal(goodsEntity.getPcShelfState()));
            addGoodsPo.setShelfState(new BigDecimal(goodsEntity.getShelfState()));
            if (ObjectUtil.isNotEmpty(updateGoodsBaseParam.getGoodsLife())) {
                addGoodsPo.setGoodsLife(updateGoodsBaseParam.getGoodsLife());
            }

            //添加商品修改记录
            //修改前的商品信息
            RecordGoods sourceGoods = new RecordGoods();
            //操作信息
            RecordGoodsOper recordGoodsOper = new RecordGoodsOper();
            Map<String, Object> map = new HashMap<>();
            map.put("shopUnique" , addGoodsPo.getShopUnique());
            map.put("goodsBarcode" , addGoodsPo.getGoodsBarcode());
            RecordGoods recordGoods = goodsDao.querySourceGoods(map);
            cn.hutool.core.bean.BeanUtil.copyProperties(recordGoods, sourceGoods);
            if(null != addGoodsPo.getGoodsId()){
                recordGoodsOper.setGoodsId(Long.valueOf(addGoodsPo.getGoodsId()));
            }
            recordGoodsOper.setGoodsBarcode(addGoodsPo.getGoodsBarcode());
            recordGoodsOper.setShopUnique(updateGoodsBaseParam.getShopUnique());
            recordGoodsOper.setOperType(OperTypeEnum.OPER_TYPE_UPDATE.getValue());
            recordGoodsOper.setDeviceSource(updateGoodsBaseParam.getDeviceSource());
            recordGoodsOper.setUserId(updateGoodsBaseParam.getUserId() == null ? "" : StrUtil.toString(updateGoodsBaseParam.getUserId()));
            recordGoodsOper.setUserName(updateGoodsBaseParam.getUserName());
            recordGoodsOper.setUserType(updateGoodsBaseParam.getUserType());
            recordGoodsOper.setCreateTtime(DateUtil.formatDateTime(DateUtil.date()));
            recordGoodsOper.setOperSource(OperSourceEnum.GOODS_CHANGE.getValue());
            recordGoodsOper.setDeviceSourceMsg(updateGoodsBaseParam.getDevicesourcemsg());
            RecordGoodsOperParams recordGoodsOperParams = new RecordGoodsOperParams();
            recordGoodsOperParams.setSourceGoods(sourceGoods);
            recordGoodsOperParams.setRecordGoodsOper(recordGoodsOper);
            goodsUpdateRecords.add(recordGoodsOperParams);
        }

        if(flag)
        {
            return ShopsResult.fail(msg);
        }
        if (ObjectUtil.isNotEmpty(wholeShopList)) {
            for (Map<String, Object> wholeShopMap : wholeShopList) {
                Map<String, Object> map = new HashMap<>();
                map.put("shopUnique", wholeShopMap.get("shopUnique"));
                map.put("goodsBarcode", wholeShopMap.get("goodsBarcode"));
                if (ObjectUtil.isNotEmpty(goodsWholesaleMapper.queryByGoodsBarcode(map))) {
                    goodsWholesaleMapper.deleteByGoodsBarcode(map);
                }
                for (GoodsWholesaleEntity goodsWholesaleEntity : (List<GoodsWholesaleEntity>) wholeShopMap.get("wholesaleList")) {
                    goodsWholesaleMapper.insert(goodsWholesaleEntity);
                }
            }
        }
        for(AddGoodsPo data:mqttList)
        {
            AddGoodsPo dataParam = new AddGoodsPo();
            dataParam.setShopUnique(data.getShopUnique());
            dataParam.setGoodsBarcode(data.getGoodsBarcode());
            if (goodsDao.queryGoodsExists(dataParam) > 0) {
                goodsDao.updateGoodsInfo(data);
            } else {
                goodsDao.addGoodsInfo(data);
            }
            if (ObjectUtil.isNotEmpty(data.getMinSaleCount())) {
                GoodsOnlineSettingDto goodsOnlineSettingDto = goodsOnlineSettingDao.queryGoodsOnlineSettingByGoodsId(data.getGoodsId());
                if (ObjectUtil.isNotEmpty(goodsOnlineSettingDto) && ObjectUtil.isNotEmpty(goodsOnlineSettingDto.getGoodsId())) {
                    MinSaleCountUpdateParams minSaleCountUpdateParams = new MinSaleCountUpdateParams();
                    minSaleCountUpdateParams.setGoodsId(goodsOnlineSettingDto.getGoodsId());
                    minSaleCountUpdateParams.setMinSaleCount(data.getMinSaleCount());
                    minSaleCountUpdateParamsList.add(minSaleCountUpdateParams);
                } else {
                    MinSaleCountAddParams minSaleCountAddParams = new MinSaleCountAddParams();
                    minSaleCountAddParams.setGoodsId(data.getGoodsId());
                    minSaleCountAddParams.setMinSaleCount(data.getMinSaleCount());
                    minSaleCountAddParamsList.add(minSaleCountAddParams);
                }
            }
            try {

                SendMqttMsg sendMqttMsg = new SendMqttMsg(updateGoodsBaseParam.getShopUnique() + "", data.getGoodsBarcode());
                sendMqttMsg.start();
            }catch (Exception e) {
            	log.error("MQTT通知收银机商品信息更新异常：", e);
                sendDingDingTalkUtils.sendDingDingTalkMsg(((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest(), "发送MQTT消息同步收银机商品信息", e, JSONUtil.toJsonStr(data), "修改商品", BusinessType.UPDATE.ordinal());
			}
        }

        try {
            if (ObjectUtil.isNotEmpty(minSaleCountAddParamsList)) {
                goodsOnlineSettingDao.saveGoodsOnlineSettingList(minSaleCountAddParamsList);
            }
            if (ObjectUtil.isNotEmpty(minSaleCountUpdateParamsList)) {
                goodsOnlineSettingDao.updateGoodsOnlineSettingList(minSaleCountUpdateParamsList);
            }

            final List<AddGoodsPo> threadMqttList = mqttList;
            final String supplierUnique = updateGoodsBaseParam.getSupplierUnique();
            ThreadUtil.execute(new Runnable() {
                public void run() {
                    for (AddGoodsPo data : threadMqttList) {
                        if (StrUtil.isNotBlank(supplierUnique)) {
                            Map<String, Object> params = new HashMap<>();
                            params.put("shopUnique", data.getShopUnique());
                            params.put("goodsBarcode", data.getGoodsBarcode());
                            params.put("supplierUnique", data.getSupplierUnique());
                            params.put("createId", "1");
                            params.put("createBy", "SYSTEM");
                            shopSupplierService.addSupGood(params);
                        }

                    }
                }
            });
        } catch (Exception e) {
            log.error("添加商品供货商异常：", e);
            sendDingDingTalkUtils.sendDingDingTalkMsg(((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest(), "商品绑定供货商", e, JSONUtil.toJsonStr(mqttList), "修改商品", BusinessType.UPDATE.ordinal());
        }

        for (RecordGoodsOperParams recordGoodsOperParams : goodsUpdateRecords) {
            try{
                Map<String, Object> map = new HashMap<>();
                map.put("shopUnique", recordGoodsOperParams.getRecordGoodsOper().getShopUnique());
                map.put("goodsBarcode", recordGoodsOperParams.getRecordGoodsOper().getGoodsBarcode());
                RecordGoods recordGoods = goodsDao.querySourceGoods(map);
                recordGoodsOperParams.setResultGoods(recordGoods);
                Map<String,Object> headerMap = new HashMap<>();
                headerMap.put("Content-Type", "application/json; charset=" + HttpUtil.DEFAULT_CHARSET);
                String jsonStr = JSONObject.toJSONString(recordGoodsOperParams);
                String result = HttpUtil.doPostStr(HelibaoPayConfig.RECORDGOODSOPER, headerMap, jsonStr);
            } catch (Exception e){
                log.error("添加商品操作记录异常：", e);
                sendDingDingTalkUtils.sendDingDingTalkMsg(((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest(), "添加商品操作记录", e, JSONUtil.toJsonStr(recordGoodsOperParams), "修改商品", BusinessType.UPDATE.ordinal());
            }
        }

        try {
            if(null != dictGoodsList && !dictGoodsList.isEmpty()){
                for (Map<String, Object> dictMap : dictGoodsList){
                    sameMsgToGoodsDict(dictMap);
                }
            }
        } catch (Exception e){
            log.error("同步商品字典异常：", e);
        }

        try {
            if (ObjectUtil.isNotEmpty(f1)) {
                bhService.createOrUpdateGoodsList(createOrUpdateGoodsListParams);
            }
        } catch (Exception e){
            log.error("同步电子价签异常：", e);
        }

        return ShopsResult.ok();
    }

    /**
     * 创建新的条码
     * @return
     */
    public ShopsResult queryGoodsBarcodeSameForeignkey(String shopUnique) {
        try {
            ShopsResult sr = new ShopsResult(1, "查询成功！");

            Map<String, Object> map = new HashMap<>();
            map.put("shopUnique", shopUnique);
            //创建新的条码
            int c = 1000000;
            while (true) {
                String goodsBarcodeStr = goodsDao.queryGoodsBarcodeSame(map);
                if (ObjectUtil.isNotEmpty(goodsBarcodeStr)) {
                    String nGoodsBarcode = String.valueOf(new BigDecimal(goodsBarcodeStr).add(BigDecimal.valueOf(c)).add(BigDecimal.valueOf(Math.random() * 10000)).setScale(0,RoundingMode.HALF_UP));
                    c++;
                    map.put("goodsBarcode", nGoodsBarcode);
                    String barcode = goodsDao.queryGoodsBarcodeSameForeignkey(map);
                    if (null == barcode) {//新条码可用
                        sr.setData(nGoodsBarcode);
                        break;
                    }
                } else {
                    String nGoodsBarcode = String.valueOf(BigDecimal.valueOf(c).add(BigDecimal.valueOf(Math.random() * 10000)).setScale(0,RoundingMode.HALF_UP));
                    c++;
                    map.put("goodsBarcode", nGoodsBarcode);
                    String barcode = goodsDao.queryGoodsBarcodeSameForeignkey(map);
                    if (null == barcode) {//新条码可用
                        sr.setData(nGoodsBarcode);
                        break;
                    }
                }

            }
            return sr;
        } catch (Exception e) {
            log.error("查询商品条码异常：", e);
            throw new MyException(1, "系统错误！");
        }
    }

    @Override
    public ShopsResult queryOneByParam(String shopUnique, String goodsBarcode) {
        ShopsResult sr = new ShopsResult(1, "查询成功！");
        List<Map<String, Object>> goodsList = new ArrayList<Map<String, Object>>();
        Map<String,Object> params = new HashMap<>();
        params.put("goodsBarcode", goodsBarcode);
        params.put("shopUnique", shopUnique);
        GoodsEntity goodsEntity = goodsDao.queryOneByParam(params);
        if (ObjectUtil.isNotNull(goodsEntity)) {
            Map<String, Object> temp = new HashMap<String, Object>();
            temp.put("goodsBarcode", goodsBarcode);
            temp.put("url", goodsEntity.getGoodsPicturepath());
            goodsList.add(temp);
        } else {
            sr.setStatus(0);
            sr.setMsg("没有满足条件的商品信息");
        }
        sr.setData(goodsList);
        return sr;
    }

    /**
     * 同步商品信息到goods_dict表
     */
    public void sameMsgToGoodsDict(Map<String,Object> data){
        try{
            //需要先确认该商品是否存在，且十分可修改
            Map<String, Object> dictMap = dictDao.queryDictGoodsMessage(data);

            if(null == dictMap || dictMap.isEmpty()){
                dictDao.newGoodsDict(data);
            } else {
                //存在，且不为空
                Integer finalSure = Integer.parseInt(dictMap.get("finalSure") == null ? "0" : dictMap.get("finalSure").toString());

                if(finalSure == 1){
                    return;
                }else {
                    dictDao.moifyGoodsDictMessage(data);
                }
            }

        } catch (Exception e){
            log.error("同步商品字典异常：", e);
        }
    }
}
