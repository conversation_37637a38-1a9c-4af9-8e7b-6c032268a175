package org.haier.shopUpdate.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.log4j.Logger;
import org.haier.shopUpdate.aop.ValidateReqAspect;
import org.haier.shopUpdate.dao.*;
import org.haier.shopUpdate.entity.*;
import org.haier.shopUpdate.enums.DeviceTypeEnum;
import org.haier.shopUpdate.enums.InventoryOperateLogTypeEnum;
import org.haier.shopUpdate.enums.InventoryTaskStatusEnum;
import org.haier.shopUpdate.enums.IsIoBoundInspectEnum;
import org.haier.shopUpdate.enums.shopStockDetail.StockKindEnum;
import org.haier.shopUpdate.enums.shopStockDetail.StockOriginEnum;
import org.haier.shopUpdate.enums.shopStockDetail.StockResourceEnum;
import org.haier.shopUpdate.enums.shopStockDetail.StockTypeEnum;
import org.haier.shopUpdate.params.inventoryTask.*;
import org.haier.shopUpdate.params.shopStock.ShopStockAddParams;
import org.haier.shopUpdate.params.shopStockDetail.ShopStockDetailAddParams;
import org.haier.shopUpdate.result.inventoryTask.*;
import org.haier.shopUpdate.util.ShopsResult;
import org.haier.shopUpdate.util.UtilForJAVA;
import org.haier.shopUpdate.util.common.utils.StringUtils;
import org.haier.shopUpdate.util.thread.SendMqttMsg;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
@Service
public class InventoryTaskServiceImpl implements InventoryTaskService{
    private Logger log = Logger.getLogger(InventoryTaskServiceImpl.class);
    @Autowired
    private ShopStaffDao shopStaffDao;

    @Autowired
    private InventoryTaskDao taskDao;

    @Autowired
    private InventoryTaskOperateLogDao operateLogDao;

    @Autowired
    private InventoryTaskDetailDao taskDetailDao;

    @Autowired
    private GoodsDao goodsDao;

    @Autowired
    private InventoryTaskGoodsLocationDao locationDao;

    @Autowired
    private InventoryTaskBucketWeightDao bucketWeightDao;

    @Autowired
    private InventoryTaskStatisticsDao statisticsDao;

    @Autowired
    private StockDao stockDao;
    @Resource
    private ShopsConfigDao shopsConfigDao;

    @Autowired
    private GoodsPositionServiceImpl goodsPositionService;

    /**
     * 新增盘点任务
     * @param req
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShopsResult addTask(AddTaskParams req) {
        try {
            ShopsResult staffResult = checkStaff(req.getShopUnique(), req.getStaffId());
            if (staffResult.hasFailed()) return staffResult;
            ShopStaffEntity staff = (ShopStaffEntity) staffResult.getData();
            if (staff.getStaffPosition() != 3){ //只有店长才能添加
                return ShopsResult.fail("权限不足");
            }

            //前端如果不传递taskName后端随机生成
            if (ObjectUtil.isEmpty(req.getTaskName())){
                req.setTaskName(genOrderNo());
            }

            //校验任务名称
            ShopsResult checkTaskNameResult = checkTaskName(req.getTaskName(), req.getShopUnique());
            if (checkTaskNameResult.hasFailed()){
                return checkTaskNameResult;
            }

            //保存盘点任务单
            InventoryTaskEntity save = new InventoryTaskEntity();
            BeanUtils.copyProperties(req,save);
            save.setTaskNo(StringUtils.join("PD",genOrderNo()));
            save.setTaskStatus(InventoryTaskStatusEnum.WAITING.getStatus());
            save.setCreateUser(req.getStaffId());
            taskDao.insert(save);

            //保存操作日志
            String operateLog = StrUtil.format("任务名称：{}",save.getTaskName());
            saveOperateLog(save.getId(),InventoryOperateLogTypeEnum.ADDTASK,operateLog,req.getStaffId());

            return ShopsResult.ok();
        }catch (Exception e){
            log.error("新增盘点任务异常：",e);
            return ShopsResult.fail("新增盘点任务异常");
        }
    }

    /**
     * 盘点任务列表
     * @param req
     * @return
     */
    @Override
    public ShopsResult taskList(TaskListParams req) {
        ShopsResult staffResult = checkStaff(req.getShopUnique(), req.getStaffId());
        if (staffResult.hasFailed()) return staffResult;

        //请求参数处理
        req.setPageIndex((req.getPageIndex()-1)*req.getPageSize());
        if (ObjectUtil.isNotEmpty(req.getStartDate())){
            req.setStartDate(StringUtils.join(req.getStartDate()," 00:00:00"));
        }
        if (ObjectUtil.isNotEmpty(req.getEndDate())){
            req.setEndDate(StringUtils.join(req.getEndDate()," 23:59:59"));
        }

        Map<String,Object> queryMap = new HashMap<>();
        queryMap.put("shopUnique",req.getShopUnique());
        queryMap.put("startDate",req.getStartDate());
        queryMap.put("endDate",req.getEndDate());
        queryMap.put("taskStatus",req.getTaskStatus());
        queryMap.put("pageIndex",req.getPageIndex());
        queryMap.put("pageSize",req.getPageSize());
        queryMap.put("searchKey",req.getSearchKey());

        //盘点任务列表
        List<InventoryTaskEntity> taskList = taskDao.queryByPage(queryMap);

        TaskListResult result = new TaskListResult();
        if (ObjectUtil.isEmpty(taskList)){
            result.setTaskList(new ArrayList<TaskListTask>());
            return ShopsResult.ok(result);
        }

        ShopsResult ok = ShopsResult.ok(handleTaskListResult(req,taskList));

        //web端需多进行一次查询
        if (DeviceTypeEnum.WEB.getName().equals(req.getFrom())){ //web端增加统计数据
            ok.setCount(taskDao.queryByPageCount(queryMap));
        }

        return ok;
    }

    /**
     * 删除盘点任务
     * @param req
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShopsResult deleteTask(DeleteTaskParams req) {
        //员工校验
        ShopsResult staffResult = checkStaff(req.getShopUnique(), req.getStaffId());
        if (staffResult.hasFailed()) return staffResult;
        ShopStaffEntity staff = (ShopStaffEntity) staffResult.getData();

        if (staff.getStaffPosition() != 3){ //只有店长才能删除
            return ShopsResult.fail("权限不足");
        }

        //任务校验
        ShopsResult taskResult = checkTask(req.getShopUnique(), req.getTaskId());
        if (taskResult.hasFailed()) return taskResult;
        InventoryTaskEntity taskEntity = (InventoryTaskEntity) taskResult.getData();
        if (taskEntity.getTaskStatus() == 2){
            return ShopsResult.fail("盘点单已提交，不允许操作");
        }

        //删除盘点任务
        taskDao.deleteById(req.getTaskId());

        //删除盘点任务详情
        InventoryTaskDetailEntity detail = new InventoryTaskDetailEntity();
        detail.setTaskId(req.getTaskId());
        taskDetailDao.deleteByParam(detail);

        //保存操作日志
        saveOperateLog(req.getTaskId(),InventoryOperateLogTypeEnum.DELTASK,InventoryOperateLogTypeEnum.DELTASK.getMsg(),req.getStaffId());

        return ShopsResult.ok();
    }

    /**
     * 盘点任务详情
     * @param req
     * @return
     */
    @Override
    public ShopsResult taskDetail(TaskDetailParams req) {
        //员工校验
        ShopsResult staffResult = checkStaff(req.getShopUnique(), req.getStaffId());
        if (staffResult.hasFailed()) return staffResult;
        ShopStaffEntity staff = (ShopStaffEntity) staffResult.getData();

        //任务单校验
        ShopsResult checkTaskResult = checkTask(req.getShopUnique(), req.getTaskId());
        if (checkTaskResult.hasFailed()) return checkTaskResult;
        InventoryTaskEntity task = (InventoryTaskEntity) checkTaskResult.getData();

        //请求参数处理
        req.setPageIndex((req.getPageIndex()-1)*req.getPageSize());

        //任务单详情
        Map<String,Object> queryMap = new HashMap<>();
        queryMap.put("taskId",req.getTaskId());
        queryMap.put("staffId",req.getStaffId());
        queryMap.put("searchKey",req.getSearchKey());
        queryMap.put("pageIndex",req.getPageIndex());
        queryMap.put("pageSize",req.getPageSize());

        List<InventoryTaskDetailEntity> detailList = taskDetailDao.findByPage(queryMap);

        //返回参数构建
        TaskDetailResult result = new TaskDetailResult();
        result.setTaskNo(task.getTaskNo());
        result.setTaskName(task.getTaskName());
        result.setCreateTime(DateUtil.format(task.getCreateTime(),"yyyy-MM-dd HH:mm:ss"));
        result.setCreateUser(staff.getStaffName());
        result.setTaskDetailList(handleTaskDetailGoods(detailList,staff.getStaffName()));
        //获取任务单号
        List<Long> taskIds = new ArrayList<>();
        taskIds.add(task.getId());
        List<InventoryTaskDetailKindCount> kindCountList = taskDetailDao.findKindCount(taskIds);
        Map<Long,Integer> taskIdKind = new HashMap<>();
        for (InventoryTaskDetailKindCount k : kindCountList){
            taskIdKind.put(k.getTaskId(),k.getTotal());
        }
        result.setKindTotal(ObjectUtil.isEmpty(taskIdKind.get(task.getId())) ? 0 : taskIdKind.get(task.getId()));
        return ShopsResult.ok(result);
    }

    /**
     * 修改盘点单名称
     * @param req
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShopsResult updateTaskName(UpdatetTaskNameParams req) {
        ShopsResult staffResult = checkStaff(req.getShopUnique(), req.getStaffId());
        if (staffResult.hasFailed()) return staffResult;
        ShopStaffEntity staff = (ShopStaffEntity) staffResult.getData();
        if (staff.getStaffPosition() != 3){ //只有店长才能修改
            return ShopsResult.fail("权限不足");
        }

        //任务单校验
        ShopsResult checkTaskResult = checkTask(req.getShopUnique(), req.getTaskId());
        if (checkTaskResult.hasFailed()) return checkTaskResult;
        InventoryTaskEntity task = (InventoryTaskEntity) checkTaskResult.getData();
        if (task.getTaskStatus() == 2){
            return ShopsResult.fail("盘点单已提交，不允许操作");
        }

        //修改盘点任务
        if (!req.getTaskName().equals(task.getTaskName())){

            //校验任务名称
            ShopsResult checkTaskNameResult = checkTaskName(req.getTaskName(), req.getShopUnique());
            if (checkTaskNameResult.hasFailed()){
                return checkTaskNameResult;
            }

            //更新任务
            InventoryTaskEntity upd = new InventoryTaskEntity();
            upd.setId(task.getId());
            upd.setTaskName(req.getTaskName());
            taskDao.updateById(upd);

            //保存操作记录
            String operateLog = StrUtil.format("任务名称由“{}”改为“{}”",task.getTaskName(),req.getTaskName());
            saveOperateLog(task.getId(),InventoryOperateLogTypeEnum.UPDATETASK,operateLog,req.getStaffId());
        }

        return ShopsResult.ok();
    }

    /**
     * 保存盘点详情
     * @param req
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShopsResult addTaskDetail(AddTaskDetailParams req) {
        ShopsResult staffResult = checkStaff(req.getShopUnique(), req.getStaffId());
        if (staffResult.hasFailed()) return staffResult;

        //任务单校验
        ShopsResult checkTaskResult = checkTask(req.getShopUnique(), req.getTaskId());
        if (checkTaskResult.hasFailed()) return checkTaskResult;
        InventoryTaskEntity task = (InventoryTaskEntity) checkTaskResult.getData();
        if (task.getTaskStatus() == 2){
            return ShopsResult.fail("盘点单已提交，不允许操作");
        }

        //校验商品
        Map<String,Object> queryGoods = new HashMap<>();
        queryGoods.put("shopUnique",req.getShopUnique());
        queryGoods.put("goodsBarcode",req.getGoodsBarcode());
        GoodsEntity goodsEntity = goodsDao.queryOneByParam(queryGoods);
        if (ObjectUtil.isEmpty(goodsEntity)){
            return ShopsResult.fail("商品参数非法");
        }

        //保存新的数据
        InventoryTaskDetailEntity taskDetail = handleTaskDetailInsert(goodsEntity, req);
        taskDetailDao.insert(taskDetail);

        //保存操作日志
        StringBuffer operateLog = new StringBuffer();
        operateLog.append(StrUtil.format("商品条码：{}，商品名称：{}，盘点数：{}，备注：{}；",taskDetail.getGoodsBarcode(),taskDetail.getGoodsName(),taskDetail.getInventoryCount(),taskDetail.getRemarks()));
        saveOperateLog(req.getTaskId(),InventoryOperateLogTypeEnum.ADDTASKDETAIL,operateLog.toString(),req.getStaffId());

        return ShopsResult.ok();
    }

    /**
     * 修改盘点详情
     * @param req
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShopsResult updateTaskDetail(UpdateTaskDetailParams req) {
        //员工校验
        ShopsResult staffResult = checkStaff(req.getShopUnique(), req.getStaffId());
        if (staffResult.hasFailed()) return staffResult;

        //任务详情单校验
        ShopsResult taskDetailResult = checkTaskDetail(req.getTaskDetailId(), req.getStaffId(),req.getTaskId());
        if (taskDetailResult.hasFailed()) return taskDetailResult;
        InventoryTaskDetailEntity oldDetail = (InventoryTaskDetailEntity) taskDetailResult.getData();

        //任务单校验
        ShopsResult checkTaskResult = checkTask(req.getShopUnique(), req.getTaskId());
        if (checkTaskResult.hasFailed()) return checkTaskResult;
        InventoryTaskEntity task = (InventoryTaskEntity) checkTaskResult.getData();
        if (task.getTaskStatus() == 2){
            return ShopsResult.fail("盘点单已提交，不允许操作");
        }

        if (ObjectUtil.isEmpty(req.getRemarks())) req.setRemarks("");

        //修改数据
        StringBuffer opLog = new StringBuffer(StrUtil.format("商品条码：{},商品名称：{}，",oldDetail.getGoodsBarcode(),oldDetail.getGoodsName()));
        boolean upd = false;
        InventoryTaskDetailEntity updEntity = new InventoryTaskDetailEntity();
        updEntity.setId(oldDetail.getId());
        updEntity.setGoodsPosition(req.getGoodsPosition());
        if(!UtilForJAVA.formatBigDecimal(oldDetail.getBucketWeight()).equals(UtilForJAVA.formatBigDecimal(req.getBucketWeight()))){
            upd = true;
            opLog.append("筐重量由").append(UtilForJAVA.formatBigDecimal(oldDetail.getBucketWeight())).append("改为").append(UtilForJAVA.formatBigDecimal(req.getBucketWeight())).append("；");
            updEntity.setBucketWeight(req.getBucketWeight());
        }
        if (!req.getRemarks().equals(oldDetail.getRemarks())){
            upd = true;
            opLog.append("备注由").append(oldDetail.getRemarks()).append("改为").append(req.getRemarks()).append("；");
            updEntity.setRemarks(req.getRemarks());
        }
        BigDecimal newIc = NumberUtil.sub(req.getInventoryCountTotal(), req.getBucketWeight());
        if (!UtilForJAVA.formatBigDecimal(oldDetail.getInventoryCount()).equals(UtilForJAVA.formatBigDecimal(newIc))){
            upd = true;
            opLog.append("盘点数由").append(UtilForJAVA.formatBigDecimal(oldDetail.getInventoryCount())).append("改为").append(UtilForJAVA.formatBigDecimal(newIc)).append("；");
            updEntity.setInventoryCount(newIc);
        }

        if (upd){
            taskDetailDao.updateById(updEntity);
            saveOperateLog(req.getTaskId(),InventoryOperateLogTypeEnum.UPDATETASKDETAIL,opLog.toString(),req.getStaffId());
        }

        return ShopsResult.ok();
    }

    /**
     * 删除盘点任务详情
     * @param req
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShopsResult deleteTaskDetail(DeleteTaskDetailParams req) {
        //员工校验
        ShopsResult staffResult = checkStaff(req.getShopUnique(), req.getStaffId());
        if (staffResult.hasFailed()) return staffResult;

        //任务详情单校验
        ShopsResult taskDetailResult = checkTaskDetail(req.getTaskDetailId(), req.getStaffId(),req.getTaskId());
        if (taskDetailResult.hasFailed()) return taskDetailResult;
        InventoryTaskDetailEntity oldDetail = (InventoryTaskDetailEntity) taskDetailResult.getData();

        //删除任务详情
        taskDetailDao.deleteById(req.getTaskDetailId());
        StringBuffer opLog = new StringBuffer(StrUtil.format("商品条码：{},商品名称：{}，",oldDetail.getGoodsBarcode(),oldDetail.getGoodsName()));
        saveOperateLog(req.getTaskId(),InventoryOperateLogTypeEnum.DELETETASKDETAIL,opLog.toString(),req.getStaffId());

        return ShopsResult.ok();
    }

    /**
     * 货位列表
     * @param req
     * @return
     */
    @Override
    public ShopsResult goodsLocationList(GoodsLocationListParams req) {
        //员工校验
        ShopsResult staffResult = checkStaff(req.getShopUnique(), req.getStaffId());
        if (staffResult.hasFailed()) return staffResult;

        //查询货位
        InventoryTaskGoodsLocationEntity entity = new InventoryTaskGoodsLocationEntity();
        entity.setStafferId(req.getStaffId());
        List<InventoryTaskGoodsLocationEntity> locationList = locationDao.findByParam(entity);

        //返回参数构建
        GoodsLocationListResult result = new GoodsLocationListResult();
        List<GoodsLocationListLocation> goodsLocationList = new ArrayList<>();
        for (InventoryTaskGoodsLocationEntity l : locationList){
            GoodsLocationListLocation ll = new GoodsLocationListLocation();
            ll.setLocationId(l.getId());
            ll.setLocationName(l.getLocationName());
            goodsLocationList.add(ll);
        }
        result.setGoodsLocationList(goodsLocationList);
        return ShopsResult.ok(result);
    }

    /**
     * 货位新增
     * @param req
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShopsResult addGoodsLocation(AddGoodsLocationParams req) {
        //员工校验
        ShopsResult staffResult = checkStaff(req.getShopUnique(), req.getStaffId());
        if (staffResult.hasFailed()) return staffResult;

        //货位校验
        ShopsResult locationResult = checkLocationName(req.getStaffId(), req.getLocationName());
        if (locationResult.hasFailed()) return locationResult;

        //添加货位
        InventoryTaskGoodsLocationEntity add = new InventoryTaskGoodsLocationEntity();
        add.setLocationName(req.getLocationName());
        add.setStafferId(req.getStaffId());
        locationDao.insert(add);

        return ShopsResult.ok();
    }

    /**
     * 修改货位
     * @param req
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShopsResult updateGoodsLocation(UpdateGoodsLocationParams req) {
        //员工校验
        ShopsResult staffResult = checkStaff(req.getShopUnique(), req.getStaffId());
        if (staffResult.hasFailed()) return staffResult;

        //校验货位
        ShopsResult locationIdResult = checkLocation(req.getStaffId(), req.getLocationId());
        if (locationIdResult.hasFailed()) return locationIdResult;
        InventoryTaskGoodsLocationEntity old = (InventoryTaskGoodsLocationEntity) locationIdResult.getData();

        if (!old.getLocationName().equals(req.getLocationName())){ //修改名称
            InventoryTaskGoodsLocationEntity upd = new InventoryTaskGoodsLocationEntity();
            upd.setId(req.getLocationId());
            upd.setLocationName(req.getLocationName());
            locationDao.updateById(upd);
        }

        return ShopsResult.ok();
    }

    /**
     * 删除货位
     * @param req
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShopsResult deleteGoodsLocation(DeleteGoodsLocationParams req) {
        //员工校验
        ShopsResult staffResult = checkStaff(req.getShopUnique(), req.getStaffId());
        if (staffResult.hasFailed()) return staffResult;

        //校验货位
        ShopsResult locationIdResult = checkLocation(req.getStaffId(), req.getLocationId());
        if (locationIdResult.hasFailed()) return locationIdResult;

        //删除货位
        locationDao.delete(req.getLocationId());

        return ShopsResult.ok();
    }

    /**
     * 盘库单预览
     * @param req
     * @return
     */
    public ShopsResult taskPreview(TaskPreviewParams req) {
        //员工校验
        ShopsResult staffResult = checkStaff(req.getShopUnique(), req.getStaffId());
        if (staffResult.hasFailed()) return staffResult;
        ShopStaffEntity staff = (ShopStaffEntity) staffResult.getData();

        //任务单校验
        ShopsResult checkTaskResult = checkTask(req.getShopUnique(), req.getTaskId());
        if (checkTaskResult.hasFailed()) return checkTaskResult;
        InventoryTaskEntity task = (InventoryTaskEntity) checkTaskResult.getData();

        //请求参数处理
        req.setPageIndex((req.getPageIndex()-1)*req.getPageSize());

        //查询创建人
        ShopsResult createUserResult = null;
        if (task.getCreateUser().equals(req.getStaffId())){
            createUserResult = staffResult;
        }else {
            createUserResult = checkStaff(task.getShopUnique(), task.getCreateUser());
        }
        String createUser = "";
        if (createUserResult.hasSuccess()){
            ShopStaffEntity createStaff = (ShopStaffEntity) createUserResult.getData();
            createUser = createStaff.getStaffName();
        }

        //查询任务单详情
        Map<String,Object> queryMap = new HashMap<>();
        queryMap.put("taskId",req.getTaskId());
        if (staff.getStaffPosition() != 3) { //不是店长只能查自己的
            queryMap.put("staffId", req.getStaffId());
        }
        queryMap.put("searchKey",req.getSearchKey());
        queryMap.put("pageIndex",req.getPageIndex());
        queryMap.put("pageSize",req.getPageSize());

        //查询的是goods_id;inventory_count的和
        List<InventoryTaskDetailEntity> detailList = taskDetailDao.findByPagePreview(queryMap);

        //返回参数构建
        TaskPreviewResult result = new TaskPreviewResult();
        result.setCreateTime(DateUtil.format(task.getCreateTime(),"yyyy-MM-dd HH:mm:ss"));
        result.setCreateUser(createUser);
        result.setTaskNo(task.getTaskNo());
        result.setTaskName(task.getTaskName());
        result.setTaskStatus(task.getTaskStatus());
        if (ObjectUtil.isEmpty(detailList)){
            result.setTaskDetailList(new ArrayList<TaskPreviewTaskDetailList>());

            ShopsResult ok = ShopsResult.ok(result);
            //web端需多进行一次查询
            if (DeviceTypeEnum.WEB.getName().equals(req.getFrom())){ //web端增加统计数据
                ok.setCount(0);
            }

            return ok;
        }

        //构建详情信息
        List<InventoryTaskDetailEntity> realDetailList = handleRealDetailList(task, detailList);

        if (staff.getStaffPosition() == 3){ //店长
            result.setTaskDetailList(handleDianzhangPreview(task,realDetailList));
        }else { //员工
            result.setTaskDetailList(handleYuangongPreview(realDetailList));
        }
        ShopsResult ok = ShopsResult.ok(result);
        //web端需多进行一次查询
        if (DeviceTypeEnum.WEB.getName().equals(req.getFrom())){ //web端增加统计数据
            ok.setCount(taskDetailDao.findByPagePreviewCount(queryMap));
        }
        return ok;
    }

    /**
     * 提交盘点
     * @param req
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShopsResult submitTask(SubmitTaskParams req) {
        //员工校验
        ShopsResult staffResult = checkStaff(req.getShopUnique(), req.getStaffId());
        if (staffResult.hasFailed()) return staffResult;
        ShopStaffEntity staff = (ShopStaffEntity) staffResult.getData();
        if (staff.getStaffPosition() != 3){ //只有店长才能修改
            return ShopsResult.fail("权限不足");
        }

        //任务单校验
        ShopsResult checkTaskResult = checkTask(req.getShopUnique(), req.getTaskId());
        if (checkTaskResult.hasFailed()) return checkTaskResult;
        InventoryTaskEntity task = (InventoryTaskEntity) checkTaskResult.getData();
        if (task.getTaskStatus() == 2){
            return ShopsResult.fail("盘点单已提交，不允许操作");
        }

        InventoryTaskDetailEntity detailQuery = new InventoryTaskDetailEntity();
        detailQuery.setTaskId(req.getTaskId());
        List<InventoryTaskDetailEntity> detailList = taskDetailDao.findByParam(detailQuery);
        if (ObjectUtil.isEmpty(detailList)){
            return ShopsResult.fail("请先添加盘点记录");
        }

        //修改盘点单状态为已提交
        InventoryTaskEntity upd = new InventoryTaskEntity();
        upd.setId(req.getTaskId());
        upd.setTaskStatus(2);
        upd.setFinishUser(req.getStaffId());
        upd.setFinishTime(new Date());
        taskDao.updateById(upd);

        //保存盘点单-盘点信息，修改库存
        saveInventoryStatisticsAndUpdateStock(req,detailList);

        //保存操作日志
        saveOperateLog(req.getTaskId(),InventoryOperateLogTypeEnum.SUBMITTASK,"盘点单提交",req.getStaffId());

        if(ObjectUtil.isNotEmpty(detailList)){
            try{
                String barcodes = null;
                for(InventoryTaskDetailEntity g : detailList){
                    if(null == barcodes){
                        barcodes = g.getGoodsBarcode();
                    }else {
                        barcodes += ";" + g.getGoodsBarcode();
                    }
                }
                SendMqttMsg mqttMsg = new SendMqttMsg(req.getShopUnique() + "", null , barcodes);
                mqttMsg.start();
            }catch (Exception e){
                log.error("发送mqtt消息失败",e);
            }
        }
        return ShopsResult.ok();
    }

    /**
     * 单个商品盘点明细-商品盘点
     * @param req
     * @return
     */
    @Override
    public ShopsResult taskGoodsDetail(TaskGoodsDetailParams req) {
        //员工校验
        ShopsResult staffResult = checkStaff(req.getShopUnique(), req.getStaffId());
        ShopStaffEntity staff = (ShopStaffEntity) staffResult.getData();
        if (staffResult.hasFailed()) return staffResult;

        //商品校验
        Map<String,Object> queryGoods = new HashMap<>();
        queryGoods.put("shopUnique",req.getShopUnique());
        queryGoods.put("goodsBarcode",req.getGoodsBarcode());
        GoodsEntity goodsEntity = goodsDao.queryOneByParam(queryGoods);
        if (ObjectUtil.isEmpty(goodsEntity)){
            return ShopsResult.fail("请求参数异常");
        }

        //任务单校验
        ShopsResult taskResult = checkTask(req.getShopUnique(), req.getTaskId());
        InventoryTaskEntity task = (InventoryTaskEntity) taskResult.getData();
        if (taskResult.hasFailed()) return taskResult;

        //详情查询
        InventoryTaskDetailEntity taskDetail = new InventoryTaskDetailEntity();
        taskDetail.setTaskId(req.getTaskId());
        taskDetail.setGoodsBarcode(req.getGoodsBarcode());
        if (staff.getStaffPosition() != 3){ //店长查全部
            taskDetail.setStaffId(req.getStaffId());
        }
        List<InventoryTaskDetailEntity> taskDetailList = taskDetailDao.findByParam(taskDetail);
        if (ObjectUtil.isEmpty(taskDetailList)){
            return ShopsResult.fail("请求参数异常");
        }
        InventoryTaskDetailEntity detail = taskDetailList.get(0);

        //返回参数构建
        TaskGoodsDetailResult result = new TaskGoodsDetailResult();
        //基本信息
        result.setGoodsName(detail.getGoodsName());
        result.setGoodsBarcode(detail.getGoodsBarcode());
        result.setGoodsPicturepath(detail.getGoodsPicturepath());
        result.setTaskName(task.getTaskName());
        result.setTaskNo(task.getTaskNo());
        //商品盘点列表
        result.setGoodsList(handleTaskGoodsDetailGoods(staff,taskDetailList));
        //库存
        handleTaskGoodsDetailStock(result,staff,task);

        return ShopsResult.ok(result);
    }

    /**
     * 新增筐
     * @param req
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShopsResult addBucketWeight(AddBucketWeightParams req) {
        //员工校验
        ShopsResult staffResult = checkStaff(req.getShopUnique(), req.getStaffId());
        if (staffResult.hasFailed()) return staffResult;

        //校验重量是否重复添加
        ShopsResult weightResult = checkBucketWeight(req.getShopUnique(), req.getBucketWeight());
        if (weightResult.hasFailed()) return weightResult;

        //保存数据
        InventoryTaskBucketWeightEntity bucket = new InventoryTaskBucketWeightEntity();
        bucket.setBucketWeight(req.getBucketWeight());
        bucket.setShopUnique(req.getShopUnique());
        bucket.setUpdateUser(req.getStaffId());
        bucketWeightDao.insert(bucket);

        return ShopsResult.ok();
    }

    /**
     * 修改筐重量
     * @param req
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShopsResult updateBucketWeight(UpdateBucketWeightParams req) {
        //员工校验
        ShopsResult staffResult = checkStaff(req.getShopUnique(), req.getStaffId());
        if (staffResult.hasFailed()) return staffResult;

        //校验筐信息
        ShopsResult bucketEntityResult = checkBucketEntity(req.getShopUnique(), req.getBucketId());
        if (bucketEntityResult.hasFailed()) return bucketEntityResult;
        InventoryTaskBucketWeightEntity bucketWeight = (InventoryTaskBucketWeightEntity) bucketEntityResult.getData();

        //修改筐重量
        if (!bucketWeight.getBucketWeight().equals(req.getBucketWeight())){
            InventoryTaskBucketWeightEntity upd = new InventoryTaskBucketWeightEntity();
            upd.setId(req.getBucketId());
            upd.setBucketWeight(req.getBucketWeight());
            upd.setUpdateUser(req.getStaffId());
            bucketWeightDao.updateById(upd);
        }

        return ShopsResult.ok();
    }

    /**
     * 删除筐重量
     * @param req
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShopsResult deleteBucketWeight(DeleteBucketWeightParams req) {
        //员工校验
        ShopsResult staffResult = checkStaff(req.getShopUnique(), req.getStaffId());
        if (staffResult.hasFailed()) return staffResult;

        //校验筐信息
        ShopsResult bucketEntityResult = checkBucketEntity(req.getShopUnique(), req.getBucketId());
        if (bucketEntityResult.hasFailed()) return bucketEntityResult;

        //删除筐
        bucketWeightDao.deleteById(req.getBucketId());

        return ShopsResult.ok();
    }

    /**
     * 筐重量列表
     * @param req
     * @return
     */
    @Override
    public ShopsResult bucketWeightList(BucketWeightListParams req) {
        //请求参数处理
        req.setPageIndex((req.getPageIndex()-1)*req.getPageSize());

        Map<String,Object> queryMap = new HashMap<>();
        queryMap.put("shopUnique",req.getShopUnique());
        queryMap.put("pageIndex",req.getPageIndex());
        queryMap.put("pageSize",req.getPageSize());
        List<InventoryTaskBucketWeightEntity> list = bucketWeightDao.findByPage(queryMap);

        BucketWeightListResult result = new BucketWeightListResult();
        List<BucketWeightListBucket> bucketList = new ArrayList<>();

        for (InventoryTaskBucketWeightEntity bw : list){
            BucketWeightListBucket b = new BucketWeightListBucket();
            b.setBucketId(bw.getId());
            b.setBucketWeight(bw.getBucketWeight());

            bucketList.add(b);
        }

        result.setBucketWeightList(bucketList);
        return ShopsResult.ok(result);
    }

    /**
     * 商品盘库记录
     * @param req
     * @return
     */
    @Override
    public ShopsResult inventoryGoodsRecord(InventoryGoodsRecordParams req) {
        //员工校验
        ShopsResult staffResult = checkStaff(req.getShopUnique(), req.getStaffId());
        if (staffResult.hasFailed()) return staffResult;
        ShopStaffEntity staff = (ShopStaffEntity) staffResult.getData();

        //商品校验
        Map<String,Object> queryGoods = new HashMap<>();
        queryGoods.put("shopUnique",req.getShopUnique());
        queryGoods.put("goodsBarcode",req.getGoodsBarcode());
        GoodsEntity goodsEntity = goodsDao.queryOneByParam(queryGoods);
        if (ObjectUtil.isEmpty(goodsEntity)){
            return ShopsResult.fail("请求参数异常");
        }

        req.setPageIndex((req.getPageIndex()-1)*req.getPageSize());

        //返回参数构建
        InventoryGoodsRecordResult result = new InventoryGoodsRecordResult();
        result.setGoodsCount(goodsEntity.getGoodsCount());
        result.setGoodsName(goodsEntity.getGoodsName());
        result.setGoodsUnit(ObjectUtil.isEmpty(goodsEntity.getGoodsUnit()) ? "" : goodsEntity.getGoodsUnit());
        result.setGoodsPicturepath(goodsEntity.getGoodsPicturepath());
        result.setInventoryList(handleInventoryGoodsRecord(req,staff));

        return ShopsResult.ok(result);
    }

    /**
     * 盘点单预览下载
     * @param req
     * @return
     */
    @Override
    public ShopsResult taskPreviewDownload(TaskPreviewDowloadParams req) {
        //员工校验
        ShopsResult staffResult = checkStaff(req.getShopUnique(), req.getStaffId());
        if (staffResult.hasFailed()) return staffResult;
        ShopStaffEntity staff = (ShopStaffEntity) staffResult.getData();
        if (staff.getStaffPosition() != 3){
            return ShopsResult.fail("权限不足");
        }

        //任务单校验
        ShopsResult checkTaskResult = checkTask(req.getShopUnique(), req.getTaskId());
        if (checkTaskResult.hasFailed()) return checkTaskResult;
        InventoryTaskEntity task = (InventoryTaskEntity) checkTaskResult.getData();

        //查询任务单详情
        Map<String,Object> queryMap = new HashMap<>();
        queryMap.put("taskId",req.getTaskId());

        //查询的是goods_id;inventory_count的和
        List<InventoryTaskDetailEntity> detailList = taskDetailDao.findByPagePreview(queryMap);

        //构建详情信息
        List<InventoryTaskDetailEntity> realDetailList = handleRealDetailList(task, detailList);

        TaskPreviewDowloadResult result = new TaskPreviewDowloadResult();
        result.setTaskName(task.getTaskName());
        result.setTaskDetailList(handleDianzhangPreview(task,realDetailList));

        return ShopsResult.ok(result);
    }

    //************************************* 华丽的分割线 *************************************

    /**
     * 根据员工id查询
     * @param shopUnique
     * @param staffId
     * @return
     */
    private ShopsResult checkStaff(Long shopUnique,Integer staffId){
        ShopStaffEntity entity = new ShopStaffEntity();
        entity.setShopUnique(shopUnique);
        entity.setStaffId(staffId);
        ShopStaffEntity staff = shopStaffDao.findByStaffId(entity);
        return ObjectUtil.isEmpty(staff) ? ShopsResult.fail("当前店员不归属于此店铺") : ShopsResult.ok(staff);
    }

    /**
     * 根据任务名称和店铺编号查询
     * @param taskName
     * @param shopUnique
     * @return
     */
    private ShopsResult checkTaskName(String taskName,Long shopUnique){

        InventoryTaskEntity query = new InventoryTaskEntity();
        query.setTaskName(taskName);
        query.setShopUnique(shopUnique);
        InventoryTaskEntity entity = taskDao.findByParam(query);
        return ObjectUtil.isEmpty(entity) ? ShopsResult.ok() : ShopsResult.fail("盘库单名称重复");
    }

    /**
     * 随机生成单号
     * @return
     */
    private String genOrderNo(){
        return StringUtils.join(DateUtil.format(new Date(), "yyMMddHHmmss"),RandomUtil.randomNumbers(4));
    }

    /**
     * 保存操作日志
     * @param taskId
     * @param logTypeEnum
     * @param operateLog
     */
    private void saveOperateLog(Long taskId, InventoryOperateLogTypeEnum logTypeEnum, String operateLog, Integer staffId){
        InventoryTaskOperateLogEntity entity = new InventoryTaskOperateLogEntity();
        entity.setTaskId(taskId);
        entity.setOperateType(logTypeEnum.getType());
        entity.setOperateLog(operateLog);
        entity.setStaffId(staffId);
        operateLogDao.insert(entity);
    }

    /**
     * 任务校验
     * @param shopUnique
     * @param taskId
     * @return
     */
    private ShopsResult checkTask(Long shopUnique,Long taskId){
        InventoryTaskEntity entity = new InventoryTaskEntity();
        entity.setShopUnique(shopUnique);
        entity.setId(taskId);
        InventoryTaskEntity query = taskDao.findByParam(entity);
        return ObjectUtil.isEmpty(query) ? ShopsResult.fail("请求参数异常") : ShopsResult.ok(query);
    }

    /**
     * 任务详情校验
     * @return
     */
    private ShopsResult checkTaskDetail(Long taskDetailId,int staffId,long taskId){
        InventoryTaskDetailEntity detail = taskDetailDao.findById(taskDetailId);
        if (ObjectUtil.isEmpty(detail) || !detail.getStaffId().equals(staffId) || !detail.getTaskId().equals(taskId)){
            return ShopsResult.fail("请求参数异常");
        }

        return ShopsResult.ok(detail);
    }

    /**
     * 盘点任务详情商品转换
     * @param detailList
     * @return
     */
    private List<TaskDetailGoods> handleTaskDetailGoods(List<InventoryTaskDetailEntity> detailList,String staffName){
        List<TaskDetailGoods> goodsList = new ArrayList<>();
        List<String> ids = detailList.stream().map(InventoryTaskDetailEntity::getGoodsPosition).collect(Collectors.toList());
        Map<String, String> result = goodsPositionService.queryDetailByPositionId(ids);
        for (InventoryTaskDetailEntity d : detailList){
            TaskDetailGoods g = new TaskDetailGoods();
            BeanUtils.copyProperties(d,g);
            g.setStaffName(staffName);

            g.setInventoryCount(UtilForJAVA.formatBigDecimal(g.getInventoryCount()));
            g.setBucketWeight(UtilForJAVA.formatBigDecimal(g.getBucketWeight()));
            g.setTaskDetailId(d.getId());
            g.setGoodsPicturepath(d.getGoodsPicturepath());
            g.setGoodsUnit(d.getGoodsUnit());
            g.setGoodsChengType(d.getGoodsChengType());
            g.setGoodsPosition(d.getGoodsPosition());
            g.setCompletePositionName(result.get(g.getGoodsPosition()));
            goodsList.add(g);
        }

        return goodsList;
    }

    /**
     * 构建保存盘点单详情的数据
     * @param goodsEntity
     * @param req
     * @return
     */
    public InventoryTaskDetailEntity handleTaskDetailInsert(GoodsEntity goodsEntity,AddTaskDetailParams req){
        InventoryTaskDetailEntity entity = new InventoryTaskDetailEntity();

        entity.setTaskId(req.getTaskId());
        entity.setStaffId(req.getStaffId());
        entity.setGoodsId(goodsEntity.getGoodsId());
        entity.setGoodsBarcode(goodsEntity.getGoodsBarcode());
        entity.setGoodsPicturepath(goodsEntity.getGoodsPicturepath());
        entity.setGoodsName(goodsEntity.getGoodsName());
        entity.setBucketWeight(req.getBucketWeight());
        entity.setInventoryCount(NumberUtil.sub(req.getInventoryCountTotal(),req.getBucketWeight()));
        entity.setGoodsUnit(goodsEntity.getGoodsUnit());
        entity.setGoodsChengType(goodsEntity.getGoodsChengType());
        entity.setGoodsPosition(req.getGoodsPosition());

        entity.setRemarks(ObjectUtil.isEmpty(req.getRemarks()) ? "" : req.getRemarks());
        return entity;
    }

    /**
     * 校验货位名称重复
     * @param staffId
     * @param locationName
     * @return
     */
    private ShopsResult checkLocationName(Integer staffId,String locationName){
        InventoryTaskGoodsLocationEntity entity = new InventoryTaskGoodsLocationEntity();
        entity.setLocationName(locationName);
        entity.setStafferId(staffId);
        List<InventoryTaskGoodsLocationEntity> list = locationDao.findByParam(entity);
        return ObjectUtil.isEmpty(list) ? ShopsResult.ok() : ShopsResult.fail("货位名称重复");
    }

    /**
     * 校验货位
     * @param staffId
     * @param locationId
     * @return
     */
    private ShopsResult checkLocation(Integer staffId,Long locationId){
        InventoryTaskGoodsLocationEntity entity = new InventoryTaskGoodsLocationEntity();
        entity.setStafferId(staffId);
        entity.setId(locationId);
        List<InventoryTaskGoodsLocationEntity> list = locationDao.findByParam(entity);
        return ObjectUtil.isEmpty(list) ? ShopsResult.fail("请求参数异常") : ShopsResult.ok(list.get(0));
    }

    /**
     * 盘点任务列表返回
     * @return
     */
    private TaskListResult handleTaskListResult(TaskListParams req,List<InventoryTaskEntity> taskList){
        TaskListResult result = new TaskListResult();
        List<TaskListTask> list = new ArrayList<>();

        Set<Integer> staffIds = new HashSet<>();
        for (InventoryTaskEntity task : taskList){
            staffIds.add(task.getCreateUser());
        }

        //查询员工
        Map<String,Object> queryMap = new HashMap<>();
        queryMap.put("shopUnique",taskList.get(0).getShopUnique());
        queryMap.put("staffIdList",staffIds);
        List<ShopStaffEntity> stafffIdList = shopStaffDao.findByStaffIdList(queryMap);

        //员工信息转map
        Map<Integer,String> staffIdName = new HashMap<>();
        for (ShopStaffEntity s : stafffIdList){
            staffIdName.put(s.getStaffId(),s.getStaffName());
        }

        //获取任务单号
        List<Long> taskIds = new ArrayList<>();
        for (InventoryTaskEntity t : taskList){
            taskIds.add(t.getId());
        }
        List<InventoryTaskDetailKindCount> kindCountList = taskDetailDao.findKindCount(taskIds);
        Map<Long,Integer> taskIdKind = new HashMap<>();
        for (InventoryTaskDetailKindCount k : kindCountList){
            taskIdKind.put(k.getTaskId(),k.getTotal());
        }

        for (InventoryTaskEntity task : taskList){

            TaskListTask tlt = new TaskListTask();
            tlt.setTaskId(task.getId());
            tlt.setTaskName(task.getTaskName());
            tlt.setTaskStatus(task.getTaskStatus());
            tlt.setCreateTime(DateUtil.format(task.getCreateTime(),"yyyy-MM-dd HH:mm:ss"));
            tlt.setFinishTime(ObjectUtil.isEmpty(task.getFinishTime()) ? "" : DateUtil.format(task.getFinishTime(),"yyyy-MM-dd HH:mm:ss"));
            tlt.setCreateUser(staffIdName.get(task.getCreateUser()));
            tlt.setGoodsList(handleTaskListGoodsList(req,task.getId()));
            tlt.setKindTotal(ObjectUtil.isEmpty(taskIdKind.get(task.getId())) ? 0 : taskIdKind.get(task.getId()));

            list.add(tlt);
        }

        result.setTaskList(list);
        return result;
    }

    /**
     * 盘点任务商品信息返回
     * @param taskId
     * @return
     */
    private List<TaskListGoodsList> handleTaskListGoodsList(TaskListParams req,Long taskId){
        List<TaskListGoodsList> goodsList = new ArrayList<>();

        //统计盘点任务详情
        Map<String,Object> detailQuery = new HashMap<>();
        detailQuery.put("staffId",req.getStaffId());
        detailQuery.put("taskId",taskId);
        List<InventoryTaskDetailEntity> taskDetailList = taskDetailDao.findByStaffIdAndTaskIdLimit4(detailQuery);

        for (InventoryTaskDetailEntity d : taskDetailList){
            TaskListGoodsList g = new TaskListGoodsList();
            g.setGoodsPicturepath(d.getGoodsPicturepath());
            g.setGoodsName(d.getGoodsName());

            goodsList.add(g);
        }
        return goodsList;
    }

    /**
     * 处理商品库存
     * @param goodsId
     * @param goodsList
     * @return
     */
    private GoodsEntity handlePreviewGoodsStock(Integer goodsId,List<GoodsEntity> goodsList){
        for (GoodsEntity g : goodsList){
            if (g.getGoodsId().equals(goodsId)){
                return g;
            }
        }
        return null;
    }

    /**
     * 处理商品统计库存
     * @param goodsBarcode
     * @param statList
     * @return
     */
    private InventoryTaskStatisticsEntity handlePreviewGoodsStat(String goodsBarcode, List<InventoryTaskStatisticsEntity> statList) {
        for (InventoryTaskStatisticsEntity stat : statList) {
            if (goodsBarcode.equals(stat.getGoodsBarcode())) return stat;
        }

        return null;
    }

    /**
     * 校验筐重量
     * @param shopUnique
     * @param bucketWeight
     * @return
     */
    private ShopsResult checkBucketWeight(Long shopUnique,BigDecimal bucketWeight){

        InventoryTaskBucketWeightEntity entity = new InventoryTaskBucketWeightEntity();
        entity.setShopUnique(shopUnique);
        entity.setBucketWeight(bucketWeight);
        InventoryTaskBucketWeightEntity byParam = bucketWeightDao.findOneByParam(entity);

        return ObjectUtil.isEmpty(byParam) ? ShopsResult.ok() : ShopsResult.fail("筐重量重复");
    }

    /**
     * 校验筐信息
     * @param shopUnique
     * @param id
     * @return
     */
    private ShopsResult checkBucketEntity(Long shopUnique,Long id){
        InventoryTaskBucketWeightEntity entity = new InventoryTaskBucketWeightEntity();
        entity.setShopUnique(shopUnique);
        entity.setId(id);
        InventoryTaskBucketWeightEntity byParam = bucketWeightDao.findOneByParam(entity);
        return ObjectUtil.isEmpty(byParam) ? ShopsResult.fail("请求参数异常") : ShopsResult.ok(byParam);
    }

    /**
     * 保存盘点单-盘点信息
     */
    private void saveInventoryStatisticsAndUpdateStock(SubmitTaskParams req, List<InventoryTaskDetailEntity> detailList){
        //key:goodsId value:盘点数
        //存储的是商品id->盘点数量
        Map<Integer,BigDecimal> goodsIdInventoryCountMap = new HashMap<>(1024);
        //存储的是 商品条码->任务id
        Map<String,Object> goodsBarcodeTaskIdMap = new HashMap<>(1024);
        Set<String> goodsBarcodeSet = new HashSet<>();
        for (InventoryTaskDetailEntity d : detailList){
            BigDecimal inventoryCount = BigDecimal.ZERO;
            if (goodsIdInventoryCountMap.containsKey(d.getGoodsId())){
                inventoryCount = goodsIdInventoryCountMap.get(d.getGoodsId());
            }
            goodsIdInventoryCountMap.put(d.getGoodsId(),NumberUtil.add(inventoryCount,d.getInventoryCount()));
            goodsBarcodeTaskIdMap.put(d.getGoodsBarcode(),d.getTaskId());
            goodsBarcodeSet.add(d.getGoodsBarcode());
        }

        //查询商品信息
        Map<String,Object> goodsQuery = new HashMap<>();
        goodsQuery.put("shopUnique",req.getShopUnique());
        goodsQuery.put("goodsBarcodeList",goodsBarcodeSet);
        List<GoodsEntity> goodsList = goodsDao.queryGoodsByParam(goodsQuery);

        //保存数据构建
        List<InventoryTaskStatisticsEntity> list = new ArrayList<>();
        Iterator<Map.Entry<Integer, BigDecimal>> iter = goodsIdInventoryCountMap.entrySet().iterator();
        Map<Long,BigDecimal> goodsForeignKeyGoodsCount = new HashMap<>(1024);
        Set<Long> foreignKeySet = new HashSet<>(1024);
        while (iter.hasNext()){
            Map.Entry<Integer, BigDecimal> next = iter.next();
            Integer goodsId = next.getKey();
            BigDecimal inventoryCount = next.getValue(); //盘点数

            GoodsEntity goodsEntity = handlePreviewGoodsStock(goodsId, goodsList);

            if(ObjectUtil.isNull(goodsEntity)){
                System.out.println("当前商品已经不存在" + goodsId);
                continue;
            }

            BigDecimal goodsStock = NumberUtil.mul(inventoryCount,goodsEntity.getGoodsContain());
            //库存操作
            if (goodsForeignKeyGoodsCount.containsKey(goodsEntity.getForeignKey())){
                BigDecimal decimal = goodsForeignKeyGoodsCount.get(goodsEntity.getForeignKey());
                goodsStock = NumberUtil.add(decimal,goodsStock);
            }
            goodsForeignKeyGoodsCount.put(goodsEntity.getForeignKey(),goodsStock);
            foreignKeySet.add(goodsEntity.getForeignKey());

            //统计数据
            InventoryTaskStatisticsEntity statistics = new InventoryTaskStatisticsEntity();
            statistics.setTaskId(req.getTaskId());
            statistics.setGoodsBarcode(goodsEntity.getGoodsBarcode());
            statistics.setGoodsName(goodsEntity.getGoodsName());
            BigDecimal goodsContain = goodsEntity.getGoodsContain();
            BigDecimal goodsCount = goodsEntity.getGoodsCount();
            //此处区别对待，非最小规格的商品，前库存设置为0
            if (goodsContain.compareTo(BigDecimal.ONE) != 0) {
                statistics.setPreStock(BigDecimal.ZERO);
                goodsCount = BigDecimal.ZERO;
            } else {
                statistics.setPreStock(goodsEntity.getGoodsCount());
            }
            statistics.setInventoryCount(inventoryCount);
            BigDecimal diffCount = NumberUtil.sub(statistics.getInventoryCount(), goodsCount);
            statistics.setDiffCount(diffCount);
            statistics.setDiffMoney(goodsEntity.getGoodsInPrice());
            statistics.setDiffMoney(NumberUtil.mul(diffCount,goodsEntity.getGoodsInPrice()));
            statistics.setGoodsInPrice(goodsEntity.getGoodsInPrice());

            list.add(statistics);
        }

        statisticsDao.insertBatch(list);

        //根据foreignKey查询商品
        Map<String,Object> queryGoods = new HashMap<>();
        queryGoods.put("shopUnique",req.getShopUnique());
        queryGoods.put("foreignKeyList",foreignKeySet);
        List<GoodsEntity> goodsEntityList = goodsDao.queryGoodsByParam(queryGoods);

        //批量更新库存
        List<GoodsEntity> updGoodsList = new ArrayList<>(1024);
        List<Map<String,Object>> stockRecords = new ArrayList<>();

        List<Map<String,Object>> goodsForeignList = new ArrayList<>();



        String listUnique = StrUtil.concat(true, DateUtil.format(DateUtil.date(), "yyyyMMddHHmmssSSS"), RandomUtil.randomNumbers(3));
        //总数量
        BigDecimal totalCount = new BigDecimal(0);
        //总金额
        BigDecimal totalAmount = new BigDecimal(0);
        //换算成每级产品数量
        for (GoodsEntity g : goodsEntityList){
            Map<String,Object> stockMap = new HashMap<>();
            BigDecimal baseCount = BigDecimal.ZERO;
            baseCount = goodsForeignKeyGoodsCount.getOrDefault(g.getForeignKey(),BigDecimal.ZERO);

            g.setGoodsCount(baseCount.divide(g.getGoodsContain(),0, RoundingMode.DOWN));
            stockMap.put("goodsBarcode",g.getGoodsBarcode());
            stockMap.put("shopUnique",g.getShopUnique());
            stockMap.put("goodsCount",g.getGoodsCount());
            stockMap.put("stockCount",g.getGoodsCount());
            stockMap.put("stockType",1);
            stockMap.put("stockResource",4);
            stockMap.put("listUnique",listUnique);
            stockMap.put("stockPrice",g.getGoodsInPrice());
            stockMap.put("stockOrigin",1);
            stockMap.put("staffId",req.getStaffId());
            updGoodsList.add(g);
            if (ObjectUtil.isEmpty(goodsBarcodeTaskIdMap.get(g.getGoodsBarcode()))) continue;
            stockRecords.add(stockMap);
            totalCount = totalCount.add(g.getGoodsCount());
            totalAmount = totalAmount.add(g.getGoodsCount().multiply(g.getGoodsInPrice()));
        }

        Map<String,Object> updGoodsCount = new HashMap<>();
        updGoodsCount.put("list",updGoodsList);
        stockDao.batchUpdateGoodsCount(updGoodsCount);
        stockDao.newStockRecords(stockRecords,"1");
        ShopsConfig shopsConfig = shopsConfigDao.selectByShopUnique(String.valueOf(req.getShopUnique()));
        ShopStockDetailAddParams shopStockDetailAddParams = new ShopStockDetailAddParams();
        shopStockDetailAddParams.setShopUnique(String.valueOf(req.getShopUnique()));
        if (ObjectUtil.equal(IsIoBoundInspectEnum.NO.getCode(), shopsConfig.getIsIoBoundInspect())) {
            shopStockDetailAddParams.setAuditStatus(1);
        } else {
            shopStockDetailAddParams.setAuditStatus(0);
        }
        shopStockDetailAddParams.setShopUnique(String.valueOf(req.getShopUnique()));
        shopStockDetailAddParams.setListUnique(listUnique);
        shopStockDetailAddParams.setStockKind(StockKindEnum.INITIALIZED.getCode());
        shopStockDetailAddParams.setStockType(StockTypeEnum.INVENTORY_IN.getCode());
        shopStockDetailAddParams.setStockResource(StockResourceEnum.STOCK_TAKING.getCode());
        shopStockDetailAddParams.setStockOrigin(StockOriginEnum.MOBILE.getCode());
        if (ObjectUtil.isNotEmpty(req.getStaffId())) {
            shopStockDetailAddParams.setStaffId(Long.valueOf(req.getStaffId()));
            shopStockDetailAddParams.setUpdateId(Long.valueOf(req.getStaffId()));
        }
        shopStockDetailAddParams.setUpdateTime(DateUtil.date());
        shopStockDetailAddParams.setStockTime(DateUtil.date());
        shopStockDetailAddParams.setTotalCount(totalCount);
        shopStockDetailAddParams.setTotalAmount(totalAmount);
        stockDao.addIntoStockDetailV2(shopStockDetailAddParams);

    }
    /**
     * 商品明细构建
     * @param staff
     * @param taskDetailList
     * @return
     */
    private List<TaskGoodsDetailGoodsList> handleTaskGoodsDetailGoods(ShopStaffEntity staff, List<InventoryTaskDetailEntity> taskDetailList){
        List<TaskGoodsDetailGoodsList> list = new ArrayList<>();

        //员工编号与员工姓名转换
        Map<Integer,String> staffIdStaffNameMap = new HashMap<>();
        if (staff.getStaffPosition() == 3){ //店长
            Set<Integer> staffIdSet = new HashSet<>();
            for (InventoryTaskDetailEntity d : taskDetailList){
                staffIdSet.add(d.getStaffId());
            }
            Map<String,Object> staffMap = new HashMap<>();
            staffMap.put("shopUnique",staff.getShopUnique());
            staffMap.put("staffIdList",staffIdSet);
            List<ShopStaffEntity> staffList = shopStaffDao.findByStaffIdList(staffMap);

            for (ShopStaffEntity s : staffList){
                staffIdStaffNameMap.put(s.getStaffId(),s.getStaffName());
            }

        }else {
            staffIdStaffNameMap.put(staff.getStaffId(),staff.getStaffName());
        }
        List<String> ids = taskDetailList.stream().map(InventoryTaskDetailEntity::getGoodsPosition).collect(Collectors.toList());
        Map<String, String> result = goodsPositionService.queryDetailByPositionId(ids);
        //参数构建
        for (InventoryTaskDetailEntity d : taskDetailList){
            TaskGoodsDetailGoodsList td = new TaskGoodsDetailGoodsList();

            td.setInventoryCount(UtilForJAVA.formatBigDecimal(d.getInventoryCount()));
            td.setStaffName(staffIdStaffNameMap.get(d.getStaffId()));
            td.setRemarks(d.getRemarks());
            td.setGoodsPosition(d.getGoodsPosition());
            td.setCompletePositionName(result.get(d.getGoodsPosition()));
            list.add(td);
        }

        return list;
    }

    /**
     * 商品明细-库存
     */
    private void handleTaskGoodsDetailStock(TaskGoodsDetailResult result,ShopStaffEntity staff,InventoryTaskEntity task){
        result.setPreStock(BigDecimal.ZERO);
        result.setInventoryCount(BigDecimal.ZERO);
        result.setDiffCount(BigDecimal.ZERO);
        result.setDiffMoney(BigDecimal.ZERO);
        result.setGoodsInPrice(BigDecimal.ZERO);
        if (staff.getStaffPosition() == 3){ //店长
            if (task.getTaskStatus() == 1){ //待提交，查询当前商品的库存
                Map<String,Object> queryGoods = new HashMap<>();
                queryGoods.put("shopUnique",task.getShopUnique());
                queryGoods.put("goodsBarcode",result.getGoodsBarcode());
                GoodsEntity goodsEntity = goodsDao.queryOneByParam(queryGoods);

                //总盘点数
                List<TaskGoodsDetailGoodsList> goodsList = result.getGoodsList();
                BigDecimal inventoryCountTotal = BigDecimal.ZERO;
                for (TaskGoodsDetailGoodsList d : goodsList){
                    inventoryCountTotal = NumberUtil.add(inventoryCountTotal,d.getInventoryCount());
                }

                result.setPreStock(UtilForJAVA.formatBigDecimal(goodsEntity.getGoodsCount()));
                result.setInventoryCount(UtilForJAVA.formatBigDecimal(inventoryCountTotal));
                BigDecimal diffCount = NumberUtil.sub(inventoryCountTotal, goodsEntity.getGoodsCount());
                result.setDiffCount(UtilForJAVA.formatBigDecimal(diffCount));
                result.setDiffMoney(UtilForJAVA.formatBigDecimal(NumberUtil.mul(diffCount,goodsEntity.getGoodsInPrice())));
                result.setGoodsInPrice(UtilForJAVA.formatBigDecimal(goodsEntity.getGoodsInPrice()));
            }else if (task.getTaskStatus() == 2){ //已提交，查询盘点统计

                Map<String,Object> staMap = new HashMap<>();
                staMap.put("taskId",task.getId());
                staMap.put("goodsBarcode",result.getGoodsBarcode());
                List<InventoryTaskStatisticsEntity> list = statisticsDao.findByParam(staMap);
                InventoryTaskStatisticsEntity statistics = list.get(0);
                result.setPreStock(UtilForJAVA.formatBigDecimal(statistics.getPreStock()));
                result.setInventoryCount(UtilForJAVA.formatBigDecimal(statistics.getInventoryCount()));
                result.setDiffCount(UtilForJAVA.formatBigDecimal(statistics.getDiffCount()));
                result.setDiffMoney(UtilForJAVA.formatBigDecimal(statistics.getDiffMoney()));
                result.setGoodsInPrice(UtilForJAVA.formatBigDecimal(statistics.getGoodsInPrice()));
            }
        }
    }

    /**
     * 处理盘库记录列表
     * @param staff
     * @return
     */
    private List<InventoryGoodsRecordInventory> handleInventoryGoodsRecord(InventoryGoodsRecordParams req,ShopStaffEntity staff){
        Map<String,Object> queryMap = new HashMap<>();
        queryMap.put("shopUnique",req.getShopUnique());
        queryMap.put("goodsBarcode",req.getGoodsBarcode());
        queryMap.put("pageIndex",req.getPageIndex());
        queryMap.put("pageSize",req.getPageSize());

        if (staff.getStaffPosition() == 3){ //店长
            List<InventoryGoodsRecordInventory> list = statisticsDao.findInventoryGoodsRecordPage(queryMap);

            for (InventoryGoodsRecordInventory r : list){
                r.setPreStock(UtilForJAVA.formatBigDecimal(r.getPreStock()));
                r.setInventoryCount(UtilForJAVA.formatBigDecimal(r.getInventoryCount()));
                r.setDiffCount(UtilForJAVA.formatBigDecimal(r.getDiffCount()));
                r.setDiffMoney(UtilForJAVA.formatBigDecimal(r.getDiffMoney()));
                r.setGoodsInPrice(UtilForJAVA.formatBigDecimal(r.getGoodsInPrice()));
            }

            return list;
        }else { //店员
            queryMap.put("staffId",req.getStaffId());
            List<InventoryGoodsRecordInventory> list = taskDetailDao.findInventoryGoodsRecordPage(queryMap);
            for (InventoryGoodsRecordInventory r : list){
                r.setInventoryCount(UtilForJAVA.formatBigDecimal(r.getInventoryCount()));
                r.setPreStock(BigDecimal.ZERO);
                r.setDiffCount(BigDecimal.ZERO);
                r.setDiffMoney(BigDecimal.ZERO);
                r.setGoodsInPrice(BigDecimal.ZERO);
            }
            return list;
        }
    }

    /**
     * 待提交预览
     * @return
     */
    private List<TaskPreviewTaskDetailList> handleDianzhangPreview(InventoryTaskEntity task, List<InventoryTaskDetailEntity> detailList){
        List<TaskPreviewTaskDetailList> list = new ArrayList<>();

        List<String> goodsBarcodeList = new ArrayList<>();
        Map<String,InventoryTaskDetailEntity> baseDetailList = new HashMap<>();
        for (InventoryTaskDetailEntity d : detailList){
            goodsBarcodeList.add(d.getGoodsBarcode());
            baseDetailList.put(d.getGoodsBarcode(),d);
        }

        //查询所有的商品信息
        List<GoodsEntity> goodsList = new ArrayList<>();
        List<InventoryTaskStatisticsEntity> statList = new ArrayList<>();
        Map<String,Object> queryGoodsMap = new HashMap<>();
        queryGoodsMap.put("shopUnique",task.getShopUnique());
        queryGoodsMap.put("goodsBarcodeList",goodsBarcodeList);
        goodsList = goodsDao.queryGoodsByParam(queryGoodsMap);
        if (task.getTaskStatus() == 2) { //已盘点查询统计数据
            Map<String,Object> statMap = new HashMap<>();
            statMap.put("taskId",task.getId());
            statList = statisticsDao.findByParam(statMap);
        }
        //基类
        Map<String,GoodsEntity> baseGoods= new HashMap<>();
        //其他产品分类
        Map<String,GoodsEntity> otherGoods= new HashMap<>();
        if (CollectionUtil.isNotEmpty(goodsList)){
            for (GoodsEntity goodsEntity : goodsList) {
                //外键跟主键相同的时候就表示是基类
                if (ObjectUtil.equals(goodsEntity.getGoodsBarcode(),goodsEntity.getForeignKey().toString())) {
                    baseGoods.put(goodsEntity.getGoodsBarcode(),goodsEntity);
                }else {
                    otherGoods.put(goodsEntity.getGoodsBarcode(),goodsEntity);
                }
            }
        }
        //按照添加顺序排序
        for (InventoryTaskDetailEntity d : detailList){
            TaskPreviewTaskDetailList td = new TaskPreviewTaskDetailList();
            td.setGoodsName(d.getGoodsName());
            td.setGoodsBarcode(d.getGoodsBarcode());

            if (task.getTaskStatus() == 1) { //待提交
                GoodsEntity goodsEntity = handlePreviewGoodsStock(d.getGoodsId(), goodsList);
                td.setPreStock(ObjectUtil.isEmpty(goodsEntity) ? BigDecimal.ZERO : UtilForJAVA.formatBigDecimal(goodsEntity.getGoodsCount()));
                td.setGoodsInPrice(ObjectUtil.isEmpty(goodsEntity) ? BigDecimal.ZERO : UtilForJAVA.formatBigDecimal(goodsEntity.getGoodsInPrice()));
            }else if (task.getTaskStatus() == 2) {
                InventoryTaskStatisticsEntity statistics = handlePreviewGoodsStat(d.getGoodsBarcode(), statList);
                //提交订单的时候已经计算数据了，这里不需要再计算了
//                if(ObjectUtil.isNotEmpty(statistics)&& otherGoods.containsKey(statistics.getGoodsBarcode())) {
//                    //当前产品对象
//                    GoodsEntity goodsEntity1 = otherGoods.get(statistics.getGoodsBarcode());
//                    //包装换算
//                    BigDecimal goodsContain = goodsEntity1.getGoodsContain();
//                    //基类
//                    Long foreignKey = goodsEntity1.getForeignKey();
//                    GoodsEntity baseGood = baseGoods.get(foreignKey.toString());
//                    if (ObjectUtil.isNotEmpty(baseGood)){
//                        //基类的盘点信息
//                        InventoryTaskDetailEntity inventoryTaskDetailEntity = baseDetailList.get(d.getGoodsBarcode());
//                        //基类的盘点数量
//                        BigDecimal inventoryCount = inventoryTaskDetailEntity.getInventoryCount();
//                        //基类的盘点数量*包装换算
//                        BigDecimal mul = NumberUtil.div(inventoryCount, goodsContain);
//                        statistics.setPreStock(mul.setScale(0, RoundingMode.FLOOR));
//                    }
//
//                }
                td.setPreStock(ObjectUtil.isEmpty(statistics) ? BigDecimal.ZERO : UtilForJAVA.formatBigDecimal(statistics.getPreStock()));
                td.setGoodsInPrice(ObjectUtil.isEmpty(statistics) ? BigDecimal.ZERO : UtilForJAVA.formatBigDecimal(statistics.getGoodsInPrice()));
            }

            td.setInventoryCount(UtilForJAVA.formatBigDecimal(d.getInventoryCount()));
            BigDecimal diffCount = NumberUtil.sub(td.getInventoryCount(),td.getPreStock());
            td.setDiffCount(UtilForJAVA.formatBigDecimal(diffCount));
            td.setDiffMoney(UtilForJAVA.formatBigDecimal(NumberUtil.mul(diffCount,td.getGoodsInPrice())));

            list.add(td);
        }

        return list;
    }

    /**
     * 已提交预览
     * @return
     */
    private List<TaskPreviewTaskDetailList> handleYuangongPreview(List<InventoryTaskDetailEntity> detailList){
        List<TaskPreviewTaskDetailList> list = new ArrayList<>();

        //按照添加顺序排序
        Set<Integer> addSet = new HashSet<>();
        for (InventoryTaskDetailEntity d : detailList){
            if (addSet.contains(d.getGoodsId())) continue;
            TaskPreviewTaskDetailList td = new TaskPreviewTaskDetailList();
            td.setGoodsName(d.getGoodsName());
            td.setGoodsBarcode(d.getGoodsBarcode());
            td.setInventoryCount(UtilForJAVA.formatBigDecimal(d.getInventoryCount()));
            td.setPreStock(BigDecimal.ZERO);
            td.setGoodsInPrice(BigDecimal.ZERO);
            td.setDiffCount(BigDecimal.ZERO);
            td.setDiffMoney(BigDecimal.ZERO);

            addSet.add(d.getGoodsId());
            list.add(td);
        }

        return list;

    }

    /**
     * 处理查询的数据
     * @return
     */
    private List<InventoryTaskDetailEntity> handleRealDetailList(InventoryTaskEntity task,List<InventoryTaskDetailEntity> detailList){
        Map<Integer,BigDecimal> goodsIdInventoryCountTotal = new HashMap<>();
        List<Integer> goodsIdList = new ArrayList<>();
        for (InventoryTaskDetailEntity tde : detailList){
            goodsIdInventoryCountTotal.put(tde.getGoodsId(),tde.getInventoryCount());
            goodsIdList.add(tde.getGoodsId());
        }
        Map<String,Object> queryDetailMap = new HashMap<>();
        queryDetailMap.put("taskId",task.getId());
        queryDetailMap.put("goodsIdList",goodsIdList);
        List<InventoryTaskDetailEntity> detailEntityList = taskDetailDao.findDetailByGoodsIdList(queryDetailMap);
        List<InventoryTaskDetailEntity> realDetailList = new ArrayList<>();
        Set<Integer> goodsIdSet = new HashSet<>();
        for (InventoryTaskDetailEntity d : detailEntityList){
            if (goodsIdSet.contains(d.getGoodsId())) continue;
            realDetailList.add(d);
            d.setInventoryCount(goodsIdInventoryCountTotal.get(d.getGoodsId()));
            goodsIdSet.add(d.getGoodsId());
        }

        return realDetailList;
    }

}
