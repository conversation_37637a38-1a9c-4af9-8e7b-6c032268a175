package org.haier.shopUpdate.service;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import lombok.extern.slf4j.Slf4j;
import org.haier.shopUpdate.dao.CashDao;
import org.haier.shopUpdate.dao.ShopsStaffDao;
import org.haier.shopUpdate.entity.Binding;
import org.haier.shopUpdate.entity.ShopEntity;
import org.haier.shopUpdate.oss.OssConstant;
import org.haier.shopUpdate.redism.RedisCache;
import org.haier.shopUpdate.upload.FileUploadService;
import org.haier.shopUpdate.util.PurResult;
import org.haier.shopUpdate.util.PwdCheckUtil;
import org.haier.shopUpdate.util.ShopsResult;
import org.haier.shopUpdate.util.ShopsUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;


import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
@Slf4j
@Service
public class CashServiceImpl implements CashService{

	@Resource
	private CashDao cashDao;

	@Resource
	private ShopsStaffDao staffDao;
	@Autowired
	private RedisCache redis;
	@Resource
	private FileUploadService fileUploadService;

	@Override
	public ShopsResult queryPayMethod() {
		ShopsResult rs=new ShopsResult(1,"");
		List<Map<String,Object>> list= cashDao.queryPayMethod();
		rs.setData(list);
		return rs;
	}


	@Override
	public ShopsResult uploadFile(HttpServletRequest request, String shopUnique) {
		ShopsResult sr=new ShopsResult();
		MultipartFile file=ShopsUtil.testMulRequest(request, "user_file");
		Map<String,Object> map=new HashMap<>();
		try{
			if(null!=file){//图片信息处理
				String orName=file.getOriginalFilename();
				String lastName=orName.substring(orName.lastIndexOf("."));
				String ngoods=Math.round(Math.random()*100)+lastName;
				String catPath=request.getServletContext().getRealPath("");//项目所在绝对路径
				catPath=new File(catPath).getParent();
				String filePath=catPath+File.separator+"image"+File.separator+shopUnique;

				String filePathDetail= "/" + OssConstant.IMAGE_SOURCE+shopUnique;

				InputStream is = file.getInputStream();
				boolean flag=ShopsUtil.savePicture(file, filePath, ngoods);//图片保存本地
				ShopsUtil.targetZoomOut2(filePath+File.separator+ngoods, filePath+File.separator+ngoods, filePath, 500, 500,1.0f);
				is=new FileInputStream(new File(filePath+File.separator+ngoods));
				fileUploadService.uploadFileByPath(is, OssConstant.IMAGE_SOURCE + shopUnique + "/" + ngoods, file.getContentType());

				//中图
				String filePath2 = this.getClass().getClassLoader().getResource("../../").getPath();
				filePath2=filePath2.substring(0, filePath2.length()-request.getContextPath().length())+File.separator+"middle"+filePathDetail;
				ShopsUtil.targetZoomOut2(filePath+File.separator+ngoods, filePath2+File.separator+ngoods, filePath2, 120, 120,0.8f);
				is=new FileInputStream(new File(filePath2+File.separator+ngoods));
				fileUploadService.uploadFileByPath(is, OssConstant.IMAGE_MIDDLE + shopUnique + "/" + ngoods, file.getContentType());
				//小图
				String filePath3 = this.getClass().getClassLoader().getResource("../../").getPath();
				filePath3=filePath3.substring(0, filePath3.length()-request.getContextPath().length())+File.separator+"small"+filePathDetail;
				ShopsUtil.targetZoomOut2(filePath+File.separator+ngoods, filePath3+File.separator+ngoods, filePath3, 70, 70,0.5f);
				is=new FileInputStream(new File(filePath3+File.separator+ngoods));
				fileUploadService.uploadFileByPath(is, OssConstant.IMAGE_SMALL + shopUnique + "/" + ngoods, file.getContentType());
				if(flag){
					map.put("file_path", filePathDetail + "/" + ngoods);
				}
			}
			sr.setStatus(1);
			sr.setData(map);
		}catch(Exception e){
			log.error("异常",e);
			sr.setStatus(2);
			sr.setMsg("系统错误！");
		}
		return sr;
	}


	@Override
	public ShopsResult queryCarList(Map<String, Object> map) {
		ShopsResult result = new ShopsResult();
		try {
			List<Map<String,Object>> list = cashDao.queryCarList(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setData(list);
		} catch (Exception e) {
			log.error("异常",e);
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}


	@Override
	public ShopsResult addCar(Map<String, Object> map) {
		ShopsResult result = new ShopsResult();
		try {
			cashDao.addCar(map);
	    	result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			log.error("异常",e);
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}


	@Override
	public ShopsResult updateCar(Map<String, Object> map) {
		ShopsResult result = new ShopsResult();
		try {
			cashDao.updateCar(map);
	    	result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			log.error("异常",e);
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}


	@Override
	public ShopsResult deleteCar(Map<String, Object> map) {
		ShopsResult result = new ShopsResult();
		try {
			cashDao.deleteCar(map);
	    	result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			log.error("异常",e);
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}


	@Override
	public ShopsResult queryRechargeConfigList(Map<String, Object> params) {
		ShopsResult result=new ShopsResult();
		List<Map<String,Object>> configList=cashDao.queryRechargeConfig(params);
		result.setStatus(1);
		result.setMsg("查询成功！");
		result.setData(configList);
		return result;
	}


	@Override
	public ShopsResult InsertRechargeConfig(Map<String, Object> params) {
		ShopsResult result=new ShopsResult();
		params.put("create_time", new Date());
		params.put("flag", "1");
		//查询是否有相同时间段金额
		Map<String,Object> data= cashDao.queryRechargeConfigByMoneyAndTime(params);
		if(data!=null){
			result.setStatus(0);
			result.setMsg("相同时间段内存在相同金额的充值优惠活动!");
			return result;
		}
		cashDao.insertCustomer_recharge_config(params);
		result.setStatus(1);
		result.setMsg("保存成功");
		return result;
	}

	@Override
	@Transactional
	public ShopsResult updateRechargeConfig(Map<String, Object> params) {
		ShopsResult result=new ShopsResult();
		params.put("create_time", new Date());
		Map<String,Object> data= cashDao.queryRechargeConfigByMoneyAndTime(params);
		if(data!=null){
			result.setStatus(0);
			result.setMsg("不能重复设置!");
			return result;
		}
		cashDao.updateCustomer_recharge_config(params);
		result.setStatus(1);
		result.setMsg("保存成功");
		return result;
	}


	@Override
	public ShopsResult queyRechargeLog(Map<String, Object> params) {
		ShopsResult result=new ShopsResult();
		List<Map<String,Object>> configList=cashDao.queyRechargeLog(params);
		result.setStatus(1);
		result.setMsg("查询成功！");
		result.setData(configList);
		return result;
	}


	@Override
	public ShopsResult getMemberLevel(Map<String, Object> map) {
		ShopsResult result = new ShopsResult();
		try {
			List<Map<String,Object>> list = cashDao.getMemberLevel(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setData(list);
		} catch (Exception e) {
			log.error("异常",e);
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}


	@Override
	public ShopsResult setMemberLevel(Map<String, Object> map) {
		ShopsResult result = new ShopsResult();
		try {
			cashDao.setMemberLevel(map);
	    	result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			log.error("异常",e);
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}


	@Override
	public ShopsResult queryCusCheckOut(Map<String, Object> map) {
		ShopsResult result = new ShopsResult();
		try {
			List<Map<String,Object>> list = cashDao.queryCusCheckOut(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setData(list);
		} catch (Exception e) {
			log.error("异常",e);
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}


	@Override
	public ShopsResult queryPointUseList(Map<String, Object> map) {
		ShopsResult result = new ShopsResult();
		try {
			List<Map<String,Object>> list = cashDao.queryPointUseList(map);
			//查询总积分总数量
			Map<String,Object> count = cashDao.queryPointUseCount(map);

	    	result.setStatus(1);
			result.setMsg("成功");
			result.setData(list);
			result.setRedundant(count);
		} catch (Exception e) {
			log.error("异常",e);
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}


	@Override
	public ShopsResult queryOrderListByPage2(Map<String, Object> params) {
		ShopsResult result = new ShopsResult();
		try {
			List<Map<String,Object>> list = cashDao.queryOrderListByPage2(params);
			int count = cashDao.queryOrderListByPageCount(params);
			Map<String ,Object> map = cashDao.queryOrderListByPageCount2(params);
			Map<String ,Object> beans= cashDao.queryShopBeans(params);
			beans.putAll(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setData(list);
			result.setRedundant(beans);
			result.setCount(count);
		} catch (Exception e) {
			log.error("查询失败,异常信息:",e);
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}


	@Override
	public ShopsResult queryTransactionList2(Map<String, Object> params) {
		ShopsResult result = new ShopsResult();
		try {
			List<Map<String,Object>> list = cashDao.queryTransactionListPage2(params);
			int count = cashDao.queryTransactionListPageCount(params);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setData(list);
			result.setCount(count);
		} catch (Exception e) {
			log.error("查询失败,异常信息:",e);
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}


	@Override
	public ShopsResult queryShopBeansPromation(Map<String, Object> params) {
		ShopsResult result = new ShopsResult();
		try {

			SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	        Calendar c = Calendar.getInstance();

	        //过去七天
	        c.setTime(new Date());
	        c.add(Calendar.DATE, - 7);
	        Date d = c.getTime();
	        String startTime = format.format(d);
	        params.put("startTime", startTime);
			List<Map<String,Object>> list = cashDao.queryShopBeansPromation(params);
			int count = cashDao.queryShopBeansPromationCount(params);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setData(list);
			result.setCount(count);
		} catch (Exception e) {
			log.error("查询失败,异常信息:",e);
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}


	@Override
	public ShopsResult queryPcActivityMenuList(Map<String, Object> params) {
		ShopsResult result = new ShopsResult();
		try {
			List<Map<String,Object>> list = cashDao.queryPcActivityMenuList(params);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setData(list);
		} catch (Exception e) {
			log.error("查询失败,异常信息:",e);
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}


	@Override
	public ShopsResult queryGoldByShop2(Map<String, Object> params) {
		ShopsResult result = new ShopsResult();
		try {
			Map<String,Object> list = cashDao.queryGoldByShop2(params);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setData(list);
		} catch (Exception e) {
			log.error("查询失败,异常信息:",e);
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}


	@Override
	public ShopsResult saveBindingJiGuang(Map<String, Object> params) {
		ShopsResult result = new ShopsResult();
		try {
			cashDao.saveBindingJiGuang(params);
	    	result.setStatus(1);
			result.setMsg("成功");
		} catch (Exception e) {
			log.error("查询失败,异常信息:",e);
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}


	@Override
	public ShopsResult updateShopsPwd(Map<String, Object> params) {
		ShopsResult result = new ShopsResult();
		try {
			if(!params.containsKey("newStaffPwd"))
			{
				result.setStatus(0);
				result.setMsg("参数异常");
			}else
			{
				int numD=PwdCheckUtil.checkPassward(params.get("newStaffPwd").toString());
				if(numD!=0)
				{
					result.setStatus(0);
					result.setMsg("密码为8-16位大小写字母、数字或英文特殊符号组合");
				}else
				{
					params.put("pwdOk",params.get("newStaffPwd"));
					params.put("staffPwd", ShopsUtil.string2MD5(params.get("newStaffPwd").toString()));
					staffDao.updateShopsStaffMessage(params);
			    	result.setStatus(1);
					result.setMsg("修改成功");
				}

			}

		} catch (Exception e) {
			log.error("查询失败,异常信息:",e);
			result.setStatus(0);
			result.setMsg("系统异常，请稍后重试");
		}
		return result;
	}


	@Override
	public ShopsResult queryBankName() {
		ShopsResult result = new ShopsResult();
		try {
			List<Map<String,Object>> list = cashDao.queryBankName();
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setData(list);
		} catch (Exception e) {
			log.error("查询失败,异常信息:",e);
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}


	@Override
	public ShopsResult queryDownload() {
		ShopsResult result = new ShopsResult();
		try {
			Map<String,Object> list = cashDao.queryDownload();
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setData(list);
		} catch (Exception e) {
			log.error("查询失败,异常信息:",e);
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}


	@Override
	public ShopsResult queryShopAppDownload() {
		ShopsResult result = new ShopsResult();
		try {
			Map<String,Object> map = new HashMap<>();
			map.put("url", "");
	    	result.setStatus(1);
			result.setData(map);
		} catch (Exception e) {
			log.error("查询失败,异常信息:",e);
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}


	@Override
	public ShopsResult queryFuncitonImage(Map<String, Object> params) {
		ShopsResult result = new ShopsResult();
		try {
			List<Map<String,Object>> list=cashDao.queryFuncitonImage(params);
	    	result.setStatus(1);
			result.setData(list);
		} catch (Exception e) {
			log.error("查询失败,异常信息:",e);
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}


	@Override
	public PurResult queryShelfStateGoodsMessage(Map<String, Object> params) {
		PurResult result=new PurResult();
		List<Map<String,Object>> goodsList=cashDao.queryShelfStateGoodsMessage(params);
		result.setStatus(1);
		result.setMsg("查询成功！");
		result.setData(goodsList);
		return result;
	}


	@Override
	public ShopsResult updateShelfState(String goods_ids, String shelf_state) {
		ShopsResult result = new ShopsResult();
		try {
			List<String> goods_id_list = Arrays.asList(goods_ids.split(","));
			Map<String ,Object> params = new HashMap<String, Object>();
			params.put("goods_ids", goods_id_list);
			params.put("shelf_state", shelf_state);
			Integer count = cashDao.updateShelfState(params);
			if(count >0 ){
				result.setStatus(0);
				result.setMsg("成功！");
			}else{
				result.setStatus(1);
				result.setMsg("异常");
			}

		} catch (Exception e) {
			log.error("查询失败,异常信息:",e);
			result.setStatus(1);
			result.setMsg("异常");
		}
		return result;
	}


	@Override
	public Map<String, Object> queryShopDelivery(String shop_unique) {
		return cashDao.queryShopDelivery(shop_unique);
	}


	@Override
	public ShopsResult updateShopDelivery(Map<String, Object> params) {
		ShopsResult shopsResult = new ShopsResult();
		int count = cashDao.updateShopDelivery(params);
		if(count > 0){
			shopsResult.setStatus(1);
			shopsResult.setMsg("修改成功！");
		}
		return shopsResult;
	}


	@Override
	public PurResult getShopCourierList(Map<String, Object> map) {
		PurResult result = new PurResult();
		System.out.println(map.toString());
		List<Map<String, Object>> list = cashDao.getShopCourierList(map);

		result.setStatus(1);
		result.setMsg("成功");
		result.setData(list);
		return result;
	}


	@Override
	public ShopsResult addShopCourier(Map<String, Object> map) {
		ShopsResult shopsResult = new ShopsResult();
		int count = cashDao.addShopCourier(map);
		if(count > 0){
			shopsResult.setStatus(1);
			shopsResult.setMsg("添加成功！");
		}

		return shopsResult;
	}


	@Override
	public ShopsResult updateShopCourier(Map<String, Object> map) {
		ShopsResult shopsResult = new ShopsResult();
		int count = cashDao.updateShopCourier(map);

		if(count > 0){
			shopsResult.setStatus(1);
			shopsResult.setMsg("修改成功！");
		}

		return shopsResult;
	}


	@Override
	public ShopsResult deleteShopCourier(String courier_id) {
		ShopsResult shopsResult = new ShopsResult();
		int count = cashDao.deleteShopCourier(courier_id);

		if(count > 0){
			shopsResult.setStatus(1);
			shopsResult.setMsg("删除成功！");
		}

		return shopsResult;
	}


	@Override
	public PurResult queryShopCouponList(String shop_unique, String start_time, String end_time, int pageNum,
			int pageSize,String coupon_name,String type) {
		PurResult result=new PurResult();
		try {
			Map<String,Object> map=new HashMap<String, Object>();
			map.put("shop_unique", shop_unique);
			if(coupon_name!=null&&!"".equals(coupon_name)){
				map.put("coupon_name", "%"+coupon_name+"%");
			}
			if(start_time != null && !start_time.equals("")){
				map.put("start_time", start_time);
			}
			if(end_time != null && !end_time.equals("")){
				map.put("end_time", end_time);
			}
			if(type != null && !type.equals("")){
				map.put("type", type);
			}
			map.put("pageSize", pageSize);
			map.put("startNum", (pageNum-1)*pageSize);
			List<Map<String,Object>> orderList=cashDao.queryShopCouponList(map);
			/*for(int i=0;i<orderList.size();i++){
				List<Map<String,Object>> timeList = activityDao.getShopCouponTimes(orderList.get(i).get("shop_coupon_id").toString());
				orderList.get(i).put("timeList", timeList);
			}*/
			//查询总数量
			int count=cashDao.queryShopCouponListCount(map);
			result.setCount(count);
			result.setStatus(1);
			result.setMsg("查询成功！");
			result.setData(orderList);
		} catch (Exception e) {
			log.error("查询失败,异常信息:",e);
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}


	@Override
	@Transactional
	public PurResult addShopCoupon(String shop_unique, String start_time, String end_time, String meet_amount,
			String coupon_amount, String type, Integer is_time, Integer is_daily, Integer daily_num, String times,
			Integer is_auto_grant, Integer is_grant_num, Integer grant_num, Integer exclusive_type, String days,
			Integer is_online, String coupon_name) {
		PurResult pr = new PurResult();

		try {
			Map<String,Object> params = new HashMap<String, Object>();

			params.put("shop_unique", shop_unique);
			params.put("startDate", start_time);
			params.put("endDate", end_time);
			params.put("meet_amount", meet_amount);
			params.put("coupon_amount", coupon_amount);
			params.put("type", type);
			params.put("give_status", 0);
			params.put("is_time", is_time);
			params.put("is_daily", is_daily);
			params.put("daily_num", daily_num);
			params.put("is_auto_grant", is_auto_grant);
			params.put("is_online", is_online);
			params.put("coupon_name", coupon_name);

			/**
			 * 防重复提交
			 * 1.查询缓存是否存在
			 * 2.加入缓存 防止重复添加
			 */
			Object o=redis.getObject(params.toString());
			if(o!=null)
			{
				pr.setStatus(0);
				pr.setMsg("重复添加！");
				return pr;
			}else
			{
				redis.putObject(params.toString(), params,60);
			}

			if(cashDao.queryShopCouponCount(params)>0)
			{
				pr.setStatus(0);
				pr.setMsg("重复添加！");
				return pr;
			}


			if(is_grant_num != null && is_grant_num == -1) {//不限制发放次数
				params.put("grant_num", "-1");
			}else {
				params.put("grant_num", grant_num);
				params.put("surplus_grant_num", grant_num);
			}
			params.put("exclusive_type", exclusive_type);
			if(Double.parseDouble(coupon_amount)>Double.parseDouble(meet_amount)){
				pr.setStatus(0);
				pr.setMsg("优惠金额不能大于满足金额");
				return pr;
			}

			cashDao.addShopCoupon(params);

			//自动发放，如果限制数量，需要限制人员数量
			if(is_auto_grant == 2) {
				cashDao.autoGrantShopCoupon(params);
				//自动发放，需要扣除剩余数量
				cashDao.deductAutoGrantCount(params);
			}


			if(is_time == 2 && times != null && !times.equals("")){//添加优惠券时段信息
				String[] timesStr = times.split(";");
				for(int i=0;i<timesStr.length;i++){
					String[] time = timesStr[i].split(" - ");
					Map<String ,Object> timeParams = new HashMap<String, Object>();
					timeParams.put("shop_coupon_id", params.get("shop_coupon_id"));
					timeParams.put("start_time", time[0]);
					timeParams.put("end_time", time[1]);

					cashDao.addShopCouponTime(timeParams);
				}
			}
			//
			if(null != days && !days.equals("")) {
				List<Map<String,Object>> dayList = new ArrayList<Map<String,Object>>();
				String[] daysArr = days.split(",");
				//将所有开始周期到结束周期内的满足条件的日期添加到数组中
				SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
				Date d1 = formatter.parse(start_time);
				Date d2 = formatter.parse(end_time);


				Calendar cs = Calendar.getInstance();
				Calendar ce = Calendar.getInstance();

				cs.setTime(d1);
				ce.setTime(d2);

				while (cs.compareTo(ce) <= 0) {
					Map<String,Object> map = new HashMap<String,Object>();
					Integer dayWeek = cs.get(Calendar.DAY_OF_WEEK);
					for(String s : daysArr) {
						if(Integer.parseInt(s) == dayWeek) {
							System.out.println(cs);
							System.out.println(cs.get(Calendar.MONTH));
							map.put("effective_day", cs.get(Calendar.YEAR) + "-" + (cs.get(Calendar.MONTH ) + 1) + "-" + cs.get(Calendar.DAY_OF_MONTH));
							map.put("shop_coupon_id", params.get("shop_coupon_id"));
							map.put("day_week", s);
							dayList.add(map);
							break;
						}
					}
					cs.add(Calendar.DATE, 1);
				}

				//将满足条件日期添加到对应优惠券信息中
				System.out.println(dayList);
				cashDao.addShopCouponEffective(dayList);
			}


		} catch (Exception e) {
			log.error("异常",e);
			pr.setStatus(0);
			pr.setMsg("异常");
		}
		pr.setStatus(1);
		pr.setMsg("成功");
		return pr;
	}


	@Override
	public PurResult deleteShopCoupon(String shop_coupon_id, int delete_status) {
		PurResult pr=new PurResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_coupon_id", shop_coupon_id);
		map.put("delete_status", delete_status);
		cashDao.deleteShopCoupon(map);
		pr.setStatus(1);
		pr.setMsg("成功");
		return pr;
	}


	@Override
	public PurResult queryCouponRecord(Map<String, Object> map) {
		PurResult pr = new PurResult();

		try {
			List<Map<String,Object>> data = cashDao.queryCouponRecordList(map);

			Integer count = cashDao.queryCouponRecordCount(map);
			pr.setTotal(count);

			pr.setStatus(1);
			pr.setMsg("查询成功！");
			pr.setData(data);
		} catch (Exception e) {
			log.error("异常",e);
			pr.setStatus(0);
			pr.setMsg("异常");
		}
		return pr;
	}


	@Override
	public PurResult queryPromotionList(Map<String, Object> map) {
		PurResult pr = new PurResult();
		try {
			List<Map<String,Object>> data = cashDao.queryPromotionList(map);

			pr.setStatus(1);
			pr.setMsg("查询成功！");
			pr.setData(data);
		} catch (Exception e) {
			log.error("查询异常",e);
			pr.setStatus(0);
			pr.setMsg("异常");
		}
		return pr;
	}

	@Override
	@Transactional
	public PurResult submitSupplierStorageOrder(String shop_unique, String promotion_activity_name, String startDate,
			String endDate, String order_activity, String detailJson,String cus_activity) {
			PurResult pr=new PurResult();
			JSONArray array= JSONArray.fromObject(detailJson);

			for (int i = 0; i < array.size(); i++) {
				JSONObject temp = (JSONObject) array.get(i);
				String goods_id= temp.getString("goods_id");
				String goods_name = temp.getString("goods_name");
				//判断当前商品是否有时间重合活动
				pr = checkSameTimeActive(shop_unique, goods_id, startDate, endDate);

				if(pr.getStatus() != 1) {
					if(pr.getStatus() == 2) {
						pr.setStatus(0);
						return pr;
					}
					pr.setMsg( goods_name + pr.getMsg());
					return pr;
				}
			}



			Map<String,Object> map=new HashMap<String, Object>();
			map.put("shop_unique", shop_unique);
			map.put("promotion_activity_name", promotion_activity_name);
			map.put("startDate", startDate);
			map.put("endDate", endDate);
			map.put("order_activity", order_activity);
			map.put("type", 1);
			map.put("promotion_activity_id", 0);
			map.put("cus_activity", cus_activity);
			//添加订单
			int k=cashDao.submitSupplierStorageOrder(map);
			Integer promotion_activity_id= (Integer) map.get("promotion_activity_id");
			List<Map<String ,Object>> map3List = new ArrayList<Map<String ,Object>>();
			for (int i = 0; i < array.size(); i++) {
				JSONObject temp = (JSONObject) array.get(i);
				String goods_id= temp.getString("goods_id");
				String meet_count1= temp.getString("meet_count1");
				String discount_percent1= temp.getString("discount_percent1");
				String meet_count2= temp.getString("meet_count2");
				String discount_percent2= temp.getString("discount_percent2");
				String meet_count3= temp.getString("meet_count3");
				String discount_percent3= temp.getString("discount_percent3");
				String limit_count= temp.getString("limit_count");
				Map<String,Object> map3=new HashMap<String, Object>();
				map3.put("goods_id", goods_id);
				map3.put("shop_unique", shop_unique);
				map3.put("meet_count1", meet_count1);
				map3.put("discount_percent1", discount_percent1);
				map3.put("meet_count2", meet_count2);
				map3.put("discount_percent2", discount_percent2);
				map3.put("meet_count3", meet_count3);
				map3.put("discount_percent3", discount_percent3);
				map3.put("limit_count", limit_count);
				map3.put("promotion_activity_id", promotion_activity_id);
				map3List.add(map3);
			}
			cashDao.addPromotionGoodsMarkdown(map3List);
			pr.setStatus(1);
			pr.setMsg("提交成功");
			return pr;
	}

	/**
	 * 1、判断商品在同一时间段内，是否有其他相同的活动信息
	 * 2、包含以下活动
	 * 2.1、单品折扣：满5件，5折
	 * 2.2、单品满增：满5
	 * @param shopUnique 店铺编号
	 * @param goodsId 商品ID
	 * @param startTime 活动开始时间
	 * @param endTime 活动结束时间
	 * @return
	 */
	public PurResult checkSameTimeActive(String shopUnique,String goodsId,String startTime,String endTime) {
		PurResult pr = new PurResult(1, "没有重复的优惠活动");
		if(null == shopUnique || null == goodsId) {
			pr.setStatus(2);
			pr.setMsg("请选择商品信息和店铺信息");
			return pr;
		}
		if(null == startTime || endTime == null) {
			pr.setStatus(2);
			pr.setMsg("请选择活动时间");
			return pr;
		}

		/*
		 * 1、校验单品促销活动（满M件，优惠N折） 对应的表：promotion_activity promotion_goods_markdown
		 * promotion_activity、type：1；
		 * 2、校验单品满增活动（满M件，赠送O品） 对应的表：promotion_activity promotion_goods_gift
		 * promotion_activity、type：2；
		 * 4、校验单品折扣活动（第M件，优惠N折）对应的表：promotion_activity promotion_goods_single
		 * promotion_activity、type：4；
		 */

		Map<String,Object> map = new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		map.put("goodsId", goodsId);
		map.put("startTime", startTime);
		map.put("endTime", endTime);

		//校验是否有对应的同时间端内的单品促销活动
		map.put("type", 1);
		Integer count = cashDao.checkGoodsActiveRepert(map);
		if(null != count && count > 0) {
			pr.setStatus(0);
			pr.setMsg("与现有的商品折扣活动重复!");
			return pr;
		}

		//校验是否有对应的同时间段内的商品满赠活动
		map.put("type", 2);
		count = cashDao.checkGoodsActiveRepert(map);
		if(null != count && count > 0) {
			pr.setStatus(0);
			pr.setMsg("与现有的商品满赠活动重复!");
			return pr;
		}
		//校验是否有对应的同时间段内的单品促销活动
		map.put("type", 4);
		count = cashDao.checkGoodsActiveRepert(map);
		if(null != count && count > 0) {
			pr.setStatus(0);
			pr.setMsg("与现有的商品促销活动重复!");
			return pr;
		}

		return pr;
	}

	@Override
	public PurResult updateActivityStatus(Map<String, Object> map) {
		PurResult pr = new PurResult();
		cashDao.updateActivityStatus(map);
		pr.setStatus(1);
		return pr;
	}


	@Override
	public PurResult queryGoodsMarkdownDetail(Map<String, Object> map) {
		PurResult pr=new PurResult();
		Map<String, Object> activity=cashDao.queryGoodsMarkdownDetail(map);
		//查询商品
		List<Map<String, Object>> goods_list=cashDao.queryGoodsMarkdownListDetail(map);
		activity.put("goods_list", goods_list);
		pr.setStatus(1);
		pr.setData(activity);
		return pr;
	}


	@Override
	@Transactional
	public PurResult deleteActivity(Map<String, Object> map) {
		PurResult pr=new PurResult();
		cashDao.deleteActivity(map);
		cashDao.deleteGoodsMarkdown(map);
		cashDao.deleteGoodsGift(map);
		cashDao.deleteOrderMarkdown(map);
		cashDao.deleteSingleGoodsMarkdown(map);
		pr.setStatus(1);
		return pr;
	}


	@Override
	@Transactional
	public PurResult submitGoodsGift(String shop_unique, String promotion_activity_name, String startDate,
			String endDate, String order_activity, String detailJson,String cus_activity) {
			PurResult pr=new PurResult();
			JSONArray array= JSONArray.fromObject(detailJson);

			for (int i = 0; i < array.size(); i++) {
				JSONObject temp = (JSONObject) array.get(i);
				String goods_id= temp.getString("goods_id");
				String goods_name= temp.getString("goods_name");
				//判断当前商品是否有时间重合活动
				pr = checkSameTimeActive(shop_unique, goods_id, startDate, endDate);

				if(pr.getStatus() != 1) {
					if(pr.getStatus() == 2) {
						pr.setStatus(0);
						return pr;
					}
					pr.setMsg( goods_name + pr.getMsg());
					return pr;
				}
			}


			Map<String,Object> map=new HashMap<String, Object>();
			map.put("shop_unique", shop_unique);
			map.put("promotion_activity_name", promotion_activity_name);
			map.put("startDate", startDate);
			map.put("endDate", endDate);
			map.put("order_activity", order_activity);
			map.put("type", 2);
			map.put("promotion_activity_id", 0);
			map.put("cus_activity", cus_activity);
			//添加订单
			int k=cashDao.submitSupplierStorageOrder(map);
			Integer promotion_activity_id= (Integer) map.get("promotion_activity_id");
			List<Map<String ,Object>> map3List = new ArrayList<Map<String ,Object>>();
			for (int i = 0; i < array.size(); i++) {
				JSONObject temp = array.getJSONObject(i);

				if(!temp.containsKey("meet_count1")) {
					pr.setStatus(0);
					pr.setMsg(temp.getString("goods_name") + "未设置促销规则");
					return pr;
				}

				String goods_id= temp.getString("goods_id");
				String meet_count1= temp.getString("meet_count1");
				String gift_count1= temp.getString("gift_count1");
				String meet_count2= temp.getString("meet_count2");
				String gift_count2= temp.getString("gift_count2");
				String meet_count3= temp.getString("meet_count3");
				String gift_count3= temp.getString("gift_count3");
				System.out.println(temp);
				System.out.println(temp.containsKey("goods_id_gift"));
				String goods_id_gift = temp.containsKey("goods_id_gift") ? temp.getString("goods_id_gift") : null;
				String goods_id_gift1= temp.getString("goods_id_gift1");
				String goods_id_gift2= temp.getString("goods_id_gift2");
				String goods_id_gift3= temp.getString("goods_id_gift3");
				Map<String,Object> map3=new HashMap<String, Object>();
				map3.put("goods_id", goods_id);
				map3.put("shop_unique", shop_unique);
				map3.put("meet_count1", meet_count1);
				map3.put("gift_count1", gift_count1);
				map3.put("meet_count2", meet_count2);
				map3.put("gift_count2", gift_count2);
				map3.put("meet_count3", meet_count3);
				map3.put("gift_count3", gift_count3);
				map3.put("goods_id_gift", goods_id_gift);
				map3.put("goods_id_gift1", goods_id_gift1);
				map3.put("goods_id_gift2", goods_id_gift2);
				map3.put("goods_id_gift3", goods_id_gift3);
				map3.put("promotion_activity_id", promotion_activity_id);
				map3List.add(map3);
			}
			cashDao.addGoodsGift(map3List);
			pr.setStatus(1);
			pr.setMsg("提交成功");
			return pr;
	}


	@Override
	public PurResult queryGoodsGiftDetail(Map<String, Object> map) {
		PurResult pr=new PurResult();
		Map<String, Object> activity=cashDao.queryGoodsMarkdownDetail(map);
		//查询商品
		List<Map<String, Object>> goods_list=cashDao.queryGoodsGiftDetail(map);
		activity.put("goods_list", goods_list);
		pr.setStatus(1);
		pr.setData(activity);
		return pr;

	}


	@Override
	@Transactional
	public PurResult submitSingleGoodsPromotion(String shop_unique, String promotion_activity_name, String startDate,
			String endDate, String detailJson,String activity_range,String order_activity,String cus_activity) {
		PurResult pr=new PurResult();
		JSONArray array= JSONArray.fromObject(detailJson);

		for (int i = 0; i < array.size(); i++) {
			JSONObject temp = (JSONObject) array.get(i);
			String goods_id= temp.getString("goods_id");
			String goods_name = temp.getString("goods_name");
			//判断当前商品是否有时间重合活动

			pr = checkSameTimeActive(shop_unique, goods_id, startDate, endDate);

			if(pr.getStatus() != 1) {
				if(pr.getStatus() == 2) {
					pr.setStatus(0);
					return pr;
				}
				pr.setMsg( goods_name + pr.getMsg());
				return pr;
			}
		}

		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("promotion_activity_name", promotion_activity_name);
		map.put("startDate", startDate);
		map.put("endDate", endDate);
		map.put("type", 4);
		map.put("promotion_activity_id", 0);
		map.put("activity_range", activity_range);
		map.put("order_activity", order_activity);
		map.put("cus_activity", cus_activity);
		//添加订单
		int k=cashDao.submitSupplierStorageOrder(map);
		Integer promotion_activity_id= (Integer) map.get("promotion_activity_id");
		List<Map<String ,Object>> map3List = new ArrayList<Map<String ,Object>>();
		for (int i = 0; i < array.size(); i++) {
			JSONObject temp = (JSONObject) array.get(i);
//			String goods_id= temp.getString("goods_id");
//			String promotion_price= temp.getString("promotion_price");
//			Map<String,Object> map3=new HashMap<String, Object>();
//			map3.put("goods_id", goods_id);
//			map3.put("promotion_price", promotion_price);
//			map3.put("shop_unique", shop_unique);
//			map3.put("promotion_activity_id", promotion_activity_id);
//			activityDao.submitSingleGoodsPromotion(map3);
			String goods_id= temp.getString("goods_id");
			String number1= temp.getString("number1");
			String discount_percent1= temp.getString("discount_percent1");
			String number2= temp.getString("number2");
			String discount_percent2= temp.getString("discount_percent2");
			String number3= temp.getString("number3");
			String discount_percent3= temp.getString("discount_percent3");
			Map<String,Object> map3=new HashMap<String, Object>();
			map3.put("shop_unique", shop_unique);
			map3.put("goods_id", goods_id);
			map3.put("promotion_activity_id", promotion_activity_id);
			map3.put("number1", number1);
			map3.put("discount_percent1", discount_percent1);
			map3.put("number2", number2);
			map3.put("discount_percent2", discount_percent2);
			map3.put("number3", number3);
			map3.put("discount_percent3", discount_percent3);
			map3List.add(map3);
		}
		cashDao.addPromotionGoodsSingle(map3List);
		pr.setStatus(1);
		pr.setMsg("提交成功");
		return pr;
	}


	@Override
	public PurResult querySingleGoodsPromotionDetail(Map<String, Object> map) {
		PurResult pr=new PurResult();
		Map<String, Object> activity=cashDao.queryGoodsMarkdownDetail(map);
		//查询商品
		List<Map<String, Object>> goods_list=cashDao.querySingleGoodsPromotionDetail(map);
		activity.put("goods_list", goods_list);
		pr.setStatus(1);
		pr.setData(activity);
		return pr;
	}


	@Override
	@Transactional
	public PurResult addOrderMarkdown(String shop_unique, String promotion_activity_name, String startDate,
			String endDate, String meet_price1, String discount_price1, String meet_price2, String discount_price2,
			String meet_price3, String discount_price3,String goods_id1, String goods_id2, String goods_id3, String gift_count1, String gift_count2, String gift_count3) {
		PurResult pr=new PurResult();

		Map<String ,Object> params = new HashMap<String, Object>();
		params.put("shop_unique", shop_unique);
		params.put("startDate", startDate);
		params.put("endDate", endDate);

		//需要防止有商品，没有数量
		if((null != goods_id1 && !goods_id1.trim().equals("") && (gift_count1 == null || gift_count1.trim().equals(""))) ||
				(null != goods_id2 && !goods_id2.trim().equals("") && (gift_count2 == null || gift_count2.trim().equals(""))) ||
				(null != goods_id3 && !goods_id3.trim().equals("") && (gift_count3 == null || gift_count3.trim().equals("")))) {
			pr.setStatus(0);
			pr.setMsg("请选择赠送商品的数量");
			return pr;
		}

		if(meet_price1 != null && !meet_price1.equals("")){
			params.put("meet_price", meet_price1);
			Integer count = cashDao.getTimeCoincideOrderMarkdown(params);
			if(count > 0) {
				pr.setData(meet_price1);
				pr.setStatus(0);
				pr.setMsg("有时间重合");
				return pr;
			}
		}
		if(meet_price2 != null && !meet_price2.equals("")){
			params.put("meet_price", meet_price2);
			Integer count = cashDao.getTimeCoincideOrderMarkdown(params);
			if(count > 0) {
				pr.setData(meet_price2);
				pr.setStatus(0);
				pr.setMsg("有时间重合");
				return pr;
			}
		}
		if(meet_price3 != null && !meet_price3.equals("")){
			params.put("meet_price", meet_price3);
			Integer count = cashDao.getTimeCoincideOrderMarkdown(params);
			if(count > 0) {
				pr.setData(meet_price3);
				pr.setStatus(0);
				pr.setMsg("有时间重合");
				return pr;
			}
		}
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("promotion_activity_name", promotion_activity_name);
		map.put("startDate", startDate);
		map.put("endDate", endDate);
		map.put("type", 3);
		map.put("promotion_activity_id", 0);
		//添加订单
		int k=cashDao.submitSupplierStorageOrder(map);
		Integer promotion_activity_id= (Integer) map.get("promotion_activity_id");
		Map<String,Object> map3=new HashMap<String, Object>();
		map3.put("shop_unique", shop_unique);
		if(!"".equals(meet_price1)){
			map3.put("meet_price1", meet_price1);
		}
		if(!"".equals(discount_price1)){
			map3.put("discount_price1", discount_price1);
		}
		if(!"".equals(meet_price2)){
			map3.put("meet_price2", meet_price2);
		}
		if(!"".equals(discount_price2)){
			map3.put("discount_price2", discount_price2);
		}
		if(!"".equals(meet_price3)){
			map3.put("meet_price3", meet_price3);
		}
		if(!"".equals(discount_price3)){
			map3.put("discount_price3", discount_price3);
		}
		if(!"".equals(goods_id1)){
			map3.put("goods_id1", goods_id1);
		}
		if(!"".equals(goods_id2)){
			map3.put("goods_id2", goods_id2);
		}
		if(!"".equals(goods_id3)){
			map3.put("goods_id3", goods_id3);
		}
		if(!"".equals(gift_count1)){
			map3.put("gift_count1", gift_count1);
		}
		if(!"".equals(gift_count2)){
			map3.put("gift_count2", gift_count2);
		}
		if(!"".equals(gift_count3)){
			map3.put("gift_count3", gift_count3);
		}
		map3.put("promotion_activity_id", promotion_activity_id);
		cashDao.addOrderMarkdown(map3);
		pr.setStatus(1);
		pr.setMsg("提交成功");
		return pr;
	}


	@Override
	public PurResult queryOrderMarkdownDetail(Map<String, Object> map) {
		PurResult pr=new PurResult();
		Map<String, Object> activity=cashDao.queryOrderMarkdownDetail(map);
		pr.setStatus(1);
		pr.setData(activity);
		return pr;
	}


	@Override
	public PurResult queryShopsBinding(Map<String, Object> map) {
		PurResult sr=new PurResult();
		try {
			List<Binding> data = cashDao.queryShopsBinding(map);
			sr.setData(data);
			sr.setStatus(1);
			sr.setMsg("成功");
		} catch (Exception e) {
			log.error("查询异常：",e);
			sr.setStatus(0);
			sr.setMsg("异常");
		}
		return sr;
	}


	@Override
	@Transactional
	public ShopsResult newBindingGoods(String goodsBarcodes,String goodsCounts,Long shopUnique,Double bindingTotal){
		ShopsResult ns=new ShopsResult();
		List<Map<String,Object>> resource=new ArrayList<Map<String,Object>>();
		Long bindingUnique=new Date().getTime();
		String[] goodsBarcodesArr = goodsBarcodes.split(",");
		String[] goodsCountsArr = goodsCounts.split(",");
		for(int i=0;i<goodsBarcodesArr.length;i++){
			Map<String,Object> map=new HashMap<String, Object>();
			map.put("shopUnique", shopUnique);
			map.put("goodsBarcode", goodsBarcodesArr[i]);
			map.put("goodsCount", goodsCountsArr[i]);
			map.put("bindingUnique", bindingUnique);
			map.put("bindingTotal", bindingTotal);
			resource.add(map);
		}
		int k=cashDao.newBindingGoods(resource);
		if(0==k){
			ns.setStatus(1);
			ns.setMsg("添加失败！");
			return ns;
		}
		ns.setStatus(0);
		ns.setMsg("添加成功！");
		return ns;
	}


	@Override
	public ShopsResult modifyBinding(Map<String, Object> map) {
		ShopsResult sr=new ShopsResult();
		int k=cashDao.modifyBinding(map);
		if(k==0){
			sr.setStatus(1);
			sr.setMsg("修改商品捆绑消息失败！");
			return sr;
		}
		sr.setStatus(0);
		sr.setMsg("修改成功！");
		return sr;
	}


	@Override
	public ShopsResult deleteBindingGoods(Map<String, Object> map) {
		ShopsResult sr=new ShopsResult();
		int k=cashDao.deleteBindingGoods(map);
		if(k==0){
			sr.setStatus(1);
			sr.setMsg("删除商品捆绑关系失败！");
			return sr;
		}
		sr.setStatus(0);
		sr.setMsg("删除成功！");
		return sr;
	}


	@Override
	public ShopsResult queryOurShopGoods(Map<String, Object> map) {
		ShopsResult sr=new ShopsResult();
		try {
			List<Map<String,Object>> data = cashDao.queryOurShopGoods(map);
			sr.setData(data);
			sr.setStatus(1);
			sr.setMsg("成功");
		} catch (Exception e) {
			log.error("查询异常：",e);
			sr.setStatus(0);
			sr.setMsg("异常");
		}
		return sr;
	}


	@Override
	@Transactional
	public ShopsResult addOurShopGoods(Map<String, Object> map) {
		ShopsResult sr=new ShopsResult();
		//查询数量不能超过3
		int count=cashDao.queryOurShopGoodsCount(map);
		if("5".equals(map.get("type").toString())){
			if(count>=3){
				sr.setStatus(0);
				sr.setMsg("商品数量不能超过3个");
				return sr;
			}
		}else if("6".equals(map.get("type").toString())){
			if(count>=4){
				sr.setStatus(0);
				sr.setMsg("商品数量不能超过4个");
				return sr;
			}
		}
		map.put("promotion_activity_id", 0);
		cashDao.submitSupplierStorageOrder(map);
		cashDao.submitSingleGoodsPromotion(map);
		sr.setStatus(1);
		sr.setMsg("成功");
		return sr;
	}


	@Override
	@Transactional
	public ShopsResult editOurShopGoods(Map<String, Object> map) {
		ShopsResult sr=new ShopsResult();
		map.put("promotion_activity_id", 0);
		cashDao.updateActitiy(map);
		cashDao.deleteSingleGoodsMarkdown(map);
		cashDao.submitSingleGoodsPromotion(map);
		sr.setStatus(1);
		return sr;
	}


	@Override
	public ShopsResult queryOurShopGoodsDetail(Map<String, Object> map) {
		ShopsResult sr=new ShopsResult();
		Map<String,Object> data= cashDao.queryOurShopGoodsDetail(map);
		sr.setStatus(1);
		sr.setData(data);
		return sr;
	}


	@Override
	public ShopsResult queryGoodsPromotionList(Map<String, Object> map) {
		ShopsResult sr=new ShopsResult();

		if(null == map.get("goods_id") || null == map.get("shop_unique")) {
			sr.setStatus(0);
			sr.setMsg("请选择商品和店铺信息");

			return sr;
		}

		Map<String,Object> data =new HashMap<>();
		Map<String,Object> goods_discount= cashDao.queryGoodsPromotion(map); //商品折扣
		Map<String,Object> goods_give= cashDao.queryGoodsMeetGive(map);//商品满赠
		Map<String,Object> singe_goods= cashDao.querySingeGoodsPromotion(map);//单品促销
		//List<Binding> binding_goods = cashDao.queryShopsBindingList(map);
		if(goods_discount!=null){
			data.put("goods_discount", goods_discount);
		}
		if(goods_give!=null){
			data.put("goods_give", goods_give);
		}
		if(singe_goods!=null){
			data.put("singe_goods", singe_goods);
		}
		//data.put("binding_goods", binding_goods);
		if(data.isEmpty()){
			sr.setStatus(0);
		}else{
			sr.setStatus(1);
		}
		sr.setData(data);
		return sr;
	}


	@Override
	public ShopsResult queryOrderMeetMoney(Map<String, Object> map) {
		ShopsResult pr=new ShopsResult();
		Map<String, Object> activity=cashDao.queryOrderMeetMoney(map);
		if(activity!=null){
			pr.setStatus(1);
			pr.setData(activity);
		}else{
			pr.setStatus(0);
		}

		return pr;
	}


	@Override
	public ShopsResult queryShopOpenStatus(Map<String, Object> map) {
		ShopsResult pr=new ShopsResult();
		Map<String, Object> shop=cashDao.queryShopOpenStatus(map);
		if(shop!=null){
			pr.setStatus(1);
			pr.setData(shop);
		}else{
			pr.setStatus(0);
			pr.setMsg("未开通");
		}
		return pr;
	}


	@Override
	public ShopsResult queryBeansDiKu(Map<String, Object> map) {
		ShopsResult pr=new ShopsResult();
		Map<String, Object> shop=cashDao.queryBeansDiKu(map);
		if(shop!=null){
			pr.setStatus(1);
			pr.setData(shop);
		}else{
			pr.setStatus(0);
			pr.setMsg("未设置");
		}
		return pr;
	}


	@Override
	public void updateBankRoot(Map<String, Object> map) {
		cashDao.updateBankRoot(map);

	}


	@Override
	public PurResult getGoodsSupplierMsg(String shopUnique) {
		PurResult result=new PurResult();
		result.setStatus(1);
		result.setMsg("查询成功！");
		List<Map<String,Object>> supList = cashDao.getGoodsSupplierMsg(shopUnique);
		result.setData(supList);
		return result;
	}


	@Override
	public PurResult queryGoodsByPage1(String shop_unique, String goodsMessage, String goods_kind_parunique,
			String goods_kind_unique, int page, int pageSize, Integer is_online) {
		PurResult pr = new PurResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("is_online", is_online);
		if(goodsMessage!=null&&!"".equals(goodsMessage)){
			map.put("goodsMessage", "%"+goodsMessage+"%");
		}
		if(!"-1".equals(goods_kind_parunique)&&!"null".equals(goods_kind_parunique)){
			map.put("goods_kind_parunique", goods_kind_parunique);
		}
		if(!"-1".equals(goods_kind_unique)){
			map.put("goods_kind_unique", goods_kind_unique);
		}

		//查询共多少条
		int count=cashDao.queryGoodsCount(map);
		map.put("pageSize", pageSize);
		map.put("startNum", (page-1)*pageSize);
		List<Map<String,Object>> list = cashDao.queryGoodsByPage(map);

		pr.setData(list);
		pr.setCount(count);
		pr.setStatus(1);
		pr.setMsg("查询成功！");
		return pr;
	}

}
