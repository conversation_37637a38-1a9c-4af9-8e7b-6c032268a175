package org.haier.shopUpdate.service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import cn.hutool.core.util.StrUtil;
import org.haier.shopUpdate.dao.AppPayDao;
import org.haier.shopUpdate.dao.CusCheckoutDao;
import org.haier.shopUpdate.project.ProjectConfig;
import org.haier.shopUpdate.util.NumRandomUntil;
import org.haier.shopUpdate.util.ShopsResult;
import org.haier.shopUpdate.util.ShopsUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.request.AlipayTradePayRequest;
import com.alipay.api.response.AlipayTradePayResponse;
import com.github.wxpay.sdk.WXPayUtil;

@Slf4j
@Service
@Transactional
public class AppPayServiceImpl implements AppPayService{

	public static final String ALIPAY_APP_ID = "2018042360033447";
	@Resource
	private  AppPayDao appPayDao;
	@Resource
	private CusCheckoutDao cusCheckoutDao;

	public ShopsResult addGoodsToCar(String shop_unique, String sale_list_unique, String goods_barcode,
			Double sale_list_detail_count, Double sale_list_detail_price,String goods_type,Integer sale_list_cashier,Integer table_id) {
		ShopsResult result=new ShopsResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("sale_list_unique", sale_list_unique);
		map.put("goods_barcode", goods_barcode);
		map.put("sale_list_detail_count", sale_list_detail_count);
		map.put("sale_list_detail_price", sale_list_detail_price);
		map.put("table_id", table_id);
		Map<String, Object> goodsMap=null;
		//判断是否是无码商品
		if("-1".equals(goods_type)){
			//无码商品生成一个条码
			goods_barcode=System.currentTimeMillis()+""+NumRandomUntil.getFixLenthString(6);

		}else{
			//查询商品是否存在
			goodsMap=appPayDao.queryGoodsExist(map);
			if(goodsMap==null){
				result.setStatus(0);
				result.setMsg("无此商品");
				return result;
			}
		}
		//查询订单是否存在
		Map<String, Object> saleListMap=appPayDao.querySaleListUniqueExist(map);
		if(saleListMap==null){
			//创建订单
			map.put("sale_type", 6);
			map.put("wait_status", 1);
			map.put("sale_list_cashier", sale_list_cashier);
			System.out.println("创建订单"+map);
			appPayDao.addSaleListWait(map);

		}else{
			appPayDao.updateSaleListWait(map);
		}
		//订单已存在查看是否有此商品
		Map<String, Object> saleListDetailMap=appPayDao.querySaleListDetailByGoodsExist(map);
		if(saleListDetailMap==null){
			//添加订单详情
			if("-1".equals(goods_type)){
				map.put("goods_name", "自营商品");
				map.put("goods_barcode", goods_barcode);
				map.put("goods_picturepath", "upload/no_goodsB.jpg");
				map.put("goods_cus_price_status", "0");
				map.put("goods_id", 0);
				map.put("goods_purprice", 0);
				map.put("goods_cus_price", 0);
			}else{
				map.put("goods_picturepath", goodsMap.get("goods_picturepath"));
				map.put("goods_name", goodsMap.get("goods_name"));
				map.put("goods_id", goodsMap.get("goods_id"));
				map.put("goods_purprice", goodsMap.get("goods_in_price"));
				map.put("sale_list_detail_price", goodsMap.get("goods_sale_price"));
				if(goodsMap.get("goods_cus_price")!=null && !"".equals(goodsMap.get("goods_cus_price"))){
					map.put("goods_cus_price", goodsMap.get("goods_cus_price"));
					map.put("goods_cus_price_status", "1");
				}else{
					map.put("goods_cus_price", 0);
					map.put("goods_cus_price_status", "0");
				}
			}
			appPayDao.addSaleListDetailWait(map);
		}else{
			//更新商品数量+1
			appPayDao.updateSaleListDetailWait(saleListDetailMap);
		}
		//查询此订单所有订单详情
		List<Map<String, Object>> detailList=appPayDao.findAllSaleListDetail(map);
		result.setStatus(1);
		if(detailList==null){
			result.setData(new ArrayList<Map<String, Object>>());
		}else{
			result.setData(detailList);
		}
		return result;
	}

	public ShopsResult editGoodsToCar(String shop_unique, String sale_list_unique, String goods_barcode,
			Double sale_list_detail_count, Double sale_list_detail_price,Double goods_cus_price) {
		ShopsResult result=new ShopsResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("sale_list_unique", sale_list_unique);
		map.put("goods_barcode", goods_barcode);
		map.put("sale_list_detail_count", sale_list_detail_count);
		map.put("sale_list_detail_price", sale_list_detail_price);
		map.put("goods_cus_price", goods_cus_price);
		//如果数量为0删除此商品
		if(sale_list_detail_count<=0){
			appPayDao.deleteGoodsBySaleListDetail(map);
		}else{
			//更新商品数量和价格
			appPayDao.editGoodsToCar(map);
		}
		//查询此订单所有订单详情
		List<Map<String, Object>> detailList=appPayDao.findAllSaleListDetail(map);
		result.setStatus(1);
		if(detailList==null){
			result.setData(new ArrayList<Map<String, Object>>());
		}else{
			result.setData(detailList);
		}
		return result;
	}

	public ShopsResult querySaleListWaitList(String shop_unique,Integer sale_list_cashier) {
		ShopsResult result=new ShopsResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("sale_list_cashier", sale_list_cashier);
		//查询挂单列表
		List<Map<String, Object>> saleListWaitList=appPayDao.findAllSaleListWait(map);
		//判断是否会员统计会员价格
		if(saleListWaitList!=null){
			for (Map<String, Object> saleListWait : saleListWaitList) {
				List<Map<String, Object>> SaleListDetail=appPayDao.findAllSaleListDetail(saleListWait);
				String cus_unique=(String) saleListWait.get("cus_unique");
				if(!"0".equals(cus_unique)){
					for (Map<String, Object> detail : SaleListDetail) {
						//如果是会员该商品又有会员价则取会员价
						String goods_cus_price_status= (String) detail.get("goods_cus_price_status");
						if("1".equals(goods_cus_price_status)){
							detail.put("sale_list_detail_price", detail.get("goods_cus_price"));
						}
					}
				}
				//计算价格
				double sum=0;
				for (Map<String, Object> detail : SaleListDetail) {
					BigDecimal sale_list_detail_price= (BigDecimal) detail.get("sale_list_detail_price");
					BigDecimal sale_list_detail_count= (BigDecimal) detail.get("sale_list_detail_count");
					sum+= (sale_list_detail_price.doubleValue()*sale_list_detail_count.doubleValue());
				}
				saleListWait.put("sale_list_total", new BigDecimal( sum).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
			}
		}
		result.setStatus(1);
		if(saleListWaitList==null){
			result.setData(new ArrayList<Map<String, Object>>());
		}else{
			result.setData(saleListWaitList);
		}
		return result;
	}

	public ShopsResult cancelSaleListWait(String shop_unique, String sale_list_unique) {
		ShopsResult result=new ShopsResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("sale_list_unique", sale_list_unique);
		appPayDao.cancelSaleListWait(map);
		result.setStatus(1);
		return result;
	}

	public ShopsResult querySaleListDetailWait(String shop_unique, String sale_list_unique) {
		ShopsResult result=new ShopsResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("sale_list_unique", sale_list_unique);
		//查询挂单列表
		List<Map<String, Object>> saleListWaitList=appPayDao.querySaleListDetailWait(map);
		result.setStatus(1);
		if(saleListWaitList==null){
			result.setData(new ArrayList<Map<String, Object>>());
		}else{
			result.setData(saleListWaitList);
		}
		return result;
	}

	public ShopsResult queryLatelySaleListDetailWait(String shop_unique) {
		ShopsResult result=new ShopsResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		//查询挂单列表
		List<Map<String, Object>> saleListWaitList=appPayDao.queryLatelySaleListDetailWait(map);
		if(saleListWaitList==null){
			String sale_list_unique= System.currentTimeMillis()+""+NumRandomUntil.getFixLenthString(5);
			Map<String,Object> map2=new HashMap<String, Object>();
			map.put("sale_list_unique", sale_list_unique);
			result.setStatus(0);
			result.setData(map2);
		}else{
			result.setStatus(1);
			result.setData(saleListWaitList);
		}
		return result;
	}

	public ShopsResult addCusJoinSaleList(String shop_unique, String sale_list_unique, String cus_unique,String sale_list_remarks) {
		ShopsResult result=new ShopsResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("sale_list_unique", sale_list_unique);
		map.put("cus_unique", cus_unique);
		map.put("sale_list_remarks", sale_list_remarks);
		appPayDao.addCusJoinSaleList(map);
		result.setStatus(1);
		return result;
	}

	public ShopsResult searchGoods(String shop_unique, String goods_message) {
		ShopsResult result=new ShopsResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("goods_message", "%"+goods_message+"%");
		//查询挂单列表
		List<Map<String, Object>> saleListWaitList=appPayDao.searchGoods(map);
		result.setStatus(1);
		if(saleListWaitList==null){
			result.setData(new ArrayList<Map<String, Object>>());
		}else{
			result.setData(saleListWaitList);
		}
		return result;
	}

	public ShopsResult deleteGoods(String sale_list_detail_id) {
		ShopsResult result=new ShopsResult();
		String  []ids=sale_list_detail_id.split(",");
		for (int i = 0; i < ids.length; i++) {
			Map<String,Object> map=new HashMap<String, Object>();
			String id = ids[i];
			map.put("sale_list_detail_id",Integer.parseInt(id));
			appPayDao.deleteGoods(map);
		}
		result.setStatus(1);
		return result;
	}

	public ShopsResult shop_pay(String shop_unique, String sale_list_unique, Integer sale_list_payment,
			Double sale_list_discount, Double sale_list_total, Double sale_list_actually_received,
			String sale_list_remarks,String spbill_create_ip, String auth_code) {
		ShopsResult result=new ShopsResult();
		Map<String,Object> params=new HashMap<String, Object>();
		params.put("sale_list_unique", sale_list_unique);
		params.put("shop_unique", shop_unique);
		//判断支付方式
		if(sale_list_payment==2){
			//支付宝
			Map<String, Object> shopInfo=appPayDao.findShopInfo(params);
			//验证商家是否开通支付宝
			if(shopInfo.get("alipay_private_key")==null||"".equals((String)shopInfo.get("alipay_private_key"))||"null".equalsIgnoreCase((String) shopInfo.get("alipay_private_key"))){
				System.out.println("商家未开通支付宝支付");
				result.setStatus(2);
				result.setMsg("商家未开通支付宝支付");
				return result;
			}
			AlipayClient alipayClient = new DefaultAlipayClient("https://openapi.alipay.com/gateway.do", ALIPAY_APP_ID,(String) shopInfo.get("alipay_private_key"), "json", "UTF-8", (String) shopInfo.get("alipay_public_key"), "RSA2");
			AlipayTradePayRequest request = new AlipayTradePayRequest();
			request.setBizContent("{" +
					"    \"out_trade_no\":\""+sale_list_unique+"\"," +
					"    \"scene\":\"bar_code\"," +
					"    \"auth_code\":\""+auth_code+"\"," +
					"    \"subject\":\"支付宝移动收银\"," +
					"    \"store_id\":\""+shop_unique+"\"," +
					"    \"timeout_express\":\"5m\"," +
					"    \"total_amount\":\""+sale_list_actually_received+"\"" +
					"  }");
					AlipayTradePayResponse response = null;
					try {
						response = alipayClient.execute(request);
					} catch (AlipayApiException e) {
						log.error("支付宝支付异常：",e);
					}
					System.out.println(response.getBody());
					if(response.isSuccess()){
						System.out.println("调用成功");
					} else {
						System.out.println("调用失败");
						result.setStatus(0);
						result.setMsg(response.getBody());
						return result;
					}
		}else if(sale_list_payment==3){
			//微信
			Map<String,String> wxmap=new HashMap<String, String>();
			wxmap.put("out_trade_no", sale_list_unique);
			String finalmoney= String.format("%.2f", Double.valueOf(sale_list_total+""));//微信请求金额单位为分
			finalmoney = finalmoney.replace(".", "");
			int total_fee= Integer.parseInt(finalmoney);
			wxmap.put("total_fee", total_fee+"");
			wxmap.put("body", "微信移动收银");
			wxmap.put("spbill_create_ip", spbill_create_ip);
			wxmap.put("auth_code", auth_code);
			//查询店铺微信配置信息
			Map<String, Object> shopInfo=appPayDao.findShopInfo(params);
			//验证商家是否开通支付宝
			if(shopInfo.get("shop_API_KEY")==null||"".equals((String)shopInfo.get("shop_API_KEY"))||"null".equalsIgnoreCase((String) shopInfo.get("shop_API_KEY"))){
				System.out.println("商家未开通微信支付");
				result.setStatus(3);
				result.setMsg("商家未开通微信支付");
				return result;
			}
			String key=(String) shopInfo.get("shop_API_KEY");//微信支付加密密钥
			wxmap.put("appid",(String)shopInfo.get("shop_APP_ID"));//项目APPID
			wxmap.put("mch_id",(String)shopInfo.get("shop_MCH_ID"));//服务商编号
			Map<String, String> data=wxPay(wxmap,key);
			System.out.println(data);
			if(!"SUCCESS".equals(data.get("return_code"))){
				result.setStatus(0);
				System.out.println("微信支付失败"+sale_list_unique+data.get("return_msg"));
				result.setMsg(data.get("return_msg"));
				return result;
			}else{
				if(!"SUCCESS".equals(data.get("result_code"))){
					result.setStatus(0);
					System.out.println("微信支付失败"+sale_list_unique+data.get("err_code_des"));
					result.setMsg(data.get("err_code_des"));
					return result;
				}else{
					System.out.println("微信支付成功"+sale_list_unique);
				}
			}

		}

		//查询挂单和详情
		Map<String, Object> saleList = appPayDao.querySaleListUniqueExist(params);
		if(saleList==null){
			saleList=new HashMap<>();
		}
		String cus_unique=saleList.get("cus_unique")==null?"0":(String) saleList.get("cus_unique");
		List<Map<String, Object>> SaleListDetail=appPayDao.findAllSaleListDetail(params);
		if(!"0".equals(cus_unique)){
			for (Map<String, Object> map : SaleListDetail) {
				//如果是会员该商品又有会员价则取会员价
				String goods_cus_price_status= (String) map.get("goods_cus_price_status");
				if("1".equals(goods_cus_price_status)){
					map.put("sale_list_detail_price", map.get("goods_cus_price"));
				}
			}
		}
		//算积分
		if(!"0".equals(cus_unique)){
		//根据后台管理配置的积分累计方案计算
		double sumPoint=0;
		for (Map<String, Object> detail : SaleListDetail) {
			//查询商品是否有配置方案
			String goods_barcode=(String) detail.get("goods_barcode");
			Map<String,Object> map=new HashMap<String,Object>();
			map.put("goods_barcode", goods_barcode);
			map.put("shop_unique", shop_unique);
			Map<String,Object> resultMap=appPayDao.findGoodsPointSet(map);
			if(resultMap!=null){
					Integer goods_points_type= (Integer) resultMap.get("goods_points_type");
					BigDecimal goods_points_val=(BigDecimal) resultMap.get("goods_points_val");
					Integer goods_kind_points_type= (Integer) resultMap.get("goods_kind_points_type");
					BigDecimal goods_kind_points_val=(BigDecimal) resultMap.get("goods_kind_points_val");
					Integer goods_kind_points_type_parent= (Integer) resultMap.get("goods_kind_points_type_parent");
					BigDecimal goods_kind_points_val_parent=(BigDecimal) resultMap.get("goods_kind_points_val_parent");
					if(goods_points_type!=null&&goods_points_val!=null){

					}else if(goods_kind_points_type!=null&&goods_kind_points_val!=null){
						//查询小分类是否有配置方案
						goods_points_type=goods_kind_points_type;
						goods_points_val=goods_kind_points_val;
					}else if(goods_kind_points_type_parent!=null&&goods_kind_points_val_parent!=null){
						//查询大分类是否有配置方案
						goods_points_type=goods_kind_points_type_parent;
						goods_points_val=goods_kind_points_val_parent;
					}
					if(goods_points_type!=null&&goods_points_val!=null){
						double point=0;
						if(goods_points_type==1){
							//商品售价
							BigDecimal sale_list_detail_price=(BigDecimal) detail.get("sale_list_detail_price");
							BigDecimal sale_list_detail_count=(BigDecimal) detail.get("sale_list_detail_count");
							point=(sale_list_detail_price.doubleValue()*sale_list_detail_count.doubleValue()/goods_points_val.doubleValue());
						}else if(goods_points_type==2){
							//固定积分
							BigDecimal sale_list_detail_count=(BigDecimal) detail.get("sale_list_detail_count");
							point=sale_list_detail_count.doubleValue()*goods_points_val.doubleValue();
						}else if(goods_points_type==3){
							//商品利润
							BigDecimal sale_list_detail_price=(BigDecimal) detail.get("sale_list_detail_price");
							BigDecimal goods_purprice=(BigDecimal) detail.get("goods_purprice");
							BigDecimal sale_list_detail_count=(BigDecimal) detail.get("sale_list_detail_count");
							point=((sale_list_detail_price.doubleValue()-goods_purprice.doubleValue())*sale_list_detail_count.doubleValue()/goods_points_val.doubleValue());
						}
						sumPoint+=point;
					}

				}
			}
			//修改会员等级和积分
			Double sale_points=updateCusPointAndLevel(Long.parseLong(shop_unique), sumPoint, cus_unique, 1);
			//保存消费积分和操作员记录
			Map<String,Object> params5=new HashMap<String,Object>();
			Map<String,Object> params2=new HashMap<String,Object>();
			params2.put("cus_unique", saleList.get("cus_unique"));
			params2.put("shopUnique", shop_unique);
			List<Map<String,Object>> custList= cusCheckoutDao.findCusById(params2);
			if(custList!=null&&custList.size()>0){
				if(custList.size()>=2){
					params5.put("cusBalance", custList.get(1).get("cus_balance"));
				}else{
					params5.put("cusBalance", custList.get(0).get("cus_balance"));
				}
			}
			saleList.put("cus_id", custList.get(0).get("cusId"));
			params5.put("cusId", custList.get(0).get("cusId"));
			params5.put("rechargeMoney", sale_list_actually_received);
			params5.put("cusType", 3);
			params5.put("saleListUnique", sale_list_unique);
			params5.put("sale_points", sale_points);
			params5.put("saleListCashier", saleList.get("sale_list_cashier"));
			appPayDao.addLog(params5);
		}
		//算提成
		double commission_sum=0;//总提成
		for (Map<String, Object> detail : SaleListDetail) {
			//查询商品是否有配置方案
			String goods_barcode=(String) detail.get("goods_barcode");
			Map<String,Object> map=new HashMap<String,Object>();
			if(goods_barcode!=null&&!"".equals(goods_barcode)){
				map.put("goods_barcode", goods_barcode);
				map.put("shop_unique", shop_unique);
				Map<String,Object> resultMap=appPayDao.findGoodsCommissionSet(map);
				if(resultMap!=null){
					Integer goods_commission_type= (Integer) resultMap.get("goods_commission_type");
					BigDecimal goods_commission_val=(BigDecimal) resultMap.get("goods_commission_val");
					Integer goods_kind_commission_type= (Integer) resultMap.get("goods_kind_commission_type");
					BigDecimal goods_kind_commission_val=(BigDecimal) resultMap.get("goods_kind_commission_val");
					Integer goods_kind_commission_type_parent= (Integer) resultMap.get("goods_kind_commission_type_parent");
					BigDecimal goods_kind_commission_val_parent=(BigDecimal) resultMap.get("goods_kind_commission_val_parent");
					if(goods_commission_type!=null&&goods_commission_val!=null){

					}else if(goods_kind_commission_type!=null&&goods_kind_commission_val!=null){
						//查询小分类是否有配置方案
						goods_commission_type=goods_kind_commission_type;
						goods_commission_val=goods_kind_commission_val;
					}else if(goods_kind_commission_type_parent!=null&&goods_kind_commission_val_parent!=null){
						//查询大分类是否有配置方案
						goods_commission_type=goods_kind_commission_type_parent;
						goods_commission_val=goods_kind_commission_val_parent;
					}
					if(goods_commission_type!=null&&goods_commission_val!=null){
						double commission_total=0;
						if(goods_commission_type==1){
							//金额比例
							BigDecimal sale_list_detail_price=(BigDecimal) detail.get("sale_list_detail_price");
							BigDecimal sale_list_detail_count=(BigDecimal) detail.get("sale_list_detail_count");
							commission_total=(sale_list_detail_price.doubleValue()*sale_list_detail_count.doubleValue()/goods_commission_val.doubleValue());
						}else if(goods_commission_type==2){
							//固定积分
							BigDecimal sale_list_detail_count=(BigDecimal) detail.get("sale_list_detail_count");
							commission_total=sale_list_detail_count.doubleValue()*goods_commission_val.doubleValue();
						}
						detail.put("commission_total", commission_total);
						commission_sum+=commission_total;
					}
				}
			}
		}
		saleList.put("commission_sum", commission_sum);
		//商品详情list
		List<Map<String,Object>>listGoods=new ArrayList<Map<String,Object>>();
		//出库记录list
		List<Map<String,Object>> stockList=new ArrayList<Map<String,Object>>();
		Double sale_list_pur=0.0;
		Double sale_list_totalCount=0.0;
		for (Map<String, Object> detail : SaleListDetail) {
			sale_list_pur+=((BigDecimal)detail.get("goods_purprice")).doubleValue()*((BigDecimal)detail.get("sale_list_detail_count")).doubleValue();
			Map<String, Object> maps = new HashMap<String, Object>();
			maps.put("goodsBarcode",detail.get("goods_barcode"));
			maps.put("shopUnique",shop_unique);
			//查询基本单位的商品信息
			Map<String, Object>  goodsInfo =appPayDao.getSmallGoodsInfo(maps);
			if(goodsInfo!=null){
				Double goods_contain = Double.parseDouble(goodsInfo.get("goods_contain").toString());
				maps.put("goodsId", goodsInfo.get("goods_id"));
				maps.put("goodsBarcode", goodsInfo.get("goodsBarcode"));
				Double goodsCount=((BigDecimal)detail.get("sale_list_detail_count")).doubleValue();
				maps.put("goodsCount", ShopsUtil.multiplicationDouble(goodsCount, goods_contain));
				maps.put("shoppingCartCount", ShopsUtil.multiplicationDouble(goodsCount, goods_contain));
				sale_list_totalCount = ShopsUtil.addDoubleSum(sale_list_totalCount, goodsCount);
				listGoods.add(maps);
			}else{
//				sale_list_totalCount += ((BigDecimal)detail.get("sale_list_detail_count")).doubleValue();
				sale_list_totalCount = ShopsUtil.addDoubleSum(sale_list_totalCount, Double.parseDouble(detail.get("sale_list_detail_count").toString()));
			}

			//添加出库记录
			Map<String,Object> smap=new HashMap<String, Object>();
			if(goodsInfo!=null){
				Double goods_contain = Double.parseDouble(goodsInfo.get("goods_contain").toString());
				Double goodsCount=((BigDecimal)detail.get("sale_list_detail_count")).doubleValue();
				smap.put("goodsBarcode", goodsInfo.get("goodsBarcode"));
				smap.put("goodsCount", ShopsUtil.multiplicationDouble(goodsCount, goods_contain));
				smap.put("stock_count",((BigDecimal)goodsInfo.get("goods_count")).doubleValue()-ShopsUtil.multiplicationDouble(goodsCount, goods_contain));
			}else{
				smap.put("goodsBarcode", detail.get("goods_barcode"));
				smap.put("goodsCount",detail.get("sale_list_detail_count"));
				smap.put("stock_count",0);
			}
			smap.put("stockType", 2);
			smap.put("shopUnique",shop_unique);
			smap.put("stockResource", 2);
			smap.put("listUnique",sale_list_unique);
			stockList.add(smap);
		}
		//减库存
		System.out.println("批量修改商品数量 listGoods：》》》》》》》》》》"+listGoods);
		if(listGoods.size()>0){
			int aa= appPayDao.batchStocks(listGoods);
			System.out.println("批量修改商品数量：》》》》》》》》》》"+aa);
		}
		//出库记录
		if(stockList!=null&&stockList.size()>0){
			appPayDao.newStockRecords(stockList);
		}

		//保存订单和详情
		saleList.put("sale_list_total", sale_list_total);
		saleList.put("sale_list_totalCount", sale_list_totalCount.intValue());
		saleList.put("sale_list_pur", sale_list_pur);
		saleList.put("cus_unique", 0);
		saleList.put("sale_list_name", "移动收银");
		saleList.put("sale_list_delfee", 0);
		saleList.put("sale_list_discount", sale_list_discount);
		saleList.put("sale_list_state", 3);
		saleList.put("sale_list_handlestate", 4);
		saleList.put("sale_list_payment", sale_list_payment);
		saleList.put("sale_list_same_type", 1);
		saleList.put("sale_list_actually_received", sale_list_actually_received);
		saleList.put("machine_num", 1);
		saleList.put("sale_list_remarks", sale_list_remarks);
		saleList.put("sale_list_unique", sale_list_unique);
		saleList.put("shop_unique", shop_unique);
		appPayDao.addrSaleList(saleList);
		for (Map<String, Object> detail : SaleListDetail) {
			appPayDao.addrSaleListDetail(detail);
		}
		//保存支付明细
//		Map<String ,Object> params2=new HashMap<String ,Object>();
		params.put("pay_method", sale_list_payment);
		params.put("pay_money", sale_list_actually_received);
		params.put("sale_list_unique", sale_list_unique);
		appPayDao.addSaleListPayDetail(params);
		//取消挂单
		appPayDao.cancelSaleListWait(params);
		result.setStatus(1);
		return result;
	}
	private double updateCusPointAndLevel(Long shopUnique, Double saleListActuallyReceived, String memberCard, Integer pointsRatio) {
		//计算会员积分是否需要修改会员等级
		Map<String ,Object> params=new HashMap<String ,Object>();
		params.put("searchKey", memberCard);
		params.put("shopUnique", shopUnique);
		List<Map<String,Object>> list= appPayDao.findCusById(params);
		for (Map<String, Object> map : list) {
			double totalPoints= ((BigDecimal) map.get("totalPoints")).doubleValue();
			if(pointsRatio==0){
				pointsRatio=1;
			}
			totalPoints+=saleListActuallyReceived/pointsRatio;
			//获取升级等级积分
			params.clear();
			params.put("shopUnique", shopUnique);
			List<Map<String,Object>> values= appPayDao.getCustLevelList(map);
			int cusLevelId=(Integer) map.get("cusLevelId");
			for (Map<String, Object> map2 : values) {
				int cusLevelStatus=  (Integer) map2.get("cusLevelStatus");
				if(cusLevelStatus==1){
					int cusLevelPoints=  (Integer) map2.get("cusLevelPoints");
					if(totalPoints>=cusLevelPoints){
						cusLevelId=(Integer) map2.get("cusLevelId");
					}
				}
			}
			params.clear();
			params.put("shopUnique", shopUnique);
			params.put("memberCard", memberCard);
			params.put("cusType", "会");
			params.put("saleListActuallyReceived", saleListActuallyReceived);
			params.put("cusLevelId", cusLevelId);
			params.put("pointsRatio", pointsRatio);
			appPayDao.updateMemberCard(params);
		}
		return BigDecimal.valueOf(saleListActuallyReceived/pointsRatio).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
	}

	public  Map<String,String> wxPay(Map<String,String> map,String key ){
		Map<String, String> data = new HashMap<String, String>();
		String cardUrl="https://api.mch.weixin.qq.com/pay/micropay";//支付调用路径
		String nonce_str=UUID.randomUUID().toString().replaceAll("-", "").substring(0, 32);
		map.put("nonce_str", nonce_str);//随机数
		map.put("fee_type","CNY");//货币类型
		map.put("sign_type","MD5");//加密方式
		URL httpUrl;

		System.out.println("参与签名的字符串数据为："+map);
		try {
			//获取需要传递的参数并生产XML格式参数
			String reqBody=WXPayUtil.generateSignedXml(map,key);
			System.out.println(null==reqBody+"参数没传入");
			//创建连接路径
			httpUrl=new URL(cardUrl);
			HttpURLConnection connection=(HttpURLConnection)httpUrl.openConnection();
			String  url = StrUtil.replaceIgnoreCase(ProjectConfig.PROJECT_URL, "https://", StrUtil.EMPTY);
			url = StrUtil.replaceIgnoreCase(url, "http://", StrUtil.EMPTY);
			connection.setRequestProperty("Host", url);
			connection.setDoOutput(true);
			connection.setRequestMethod("POST");
			connection.setConnectTimeout(10*1000);
			connection.setReadTimeout(10*1000);
			connection.connect();
			OutputStream outputStream = connection.getOutputStream();
	        outputStream.write(reqBody.getBytes("utf-8"));


	        InputStream inputStream=connection.getInputStream();
	        BufferedReader buffer=new BufferedReader(new InputStreamReader(inputStream, "utf-8"));
	        final StringBuffer stringBuffer = new StringBuffer();
	        String line=null;
	        while((line=buffer.readLine())!=null){
	        	stringBuffer.append(line);
	        }
	        String resp = stringBuffer.toString();
	        if (stringBuffer!=null) {
	            try {
	                buffer.close();
	            } catch (IOException e) {
	                log.error("微信支付异常：", e);
	            }
	        }
	        if (inputStream!=null) {
	            try {
	                inputStream.close();
	            } catch (IOException e) {
	                log.error("微信支付异常：", e);
	            }
	        }
	        if (outputStream!=null) {
	            try {
	                outputStream.close();
	            } catch (IOException e) {
	                log.error("微信支付异常：", e);
	            }
	        }

	        data=WXPayUtil.xmlToMap(resp);
	        return data;
		} catch (Exception e) {
			log.error("微信支付异常：", e);
		}
		//支付失败！
		return data;
	}

	@Override
	public ShopsResult queryFoodTableList(String shop_unique) {
		ShopsResult result=new ShopsResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		//查询挂单列表
		List<Map<String, Object>> saleListWaitList=appPayDao.queryFoodTableList(map);
		if(saleListWaitList!=null){
			for (Map<String, Object> saleListWait : saleListWaitList) {
				if(saleListWait.get("sale_list_unique")!=null){
				List<Map<String, Object>> SaleListDetail=appPayDao.findAllSaleListDetail(saleListWait);
				String cus_unique=(String) saleListWait.get("cus_unique");
				if(!"0".equals(cus_unique)){
					for (Map<String, Object> detail : SaleListDetail) {
						//如果是会员该商品又有会员价则取会员价
						String goods_cus_price_status= (String) detail.get("goods_cus_price_status");
						if("1".equals(goods_cus_price_status)){
							detail.put("sale_list_detail_price", detail.get("goods_cus_price"));
						}
					}
				}
				//计算价格
				double sum=0;
				for (Map<String, Object> detail : SaleListDetail) {
					BigDecimal sale_list_detail_price= (BigDecimal) detail.get("sale_list_detail_price");
					BigDecimal sale_list_detail_count= (BigDecimal) detail.get("sale_list_detail_count");
					sum+= (sale_list_detail_price.doubleValue()*sale_list_detail_count.doubleValue());
				}
				saleListWait.put("sale_list_total", new BigDecimal( sum).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
			}
			}
		}
		result.setStatus(1);
		if(saleListWaitList==null){
			result.setData(new ArrayList<Map<String, Object>>());
		}else{
			result.setData(saleListWaitList);
		}
		return result;
	}

	public ShopsResult addFoodTable(String shop_unique, String table_name) {
		ShopsResult result=new ShopsResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("table_name", table_name);
		appPayDao.addFoodTable(map);
		result.setStatus(1);
		return result;
	}

	public ShopsResult deleteFoodTable(String id) {
		ShopsResult result=new ShopsResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("id", id);
		appPayDao.deleteFoodTable(map);
		result.setStatus(1);
		return result;
	}

	public ShopsResult upadatePrintCount(String shop_unique, String sale_list_unique, String goods_barcode,
			String print_count) {
		ShopsResult result=new ShopsResult();
		String []barcodes=goods_barcode.split(",");
		String []print_counts=print_count.split(",");
		for (int i = 0; i < print_counts.length; i++) {
			String count = print_counts[i];
			String barcode = barcodes[i];
			Map<String,Object> map=new HashMap<String, Object>();
			map.put("shop_unique", shop_unique);
			map.put("sale_list_unique", sale_list_unique);
			map.put("print_count", count);
			map.put("goods_barcode", barcode);
			appPayDao.upadatePrintCount(map);

		}
		result.setStatus(1);
		return result;
	}
}
