package org.haier.shopUpdate.service;

import org.haier.shopUpdate.dto.GoodsKindByShopUniqueDto;
import org.haier.shopUpdate.entity.GoodsKind;
import org.haier.shopUpdate.util.ShopsResult;

import java.util.List;
import java.util.Map;

public interface GoodsKindsService {
	/**
	 * 商品分类查询
	 * @param map
	 * @return
	 */
	public ShopsResult queryGoodsKindsByShop(Map<String,Object> map);
	/**
	 * 商品分类查询（分开查询）
	 * @param map
	 * @return
	 */
	public ShopsResult appQueryGoodsKinds(Map<String,Object> map);
	public ShopsResult queryGoodsBigKindsByShop(Map<String, Object> map);
	public ShopsResult queryMoreGoodsBigKinds(Map<String, Object> map);
	public ShopsResult saveGoodsBigKindsByShop(String shop_unique, String goods_kind_list);
	/**
	 * 修改店铺分类使用类型；
	 * @param kindType；1、公共分类；2、自定义分类
	 * @param shopUnique
	 * @return
	 */
	public ShopsResult useCustomeKind(Integer kindType,String shopUnique);	
	
	/**
	 * 添加新的商品自定义分类信息
	 * @param map
	 * @return
	 */
	public ShopsResult modifyCustomKinds(GoodsKind kind);
	
	/**
	 * 删除商品分类前，查询该分类下的商品数量
	 * @param kindType
	 * @return
	 */
	public ShopsResult getGoodsCountByKindUnique(GoodsKind goodsKind);
	
	/**
	 * 获取当前店铺的分类使用状态
	 * @param goodsKind
	 * @return
	 */
	public ShopsResult getNowKindStatus(GoodsKind goodsKind);
	public ShopsResult queryGoodsKindsByShop2(Map<String, Object> map);
    List<GoodsKindByShopUniqueDto> queryGoodsKindByShopService(String shopUnique);
	public ShopsResult queryListByIconType(Integer icon_type, String shop_unique);

	/**
	 * 停用 禁用
	 *
	 * @param kindUnique
	 * @return
	 */
	ShopsResult deleteKind(Long kindUnique, String shopUnique);
	/**
	 * 根据分类查询所有子集编号
	 *
	 * @param shopUnique
	 * @param kindUnique
	 * @return
	 */
	List<Long> goodKindUniqueList(String shopUnique,Long kindUnique);
}
