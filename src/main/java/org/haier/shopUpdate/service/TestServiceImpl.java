package org.haier.shopUpdate.service;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.tools.ant.types.FileList;
import org.haier.shopUpdate.dao.TestDao;
import org.haier.shopUpdate.util.MUtil;
import org.haier.shopUpdate.util.ShopsResult;
import org.haier.shopUpdate.util.Test;
import org.springframework.stereotype.Service;
@Slf4j
@Service
public class TestServiceImpl implements TestService{
	@Resource
	private TestDao testDao;

	public ShopsResult test(){
//		testDao.dropTable();
		testDao.createTemTable();
		List<Map<String,String>> list=new ArrayList<Map<String,String>>();
		for(int i=0;i<5;i++){
			Map<String,String> map=new HashMap<String,String>();
			map.put("id", ""+i);
			map.put("class", ""+i);
			list.add(map);
		}
		testDao.addTemRows(list);
		List<Map<String,Object>> data= testDao.queryTemRows();

		ShopsResult sr=new ShopsResult();
		sr.setData(data);
		return sr;
	}

	/**
	 * 更新店铺的注册时间
	 * @return
	 */
	public ShopsResult modifyShopTime(){
		ShopsResult sr=new ShopsResult(1, "成功");
		 Calendar c=Calendar.getInstance();
		 List<Map<String,Object>> nlist=new ArrayList<>();
		 Integer count=0;
		 try {
			 c.set(2018, 05, 01, 8, 12, 05);
			 List<Map<String,Object>> list=testDao.queryShopList();
			 for(int i=0;i<list.size();i++){
				 c.add(Calendar.DAY_OF_MONTH, 1);
				 Map<String,Object> map=list.get(i);
				 map.put("datetime", c.get(Calendar.YEAR)+"-"+(c.get(Calendar.MONTH)+1)+"-"+c.get(Calendar.DAY_OF_MONTH)+" "
						 +c.get(Calendar.HOUR_OF_DAY)+":"+c.get(Calendar.MINUTE)+":"+c.get(Calendar.SECOND));
				 nlist.add(map);
			 }
			 for(int j=0;j<list.size()&&j<100;j++){
				 System.out.println(list.get(j));
			 }

			 count=testDao.modifyShopTime(list);
			 System.out.println(count);
		} catch (Exception e) {
			log.error("更新店铺注册时间失败",e);
		}
		return sr;
	}

	/**
	 *
	 * @return
	 */
	public ShopsResult modifyGoodsCount(){
		ShopsResult sr=new ShopsResult(1, "查询成功！");
		List<Map<String,Object>> list=testDao.queryGoodsForUpdate();
		Integer count=testDao.modifyGoods(list);
		sr.setData(count);
		return sr;
	}

	/**
	 * @return
	 */
	public ShopsResult modifyShopLong(){
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> list=testDao.queryShopsLong();
		List<Map<String,Object>> listn=testDao.queryShopNeedLong();
		List<Map<String,Object>> l=new ArrayList<>();
		int k=0;
		for(int i=0;i<listn.size();i++,k++){
			Map<String,Object> map=listn.get(i);

			if(k<list.size()){

			}else{
				k=0;
			}
			String address=list.get(k).get("shop_address_detail").toString();
			while(address.indexOf("兰山")>=0||address.indexOf("河东")>=0||address.indexOf("罗庄")>=0){
				k++;
				if(k>=list.size()){
					k=0;
				}
//				System.out.println(k);
//				System.out.println(list.size());
				address=list.get(k).get("shop_address_detail").toString();
			}
			map.put("longitude",Double.parseDouble(list.get(k).get("longitude").toString()) - Math.random()*0.02);
			map.put("latitude",Double.parseDouble(list.get(k).get("longitude").toString())+Math.random()*0.01);
			l.add(map);
//			if(k<10){
//				System.out.println(map.get("longitude"));
//			}
		}
		k=testDao.modifyLong(l);
		sr.setData(l);
		return sr;
	}

	public static void main(String[] args) {
		//System.out.println(Math.random());
		List<Map<String ,Object>> oldList = new ArrayList<Map<String ,Object>>();
		Map<String ,Object> m1 = new HashMap<>();
		m1.put("shopUnique", "1111");
		m1.put("goods_barcode", "2222");
		m1.put("aa", "a");

		Map<String ,Object> m2 = new HashMap<>();
		m2.put("shopUnique", "1111");
		m2.put("goods_barcode", "3333");
		m2.put("aa", "b");

		Map<String ,Object> m3 = new HashMap<>();
		m3.put("shopUnique", "1111");
		m3.put("goods_barcode", "3333");
		m3.put("aa", "c");

		oldList.add(m1);
		oldList.add(m2);
		oldList.add(m3);
		System.out.println(oldList.toString());
		List<Map<String ,Object>> list = removedup(oldList);
		System.out.println(list.toString());
	}

	public ShopsResult goodsKind(){
		ShopsResult sr=new ShopsResult(1, "");
		Map<String,Object> map=new HashMap<>();
		map.put("startNum", 2001);
		map.put("pageSize", 100);
		List<String> shopList=testDao.queryShopUnique(map);
		List<Map<String,Object>> kindList=testDao.getNewGoodsKind();
		List<Map<String,Object>> nk=new ArrayList<>();
		for(int i=0;i<shopList.size();i++){
			for(int j=0;j<kindList.size();j++){
				Map<String,Object> m=kindList.get(j);
				m.put("shopUnique", shopList.get(i));
				nk.add(m);
			}
		}
		int k=testDao.addNewGoodsKind(nk);
		sr.setData(map);
		return sr;
	}

	public ShopsResult addGoods(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		for(int k=0;k<2000;k++){
			map.put("startNum", k);
			map.put("pageSize",1);
			System.out.println(k);
			List<String> shopList=testDao.queryShopUnique(map);
			if(shopList==null||shopList.isEmpty()){
				break;
			}
			List<Map<String,Object>> goodsList=testDao.queryGoodsMsg();
			List<Map<String,Object>> nk=new ArrayList<>();
			if(null==shopList||shopList.isEmpty()){
				sr.setData("全部完成了");
				return sr;
			}
			for(int i=0;i<shopList.size();i++){
				for(int j=0;j<goodsList.size();j++){
					Map<String,Object> m=goodsList.get(j);
					m.put("shopUnique", shopList.get(i));
//				}
					nk.add(m);
				}
			}
			System.out.println(nk.size());
			testDao.addGoodsToShop(nk);
			sr.setData(map);
		}
		return sr;
	}


	public  static List<Map<String ,Object>> removedup(List<Map<String ,Object>> list){
        Map<String,Map<String ,Object>> hash = new HashMap<>();
        // 通过hashMap的key不能重复，达到去重的目的
        for(Map<String ,Object> aa :list){
            hash.put(MUtil.strObject(aa.get("shopUnique"))+"&"+MUtil.strObject(aa.get("goods_barcode")),aa);
        }
        List<Map<String ,Object>>  newList = new ArrayList<Map<String ,Object>>()  ;
        for(Map.Entry<String,Map<String ,Object>> set : hash.entrySet()){
            newList.add(set.getValue());
        }
        return  newList;
    }

	public ShopsResult querySaleListUnique(){
		ShopsResult sr=new ShopsResult(1, "");
		List<Map<String,Object>> l=testDao.querySaleListUnique();
		for(int i=0;i<l.size();i++){
			Random r=new Random();
			int pay_method=r.nextInt(10);
			if(pay_method==6||pay_method==7||pay_method==8){
				if(r.nextInt(2)==0){
					pay_method=2;
				}else{
					pay_method=3;
				}
			}
			l.get(i).put("pay_method", pay_method);
		}
		testDao.addNewPayDetail(l);
		return sr;
	}


	/**
	 * 将goods_new中的商品数量添加到goods中
	 * @param shopUnique
	 * @return
	 */
	public ShopsResult modifyShopGoods(String shopUnique){
		ShopsResult sr=new ShopsResult(1, "查询成功！");
		List<Map<String,Object>> list=testDao.queryGoodsForUpdateShop();
		for(int i=0;i<list.size();i++){
			list.get(i).put("shopUnique", shopUnique);
		}
		System.out.println(list.get(0));
		int k=testDao.modifyShopGoods(list);
		sr.setData(k);
		return sr;
	}

	/**
	 *
	 * @param path:需要修改的图片路径或文件夹路径
	 * @param size：修改后图片的宽度
	 * @return
	 */
	public ShopsResult modifyGoodsSize(String path,Integer size,Integer type) {
		ShopsResult sr = new ShopsResult(1, "修改成功！");
		Test.testPic(path, size ,type);
		return sr;
	}
}
