package org.haier.shopUpdate.service;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.haier.shopUpdate.dao.CurrencyManageDao;
import org.haier.shopUpdate.dao.ShopsStaffDao;
import org.haier.shopUpdate.entity.CusRecharge;
import org.haier.shopUpdate.entity.ShopManager;
import org.haier.shopUpdate.entity.ShopStaffEntity;
import org.haier.shopUpdate.oss.OssConstant;
import org.haier.shopUpdate.oss.UploadResult;
import org.haier.shopUpdate.params.QueryStaffListByStaffIdParams;
import org.haier.shopUpdate.redism.RedisCache;
import org.haier.shopUpdate.result.shopStaff.HandoverRecordResult;
import org.haier.shopUpdate.upload.FileUploadService;
import org.haier.shopUpdate.util.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
@Slf4j
@Service
@Transactional
public class ShopsStaffServiceImpl implements ShopsStaffService{
	@Resource
	private ShopsStaffDao staffDao;
	@Resource
	private FileUploadService fileUploadService;

		@Resource
	private CurrencyManageDao currencyManageDao;
	@Resource
	private I18nLanguageStaticReturnParamsUtil i18nRtUtil;
	@Resource
	private I18nLanguageMsgUtil i18nLanguageMsgUtil;



	/**
	 * 查询员工列表
	 * @param staffId
	 * @return
	 */
	public ShopsResult queryStaffListByStaffId(QueryStaffListByStaffIdParams params) {
		ShopStaffEntity staff=staffDao.queryStaffByStaffId(params.getStaffId());

		if (null == staff || ObjectUtil.isNull(staff)) {
			return ShopsResult.fail("该员工不存在");
		}
		if (staff.getStaffPosition() == 1) {
			//普通员工，返回自身信息即可
			List<ShopStaffEntity> list = new ArrayList<>();
			list.add(staff);
			return ShopsResult.ok(list);
		} else if (staff.getStaffPosition() == 3) {
			//管理员，查询所有员工信息
			List<ShopStaffEntity> list = staffDao.queryStaffListByStaffId(staff.getShopUnique().toString());
			return ShopsResult.ok(list);
		}

		return ShopsResult.fail("员工信息不正确");
	}
	/**
	 * 管理员登录
	 * @param map
	 * @return
	 */
	public ShopsResult staffLoginByAccountPwd(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		Map<String,Object> data= staffDao.staffLoginByAccountPwd(map);
		if(null==data||data.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("用户名或密码错误");
			return sr;
		}else if(data.containsKey("examinestatus")&&!data.get("examinestatus").equals(4))
		{
			sr.setStatus(2);
			String examinestatus = data.get("examinestatus").toString();
			if(examinestatus.equals("3"))
			{
				sr.setMsg(i18nLanguageMsgUtil.getMessage("审核未通过!")+"  【"+data.get("examinestatus_reason")+"】");

			}else if(examinestatus.equals("2")) {

				sr.setMsg("该店铺正在审核中!");

			}else if(examinestatus.equals("5")) {
				sr.setMsg("账号已注销");
			}

			return sr;
		}
		Object currencyObj = data.get("currency");
		if (ObjectUtil.isNotEmpty(currencyObj)) {
			String currency = currencyObj.toString();
			String currencySymbol = currencyManageDao.getCurrencySymbolByCurrencyCode(currency);
			data.put("currencySymbol",currencySymbol);
		}
		sr.setStatus(1);
		sr.setMsg("登录成功");
		sr.setData(data);
		return sr;
	}

	/**
	 * 根据管理员信息，查询管理员管理的店铺信息
	 * @param map
	 * @return
	 */
	public ShopsResult queryShopsByManager(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();

		//验证是否超级管理员，如果超级管理，查询所有店铺
		String managerUnique = staffDao.checkIsManager(map);
		map.put("managerUnique", managerUnique);
		String shop_name = map.get("shop_name") == null ? "" : map.get("shop_unique").toString();

		RedisCache rc = new RedisCache();

		String shopMsg = map.get("shopMsg") == null ? "" : map.get("shopMsg").toString();

		String key = managerUnique + shop_name + (map.get("startNum") == null ? "" : map.get("startNum").toString())
				+ (map.get("pageSize") == null ? "" : map.get("pageSize").toString()) + shopMsg;

		/*if(null != rc.getObject(key)) {
			sr.setData(rc.getObject(key));
			sr.setStatus(1);
			sr.setMsg("查询成功！");
			return sr;
		}*/

		List<ShopManager> data=staffDao.queryShopManagerPower(map);
		rc.putObject(key, data,3000);
		if(null==data||data.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("店铺信息不存在!");
			return sr;
		}
		List<String> currencys = new ArrayList<>();
		for (ShopManager datum : data) {
			String currency = datum.getCurrency();
			if(StrUtil.isNotEmpty(currency)) {
				currencys.add(currency);
			}
		}
		if (CollectionUtil.isNotEmpty(currencys)) {
			List<Map<String,Object>> currencyManageEntities = currencyManageDao.getCurrencySymbolsByCurrencyCodes(currencys);
			if (CollectionUtil.isNotEmpty(data) && CollectionUtil.isNotEmpty(currencyManageEntities)) {
				for (ShopManager datum : data) {
					for (Map<String,Object> currencyManageEntity : currencyManageEntities) {
						if (ObjectUtil.equal(datum.getCurrency(),currencyManageEntity.get("currency_code"))) {
							Object currencySymbol = currencyManageEntity.get("currency_symbol");
							datum.setCurrencySymbol(ObjectUtil.isNotEmpty(currencySymbol)?currencySymbol.toString():"");
						}
					}
				}
			}
		}
		sr.setData(data);
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		return sr;
	}

	/**
	 * 获取短信验证码
	 * @param staffAccount
	 * @return
	 */
	public ShopsResult sendMessage(Map<String,Object> map,HttpServletRequest request,HttpServletResponse response,Integer phoneType){
		ShopsResult sr=new ShopsResult();
		Map<String,Object> data= staffDao.staffLoginByAccountPwd(map);
		if(null==data||data.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("账号不存在！");
			return sr;
		}
		RedisCache rc = new RedisCache();
		String mobile=map.get("staffAccount").toString();
		Integer code=0;
		boolean flag=true;
		try {
			code=ShopsUtil.getMsgCode();
			flag=SendMsgUtil.sendCodeMsg(mobile, code+"");
			if(!flag){
				sr.setStatus(2);
				sr.setMsg("短信发送失败");
				return sr;
			}
			rc.putObject(mobile, code);
			if(phoneType==2){//IOS型号
				flag=SessionUtil.createSession(request, response, mobile, code+"", 180);
			}else if(phoneType==1){
				String string=SessionUtil.createANDRIODSession(request, response, mobile, code, 180);
				sr.setData(string);
			}
			if(!flag){
				sr.setMsg("保存密码失败！");
				sr.setStatus(2);
				return sr;
			}
		} catch (Exception e) {
			sr.setStatus(2);
			sr.setMsg("系统错误；短信验证码发送失败！");
			log.error("短信验证码发送失败：",e);
			return sr;
		}
		sr.setStatus(1);
		sr.setMsg("发送成功！");
//		sr.setData(code);
		return sr;
	}

	/**
	 * 验证码验证通过
	 * @param request
	 * @param response
	 * @param map
	 * @return
	 */
	public ShopsResult passMsg(HttpServletRequest request,HttpServletResponse response,Map<String,Object> map,Integer phoneType,String sessionId){
		ShopsResult sr=new ShopsResult();
		String staffAccount=map.get("staffAccount").toString();
		String smsCode=map.get("smsCode").toString();
		Integer code=0;
		RedisCache rc = new RedisCache();
		Object msgCode = rc.getObject(staffAccount);
		if(null == msgCode ) {
			code = 2;
		}else if(!msgCode.toString().equals(smsCode)) {
			code = 3;
		}

//		if(2==phoneType){
//			code=SessionUtil.getSessionMsg(request, response, staffAccount, smsCode);
//		}else if(1==phoneType){
//			code=SessionUtil.getANDRIODSession(request, response, staffAccount, smsCode, sessionId);
//		}
		if(code==2){
			sr.setStatus(2);
			sr.setMsg("验证超时！");
			return sr;
		}
		if(3==code){
			sr.setStatus(2);
			sr.setMsg("验证码错误！");
			return sr;
		}
		Map<String,Object> data=staffDao.staffLoginByAccountPwd(map);
		if(null==data||data.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("该账户不存在！");
			return sr;
		}
		//创建新的密钥
		String token=ShopsUtil.newToken();

		rc.putObject(staffAccount+"token", token,180);

//		ShopsUtil.map.put(staffAccount, token);
		//将密钥保存到session
//		SessionUtil.createNewToken(request, response, 180, token);
		Object currencyObj = data.get("currency");
		if (ObjectUtil.isNotEmpty(currencyObj)) {
			String currency = currencyObj.toString();
			String currencySymbol = currencyManageDao.getCurrencySymbolByCurrencyCode(currency);
			data.put("currencySymbol",currencySymbol);
		}
		sr.setStatus(1);
		sr.setMsg("验证成功！");
		sr.setData(data);
		sr.setMsg(token);
		return sr;
	}

	/**
	 * 更新管理员密码
	 * @param map
	 * @return
	 */
	public ShopsResult updateStaffPwd(Map<String,Object> map,Integer phoneType,String sessionId){
		ShopsResult sr=new ShopsResult();

				int k=staffDao.updateShopsStaffMessage(map);
				if(k>0){
					sr.setStatus(1);
					sr.setMsg("更新成功！");
				}else
				{
					sr.setStatus(2);
					sr.setMsg("更新失败");
				}


		return sr;
	}

	/**
	 * 更新管理员信息
	 * @param map
	 * @return
	 */
	public ShopsResult updateStaffPwd(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		int k=staffDao.updateShopsStaffMessage(map);
		if(k==0){
			sr.setStatus(2);
			sr.setMsg("更新失败");
			return sr;
		}
		sr.setStatus(1);
		sr.setMsg("更新成功！");
		return sr;
	}

	/**
	 * 更新店铺信息
	 * @param map
	 * @return
	 * @throws Exception
	 */
	public ShopsResult updateShopsMessage(Map<String,Object> map,HttpServletRequest request) throws Exception{
		ShopsResult sr=new ShopsResult();

		MultipartFile file=ShopsUtil.testMulRequest(request, "shopPicture");
		//若图片信息不为空，则存储图片并保存图片路径
		if(null!=file){
			String shopUnique=map.get("shopUnique").toString();
			String orName=file.getOriginalFilename();//获取文件原名称
			String lastName=orName.substring(orName.lastIndexOf("."));
			String fileName=shopUnique+Math.round(Math.random()*100)+lastName;
			String filePath= OssConstant.IMAGE_SOURCE + shopUnique + "/" + fileName;
			UploadResult fileResult = fileUploadService.uploadFileByPath(file.getInputStream(), filePath, file.getContentType());
			if(ObjectUtil.isNotNull(fileResult)){
				map.put("shopImagePath", fileResult.getUrl());
			}
		}
		int k =staffDao.updateShopsMessage(map);
		if(k==0){
			sr.setStatus(2);
			sr.setMsg("店铺信息不存在！");
			return sr;
		}
		sr.setStatus(1);
		sr.setMsg("更新成功！");
		return sr;
	}
	/**
	 * 店铺新信息查询
	 * @param map
	 * @return
	 */
	public ShopsResult shopNewMessage(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
//		List<ShopManager> data=staffDao.queryShopManagerPower(map);
		List<Map<String,Object>> data=staffDao.queryShopsByManager(map);
		if(null==data||data.isEmpty()){
			sr.setData(ShopsUtil.map);
		}else{
			Map<String, Object> stringObjectMap = data.get(0);
			Object currency = stringObjectMap.get("currency");
			if(ObjectUtil.isNotEmpty(currency)){
				String currencyStr = currency.toString();
				String currencySymbol = currencyManageDao.getCurrencySymbolByCurrencyCode(currencyStr);
				stringObjectMap.put("currencySymbol",currencySymbol);
			}
			sr.setData(stringObjectMap);
		}
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		return sr;
	}

	public ShopsResult editShopsInfo(Map<String, Object> map, HttpServletRequest request) throws IOException, Exception {
		ShopsResult sr=new ShopsResult();

		MultipartFile file=ShopsUtil.testMulRequest(request, "staffProtrait");
		//若图片信息不为空，则存储图片并保存图片路径
		if(null!=file){
			String shopUnique=map.get("staffAccount").toString();
			String orName=file.getOriginalFilename();//获取文件原名称
			String lastName=orName.substring(orName.lastIndexOf("."));
			String fileName=shopUnique+Math.round(Math.random()*100)+lastName;
			String filePath=OssConstant.IMAGE_SOURCE + shopUnique + "/" + fileName;
			UploadResult fileResult = fileUploadService.uploadFileByPath(file.getInputStream(), filePath, file.getContentType());
			if(ObjectUtil.isNotNull(fileResult)){
				map.put("staffProtrait", fileResult.getUrl());
			}
		}
		int k =staffDao.editShopsInfo(map);
		if(k==0){
			sr.setStatus(0);
			sr.setMsg("修改失败");
			return sr;
		}
		sr.setStatus(1);
		sr.setMsg("修改成功！");
		return sr;
	}
	/**
	 * 退出管理员的登录信息
	 * @param map
	 * @return
	 */
	public ShopsResult loginOut(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		int k=staffDao.updateShopsStaffMessage(map);
		if(k==0){
			sr.setStatus(2);
			sr.setMsg("退出失败！请检查你的登录账号是否正确");
			return sr;
		}
		sr.setStatus(1);
		sr.setMsg("退出成功！");
		return sr;
	}

	/**
	 * 查询店铺管理员信息
	 * @param map
	 * @return
	 */
	public ShopsResult shopsStaffsSearchByShopUnique(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> data=staffDao.shopsStaffsSearchByShopUnique(map);
		if(null==data||data.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("没有满足条件的信息！");
			return sr;
		}
		sr.setData(data);
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		return sr;
	}

	/**
	 * 修改员工权限
	 * @param map
	 * @return
	 */
	public ShopsResult modifyStaffPower(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
//		System.out.println(map);
		int k=staffDao.modifyStaffPower(map);
		if(k==0){
			sr.setStatus(2);
			sr.setMsg("更新失败");
			return sr;
		}
		sr.setStatus(1);
		sr.setMsg("保存成功");
		return sr;
	}

	public ShopsResult editStafPwd(Map<String, Object> map) {
		ShopsResult sr=new ShopsResult();
		int k=staffDao.editStafPwd(map);
		if(k==0){
			sr.setStatus(0);
			sr.setMsg("更新失败");
			return sr;
		}
		sr.setStatus(1);
		sr.setMsg("保存成功");
		return sr;
	}

	public ShopsResult updateRegistrationId(Map<String, Object> map) {
		ShopsResult sr=new ShopsResult();
		int k=staffDao.updateRegistrationId(map);
		if(k==0){
			sr.setStatus(0);
			sr.setMsg("更新失败");
			return sr;
		}
		sr.setStatus(1);
		sr.setMsg("保存成功");
		return sr;
	}

	/**
	 * 交接班统计
	 */
	public ShopsResult queryHandoverRecord(String startTime, String endTime, Long shopUnique, Long saleListCashier, Integer pageNum, Integer pageSize) {
		ShopsResult sr = new ShopsResult();
		if (pageNum < 1) {
			pageNum = 1;
		}
		if (pageSize < 1) {
			pageSize = 20;
		}
		sr.setPageIndex(pageNum);
		sr.setPageSize(pageSize);
		if (null == shopUnique) {
			sr.setStatus(0);
			sr.setMsg("店铺唯一标识不能为空");
			return sr;
		}
		Map<String,Object> map = new HashMap<String, Object>();
		if (StrUtil.isBlank(startTime) || startTime.length() != 10) {
			startTime = DateUtil.formatDate(DateUtil.date());
		}
		if (StrUtil.isBlank(endTime) || endTime.length() != 10) {
			endTime = startTime;
		}
		map.put("startTime", startTime + " 00:00:00");
		map.put("endTime", endTime + " 23:59:59");
		map.put("shopUnique", shopUnique);
		if(null != saleListCashier || saleListCashier!=-1){
			map.put("saleListCashier", saleListCashier);
		}
		map.put("pageSize", pageSize);
		map.put("pageNum", (pageNum - 1) * pageSize);

		List<HandoverRecordResult> resultList = new ArrayList<>();
		//交接班时间查询
		List<Map<String, Object>> staffList = staffDao.queryStaffList(map);
		if (CollUtil.isEmpty(staffList)) {
			sr.setStatus(0);
			sr.setMsg("没有交接班记录信息！");
			sr.setData(resultList);
			return sr;
		}
		for (Map<String, Object> staffMap : staffList) {
			HandoverRecordResult result = new HandoverRecordResult();
			result.setSale_list_cashier((Long) staffMap.get("staff_id"));
			result.setStaff_name((String) staffMap.get("staff_name"));
			result.setLogin_datetime((String) staffMap.get("login_datetime"));
			map.put("sale_list_cashier", staffMap.get("staff_id")) ;
			String sign_out_datetime = (String)staffMap.get("sign_out_datetime");
			result.setSign_out_datetime(sign_out_datetime);
			map.put("start_time", staffMap.get("login_datetime"));
			if(StrUtil.isBlank(sign_out_datetime)){
				map.put("end_time", DateUtil.date());
			} else {
				map.put("end_time", sign_out_datetime);
			}
			//总金额、总订单数
			List<Map<String, Object>> totalRecord = staffDao.queryTotalInfo(map);
			BigDecimal totalMoney = BigDecimal.ZERO;
			Long totalOrderNum = 0L;

			//----------------------------------------营业额-----start------------------------------
			BigDecimal wechatMoney = BigDecimal.ZERO;
			BigDecimal alipayMoney = BigDecimal.ZERO;
			BigDecimal cashMoney = BigDecimal.ZERO;
			BigDecimal storedCardMoney = BigDecimal.ZERO;
			BigDecimal bankCardMoney = BigDecimal.ZERO;
			Long wechatOrderNum = 0L;
			Long alipayOrderNum = 0L;
			Long cashOrderNum = 0L;
			Long storedCardOrderNum = 0L;
			Long bankCardOrderNum = 0L;
			BigDecimal bhdMoney = BigDecimal.ZERO;
			Long bhdOrderNum = 0L;
			BigDecimal jqMoney = BigDecimal.ZERO;
			Long jqOrderNum = 0L;
			BigDecimal onlineMoney = BigDecimal.ZERO;
			Long onlineOrderNum = 0L;
			Date firstSaleTime = null, endSaleTime = null;
			if (null != totalRecord && CollUtil.isNotEmpty(totalRecord)) {
				Long orderNum = Long.valueOf(totalRecord.get(0).get("orderNum").toString());
				if (ObjectUtil.isNull(orderNum) || orderNum <= 0) {
					continue;
				}
				totalMoney = (BigDecimal) (null == totalRecord.get(0).get("totalMoney") ? BigDecimal.ZERO : totalRecord.get(0).get("totalMoney"));
				totalOrderNum = (Long) totalRecord.get(0).get("orderNum");
				LocalDateTime time=	(LocalDateTime) totalRecord.get(0).get("minDatetime");
				ZonedDateTime zonedDateTime = time.atZone(ZoneId.systemDefault());
				java.time.Instant instant = zonedDateTime.toInstant();
				firstSaleTime = Date.from(instant);
				LocalDateTime time2=	(LocalDateTime) totalRecord.get(0).get("maxDatetime");
				ZonedDateTime zonedDateTime2 = time2.atZone(ZoneId.systemDefault());
				java.time.Instant instant2 = zonedDateTime2.toInstant();
				endSaleTime = Date.from(instant2);
				List<Map<String, Object>> saleList = staffDao.querySaleList(map);
				if (null != saleList && CollUtil.isNotEmpty(saleList)) {
					for (Map<String, Object> m : saleList) {
						Integer saleType = (Integer) m.get("sale_type");
						Integer payMethod = (Integer) m.get("pay_method");
						Integer serverType = (Integer) m.get("server_type");
						BigDecimal payMoney = (BigDecimal) m.get("pay_money") == null ? BigDecimal.ZERO : (BigDecimal) m.get("pay_money");



						if (ObjectUtil.equals(saleType, 0) && ObjectUtil.equals(serverType, 0)) {
							switch (payMethod) {
								case 1:
									//现金
									cashMoney = cashMoney.add(payMoney);
									cashOrderNum += 1;
									break;
								case 2:
									//支付宝
									alipayMoney = alipayMoney.add(payMoney);
									alipayOrderNum += 1;
									break;
								case 3:
									//微信
									wechatMoney = wechatMoney.add(payMoney);
									wechatOrderNum += 1;
									break;
								case 4:
									//银行卡
									bankCardMoney = bankCardMoney.add(payMoney);
									bankCardOrderNum += 1;
									break;
								case 5:
									//储值卡
									storedCardMoney = storedCardMoney.add(payMoney);
									storedCardOrderNum += 1;
									break;
							}
						}
						//百货豆
						if (ObjectUtil.equals(payMethod, 11)) {
							bhdMoney = bhdMoney.add(payMoney);
							bhdOrderNum += 1;
						}
						//金圈支付
						if (ObjectUtil.equals(saleType, 0) && (!ObjectUtil.equals(serverType, 0) || ObjectUtil.equals(payMethod, 13))) {
							jqMoney = jqMoney.add(payMoney);
							jqOrderNum += 1;
						}
						//线上支付
						if (!ObjectUtil.equals(serverType, 1) && (ObjectUtil.equals(saleType, 2) || ObjectUtil.equals(saleType, 7))) {
							onlineMoney = onlineMoney.add(payMoney);
							onlineOrderNum += 1;
						}
					}
				}
			}
			result.setOrderCount(totalOrderNum);
			result.setSumMoney(totalMoney);
			result.setStart_datetime(firstSaleTime != null ? DateUtil.formatDateTime(firstSaleTime) : "");
			result.setEnd_datetime(endSaleTime != null ? DateUtil.formatDateTime(endSaleTime) : "");


			BigDecimal jqReturnMoney = BigDecimal.ZERO;
			BigDecimal cashReturnMoney = BigDecimal.ZERO;
			BigDecimal wechatReturnMoney = BigDecimal.ZERO;
			BigDecimal alipayReturnMoney = BigDecimal.ZERO;
			BigDecimal storedCardReturnMoney = BigDecimal.ZERO;
			BigDecimal bankCardReturnMoney = BigDecimal.ZERO;
			BigDecimal bhdReturnMoney = BigDecimal.ZERO;
			BigDecimal onlineReturnMoney = BigDecimal.ZERO;

			//查询退款数据
			List<Map<String, Object>> returnList = staffDao.queryReturnList(map);
			if (null != returnList && CollUtil.isNotEmpty(returnList)) {
				for (Map<String, Object> m : returnList) {
					Integer payType = (Integer) m.get("pay_type");
					Integer serviceType = (Integer) m.get("service_type");
					BigDecimal payMoney = (BigDecimal) m.get("payMoney");
					Integer saleType = (Integer) m.get("sale_type");
					if (null == payMoney) {
						continue;
					}
					if (!ObjectUtil.equals(serviceType, 1) && !ObjectUtil.equals(payType, 1)) {
						if (ObjectUtil.equals(saleType, 2) || ObjectUtil.equals(saleType, 7)) {
							//线上退款
							onlineReturnMoney = onlineReturnMoney.add(payMoney);
						} else {
							//金圈退款
							jqReturnMoney = jqReturnMoney.add(payMoney);
						}
					}
					if (ObjectUtil.equals(serviceType, 1)) {
						switch (payType)
						{
							case 1:
								cashReturnMoney = cashReturnMoney.add(payMoney);
								break;
							case 2:
								alipayReturnMoney = alipayReturnMoney.add(payMoney);
								break;
							case 3:
								wechatReturnMoney = wechatReturnMoney.add(payMoney);
								break;
							case 4:
								bankCardReturnMoney = bankCardReturnMoney.add(payMoney);
								break;
							case 5:
								storedCardReturnMoney = storedCardReturnMoney.add(payMoney);
								break;
							case 8:
								bhdReturnMoney = bhdReturnMoney.add(payMoney);
								break;
						}
					}
				}
			}

			List<HandoverRecordResult.PayMethodDetail> details = new ArrayList<>();
			HandoverRecordResult.PayMethodDetail d = new HandoverRecordResult.PayMethodDetail();
			d.setName(i18nRtUtil.getMessage("微信"));
			d.setOrderNum(wechatOrderNum);
			d.setSale_list_actually_received(wechatMoney);
			d.setSale_list_return_money(wechatReturnMoney);
			d.setAccounting(culRate(wechatMoney, totalMoney));
			details.add(d);
			d = new HandoverRecordResult.PayMethodDetail();
			d.setName(i18nRtUtil.getMessage("支付宝"));
			d.setOrderNum(alipayOrderNum);
			d.setSale_list_actually_received(alipayMoney);
			d.setSale_list_return_money(alipayReturnMoney);
			d.setAccounting(culRate(alipayMoney, totalMoney));
			details.add(d);
			d = new HandoverRecordResult.PayMethodDetail();
			d.setName(i18nRtUtil.getMessage("现金"));
			d.setOrderNum(cashOrderNum);
			d.setSale_list_actually_received(cashMoney);
			d.setSale_list_return_money(cashReturnMoney);
			d.setAccounting(culRate(cashMoney, totalMoney));
			details.add(d);
			d = new HandoverRecordResult.PayMethodDetail();
			d.setName(i18nRtUtil.getMessage("百货豆"));
			d.setOrderNum(bhdOrderNum);
			d.setSale_list_actually_received(bhdMoney);
			d.setSale_list_return_money(bhdReturnMoney);
			d.setAccounting(culRate(bhdMoney, totalMoney));
			details.add(d);
			d = new HandoverRecordResult.PayMethodDetail();
			d.setName(i18nRtUtil.getMessage("储值卡"));
			d.setOrderNum(storedCardOrderNum);
			d.setSale_list_actually_received(storedCardMoney);
			d.setSale_list_return_money(storedCardReturnMoney);
			d.setAccounting(culRate(storedCardMoney, totalMoney));
			details.add(d);
			d = new HandoverRecordResult.PayMethodDetail();
			d.setName(i18nRtUtil.getMessage("银行卡"));
			d.setOrderNum(bankCardOrderNum);
			d.setSale_list_actually_received(bankCardMoney);
			d.setSale_list_return_money(bankCardReturnMoney);
			d.setAccounting(culRate(bankCardMoney, totalMoney));
			details.add(d);
			d = new HandoverRecordResult.PayMethodDetail();
			d.setName(i18nRtUtil.getMessage("金圈平台"));
			d.setOrderNum(jqOrderNum);
			d.setSale_list_actually_received(jqMoney);
			d.setSale_list_return_money(jqReturnMoney);
			d.setAccounting(culRate(jqMoney, totalMoney));
			details.add(d);
			d = new HandoverRecordResult.PayMethodDetail();
			d.setName(i18nRtUtil.getMessage("线上收入"));
			d.setOrderNum(onlineOrderNum);
			d.setSale_list_actually_received(onlineMoney);
			d.setSale_list_return_money(onlineReturnMoney);
			d.setAccounting(culRate(onlineMoney, totalMoney));
			details.add(d);
			result.setDetail(details);
			//----------------------------------------营业额-----end------------------------------

			//----------------------------------------充值金额-----start------------------------------

			List<Map<String, Object>> rechargeList = staffDao.queryRechargeRecord(map);
			BigDecimal cashRechargeMoney = BigDecimal.ZERO;
			Long cashRechargeOrderNum = 0L;
			BigDecimal wechatRechargeMoney = BigDecimal.ZERO;
			Long wechatRechargeOrderNum = 0L;
			BigDecimal alipayRechargeMoney = BigDecimal.ZERO;
			Long alipayRechargeOrderNum = 0L;
			BigDecimal depositRechargeMoney = BigDecimal.ZERO;
			Long depositRechargeOrderNum = 0L;
			BigDecimal jqRechargeMoney = BigDecimal.ZERO;
			Long jqRechageOrderNum = 0L;
			BigDecimal totalRechargeMoney = BigDecimal.ZERO;
			Long totalRechageOrderNum = 0L;
			if (null != rechargeList && CollUtil.isNotEmpty(rechargeList)) {
				for (Map<String, Object> m : rechargeList) {
					Integer recharge_method = (Integer) m.get("recharge_method");
					BigDecimal recharge_money = (BigDecimal) m.get("recharge_money");
					Long orderNum = (Long) m.get("orderNum");
					if (null == recharge_money) {
						recharge_money = BigDecimal.ZERO;
					}
					if (null != recharge_method) {
						totalRechageOrderNum += orderNum;
						totalRechargeMoney = totalRechargeMoney.add(recharge_money);
						switch (recharge_method)
						{
							case 1:
								cashRechargeMoney = cashRechargeMoney.add(recharge_money);
								cashRechargeOrderNum += orderNum;
								break;
							case 2:
								wechatRechargeMoney = wechatRechargeMoney.add(recharge_money);
								wechatRechargeOrderNum += orderNum;
								break;
							case 3:
								alipayRechargeMoney = alipayRechargeMoney.add(recharge_money);
								alipayRechargeOrderNum += orderNum;
								break;
							case 4:
								depositRechargeMoney = depositRechargeMoney.add(recharge_money);
								depositRechargeOrderNum += orderNum;
								break;
							case 6:
								jqRechargeMoney = jqRechargeMoney.add(recharge_money);
								jqRechageOrderNum += orderNum;
								break;
						}
					}
				}
			}
			result.setRechargeCount(totalRechageOrderNum);
			result.setRechargeSum(totalRechargeMoney);
			List<HandoverRecordResult.PayMethodDetail> rechargeDetail = new ArrayList<>();
			d = new HandoverRecordResult.PayMethodDetail();
			d.setName(i18nRtUtil.getMessage("现金"));
			d.setRecharge_method(1);
			d.setOrderNum(cashRechargeOrderNum);
			d.setSale_list_actually_received(cashRechargeMoney);
			d.setAccounting(culRate(cashRechargeMoney, totalRechargeMoney));
			rechargeDetail.add(d);
			d = new HandoverRecordResult.PayMethodDetail();
			d.setName(i18nRtUtil.getMessage("微信"));
			d.setRecharge_method(2);
			d.setOrderNum(wechatRechargeOrderNum);
			d.setSale_list_actually_received(wechatRechargeMoney);
			d.setAccounting(culRate(wechatRechargeMoney, totalRechargeMoney));
			rechargeDetail.add(d);
			d = new HandoverRecordResult.PayMethodDetail();
			d.setName(i18nRtUtil.getMessage("存零"));
			d.setRecharge_method(4);
			d.setOrderNum(depositRechargeOrderNum);
			d.setSale_list_actually_received(depositRechargeMoney);
			d.setAccounting(culRate(depositRechargeMoney, totalRechargeMoney));
			rechargeDetail.add(d);
			d = new HandoverRecordResult.PayMethodDetail();
			d.setName(i18nRtUtil.getMessage("支付宝"));
			d.setRecharge_method(3);
			d.setOrderNum(alipayRechargeOrderNum);
			d.setSale_list_actually_received(alipayRechargeMoney);
			d.setAccounting(culRate(alipayRechargeMoney, totalRechargeMoney));
			rechargeDetail.add(d);
			d = new HandoverRecordResult.PayMethodDetail();
			d.setName(i18nRtUtil.getMessage("金圈平台"));
			d.setRecharge_method(6);
			d.setOrderNum(jqRechageOrderNum);
			d.setSale_list_actually_received(jqRechargeMoney);
			d.setAccounting(culRate(jqRechargeMoney, totalRechargeMoney));
			rechargeDetail.add(d);
			result.setRechargeDetail(rechargeDetail);
			//----------------------------------------充值金额-----end------------------------------
			resultList.add(result);

		}
		sr.setData(resultList);
		sr.setStatus(1);
		return sr;
	}

	private String culRate(BigDecimal money, BigDecimal totalMoney) {
		if (BigDecimal.ZERO.compareTo(totalMoney) == 0) {
			return "0.00%";
		} else {
			return money.multiply(BigDecimal.valueOf(100)).divide(totalMoney, 2, BigDecimal.ROUND_HALF_UP) + "%";
		}
	}

	public ShopsResult submitFeedBack(String shop_unique, String staff_id, String feed_back_source,
			String feed_back_type, String feed_back_content, String feed_back_system, HttpServletRequest request) {

		ShopsResult  result=new ShopsResult();
		HashMap<String,Object> params=new HashMap<String, Object>();
		params.put("shop_unique", shop_unique);
		params.put("staff_id", staff_id);
		Map<String, Object> staffName= staffDao.getStaffName(params);
		params.put("staff_name", staffName.get("staff_name"));
		params.put("shop_name", staffName.get("shop_name"));
		params.put("feed_back_source", feed_back_source);
		params.put("feed_back_type", feed_back_type);
		params.put("feed_back_content", feed_back_content);
		params.put("feed_back_system", feed_back_system);
		params.put("shop_unique", shop_unique);
		Long feedBackId=0l;
		params.put("feedBackId", feedBackId);
		staffDao.submitFeedBack(params);
		feedBackId=(Long) params.get("feedBackId");
		//添加图片
		HashMap<String,Object> map=new HashMap<String, Object>();
		List<MultipartFile> fileList=ShopsUtil.getFiles(request, "imgPath");
		if(null!=fileList&&fileList.size()>0){
			try {
				for (MultipartFile file : fileList) {
					String orName=file.getOriginalFilename();
					String lastName=orName.substring(orName.lastIndexOf("."));
					String ngoods=UUID.randomUUID()+lastName;
					String filePath = OssConstant.IMAGE_SOURCE + shop_unique + "/" + ngoods;
					UploadResult fileResult = fileUploadService.uploadFileByPath(file.getInputStream(), filePath, file.getContentType());
					if(ObjectUtil.isNotNull(fileResult)){
						map.clear();
						map.put("imgPath", fileResult.getUrl());
						map.put("feedBackId", feedBackId);
						staffDao.saveFeedBackImage(map);
					}
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		//查询发送人名称
		StringBuffer sb=new StringBuffer();
		sb.append(staffName.get("shop_name")+"/");
		sb.append(staffName.get("staff_name"));
		//发送短信
		Map<String, Object> pp=new HashMap<String, Object>();
		List<Map<String, Object>>list=new ArrayList<Map<String,Object>>();
		if("1".equals(feed_back_source)){
			//PC收银端
			pp.put("feed_back_type", 3);
			list=staffDao.getFeedBackPhoneList(pp);
			sb.append("/收银端");
		}else if("2".equals(feed_back_source)){
			//商家app
			if("1".equals(feed_back_system)){
				//ios
				pp.put("feed_back_type", 1);
				list=staffDao.getFeedBackPhoneList(pp);
				sb.append("/商家app(ios)");
			}else if("2".equals(feed_back_system)){
				//安卓
				pp.put("feed_back_type", 2);
				list=staffDao.getFeedBackPhoneList(pp);
				sb.append("/商家app(android)");
			}
		}

		if("1".equals(feed_back_type)){
			sb.append("/收银流程");
		}else if("2".equals(feed_back_type)){
			sb.append("/订单数据");
		}else if("3".equals(feed_back_type)){
			sb.append("/商品信息");
		}else if("4".equals(feed_back_type)){
			sb.append("/支付问题");
		}else if("5".equals(feed_back_type)){
			sb.append("/流程优化");
		}else if("6".equals(feed_back_type)){
			sb.append("/新增需求");
		}else if("7".equals(feed_back_type)){
			sb.append("/售后服务");
		}else if("8".equals(feed_back_type)){
			sb.append("/硬件问题");
		}else if("9".equals(feed_back_type)){
			sb.append("/其他");
		}
		sb.append("/"+feed_back_content);
		for (Map<String, Object> map2 : list) {
			try {
				if(map2.get("feed_back_phone")!=null&&!"".equals(map2.get("feed_back_phone"))){
					System.out.println("发送短信"+(String)map2.get("feed_back_phone")+sb.toString());
					xioo.sendMsg((String)map2.get("feed_back_phone"), sb.toString());
				}
			} catch (Exception e) {
				log.error("发送短信异常",e);
			}

		}
		result.setStatus(1);
		return result;
	}

	public ShopsResult submitShopQualification(String shop_unique, String legal_name, String shop_phone,
			String register_shop_name, String use_shop_name, HttpServletRequest request) throws IOException, Exception {
		ShopsResult  result=new ShopsResult();
		HashMap<String,Object> params=new HashMap<String, Object>();
		params.put("shop_unique", shop_unique);
		params.put("legal_name", legal_name);
		params.put("shop_phone", shop_phone);
		params.put("register_shop_name", register_shop_name);
		params.put("use_shop_name", use_shop_name);
		//添加图片
		List<MultipartFile> fileList=ShopsUtil.getFiles(request, "yingyezhao_img");
		if(null!=fileList&&fileList.size()>0){
			for (MultipartFile file : fileList) {
				String orName=file.getOriginalFilename();
				String lastName=orName.substring(orName.lastIndexOf("."));
				String ngoods=UUID.randomUUID()+lastName;
				String filePath = OssConstant.IMAGE_SOURCE + shop_unique + "/" +ngoods;
				UploadResult fileResult = fileUploadService.uploadFileByPath(file.getInputStream(), filePath, file.getContentType());
				if(ObjectUtil.isNotNull(fileResult)){
					params.put("yingyezhao_img", fileResult.getUrl());
				}
			}
		}
		List<MultipartFile> card_zheng_imgList=ShopsUtil.getFiles(request, "card_zheng_img");
		if(null!=card_zheng_imgList&&card_zheng_imgList.size()>0){
			for (MultipartFile file : card_zheng_imgList) {
				String orName=file.getOriginalFilename();
				String lastName=orName.substring(orName.lastIndexOf("."));
				String ngoods=UUID.randomUUID()+lastName;
				String filePath = OssConstant.IMAGE_SOURCE + shop_unique + "/" +ngoods;
				UploadResult fileResult = fileUploadService.uploadFileByPath(file.getInputStream(), filePath, file.getContentType());
				if(ObjectUtil.isNotNull(fileResult)){
					params.put("card_zheng_img", fileResult.getUrl());
				}
			}

		}
		List<MultipartFile> card_fan_imgList=ShopsUtil.getFiles(request, "card_fan_img");
		if(null!=card_fan_imgList&&card_fan_imgList.size()>0){
			for (MultipartFile file : card_fan_imgList) {
				String orName=file.getOriginalFilename();
				String lastName=orName.substring(orName.lastIndexOf("."));
				String ngoods=UUID.randomUUID()+lastName;
				String filePath = OssConstant.IMAGE_SOURCE + shop_unique + "/" +ngoods;
				UploadResult fileResult = fileUploadService.uploadFileByPath(file.getInputStream(), filePath, file.getContentType());
				if(ObjectUtil.isNotNull(fileResult)){
					params.put("card_fan_img", fileResult.getUrl());
				}
			}
		}
		List<MultipartFile> bank_card_imgList=ShopsUtil.getFiles(request, "bank_card_img");
		if(null!=bank_card_imgList&&bank_card_imgList.size()>0){
			for (MultipartFile file : bank_card_imgList) {
				String orName=file.getOriginalFilename();
				String lastName=orName.substring(orName.lastIndexOf("."));
				String ngoods=UUID.randomUUID()+lastName;
				String filePath = OssConstant.IMAGE_SOURCE + shop_unique + "/" +ngoods;
				UploadResult fileResult = fileUploadService.uploadFileByPath(file.getInputStream(), filePath, file.getContentType());
				if(ObjectUtil.isNotNull(fileResult)){
					params.put("bank_card_img", fileResult.getUrl());
				}
			}
		}
		//验证是否已经提交过
		int i=0;
		Map<String, Object> map= staffDao.queryShopQualification(params);
		if(map==null){
			i=staffDao.submitShopQualification(params);
		}else{
			i=staffDao.updateShopQualification(params);
		}
		if(i>0){
			result.setStatus(1);
		}else{
			result.setStatus(0);
		}
		return result;
	}

	/**
	 *
	 */
	public ShopsResult queryShopQualification(String shop_unique) {
		ShopsResult  result=new ShopsResult();
		HashMap<String,Object> params=new HashMap<String, Object>();
		params.put("shop_unique", shop_unique);
		Map<String, Object> map= staffDao.queryShopQualification(params);
		if(map==null){
			result.setStatus(0);
		}else{
			result.setStatus(1);
			result.setData(map);
		}
		return result;
	}

	/**
	 * 会员充值详情记录
	 * @param map
	 * @return
	 */
	public ShopsResult queryCusRechargeRecord(Map<String,Object> map){
		ShopsResult sr=new ShopsResult(1,"查询成功！");
		List<CusRecharge> list=staffDao.queryCusRechargeRecord(map);
		sr.setData(list);
		return sr;
	}
}
