package org.haier.shopUpdate.service;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import cc.buyhoo.common.i18n.I18nUtil;
import cc.buyhoo.common.i18n.params.I18nQueryByChineseListParams;
import cc.buyhoo.common.i18n.result.I18nChineseListQueryDto;
import cn.hutool.core.util.ObjectUtil;
import org.haier.shopUpdate.dao.TitleDao;
import org.haier.shopUpdate.entity.ShopTitle;
import org.haier.shopUpdate.entity.ShopTitleDetail;
import org.haier.shopUpdate.entity.ShopTitleMain;
import org.haier.shopUpdate.entity.ShopTitleMainNew;
import org.haier.shopUpdate.util.I18nLanguageStaticReturnParamsUtil;
import org.haier.shopUpdate.util.ShopsResult;
import org.haier.shopUpdate.util.common.CommonResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

@Service
@Transactional
public class ShopTitleServiceImpl implements ShopTitleService{
	@Resource
	private TitleDao titleDao;
	@Resource
	private I18nLanguageStaticReturnParamsUtil i18nRtUtil;
	
	/**
	 * 查询商家端APP主界面的模块信息
	 * @param shopTitle
	 * @return
	 */
	public ShopsResult queryMainPageTitle(ShopTitle shopTitle){
		ShopsResult sr=new ShopsResult(1,"查询成功！");
		List<ShopTitleMain> list=titleDao.queryMainPageTitle(shopTitle);
		sr.setData(list);
		return sr;
	}
	
	/**
	 * 查询商家端APP主界面的模块信息
	 * @param shopTitle
	 * @return
	 */
	public CommonResult queryMainPageTitle_v2(ShopTitle shopTitle){
		CommonResult sr=new CommonResult(1,"查询成功！");
		List<ShopTitleMainNew> list=titleDao.queryMainPageTitle_v2(shopTitle);
		if (ObjectUtil.isNotEmpty(list)) {
			for (ShopTitleMainNew shopTitleMainNew : list) {
				if (ObjectUtil.isNotEmpty(shopTitleMainNew.getModularName())) {
					shopTitleMainNew.setModularName(i18nRtUtil.getMessage(shopTitleMainNew.getModularName()));
				}
				if (ObjectUtil.isNotEmpty(shopTitleMainNew.getList())) {
					for (ShopTitleDetail shopTitleDetail : shopTitleMainNew.getList()) {
						if (ObjectUtil.isNotEmpty(shopTitleDetail.getTitleName())) {
							shopTitleDetail.setTitleName(i18nRtUtil.getMessage(shopTitleDetail.getTitleName()));
						}
					}
				}
			}
		}
		sr.setData(list);
		return sr;
	}
	
	/**
	 * 更新店铺模块的排序信息
	 * @param list
	 * @return
	 */
	public ShopsResult modifyTitle(List<ShopTitle> list){
		ShopsResult sr=new ShopsResult(1,"更新成功！");
		Integer count=titleDao.modifyTitle(list);
		if(count>0){
			return sr;
		}
		sr.setStatus(2);
		sr.setMsg("更新失败");
		return sr;
	}
}
