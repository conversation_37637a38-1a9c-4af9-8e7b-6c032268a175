package org.haier.shopUpdate.service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.haier.shopUpdate.dao.BaiDuFaceDao;
import org.haier.shopUpdate.dao.CusCheckoutDao;
import org.haier.shopUpdate.dao.GoodsDao;
import org.haier.shopUpdate.dao.PurDao;
import org.haier.shopUpdate.entity.CusCheckout;
import org.haier.shopUpdate.entity.PalmResult;
import org.haier.shopUpdate.log.enums.BusinessType;
import org.haier.shopUpdate.log.util.SendDingDingTalkUtils;
import org.haier.shopUpdate.oss.UploadResult;
import org.haier.shopUpdate.upload.FileUploadService;
import org.haier.shopUpdate.util.*;
import org.jfree.util.Log;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

/**
 * 类名：com.palmshop.online.service.CusCheckoutServiceImpl;
 * 描述：收银端会员 信息相关的service实现类
 * 包括注册会员信息、修改会员信息、查询会员信息等
 * 方法顺序：增、删、改、查
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional
public class CusCheckoutServiceImpl implements CusCheckoutService {
	@Resource
	private CusCheckoutDao dao;//用户相关
	@Resource
	private GoodsDao goodsDao;
	@Resource
	private PurDao purDao;
	@Resource
	private BaiDuFaceDao bdDao;
	@Resource
	private CusCheckoutDao cusDao;
	@Resource
	private SendDingDingTalkUtils sendDingDingTalkUtils;

	@Resource
	private FileUploadService fileUploadService;
	@Resource
	private I18nLanguageStaticReturnParamsUtil i18nRtUtil;
	
	/**
	 * 1、添加新的会员信息
	 * @param cusUnique 会员编号
	 * @param shopUnique 店铺编号
	 * @param cusName 会员名称
	 * @param cusPhone 会员电话
	 * @return
	 */
	public PalmResult addCusNP(String cusUnique,String shopUnique,String cusName,String cusPhone) {
		PalmResult pr = new PalmResult(1, "添加成功!");

		//校验手机号，防止重复添加
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("shopUnique", shopUnique);
		map.put("cusPhone", cusPhone);

		//添加会员信息
		CusCheckout cus = new CusCheckout();
		cus.setShopUnique(Long.parseLong(shopUnique));
		cus.setCusUnique(cusUnique);
		cus.setCusName(cusName);
		cus.setCusPhone(cusPhone);
		if(null == cusUnique || cusUnique.equals("")) {
			List<Map<String,Object>> cusList = cusDao.findCusByCusPhone(map);
			if(null != cusList && !cusList.isEmpty()) {
				pr.setStatus(0);
				pr.setMsg("该手机号的会员信息已存在");
				return pr;
			}

			//添加会员信息

			Map<String,Object> levelMap = cusDao.getCusLevelByShopUnique(map);
			cus.setCusUnique(cusPhone);
			cus.setCusLevelId(Integer.parseInt(levelMap.get("cusLevelId").toString()));
			cus.setCusBalance(0.0);
			cus.setCusType("储");
			cus.setCusSex(1);

			cusDao.addCus(cus);
		}else {
			List<Map<String,Object>> cusList = cusDao.findCusByCusPhone(map);
			if(null != cusList && !cusList.isEmpty()) {
				String cusUniqueOld = cusList.get(0).get("cusUnique").toString();
				if(cusUnique.equals(cusUniqueOld)) {
					pr.setStatus(0);
					pr.setMsg("该手机号已被占用");
					return pr;
				}else {
					cusDao.editCus(cus);
				}
			}
		}


		return pr;
	}
	/**
	 * 注册会员信息
	 * @param request
	 * @param cus：用户实体类
	 * @return
	 */

	public PalmResult addCus(CusCheckout cus,HttpServletRequest request){
			PalmResult result=new PalmResult();
			cus.setCusAvailable(1);//可用状态
			cus.setSameType(1);//已同步状态
			//头像图片保存
			MultipartFile file=ShopsUtil.testMulRequest(request, "cusHeadPath");
			if(null!=file){
				try {
					UploadResult fileResult = fileUploadService.upload(file);
					cus.setCusHeadPath(ObjectUtil.isNotNull(fileResult) ?  fileResult.getUrl() : StrUtil.EMPTY);
				} catch (Exception e) {
					throw new RuntimeException(e);
				}
			}

			String cusType=cus.getCusType();
			if(cusType.split(",").length>1){
				String cusType1=cusType.split(",")[0];
				cus.setCusType(cusType1);
				dao.addCus(cus);
				String cusType2=cusType.split(",")[1];
				cus.setCusType(cusType2);
//				根据商铺ID查询初始会员等级
				Map<String,Object> map=new HashMap<String,Object>();
				map.put("shopUnique", cus.getShopUnique());
				map= dao.getCusLevelByShopUnique(map);
				int cusLevelId=(Integer) map.get("cusLevelId");
				cus.setCusLevelId(cusLevelId);
				dao.addCus(cus);
				result.setStatus(1);
			}else{
				if(dao.addCus(cus)>0){
					result.setStatus(1);
					result.setMsg("注册成功");
					result.setData(cus.getCusId());
				}
			}


		return result;
	}
	//查询会员列表
	public PalmResult getCustList(String searchKey, Long shopUnique,int pages, int perpage) {
		Map<String,Object> params=new HashMap<String, Object>();
		PalmResult result=new PalmResult();
		params.put("shopUnique", shopUnique);
		//查询本店所有会员人数
		Map<String,Object> shopMembershipMap= dao.getAllCustCountByShop(params);
		//查询本周新增会员人数
		Map<String,Object> addMembershipMap= dao.getAddCustCountByShop(params);
		//查询一周到店人数
		List<Map<String,Object>> weekToShopCusNumMap= dao.getWeekToShopCusNum(params);
		result.setShopMembership((Long) shopMembershipMap.get("shopMembership"));
		result.setAddMembership((Long) addMembershipMap.get("addMembership"));
		result.setWeekToShopCusNum(weekToShopCusNumMap==null?0:weekToShopCusNumMap.size());
		//查询会员
		if(null!=searchKey){
			params.put("searchKey", "%"+searchKey+"%");
		}
		params.put("pages", (pages-1)*perpage);
		params.put("perpage", perpage);
		List<Map<String,Object>> custList= dao.getCustList(params);
		if(custList!=null&&custList.size()>0){
			for (int i = 0; i < custList.size(); i++) {
				Long count=(Long) custList.get(i).get("count");
				if(count>=2){
					BigDecimal cus_level_val=(BigDecimal) custList.get(i).get("cus_level_val");
					custList.get(i).put("cus_level_val", cus_level_val.intValue()-1);
				}
				//查询会员类型
				Map<String, Object> param=new HashMap<String, Object>();
				param.put("cus_unique", custList.get(i).get("cus_unique"));
				param.put("shopUnique", shopUnique);
			    List<Map<String, Object>> cusList= dao.findCusById(param);
			    if(cusList.size()>1){
			    	custList.get(i).put("cus_type", "会,储");
			    }else{
			    	custList.get(i).put("cus_type", cusList.get(0).get("cusType"));
			    }
			}
//			result.setStatus(1);
		}
//		else{
//			result.setStatus(0);
//		}
		result.setStatus(1);
		result.setData(custList);
		return result;
	}
	//查询会员详情
	public PalmResult findCusById(String cus_unique, Long shopUnique,Integer searchType) {
		PalmResult result=new PalmResult(1,"操作成功");
		Map<String,Object> params=new HashMap<String, Object>();
		if(null!=cus_unique){
			if(searchType!=null&&searchType==1){
				params.put("cus_unique", "%"+cus_unique+"%");
			}else{
				params.put("cus_unique", cus_unique);
			}
		}
		//查询是否是连锁店铺需要会员同步会员保存到总店
		Map<String,Object> cusSynchroMap=dao.queryCusSynchroStatus(shopUnique.toString());
		if(cusSynchroMap!=null){
			shopUnique=Long.parseLong((String)cusSynchroMap.get("manager_unique"));
		}
		params.put("shopUnique", shopUnique);
		List<Map<String,Object>> custList= dao.findCusById(params);
		if((null==custList||custList.isEmpty())&&searchType==1&&cus_unique!=null){
			params.put("cus_unique", "%"+ChineseCharToEn.getAllFirstLetter(cus_unique)+"%");
			custList=dao.findCusById(params);
		}
		if(custList!=null&&custList.size()>0){
			result.setStatus(1);
			if(custList.size()>=2){
				custList.get(0).put("cus_balance",custList.get(1).get("cus_balance"));
				custList.get(0).put("cusRebate",custList.get(1).get("cusRebate"));
				custList.get(0).put("cusHeadPath",custList.get(1).get("cusHeadPath"));
				custList.get(0).put("cusType", "会,储");

			}
			result.setData(custList.get(0));
		}else{
			result.setStatus(0);
			result.setMsg("没有该会员信息");
		}
		return result;
	}
	//修改会员信息
	public PalmResult editCus(CusCheckout cus, HttpServletRequest request) {
			PalmResult result=new PalmResult(1,"更新成功");
			 try {
			//头像图片保存
			HashMap<String, String> paramsMap=new HashMap<String,String>();//用于百度API参数保存
			Map<String,Object> map=new HashMap<>();//用于项目的数据保存和查询
			MultipartFile file=ShopsUtil.testMulRequest(request, "cusHeadPath");
			String cusImages = StrUtil.EMPTY;
			if(null!=file){
				UploadResult fileResult = fileUploadService.upload(file);
				cusImages = ObjectUtil.isNotNull(fileResult) ? fileResult.getUrl() : StrUtil.EMPTY;
				cus.setCusHeadPath(cusImages);
			}
			//先验证是否上传图片信息和格式信息，若无，则无需相关操作
			if(cus.getImageMsg()!=null&&cus.getImgFormat()!=null){
				/**
				 * 验证会员是否存在人脸信息，若存在，不修改
				 */
				boolean hface=false;//该会员是否已绑定人脸信息，若绑定过，暂时不允许修改
				//获取现有会员人脸信息
				String cusType=cus.getCusType();//防止查询不出会员信息
				cus.setCusType(null);
				List<Map<String,Object>> clist=cusDao.findCusExist(cus);
				cus.setCusType(cusType);
				String faceToken=null;//人脸faceToken
				for(int j=0;j<clist.size();j++){
					if(clist.get(j).get("faceToken")!=null&&!clist.get(j).get("faceToken").equals("")){
						hface=true;
						break;
					}
				}
				//验证人脸信息是否存在，若存在，则比对线上会员信息并修改相应的人脸信息
				cus.setCusPicPath(ImageBinary.mysqlPath(cus.getImgFormat()));//图片存储绝对路径
				map.put("cus_face_image", cus.getCusPicPath());
				//通过BASE64上传，经常失败，不知道原因
				paramsMap.put("imageType", "BASE64");//
				paramsMap.put("image", cusImages);//图片路径
				paramsMap.put("groupIdList", "1000");//

				if(hface){//若已有会员人脸信息，仅更新人脸图片路径，
					Log.debug("已有该会员信息");
				}else{
					//比对人脸信息，获取相同或相似度90%以上的人脸信息，若无，则获取此图片的人脸信息
					//查询该会员是否有人脸信息，若有且相同，则更新图片，
					//若有且不相同，提示人脸信息不相同，不允许更新；
					//若没有，比较本店其他会员是否有相同，若有，则提示已绑定其他会员，
					//若没有，更新并保存

					//获取人脸信息
					paramsMap.put("type", "5");//检索相同人脸信息

					JSONObject jo=BaiDuFace.sample(paramsMap);
					System.out.println("editCus：：："+jo);
					boolean flag=false;
					String cusUnique=null;//对应的线上会员的人脸信息
					if(null!=jo&&jo.getInt("error_code")==0){//有相同人脸信息，
						//比较相似度，
						JSONArray jar=jo.getJSONObject("result").getJSONArray("user_list");
						for(int i=0;i<jar.length();i++){
							org.json.JSONObject j=jar.getJSONObject(i);
							if(j.getDouble("score")>=ConfigForFace.SCORE){//相似度大于90%
								flag=true;
								cusUnique=j.getString("user_id");
								break;
							}
						}

						if(flag){//有相同的人脸信息
							//根据cusUnique获取用户人脸信息
							List<Map<String,String>> list=bdDao.queryCusMsgByUnique(cusUnique,cus);
							boolean hcus=false;//记录是否已有相同人脸的其他会员信息
							for(int i=0;i<list.size();i++){
								System.out.println(list.get(i).get("faceToken"));
							}
							if(list!=null&&!list.isEmpty()){
								for(int i=0;i<list.size();i++){
									if(list.get(i).get("cusUnique")!=null){//线下已有相同人脸的会员信息
										hcus=true;
										break;
									}
								}
								if(hcus){
									result.setStatus(0);
									result.setMsg("此人脸已与本店其他会员信息绑定");
									return result;
								}else{
									faceToken=list.get(0).get("faceToken");
								}
							}
							//
						}else{
							/**
							 * 没有相同的人脸信息
							 * 1、创建线上会员信息；
							 * 2、将人脸信息添加到人脸库
							 */
							//创建新的线上会员
							cusUnique=Calendar.getInstance().getTimeInMillis()+"";
							paramsMap.put("type", "12");
							paramsMap.put("userId",cusUnique);
							JSONObject j=BaiDuFace.sample(paramsMap);//将人脸信息添加到人脸库
							if(null!=j&&j.getInt("error_code")==0){//添加成功，获取当前图片中的人脸信息,并创建新的平台会员信息
								JSONObject jr=j.getJSONObject("result").getJSONArray("face_list").getJSONObject(0);
								faceToken=jr.getString("face_token");
								createNewPCus(map,jr,2,faceToken,cusUnique);
								//添加线上会员信息
								map.put("cusAccount", cusUnique);
								bdDao.addNewCustomerMsg(map);

								//将人脸信息添加到人脸库
								paramsMap.put("type", "4");
								BaiDuFace.sample( paramsMap);

							}else{//添加失败，此图中不包含人脸信息，或会员信息已存在
								result.setStatus(0);
								result.setMsg("上传的图片中不包含人脸信息");
								result.setData(cus.getCusPicPath());
								return result;
							}
						}

						//将人脸信息添加到cus，方便更新
						cus.setCus_face_token(faceToken);
					}else{//没找到相同的人脸信息
						cusUnique=Calendar.getInstance().getTimeInMillis()+"";
						paramsMap.put("type", "12");
						JSONObject j=BaiDuFace.sample( paramsMap);
						if(null!=j&&j.getInt("error_code")==0){//添加成功，获取当前图片中的人脸信息,并创建新的平台会员信息
							JSONObject jr=j.getJSONObject("result").getJSONArray("face_list").getJSONObject(0);
							faceToken=jr.getString("face_token");
							createNewPCus(map,jr,2,faceToken,cusUnique);
							//添加线上会员信息
							map.put("cusAccount", cusUnique);
							bdDao.addNewCustomerMsg(map);

							//将人脸信息添加到人脸库
							paramsMap.put("type", "4");
							paramsMap.put("userId",cusUnique);
							BaiDuFace.sample(paramsMap);

						}else{//添加失败，此图中不包含人脸信息
							result.setStatus(0);
							result.setMsg("上传的图片中不包含人脸信息");
							return result;
						}
					}
				}
				cus.setCus_face_token(faceToken);
			}
			if("会,储".equals(cus.getCusType())||"储,会".equals(cus.getCusType())){
				 cus.setCusType("会");
				 dao.editCus(cus);
				 cus.setCusLevelId(null);
				 cus.setCusType("储");
				 dao.editCus(cus);
				 result.setStatus(1);
				 result.setMsg("修改成功");
				 result.setData(cus.getCusId());
			}else{
				if(dao.editCus(cus)>0){
					result.setStatus(1);
					result.setMsg("修改成功");
					result.setData(cus.getCusId());
				}
			}
			 } catch (Exception e) {
					log.error("修改会员信息失败",e);
				 sendDingDingTalkUtils.sendDingDingTalkMsg(request, "修改会员信息", e, JSONUtil.toJsonStr(cus), "修改会员信息", BusinessType.UPDATE.ordinal());
				}
		return result;
	}
	/**
	 * 创建新的平台会员信息
	 * @param map
	 * @return
	 */
	public static Map<String,Object> createNewPCus(Map<String,Object> map,org.json.JSONObject ja,Integer utilType,String faceToken,String cusUnique){

		Integer cusSex=1,cusAge=0;
		cusAge=ja.getInt("age");
		if(ja.getJSONObject("gender").getString("type").equals("male")){
			cusSex=1;
		}else{
			cusSex=2;
		}

		//添加新的平台会员信息
		map.put("cusUnique",cusUnique);
		map.put("cusFaceToken", faceToken);
		map.put("cusSource", "3");
		map.put("hardwareType", 3);
		map.put("groupId", "1000");//暂时固定，所有的用户头像都添加到这个库中
		map.put("apiType", utilType);//
		map.put("cusSex", cusSex);
		map.put("cusAge", cusAge);
		return map;
	}
	//查询消息列表
	public PalmResult getMsgList(Long shopUnique, Integer shopMsgType, Integer pages, Integer perpage) {

		Map<String,Object> params=new HashMap<String, Object>();
		params.put("shopUnique", shopUnique);
		if(null==shopMsgType||shopMsgType==-1){
			System.out.println(shopMsgType);
		}else{
			params.put("shopMsgType", shopMsgType);
		}
		params.put("pages", (pages-1)*perpage);
		params.put("perpage", perpage);
		List<Map<String,Object>> msgList= dao.getMsgList(params);
		if (ObjectUtil.isNotEmpty(msgList)) {
			for(Map<String,Object> map:msgList){
				if (ObjectUtil.contains(map.get("shopMsgInfo"), " ")) {
					map.put("shopMsgInfo", String.valueOf(map.get("shopMsgInfo")).substring(0, String.valueOf(map.get("shopMsgInfo")).indexOf(" ") + 1)
							+ i18nRtUtil.getMessage(String.valueOf(map.get("shopMsgInfo")).substring(String.valueOf(map.get("shopMsgInfo")).indexOf(" ") + 1)));
				}
				if (ObjectUtil.contains(map.get("shopMsgTitle"), " ")) {
					map.put("shopMsgTitle", String.valueOf(map.get("shopMsgTitle")).substring(0, String.valueOf(map.get("shopMsgTitle")).indexOf(" ") + 1)
							+ i18nRtUtil.getMessage(String.valueOf(map.get("shopMsgTitle")).substring(String.valueOf(map.get("shopMsgTitle")).indexOf(" ") + 1)));
				}
				map.put("timeType", i18nRtUtil.getMessage(String.valueOf(map.get("timeType"))));
			}
		}
		PalmResult result=new PalmResult(1,"查询成功！");
		result.setData(msgList);
		result.setStatus(1);
		return result;
	}


	public PalmResult editMsgById(Long shopMsgId) {
		Map<String,Object> params=new HashMap<String, Object>();
		PalmResult result=new PalmResult();
		params.put("shopMsgId", shopMsgId);
		if(dao.editMsgById(params)>0){
			result.setStatus(1);
			result.setMsg("修改成功");
		}
		return result;
	}
	//用户评价回复
	public EvaluateResult userEvaluate(int evaluateUserId, int evaluateScore, int goodsId, String evaluateContent,
			Long evaluateId, int userType, HttpServletRequest request) {
		EvaluateResult  result=new EvaluateResult();
		//判断初评还是回复
		if(evaluateId==null){
			//初评
			HashMap<String,Object> params=new HashMap<String, Object>();
			params.put("evaluateUserId", evaluateUserId);
			params.put("evaluateScore", evaluateScore);
			params.put("goodsId", goodsId);
			params.put("evaluateId", evaluateId);
			dao.saveUserEvaluate(params);
			evaluateId=(Long) params.get("evaluateId");
		}
		//添加评价内容
		HashMap<String,Object> params=new HashMap<String, Object>();
		Long evaluateDetailId=null;
		params.put("evaluateContent",evaluateContent);
		params.put("evaluateDate", new Date());
		params.put("evaluateId", evaluateId);
		params.put("userType", userType);
		params.put("evaluateUserId", evaluateUserId);
		params.put("evaluateDetailId", evaluateDetailId);
		dao.saveUserEvaluateDetail(params);
		evaluateDetailId=(Long) params.get("evaluateDetailId");
		//添加图片
		HashMap<String,Object> map=new HashMap<String, Object>();
		//头像图片保存
		List<MultipartFile> fileList=ShopsUtil.getFiles(request, "imgPath");
		if(null!=fileList&&fileList.size()>0){
			try {
				for (MultipartFile file : fileList) {
					UploadResult fileResult = fileUploadService.upload(file);
					if(ObjectUtil.isNotNull(fileResult)){
						map.clear();
						map.put("imgPath", fileResult.getUrl());
						map.put("evaluateDetailId", evaluateDetailId);
						dao.saveUserEvaluateImg(map);
					}
				}
			} catch (Exception e) {
				throw new RuntimeException(e);
			}

		}
		result.setStatus(1);
		return result;
	}
	//获取评价列表
	public EvaluateResult getEvaluateList(Long goodsId, int pages, int perpage) {
		EvaluateResult result=new EvaluateResult();
		//获取评分列表
		HashMap<String,Object> params=new HashMap<String, Object>();
		params.put("goodsId", goodsId);
		List<HashMap<String, Object>> scoreListMap= dao.getScoreList(params);
		HashMap<String,Object> scoreList=new HashMap<String, Object>();
		scoreList.put("five", 0);
		scoreList.put("four", 0);
		scoreList.put("three", 0);
		scoreList.put("two", 0);
		scoreList.put("one", 0);
		double sum=0;
		double num=0;
		for (HashMap<String, Object> hashMap : scoreListMap) {
			Integer evaluateScore= (Integer) hashMap.get("evaluateScore");
			Long countNum= (Long) hashMap.get("countNum");
			if(evaluateScore==5){
				scoreList.put("five", hashMap.get("countNum"));
			}
			if(evaluateScore==4){
				scoreList.put("four", hashMap.get("countNum"));
			}
			if(evaluateScore==3){
				scoreList.put("three", hashMap.get("countNum"));
			}
			if(evaluateScore==2){
				scoreList.put("two", hashMap.get("countNum"));
			}
			if(evaluateScore==1){
				scoreList.put("one", hashMap.get("countNum"));
			}
			sum+=countNum*evaluateScore;
			num+=countNum;
		}
		Double averageScore=BigDecimal.valueOf(sum/num).setScale(1,   BigDecimal.ROUND_HALF_UP).doubleValue();
		result.setScoreList(scoreList);
		result.setAverageScore(averageScore);
		//查询评价主题列表
		params.put("pages", (pages-1)*perpage);
		params.put("perpage", perpage);
		List<HashMap<String, Object>> userEvaluateList= dao.getUserEvaluatePage(params);
		for (HashMap<String, Object> hashMap : userEvaluateList) {
			//查询评价详情列表
			params.clear();
			params.put("evaluateId",hashMap.get("evaluateId"));
			params.put("goodsId",hashMap.get("goodsId"));
			List<HashMap<String, Object>> userEvaluateDetailList= dao.getUserEvaluateDetail(params);
			for (HashMap<String, Object> hashMap2 : userEvaluateDetailList) {
				//查询图片列表
				Integer evaluateDetailId= (Integer) hashMap2.get("evaluateDetailId");
				params.clear();
				params.put("evaluateDetailId",evaluateDetailId);
				List<HashMap<String, Object>> userEvaluateImgList=dao.getUserEvaluateImgList(params);
				//把图片列表封装详情里
				hashMap2.put("imgPath", userEvaluateImgList);
			}
			//把详情列表封装到主题里
			hashMap.put("evaluateDetail", userEvaluateDetailList);
		}
		result.setData(userEvaluateList);
		result.setStatus(1);
		return result;
	}
	//获取轮播图
	public PalmResult getImages(Map<String,Object> map) {
		PalmResult result=new PalmResult();
		List<HashMap<String, Object>> imagesList =dao.getImages(map);
		result.setData(imagesList);
		result.setStatus(1);
		return result;
	}
	//查询促销商品
	public PalmResult getPromotionList(Long supplierUnique,Long shopUnique, int pages, int perpage) {
		PalmResult result=new PalmResult();
		HashMap<String,Object> params=new HashMap<String, Object>();
		if(null==supplierUnique||supplierUnique==0){
			supplierUnique=null;
		}
		params.put("supplierUnique", supplierUnique);
		params.put("shopUnique", shopUnique);
		params.put("pages", (pages-1)*perpage);
		params.put("perpage", perpage);
		List<HashMap<String, Object>> promotionList=dao.getPromotionList(params);
		result.setData(promotionList);
		result.setStatus(1);
		return result;
	}
	//查询商家头条
	public PalmResult getBusinessHead(Long pages, Long perpage) {
		PalmResult result=new PalmResult();
		HashMap<String,Object> params=new HashMap<String, Object>();
		if(pages!=null && perpage!=null){
			params.put("pages", (pages-1)*perpage);
			params.put("perpage", perpage);
		}
		List<HashMap<String, Object>> businessHeadList=dao.getBusinessHead(params);
		if(businessHeadList!=null&&businessHeadList.size()>0){
			result.setData(businessHeadList);
			result.setStatus(1);
		}else{
			result.setData(new ArrayList<HashMap<String, Object>>());
			result.setStatus(0);
		}

		return result;
	}
	//查询头条促销商品
	public PalmResult getBusinessHeadGoods(Long businessHeadId, int pages, int perpage) {
		PalmResult result=new PalmResult();
		HashMap<String,Object> params=new HashMap<String, Object>();
		params.put("businessHeadId", businessHeadId);
		params.put("pages", (pages-1)*perpage);
		params.put("perpage", perpage);
		List<HashMap<String, Object>> promotionList=dao.getBusinessHeadGoods(params);
		result.setData(promotionList);
		result.setStatus(1);
		return result;
	}
	//更新头条点击量
	public PalmResult updateClickCount(Long businessHeadId) {
		PalmResult result=new PalmResult();
		HashMap<String,Object> params=new HashMap<String, Object>();
		params.put("businessHeadId", businessHeadId);
		int r=dao.updateClickCount(params);
		if(r>0){
			result.setStatus(1);
		}
		return result;
	}

	/**
	 * 促销商品的采购
	 * @param map
	 * @return
	 */
	public ShopsResult  promotionGoodsPurchase(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		List<Map<String ,Object>> goods=goodsDao.queryHavingPromotionGoods(map);
		List<Map<String,Object>> ngoods=new ArrayList<Map<String,Object>>();
		if(null==goods||goods.isEmpty()){//没有该商品，请先设置销售价格
			sr.setStatus(2);
			sr.setMsg("店铺无此商品，请先输入销售价格");
			return sr;
		}
		Object supGoodsBarcode=map.get("goodsBarcode");
		Object supplierUnique=map.get("supplierUnique");
		Object shopUnique=map.get("shopUnique");
		Timestamp updateTime=new Timestamp(new Date().getTime());
		for(int i=0;i<goods.size();i++){
			Map<String,Object> m=goods.get(i);
			m.put("supGoodsBarcode", supGoodsBarcode);
			m.put("supplierUnique",supplierUnique);
			m.put("shopUnique",shopUnique);
			m.put("updateTime",updateTime);
			ngoods.add(m);
		}
		//批量更新需要修改的商品信息
		int k=goodsDao.updateGoodsSuppliers(goods);
		System.out.println(k);
		List<Map<String,Object>> cartGoods=purDao.queryPurCartGoods(map);//查询购物车已有商品
		String goodsBarcode=map.get("goodsBarcode").toString();//本次采购的商品条码
		//更新本店商品的默认供货商信息

		if(null==cartGoods||cartGoods.isEmpty()){//无购物车
			//创建新的购物车
			Long purListUnique= ShopsUtil.newUnique();
			map.put("purListUnique",purListUnique);
			map.put("purListDate",ShopsUtil.getRightNow());
			map.put("purListStatus", "2");
			//创建购物车
			purDao.createNewShopCart(map);
			//将新的商品添加到购物车详情
			purDao.addNewGoodsToCart(map);
		}else{//有购物车
			boolean flag=true;
			String purListUnique=cartGoods.get(0).get("purListUnique").toString();
			map.put("purListUnique",purListUnique);
			for(int i=0;i<cartGoods.size();i++){
				Object barcode=cartGoods.get(i).get("goodsBarcode");
				if(null==barcode){//购物车无商品
					break;
				}
				if(goodsBarcode.equals(barcode.toString())){//购物车有相同商品
					flag=false;
					//查看修改后的商品数量，若为零则删除，若不为零，则修改
					Double hCount=Double.parseDouble((cartGoods.get(i).get("detailCount").toString()));
					Double detailCount=Double.parseDouble((map.get("detailCount").toString()));
					if((hCount+detailCount)<=0){//若修改后商品数量为零，则删除
						purDao.deleteCartGoods(map);
					}else{//若修改后商品数量不为零，则更新
						map.put("detailCount",hCount+detailCount);
						purDao.modifyCartGoods(map);
					}
					break;
				}
			}
			if(flag){//购物车无此商品
				purDao.addNewGoodsToCart(map);
			}
		}
		sr.setStatus(1);
		sr.setMsg("采购成功！");
		return sr;
	}

	/**
	 * 若促销商品不在该店铺信息中，先添加商品信息，再添加商品至购物车
	 * @param map
	 * @return
	 */
	public ShopsResult newPromotionGoodsPurchase(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		Map<String,Object> goods=goodsDao.selectNewGoods(map);
		goods.putAll(map);
//		System.out.println(goods);
		goodsDao.newGoodsMessage(goods);//店铺添加新的商品
		List<Map<String,Object>> cartGoods=purDao.queryPurCartGoods(map);//查询购物车已有商品
		String goodsBarcode=map.get("goodsBarcode").toString();//本次采购的商品条码
		//更新本店商品的默认供货商信息

		if(null==cartGoods||cartGoods.isEmpty()){//无购物车
			//创建新的购物车
			Long purListUnique= ShopsUtil.newUnique();
			map.put("purListUnique",purListUnique);
			map.put("purListDate",ShopsUtil.getRightNow());
			map.put("purListStatus", "2");
			//创建购物车
			purDao.createNewShopCart(map);
			//将新的商品添加到购物车详情
			purDao.addNewGoodsToCart(map);
		}else{//有购物车
			boolean flag=true;
			String purListUnique=cartGoods.get(0).get("purListUnique").toString();
			map.put("purListUnique",purListUnique);
			for(int i=0;i<cartGoods.size();i++){
				Object barcode=cartGoods.get(i).get("goodsBarcode");
				if(null==barcode){//购物车无商品
					break;
				}
				if(goodsBarcode.equals(barcode.toString())){//购物车有相同商品
					flag=false;
					//查看修改后的商品数量，若为零则删除，若不为零，则修改
					Double hCount=Double.parseDouble((cartGoods.get(i).get("detailCount").toString()));
					Double detailCount=Double.parseDouble((map.get("detailCount").toString()));
					if((hCount+detailCount)<=0){//若修改后商品数量为零，则删除
						purDao.deleteCartGoods(map);
					}else{//若修改后商品数量不为零，则更新
						map.put("detailCount",hCount+detailCount);
						purDao.modifyCartGoods(map);
					}
					break;
				}
			}
			if(flag){//购物车无此商品
				purDao.addNewGoodsToCart(map);
			}
		}
		sr.setStatus(1);
		sr.setMsg("添加成功！");
		return sr;
	}

	/**
	 * 会员充值消费记录（缺少会员积分兑换记录）
	 * @param map
	 * @return
	 */
	public ShopsResult queryCusConRecord(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> custList= dao.findCusById(map);
		map.put("cusId", custList.get(0).get("cusId"));
		if(custList.size()>=2){
			map.put("cusId2", custList.get(1).get("cusId"));
		}else{
			map.put("cusId2", custList.get(0).get("cusId"));
		}
		List<Map<String,Object>>  data=dao.queryCusConRecord(map);
//		System.out.println(data);
		if(data!=null&&data.size()>0){
			sr.setStatus(1);
			sr.setMsg("查询成功！");
			sr.setData(data);
		}else{
			sr.setStatus(0);
			sr.setData(data);
		}
		return sr;
	}

	public ShopsResult queryConType(){
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> data=dao.queryConType();
		sr.setData(data);
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		return sr;
	}
	public PalmResult queryCusDetailDown(String cus_unique, Long shopUnique) {
		PalmResult result=new PalmResult();
		Map<String,Object> params=new HashMap<String, Object>();
		params.put("cus_unique", cus_unique);
		params.put("shopUnique", shopUnique);
		//查询消费排名和消费总金额
		Map<String,Object> orderMap= dao.findSaleListOrder(params);
		if(orderMap!=null&&((BigDecimal)orderMap.get("saleTotalSum")).doubleValue()>0){
			List<Map<String,Object>> custList= dao.findCusById(params);
			orderMap.put("cus_id", custList.get(0).get("cusId"));
			if(custList.size()>=2){
				orderMap.put("cus_id2", custList.get(1).get("cusId"));
			}else{
				orderMap.put("cus_id2", custList.get(0).get("cusId"));
			}
			//查询客单价
			orderMap.put("shopUnique", shopUnique);
			Map<String, Object> singlePriceMap=dao.findSinglePrice(orderMap);
			orderMap.put("singlePrice", singlePriceMap.get("singlePrice"));
			//查询最近一次消费天数
			Map<String, Object> saleDayMap=dao.findSaleDay(orderMap);
			orderMap.put("saleDay", saleDayMap.get("saleDay"));
			//查询购物时段分布
			List<Map<String, Object>> saleHistoryList=dao.findSaleHistory(orderMap);
			Map<String, Object> saleHistory=new HashMap<String, Object>();
			//查询总共多少单
			Map<String, Object> saleCountMap=dao.findSaleCount(orderMap);
			Long count= (Long) saleCountMap.get("count");
			if(count!=0){
				int zero=0;
				int four=0;
				int eight=0;
				int twelve=0;
				int sixteen=0;
				int twenty=0;
				for (int i = 0; i < 24; i++) {
						for (Map<String, Object> map : saleHistoryList) {
							Integer time= Integer.valueOf((String) map.get("time"));
							if(time==i){
								if(i<=3){
									zero+=(Long)map.get("count");
								}else if(i<=7){
									four+=(Long)map.get("count");
								}else if(i<=11){
									eight+=(Long)map.get("count");
								}else if(i<=15){
									twelve+=(Long)map.get("count");
								}else if(i<=19){
									sixteen+=(Long)map.get("count");
								}else if(i<=23){
									twenty+=(Long)map.get("count");
								}
							}
						}
				}
				//计算每个时段占的比例
				saleHistory.put("zero",new BigDecimal( (float)zero/ (float)count*100).setScale(2, BigDecimal.ROUND_DOWN).intValue());
				saleHistory.put("four",new BigDecimal( (float)four/ (float)count*100).setScale(2, BigDecimal.ROUND_DOWN).intValue());
				saleHistory.put("eight",new BigDecimal( (float)eight/ (float)count*100).setScale(2, BigDecimal.ROUND_DOWN).intValue());
				saleHistory.put("twelve",new BigDecimal( (float)twelve/ (float)count*100).setScale(2, BigDecimal.ROUND_DOWN).intValue());
				saleHistory.put("sixteen",new BigDecimal( (float)sixteen/ (float)count*100).setScale(2, BigDecimal.ROUND_DOWN).intValue());
				saleHistory.put("twenty",new BigDecimal( (float)twenty/ (float)count*100).setScale(2, BigDecimal.ROUND_DOWN).intValue());
			}else{
				saleHistory.put("zero",0);
				saleHistory.put("four",0);
				saleHistory.put("eight",0);
				saleHistory.put("twelve",0);
				saleHistory.put("sixteen",0);
				saleHistory.put("twenty",0);
			}
			orderMap.put("saleHistory", saleHistory);
			//查询消费品类分布
			orderMap.put("shopUnique", shopUnique);
			List<Map<String, Object>> goodsKindList=dao.findSaleKindList(orderMap);
			List<Map<String, Object>> saleKindTop3=new ArrayList<Map<String,Object>>();
			Integer goodsKindValueSum=0;
			for (Map<String, Object> map : goodsKindList) {
				Integer value=((Long) map.get("value2")).intValue();
				goodsKindValueSum+=value;
			}
			for (int i = 0; i < goodsKindList.size(); i++) {
				if(i<=2){
					Integer value=((Long)goodsKindList.get(i).get("value2")).intValue();
					goodsKindList.get(i).put("kind_val", new BigDecimal( (float)value/ (float)goodsKindValueSum*100).setScale(2, BigDecimal.ROUND_DOWN).intValue());
					saleKindTop3.add(goodsKindList.get(i));
				}
			}
			orderMap.put("saleKindTop3", saleKindTop3);
			//查询最喜欢的商品TOP3
			List<Map<String, Object>> goodsList=dao.findSaleGoodsList(orderMap);
			orderMap.put("saleGoodsTop3", goodsList);
		}else{
			orderMap=new HashMap<String, Object>();
			orderMap.put("saleListOrder", 0);
			orderMap.put("saleTotalSum", 0);
			orderMap.put("singlePrice", 0);
			orderMap.put("saleDay", -1);
			orderMap.put("saleHistory", new HashMap<String, Object>());
			orderMap.put("saleKindTop3", new ArrayList<Map<String, Object>>());
			orderMap.put("saleGoodsTop3", new ArrayList<Map<String, Object>>());
		}
		result.setStatus(1);
		System.out.println(orderMap);
		result.setData(orderMap);
		return result;
	}
	/**
     * 保留两位小数，四舍五入的一个老土的方法
     * @param d
     * @return
     */
    public  int formatDouble1(double d) {
        return new BigDecimal(d*100).setScale(2, BigDecimal.ROUND_DOWN).intValue();
    }
	public PalmResult editCusStatus(String cus_unique, Long shopUnique,String cus_status) {
		PalmResult result=new PalmResult();
		Map<String,Object> params=new HashMap<String, Object>();
		params.put("cus_unique", cus_unique);
		params.put("shopUnique", shopUnique);
		params.put("cus_status", cus_status);
		if(dao.editCusStatus(params)>0){
				result.setStatus(1);
				result.setMsg("修改成功");
		}else{
			result.setStatus(0);
			result.setMsg("修改失败");
		}
	return result;
	}
	public PalmResult editCusPassword(String cus_unique, Long shopUnique, String cus_password) {
		PalmResult result=new PalmResult();
		Map<String,Object> params=new HashMap<String, Object>();
		params.put("cus_unique", cus_unique);
		params.put("shopUnique", shopUnique);
		if("".equals(cus_password)||cus_password==null){
			params.put("cus_password", "");
		}else{
			params.put("cus_password", ShopsUtil.string2MD5(cus_password));
		}
		if(dao.editCusPassword(params)>0){
				result.setStatus(1);
				result.setMsg("修改成功");
		}else{
			result.setStatus(0);
			result.setMsg("修改失败");
		}
		return result;
	}
	public PalmResult editCusLevel(String cus_unique, Long shopUnique, Integer cus_level_id) {
		PalmResult result=new PalmResult();
		Map<String,Object> params=new HashMap<String, Object>();
		params.put("cus_unique", cus_unique);
		params.put("shopUnique", shopUnique);
		params.put("cus_level_id", cus_level_id);
		if(dao.editCusLevel(params)>0){
				result.setStatus(1);
				result.setMsg("修改成功");
		}else{
			result.setStatus(0);
			result.setMsg("修改失败");
		}
		return result;
	}
	@Override
	public PalmResult findCusByCusPhone(String cusPhone, Long shopUnique) {
		PalmResult result=new PalmResult();
		Map<String,Object> params=new HashMap<String, Object>();
		params.put("cusPhone", cusPhone);
		params.put("shopUnique", shopUnique);
		List<Map<String,Object>> custList= dao.findCusByCusPhone(params);
		if(custList!=null&&custList.size()>0){
			result.setStatus(1);
			if(custList.size()>=2){
				custList.get(0).put("cus_balance",custList.get(1).get("cus_balance"));
				custList.get(0).put("cusType", "会,储");

			}
			result.setData(custList.get(0));
		}else{
			result.setStatus(0);
		}
		return result;
	}
	@Override
	public Map<String, Object> queryCusSynchroStatus(String string) {
		//查询是否是连锁店铺需要会员同步会员保存到总店
		Map<String,Object> cusSynchroMap=dao.queryCusSynchroStatus(string);
		return cusSynchroMap;
	}
	@Override
	public ShopsResult getMemberLevel(Map<String, Object> map) {
		ShopsResult result = new ShopsResult();
		try {
			List<Map<String,Object>> list = dao.getMemberLevel(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setData(list);
		} catch (Exception e) {
			log.error("获取会员等级异常",e);
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	@Override
	public ShopsResult updateMemberLevel(Map<String, Object> map) {
		ShopsResult sr=new ShopsResult(1,"修改成功");
		Integer c=dao.updateMemberLevel(map);
		if(c==0){
			sr.setStatus(2);
			sr.setMsg("修改失败!");
		}
		return sr;
	}
	@Override
	public ShopsResult queryCusCheckOut(Map<String, Object> map) {
		ShopsResult result = new ShopsResult();
		try {
			List<Map<String,Object>> list = dao.queryCusCheckOut(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setData(list);
		} catch (Exception e) {
			log.error("获取会员等级异常",e);
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}
	@Override
	public ShopsResult queryCusRechargeLog(Map<String, Object> map) {
		ShopsResult result = new ShopsResult();
		try {
			List<Map<String,Object>> list = dao.queryCusRechargeLog(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setData(list);
		} catch (Exception e) {
			log.error("获取会员等级异常",e);
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}

}
