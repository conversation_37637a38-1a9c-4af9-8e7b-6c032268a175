package org.haier.shopUpdate.service;

import lombok.extern.slf4j.Slf4j;
import org.haier.shopUpdate.dao.*;
import org.haier.shopUpdate.entity.*;
import org.haier.shopUpdate.params.speech.QueryCommonSpeechListParams;
import org.haier.shopUpdate.util.*;
import org.haier.shopUpdate.util.unionpay.HttpUtil;
import org.haier.shopUpdate.params.speech.SysQueryParams;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.haier.shopUpdate.enums.ShopTitleEnum;
import org.haier.shopUpdate.enums.speech.SpeechAppTypeEnum;
import org.haier.shopUpdate.enums.speech.SpeechProgressEnum;
import org.haier.shopUpdate.enums.speech.SpeechValidTypeEnums;
import org.haier.shopUpdate.params.speech.AddNewSpeechEntityParams;
import org.haier.shopUpdate.wechat.AuthUtil;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
@Slf4j
@Service
public class SpeechServiceImpl implements SpeechService {
    @Resource
    private SpeechListDao speechListDao;
    @Resource
    private SpeechCmdDao speechCmdDao;
    @Resource
    private SpeechCmdPermissionDao speechCmdPermissionDao;
    @Resource
    private ShopTitleDao shopTitleDao;
    @Resource
    private SpeechSetDao speechSetDao;

    /**
     * 上传一条新的语音记录，创建并向服务器发送请求
     *
     * @param addNewSpeechEntityParams
     */
    public NewResultObject<SpeechListEntity> addNewSpeechEntity(AddNewSpeechEntityParams addNewSpeechEntityParams, HttpServletRequest request) throws Exception {
        MultipartFile file = ShopsUtil.testMulRequest(request, "file");
        System.out.println("请求的参数信息为" + addNewSpeechEntityParams);
        //语音和文字必须上传一种
        if (addNewSpeechEntityParams.getSpeechTextPhone() == null && file == null) {
            return NewResultObject.fail("语音和文字必须上传一种");
        }

        //判断是否有上传申请单号，如果没有，创建一个
        if (addNewSpeechEntityParams.getApplyNo() == null || addNewSpeechEntityParams.getApplyNo().equals("") || addNewSpeechEntityParams.getApplyNo().equals("null")) {
            addNewSpeechEntityParams.setApplyNo(ShopsUtil.UUID().replace("-",""));
        }
        //查询语音设置信息
        SpeechSetEntity speechSetEntity = speechSetDao.querySpeechSetEntity(new SpeechSetEntity());

        //查看是否开启文字验证，是否开启文件验证
        if (speechSetEntity.getTextIdentify() == 1 && (speechSetEntity.getFileIdentify() != 1 || file == null)) {
            //开启文字验证
            if (addNewSpeechEntityParams.getSpeechTextPhone() == null || addNewSpeechEntityParams.getSpeechTextPhone().equals("")) {
                return NewResultObject.fail("开启文字验证，必须上传文字");
            }
        }
        if (speechSetEntity.getFileIdentify() == 1 && (speechSetEntity.getTextIdentify() != 1 || addNewSpeechEntityParams.getSpeechTextPhone() == null)) {
            //开启文件验证
            if (file == null) {
                return NewResultObject.fail("开启文件验证，必须上传文件");
            }
        }

        String fileUrl = null;
        String fileBase64Text = null;
        String fileType = null;
        String recognitionType = "1";
        boolean fileFlag = false;
        if (file != null && speechSetEntity.getFileIdentify() == 1) {
            //需要将文件存储到文件服务器，并获取文件地址，此处文件名必须重置，防止不同线程的文件名称相同
            fileType = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1);
            fileBase64Text = ImageBinary.getImageStr(file.getInputStream());
            recognitionType = "2";
            fileFlag = true;
        }
        //如果文本存在，先调用文本，如果没有，直接调用文件
        if (addNewSpeechEntityParams.getSpeechTextPhone() != null && !addNewSpeechEntityParams.getSpeechTextPhone().equals("") && speechSetEntity.getTextIdentify() ==1 ) {
            recognitionType = "1";
        }
        //保存本次申请记录
        SpeechListEntity speechEntity = new SpeechListEntity();
        speechEntity.setAppType(addNewSpeechEntityParams.getAppType());
        speechEntity.setApplyNo(addNewSpeechEntityParams.getApplyNo());
        speechEntity.setShopUnique(addNewSpeechEntityParams.getShopUnique());
        speechEntity.setSpeechTextPhone(addNewSpeechEntityParams.getSpeechTextPhone());
        speechEntity.setProgress(SpeechProgressEnum.ACCEPTED.getCode());
        speechEntity.setCreateTime(DateCommon.getStringDate());
        SysQueryParams sysQueryParams = new SysQueryParams();
        sysQueryParams.setRecog_text(addNewSpeechEntityParams.getSpeechTextPhone());
        sysQueryParams.setRecognition_type(recognitionType);
        sysQueryParams.setApply_no(addNewSpeechEntityParams.getApplyNo());
        sysQueryParams.setFileType(fileType);
        sysQueryParams.setFileBase64Text(fileBase64Text);

        try {
            //申请服务器数据
            String jsonStr = JSON.toJSONString(sysQueryParams);
            Map<String, Object> hearder = new HashMap<String, Object>();
            hearder.put("Content-Type", "application/json");
            String result = HttpUtil.doPostStr(AuthUtil.SPEECH_SERVER_URL,hearder , jsonStr);
            boolean flag = true;
            String params = null;
            String cmdSys = null;
            String speechText = addNewSpeechEntityParams.getSpeechTextPhone();
            if (null == result || result.equals("")) {
                flag = false;
                //操作失败了，但是也要记录
                speechEntity.setErrorMsgSys("服务器请求失败");
                speechEntity.setProgress(SpeechProgressEnum.FAIL.getCode());
                speechListDao.addNewSpeechEntity(speechEntity);
                return NewResultObject.fail("服务器请求失败");
            }
            //获得了请求结果
            JSONObject jsonObject = JSONObject.parseObject(result);

            if (!jsonObject.containsKey("status") || jsonObject.getInteger("status") != 1 || jsonObject.getString("cmdId").equals("0")) {
                flag = false;
                //操作失败了，但是也要记录
                speechEntity.setErrorMsgSys(jsonObject.containsKey("msg") ? jsonObject.getString("msg") : "服务器请求失败");
                speechEntity.setProgress(SpeechProgressEnum.FAIL.getCode());

                //如果本次是文字请求，检查是否有文件，如果有文件，再请求一次带文件的
                if (fileFlag && recognitionType.equals("1") && speechSetEntity.getFileIdentify() == 1) {
                    flag = true;
                    recognitionType = "2";
                    //如果有文件识别的权限，重新申请一次
                    sysQueryParams.setRecognition_type(recognitionType);
                    String jsonStr2 = JSON.toJSONString(sysQueryParams);
                    String result2 = HttpUtil.doPostStr(AuthUtil.SPEECH_SERVER_URL,hearder , jsonStr2);
                    if (null == result2 || result2.equals("")) {
                        //操作失败了，但是也要记录
                        speechEntity.setErrorMsgSys("服务器请求失败");
                        speechEntity.setProgress(SpeechProgressEnum.FAIL.getCode());
                        speechListDao.addNewSpeechEntity(speechEntity);
                        return NewResultObject.fail("服务器请求失败");
                    } else {
                        //
                        JSONObject jsonObject2 = JSONObject.parseObject(result2);
                        if (!jsonObject2.containsKey("status") || jsonObject2.getInteger("status") != 1) {
                            flag = false;
                            //操作失败了，但是也要记录
                            speechEntity.setErrorMsgSys(jsonObject.containsKey("msg") ? jsonObject.getString("msg") : "服务器请求失败");
                            speechEntity.setProgress(SpeechProgressEnum.FAIL.getCode());
                        } else {
                            //请求成功了
                            //成功
                            params = jsonObject.getString("data");
                            cmdSys = jsonObject.getString("cmdId");
                            speechText = jsonObject.getString("speechText");
                        }
                    }
                }
            } else {
                //成功
                params = jsonObject.getString("data");
                cmdSys = jsonObject.getString("cmdId");
                speechText = jsonObject.getString("speechText");
            }
            //保存申请结果
            String cmdId = null;
            if (flag) {
                //根据结果判定成功或者失败
                SpeechCmdEntity speechCmdEntityPar = new SpeechCmdEntity();
                speechCmdEntityPar.setCmdSys(cmdSys);

                SpeechCmdEntity speechCmdEntity = speechCmdDao.querySpeechCmdEntity(speechCmdEntityPar);
                if (speechCmdEntity != null) {
                    speechEntity.setProgress(SpeechProgressEnum.SUCCESS.getCode());
                    speechEntity.setSpeechTextSys(speechText);
                    speechEntity.setCmdType(speechCmdEntity.getCmdType());
                    speechEntity.setPageIndex(speechCmdEntity.getPageIndex());
                    speechEntity.setParams(params);
                    speechEntity.setCmdSys(cmdSys);
                    cmdId = speechCmdEntity.getId().toString();
                    //根据结果，判断用户是否有权限，
                    boolean checkPower = checkStaffPower(addNewSpeechEntityParams.getAppType(), addNewSpeechEntityParams.getShopUnique(), addNewSpeechEntityParams.getStaffId(), cmdId);
                    if (!checkPower) {
                        speechEntity.setErrorMsgSys("没有操作权限");
                        speechEntity.setProgress(SpeechProgressEnum.FAIL.getCode());
                    } else {
                        //成功
                        speechEntity.setErrorMsgSys("查询成功！");
                        speechEntity.setProgress(SpeechProgressEnum.SUCCESS.getCode());
                    }

                } else {
                    //返回了當前未记录的命令，记录并返回前端失败
                    speechEntity.setErrorMsgSys("未找到匹配指令");
                    speechEntity.setSpeechTextSys(speechText);
                    speechEntity.setProgress(SpeechProgressEnum.FAIL.getCode());
                }
            } else {
                speechEntity.setProgress(SpeechProgressEnum.FAIL.getCode());
                speechEntity.setErrorMsgSys("未找到匹配指令");
            }
        } catch (Exception e) {
            log.error("异常",e);
            //失败了，记录状态为失败，并返回失败
            speechEntity.setErrorMsgSys("服务器请求失败");
            speechEntity.setProgress(SpeechProgressEnum.FAIL.getCode());
        }
        //保存当前记录
        speechEntity.setCompleteTime(DateCommon.getStringDate());
        speechListDao.addNewSpeechEntity(speechEntity);
        if (recognitionType.equals("1")) {
            speechEntity.setSpeechTextSys(speechEntity.getSpeechTextPhone());
        }
        return NewResultObject.success(speechEntity);
    }

    /**
     *
     * @param staffId
     * @param cmdId
     * @param appType
     * @param shopUnique
     * @return
     */
    public boolean checkStaffPower(String appType, Long shopUnique, Long staffId, String cmdId) {
        //获取当前员工的所有权限信息
        if (appType.equals(SpeechAppTypeEnum.BUYHOOAPPFORFARM.getCode()) || appType.equals(SpeechAppTypeEnum.BUYHOOAPPFORFOOR.getCode())
                || appType.equals(SpeechAppTypeEnum.BUYHOOAPPFORSHOP.getCode())) {
            //如果是商家端，只需要判断店铺是否有次权限即可
            SpeechCmdPermissionEntity speechCmdPermissionEntityParams= new SpeechCmdPermissionEntity();
            speechCmdPermissionEntityParams.setAppType(appType);
            speechCmdPermissionEntityParams.setCmdId(cmdId);

            SpeechCmdPermissionEntity speechCmdPermissionEntity = speechCmdPermissionDao.querySpeecCmdPermissionEntity(speechCmdPermissionEntityParams);
            //如果没有记录，则认为有权限
            if (speechCmdPermissionEntity == null || speechCmdPermissionEntity.getValidType() == SpeechValidTypeEnums.INVALID.getCode()) {
                return true;
            }

            //如果有记录，需要判定该店铺是否有次权限
            String modularNum = speechCmdPermissionEntity.getModularNum();
            ShopTitleEntity shopTitleEntityParams = new ShopTitleEntity();
            shopTitleEntityParams.setShopUnique(shopUnique);
            shopTitleEntityParams.setModularNum(modularNum);

            ShopTitleEntity shopTitleEntity = shopTitleDao.queryShopTitleEntity(shopTitleEntityParams);
            if (shopTitleEntity == null || shopTitleEntity.getValidType() == ShopTitleEnum.INVALID.getCode()) {
                //没有次权限，或者已失效，提示用户没权限
                return false;
            }
            //有权限
            return true;
        } else if (appType.equals(SpeechAppTypeEnum.JINQUANYUNSHANG.getCode()) || appType.equals(SpeechAppTypeEnum.JINQUANYUNSHANG_.getCode())) {
            //如果是金圈云商
        }
        return true;
    }

    /**
     * 查询常用语音列表
     * @param queryCommonSpeechListParams
     * @return
     */
    public NewResultObject<List<SpeechCmdEntity>> queryCommonSpeechList(QueryCommonSpeechListParams queryCommonSpeechListParams){
        //查询五个本店常用的命令，如果没有，就查询全部
        List<SpeechCmdEntity> list = speechCmdDao.queryCommonSpeechList(queryCommonSpeechListParams);
        if (list == null || list.size() <= queryCommonSpeechListParams.getPageSize()) {
            //返回的数量不足，查询全部的
            Integer size = list == null ? 0 : list.size();
            queryCommonSpeechListParams.setPageSize(queryCommonSpeechListParams.getPageSize() - size);
            queryCommonSpeechListParams.setShopUnique(null);
            List<String> cmdList = new ArrayList<>();
            for (SpeechCmdEntity speechCmdEntity : list) {
                cmdList.add(speechCmdEntity.getCmdSys());
            }
            if (null != cmdList && cmdList.size() > 0) {
                queryCommonSpeechListParams.setList(cmdList);
            }
            List<SpeechCmdEntity> list2 = speechCmdDao.queryCommonSpeechList(queryCommonSpeechListParams);
            list.addAll(list2);
            return NewResultObject.success(list);
        }
        return NewResultObject.success(list);
    }
}
