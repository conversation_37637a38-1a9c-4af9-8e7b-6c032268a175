package org.haier.shopUpdate.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import org.haier.shopUpdate.dao.GoodsDao;
import org.haier.shopUpdate.dao.InventoryDao;
import org.haier.shopUpdate.dao.StockDao;
import org.haier.shopUpdate.dao.dojo.QueryGoodsInventoryDo;
import org.haier.shopUpdate.entity.GoodsEntity;
import org.haier.shopUpdate.entity.Inventory;
import org.haier.shopUpdate.entity.InventoryBwDetail;
import org.haier.shopUpdate.entity.InventoryDetail;
import org.haier.shopUpdate.redism.RedisCache;
import org.haier.shopUpdate.util.InventorySmDetail;
import org.haier.shopUpdate.util.ShopsResult;
import org.haier.shopUpdate.util.ShopsUtil;
import org.haier.shopUpdate.util.common.CommonResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
@Transactional
public class InventoryServiceImpl implements InventoryService {
    @Resource
    private InventoryDao invenDao;
    @Resource
    private StockDao stockDao;

    @Resource
    private GoodsDao goodsDao;
    @Resource
    private RedisCache redis;
    @Resource
    private GoodsPositionServiceImpl goodsPositionService;

    /**
     * 各状态的订单数量查询
     *
     * @param map
     * @return
     */
    public ShopsResult queryInvertoryCountByType(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        List<Map<String, Long>> data = invenDao.queryInvertoryCountByType(map);
        if (null == data || data.isEmpty()) {
            sr.setStatus(2);
            sr.setMsg("没有满足条件的信息！");
            return sr;
        }
        Long all = 0l;
        for (int i = 0; i < data.size(); i++) {
            all += data.get(i).get("invenCount");
        }
        Map<String, Long> mp = new HashMap<String, Long>();
        mp.put("invenType", 0l);
        mp.put("invenCount", all);
        sr.setStatus(1);
        sr.setMsg("查询成功！");
        data.add(mp);
        sr.setData(data);
        return sr;
    }

    /**
     * 盘点列表的信息查询
     *
     * @param map
     * @return
     */
    public ShopsResult queryInventoryList(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        try {
            List<Inventory> data = invenDao.queryInventoryList(map);
            if (null == data || data.isEmpty()) {
                sr.setStatus(2);
                sr.setMsg("没有满足条件的信息！");
                return sr;
            }
            for (Inventory item : data) {
                if (item.getListDetail() != null && item.getListDetail().size() > 0) {
                    //商品种类
                    item.setGoodsCount(item.getListDetail().size());
                    if (item.getListDetail().size() > 10) {
                        List<InventoryDetail> list = new ArrayList<InventoryDetail>();
                        for (int i = 0; i < 10; i++) {
                            list.add(item.getListDetail().get(i));
                        }
                        item.setListDetail(list);
                    }
                } else {
                    item.setGoodsCount(0);
                }


            }
            sr.setStatus(1);
            sr.setMsg("查询成功！");
            sr.setData(data);
        } catch (Exception e) {

            sr.setStatus(2);
            sr.setMsg("系统异常,请联系管理员！");
        }
        return sr;
    }

    /**
     * 盘点详情
     *
     * @param map
     * @return
     */
    public ShopsResult queryInventoryDetail(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();

        Inventory data = invenDao.queryInventoryDetail(map);

        sr.setStatus(1);
        sr.setMsg("查询成功！");
        sr.setData(data);
        return sr;
    }


    /**
     * 创建新的盘点草稿
     *
     * @param inven
     * @return
     */
    public ShopsResult createNewInvenDraft(Inventory inven) {
        ShopsResult sr = new ShopsResult();

        int k = invenDao.createNewInvenDraft(inven);
        if (k == 0) {
            sr.setStatus(2);
            sr.setMsg("创建失败！");
            return sr;
        }
        sr.setStatus(1);
        sr.setMsg("创建成功！");
        sr.setData(inven.getId());
        return sr;
    }

    /**
     * 盘库-扫码查询商品库存信息
     *
     * @param map
     * @return
     */
    public CommonResult queryGoodsByBarcode(Map<String, Object> map) {
        CommonResult sr = new CommonResult();

        if (map.containsKey("shopUnique") && map.get("shopUnique") != null) {
//			Integer stockType = checkStockType(map.get("shopUnique").toString());

            InventorySmDetail data = invenDao.queryGoodsByBarcode(map);

            if (null == data) {
                sr.setStatus(2);
                sr.setMsg("本店暂无该商品库存信息！");
                return sr;
            }

            if (data.getEndTime() != null && !data.getEndTime().equals("")) {
                map.put("endTime", data.getEndTime());
                InventoryBwDetail data2 = invenDao.queryBoundCount(map);
                if (data2 != null) {
                    data.setInBoundCount("入库" + data2.getInBoundCount() + "(手动" + data2.getInHandCount() + ",订单" + data2.getInOrderCount() + ")");

                    data.setOutBoundCount("出库" + data2.getOutBoundCount() + "(手动" + data2.getOutHandCount() + ",订单" + data2.getOutOrderCount() + ")");
                } else {
                    data.setInBoundCount("0");
                    data.setOutBoundCount("0");
                }
            } else {
                data.setInBoundCount("0");
                data.setOutBoundCount("0");
            }
            sr.setMsg("查询成功！");
            sr.setStatus(1);
            sr.setData(data);
        } else {
            sr.setMsg("参数异常！");
            sr.setStatus(0);
        }


        return sr;
    }


    public Integer checkStockType(String shopUnique) {
        Integer stockType = 2;

        Object type = redis.getObject("stockType" + shopUnique);
        if (null == type) {
            Map<String, Object> map = stockDao.queryShopSetting(shopUnique);
            if (null == map || map.isEmpty()) {
                stockType = 2;
            } else {
                stockType = Integer.parseInt(map.get("is_stock_type").toString());
            }
            //从数据库获取
            redis.putObject("stockType" + shopUnique, stockType, 3600 * 8);
        } else {
            stockType = Integer.parseInt(type.toString());
        }

        System.out.println("长哦的当前的库存类型" + stockType);

        return stockType;
    }

    /**
     * 扫码查询商品库存信息
     *
     * @param map
     * @return
     */
    public ShopsResult queryGoodsByGoodsMessage(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        List<Map<String, Object>> data = null;
        //判断是否为已提交的信息，若已提交则不允许修改
        Integer stockType = checkStockType(map.get("shopUnique").toString());

        //获取店铺盘点类型
        data = invenDao.queryGoodsByGoodsMessage(map);
        if (null == data || data.isEmpty()) {
            sr.setStatus(2);
            sr.setMsg("本店暂无该商品库存信息！");
            return sr;
        }
        for (Integer i = 0; i < data.size(); i++) {
            data.get(i).put("stockType", stockType);
        }
        sr.setStatus(1);
        sr.setMsg("查询成功！");
        sr.setData(data);
        return sr;
    }

    /**
     * 添加或更新盘点记录
     *
     * @param map
     * @return
     */
    public CommonResult addNewGoodsStockRecord(Map<String, Object> map, String goodsBarcode) {

        CommonResult sr = new CommonResult();
        try {
            Double count = Double.parseDouble(map.get("invenCount").toString());//盘点数
            Double goodsCount = Double.parseDouble(map.get("goodsCount").toString());//盘点数
            Integer stockType = Integer.parseInt(map.get("stockType").toString());
            if (!map.containsKey("invenId") || map.get("invenId") == null) {
                Inventory inven = new Inventory();
                inven.setShopUnique(Long.parseLong(map.get("shopUnique").toString()));
                inven.setStaffId(Integer.parseInt(map.get("staffId").toString()));


                int k = invenDao.createNewInvenDraft(inven);
                if (k > 0) {
                    map.put("invenId", inven.getId());
                    sr.setData(inven.getId());
                } else {
                    sr.setStatus(2);
                    sr.setMsg("系统异常,请联系管理员！");
                    return sr;
                }

                if (stockType == 1) {
                    map.put("add_count", count);//实际变化的数量
                    map.put("afterInventoryCount", count + goodsCount);//盘后数
                } else {
                    map.put("add_count", count - goodsCount);//实际变化的数量
                    map.put("afterInventoryCount", count);//盘后数
                }

                k = invenDao.addNewGoodsStockRecord(map);
                if (k > 0) {
                    sr.setStatus(1);
                    sr.setMsg("添加成功！");
                    return sr;
                }
            }


            Map<String, Object> m = invenDao.queryInvenStatus(map);
//			if(m==null){
//				sr.setStatus(2);
//				sr.setMsg("该盘点单号不存在");
//				return sr;
//			}
//			Integer stockType = Integer.parseInt(m.get("stockType").toString());

            // 查询老的盘库记录
//			Map<String,Object> m=invenDao.queryInvenDetailByCode(map);
            long k = (Long) m.get("invenType");
            if (k == 2) {
                sr.setStatus(2);
                sr.setMsg("该盘点订单已提交，禁止修改");
                return sr;
            }


            //获取商品的价格,盘点按商品的售价计算
            //此处需要根据增量或直接修改改变数值
            Double price = invenDao.queryGoodsSalePriceForInven(map);
            if (price == null) {
                price = 0.0;
            }
            //增量
            if (stockType == 1) {
                System.out.println("增量修改");
                map.put("profitLoss", price * count);
//				map.put("invenCount", count + goodsCount);
                map.put("add_count", count);//实际变化的数量
                map.put("afterInventoryCount", count + goodsCount);//盘后数
            } else {
                //直接修改
                System.out.println("直接修改");
                map.put("profitLoss", price * (count - goodsCount));
//				map.put("invenCount", count);
                map.put("add_count", count - goodsCount);//实际变化的数量
                map.put("afterInventoryCount", count);//盘后数

            }

            k = invenDao.updateGoodsStockRecord(map);
            if (k == 0) {
                k = invenDao.addNewGoodsStockRecord(map);
            }
            if (k > 0) {
                /**
                 * 更新详情后，更新盘库草稿信息
                 */
                Map<String, Object> d = invenDao.queryDetailCount(map);
                k = invenDao.updateInventoryCount(d);

                sr.setStatus(1);
                sr.setMsg("更新成功！");
            } else {

                sr.setStatus(2);
                sr.setMsg("系统异常,请联系管理员！");
            }


        } catch (Exception e) {

            sr.setStatus(2);
            sr.setMsg("系统异常,请联系管理员！");
        }

        return sr;
    }

    /**
     * 提交盘点订单
     *
     * @param map
     * @param stockOrigin 1、操作源为手机；2、操作源头为PC端；3、操作源头为网页；
     * @return
     */
    public CommonResult subInventory(Map<String, Object> map, Integer staffId, Integer stockOrigin) {
        CommonResult sr = new CommonResult();
        try {
            Map<String, Object> m = invenDao.queryInvenStatus(map);
            //需要防止重复提交
            if (m == null) {
                sr.setStatus(2);
                sr.setMsg("该盘点单号不存在");
                return sr;
            }
            long k = (Long) m.get("invenType");
            if (k == 2) {
                sr.setStatus(2);
                sr.setMsg("该盘点订单已提交，禁止修改");
                return sr;
            }
            //将盘点订单状态更新
            //根据员工编号获取店铺类型,如果未设置，默认为直接修改数量
//		Integer stockType = invenDao.queryStockTypeByStaffId(staffId + "");
//		if(null == stockType) {
//			stockType = 2;
//		}
            //查询盘点详情，用于更新
//		map.put("stockType", stockType);
            List<Map<String, Object>> list = invenDao.queryDetailForUpdate(map);
            //盘点盈亏总商品数量
            Double counts = 0.0;
            //盘点盈亏额
            Double amounts = 0.0;
            //盘点实际商品总数量
            Double acounts = 0.0;
            for (int i = 0; i < list.size(); i++) {
                Map<String, Object> data = list.get(i);
                Double count = null;
                count = Double.parseDouble(data.get("invenCount").toString());
                counts = ShopsUtil.addDoubleSum(counts, count);
                Double amount = Double.parseDouble(data.get("profitLoss").toString());
                System.out.println("当前盈亏" + amount + "===" + amounts);
                amounts = ShopsUtil.addDoubleSum(amounts, amount);
                Double acount = Double.parseDouble(data.get("inventoryCount").toString());
                acounts = ShopsUtil.addDoubleSum(acount, acounts);
            }
            map.put("invenCount", counts);
            map.put("invenAmount", amounts);
            map.put("inventoryAcount", acounts);
            k = invenDao.updateGoodsInven(map);
            k = invenDao.updateGoodsStock(list);
            sr.setStatus(1);
            sr.setMsg("提交成功！");
            //根据实际数量做出入库比对
            List<Map<String, Object>> stock = new ArrayList<Map<String, Object>>();
            for (int i = 0; i < list.size(); i++) {
                boolean flag = true;
                Map<String, Object> p = list.get(i);
                Map<String, Object> d = new HashMap<String, Object>();
                d.put("goodsBarcode", p.get("goodsBarcode"));
                //盘库模式： 1 累加 2 覆盖
                Integer stockType = Integer.parseInt(p.get("stockType").toString());
                d.put("listUnique", m.get("id"));
                d.put("shopUnique", p.get("shopUnique"));
                d.put("stockResource", 4);
                d.put("stockOrigin", stockOrigin == null ? 1 : stockOrigin);
                Double invenCount = Double.parseDouble(p.get("invenCount").toString());
                Double inventoryCount = Double.parseDouble(p.get("inventoryCount").toString());
                staffId = (staffId != null) ? staffId : Integer.parseInt(p.get("staffId").toString());
                d.put("staffId", staffId);
                d.put("stockCount", p.get("afterInventoryCount"));//实际库存，即修改后库存
                if (stockType == 2) {
                    //2 覆盖
                    d.put("goodsCount", Math.abs(invenCount));//操作数量
                    if (invenCount > 0) {//入库
                        d.put("stockType", 1);
                        d.put("stockPrice", p.get("goodsInPrice"));
                    } else if (invenCount < 0) {//出库
                        d.put("stockType", 2);
                        d.put("stockPrice", p.get("goodsSalePrice"));
                    } else {
                        flag = false;
                    }
                } else {
                    //累加模式
                    d.put("goodsCount", Math.abs(inventoryCount));//操作数量
                    if (inventoryCount > 0) {//入库
                        d.put("stockType", 1);
                        d.put("stockPrice", p.get("goodsInPrice"));
                    } else if (inventoryCount < 0) {//出库
                        d.put("stockType", 2);
                        d.put("stockPrice", p.get("goodsSalePrice"));
                    } else {
                        flag = false;
                    }
                }


                if (flag) {
                    stock.add(d);
                }
            }
            //批量添加入库单
//		System.out.println(stock);
            if (stock.size() > 0) {
                stockDao.newStockRecords(stock, null);
            }
        } catch (Exception e) {

            sr.setStatus(2);
            sr.setMsg("系统异常,请联系管理员！");
        }
        return sr;
    }


    /**
     * 某一商品的盘点详情查询
     *
     * @param map
     * @return
     */
    public ShopsResult queryGoodsInvenRecord(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        List<Map<String, Object>> data = invenDao.queryGoodsInvenRecord(map);

        if (null == data || data.isEmpty()) {
            sr.setStatus(2);
            sr.setMsg("咱无该商品的盘点信息！");
            return sr;
        }
        sr.setStatus(1);
        sr.setMsg("查询成功！");
        sr.setData(data);
        return sr;
    }

    /**
     * 编辑界面，删除勾选商品
     *
     * @return
     */
    public ShopsResult deleteInvenGoods(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        Map<String, Object> m = invenDao.queryInvenStatus(map);
        //需要防止重复提交
        if (m == null) {
            sr.setStatus(2);
            sr.setMsg("该盘点单号不存在");
            return sr;
        }
        long k = (Long) m.get("invenType");
        if (k == 2) {
            sr.setStatus(2);
            sr.setMsg("该盘点订单已提交，禁止修改");
            return sr;
        }

        k = invenDao.deleteInvenGoods(map);
        if (k == 0) {
            sr.setStatus(2);
            sr.setMsg("您所选中的商品已删除或不存在!");
            return sr;
        }
        sr.setStatus(1);
        sr.setMsg("编辑成功！");
        return sr;
    }

    /**
     * 删除未提交的盘点订单及详情
     *
     * @param map
     * @return
     */
    public ShopsResult deleteDraftLists(Map<String, Object> map) {
        ShopsResult sr = new ShopsResult();
        int k = invenDao.deleteDraftLists(map);
        if (k == 0) {
            sr.setStatus(2);
            sr.setMsg("没有可删除的信息！");
            return sr;
        }
        k = invenDao.deleteDrafListDetail(map);
        sr.setStatus(1);
        sr.setMsg("删除成功！");
        return sr;
    }

    @Override
    public CommonResult queryGoodsByBarcodeCurrent(Long shopUnique, String goodsBarcode) {
        Map<String, Object> goodsInfo = goodsDao.queryGoodsByGoodsBarcode(shopUnique, goodsBarcode);
        BigDecimal goodsContain =new BigDecimal(goodsInfo.get("goodsContain").toString());
        BigDecimal goodsCount = BigDecimal.ZERO;
        System.out.println("当前商品的兑换比例为" + goodsContain);
        System.out.println(goodsContain.compareTo(BigDecimal.ONE) == 0);
        if (goodsContain.compareTo(BigDecimal.ONE) != 0) {
            //由于商品出入库不能立即修改库存，所以此处需要重新计算库存
            GoodsEntity goods = new GoodsEntity();
            goods.setShopUnique(shopUnique);
            goods.setForeignKey(Long.parseLong(goodsInfo.get("foreignKey").toString()));
            goods.setGoodsContain(BigDecimal.ONE);

            GoodsEntity goodsEntity = goodsDao.selectOneByParam(goods);
            Integer goodsChengType = goodsEntity.getGoodsChengType();
            System.out.println("最小规格的商品信息" + goodsEntity);
            if (goodsChengType == 0) {
                //按件商品
                goodsCount = goodsEntity.getGoodsCount().divide(goodsContain).setScale(0, BigDecimal.ROUND_DOWN);
            } else {
                goodsCount = goodsEntity.getGoodsCount().divide(goodsContain, 2, BigDecimal.ROUND_DOWN);
            }
            System.out.println("计算得出的库存为" + goodsCount);
        } else {
            goodsCount = new BigDecimal(goodsInfo.get("goodsCount").toString());
        }

        // 上次盘库数据
        Map<String, Object> inventoryInfo = invenDao.queryInventoryByGoodsBarcode(shopUnique, goodsBarcode);

        QueryGoodsInventoryDo queryGoodsInventoryDo = new QueryGoodsInventoryDo();
        if (null != goodsInfo) {
            queryGoodsInventoryDo.setUnit(String.valueOf(goodsInfo.get("unit")));
            queryGoodsInventoryDo.setGoodsCount(goodsCount);
            queryGoodsInventoryDo.setGoodsInPrice(new BigDecimal(String.valueOf(goodsInfo.get("goodsInPrice"))));
            queryGoodsInventoryDo.setGoodsPicturePath(String.valueOf(goodsInfo.get("goodsPicturePath")));
            queryGoodsInventoryDo.setGoodsName(String.valueOf(goodsInfo.get("goodsName")));
            queryGoodsInventoryDo.setGoodsBarcode(String.valueOf(goodsInfo.get("goodsBarcode")));
            queryGoodsInventoryDo.setGoodsStandard(String.valueOf(goodsInfo.get("goodsStandard")));
            queryGoodsInventoryDo.setGoodsSalePrice(new BigDecimal(String.valueOf(goodsInfo.get("goodsSalePrice"))));
            queryGoodsInventoryDo.setGoodsPosition(ObjectUtil.isEmpty(goodsInfo.get("goodsPosition"))?"":String.valueOf(goodsInfo.get("goodsPosition")));
            if (StrUtil.isNotEmpty(queryGoodsInventoryDo.getGoodsPosition())) {
                String goodsPosition = queryGoodsInventoryDo.getGoodsPosition();
                Map<String, String> stringStringMap = goodsPositionService.queryDetailByPositionId(Lists.newArrayList(goodsPosition));
                queryGoodsInventoryDo.setCompletePositionName(stringStringMap.get(goodsPosition));
            }

        }
        if (null != inventoryInfo) {
            queryGoodsInventoryDo.setEndTime(String.valueOf(inventoryInfo.get("endTime")));
            queryGoodsInventoryDo.setInventoryCount(new BigDecimal(String.valueOf(inventoryInfo.get("inventoryCount"))));
            queryGoodsInventoryDo.setDayCount(new BigDecimal(String.valueOf(inventoryInfo.get("dayCount"))));
        }
        //新盘点不再需要inventoryCount，有bug，前段升级还要发市场，后端修改下
        queryGoodsInventoryDo.setInventoryCount(BigDecimal.ZERO);
        CommonResult cr = new CommonResult();
        cr.setStatus(1);
        cr.setData(queryGoodsInventoryDo);
        return cr;
    }
}
