package org.haier.shopUpdate.service;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.haier.shopUpdate.config.ElectronicConfig;
import org.haier.shopUpdate.entity.electronic.CreateOrUpdateGoodsListParams;
import org.haier.shopUpdate.entity.electronic.CreateOrUpdateGoodsParams;
import org.haier.shopUpdate.entity.electronic.DeleteGoodsParams;
import org.haier.shopUpdate.util.unionpay.HttpUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class BHServiceImpl implements BHService{

    @Resource
    private ElectronicConfig electronicConfig;
    /**
     * 创建或修改单个商品
     * @param createOrUpdateGoodsParams
     */
    public void createOrUpdateGoods(CreateOrUpdateGoodsParams createOrUpdateGoodsParams) {
        //修改商品或者新建商品后，同步商品信息到电子价签

        try {
            Map<String, Object> hearder = new HashMap<String, Object>();
            hearder.put("Content-Type", "application/json");
            String json = JSON.toJSONString(createOrUpdateGoodsParams);
            HttpUtil.doPostStr(electronicConfig.getBaseurl() + electronicConfig.getSameSigleGoods(), hearder, json);
        } catch (Exception e) {
            log.info("同步商品信息到电子价签失败!");
        }
    }

    /**
     * 批量同步商品信息到电子价签
     * @param createOrUpdateGoodsListParams
     */
    public void createOrUpdateGoodsList(CreateOrUpdateGoodsListParams createOrUpdateGoodsListParams) {
        try {
            Map<String, Object> hearder = new HashMap<String, Object>();
            hearder.put("Content-Type", "application/json");
            String json = JSON.toJSONString(createOrUpdateGoodsListParams);
            HttpUtil.doPostStr(electronicConfig.getBaseurl() + electronicConfig.getCreateOrUpdateGoodsList(), hearder, json);
        } catch (Exception e) {
            log.info("同步商品信息到电子价签失败!");
            e.printStackTrace();
        }
    }


    /**
     * 删除商品同步到电子价签
     * @param deleteGoodsParams
     */
    public void deleteGoods(DeleteGoodsParams deleteGoodsParams) {
        try {
            Map<String, Object> hearder = new HashMap<String, Object>();
            hearder.put("Content-Type", "application/json");
            String json = JSON.toJSONString(deleteGoodsParams);
            HttpUtil.doPostStr(electronicConfig.getBaseurl() + electronicConfig.getDeleteGoods(), hearder, json);
        } catch (Exception e) {
            log.info("同步商品信息到电子价签失败!");
            e.printStackTrace();
        }
    }
}
