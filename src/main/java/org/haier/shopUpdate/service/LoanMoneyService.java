package org.haier.shopUpdate.service;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.haier.shopUpdate.util.ShopsResult;

public interface LoanMoneyService {

	
	/**
	 * 查询订单还款列表详情
	 * @param shop_unique 店铺编号
	 * @param order_no 订单编号
	 * @param page 页码
	 * @param page_size 单页查询数量
	 * @return
	 */
	public ShopsResult queryOrderFenqiList(String shop_unique,String order_no,Integer page,Integer page_size);
	/**
	 * 提交店铺赊销申请信息
	 * @param shop_unique 店铺编号
	 * @param business_image 营业执照照片路径
	 * @param business_image_hand 手持营业执照照片路径
	 * @param ID_front_image 身份证正面照片
	 * @param ID_back_image 身份证反面照片（人脸面）
	 * @param ID_back_image_hand 手持身份证人脸面照片
	 * @param bank_front_image 银行卡正面照
	 * @param bank_back_image 银行卡反面照
	 * @param shop_doorhead 门头照
	 * @param shop_cashier 店内收银台照片
	 * @param loan_sign 签名信息
	 * @param shop_inside 店内照片
	 * @return
	 */
	public ShopsResult addOpenLoanNew(String shop_unique,String business_image,String business_image_hand,
			String ID_front_image,String ID_back_image,	String ID_back_image_hand,String bank_front_image,String bank_back_image,
			String shop_doorhead,String shop_cashier,String loan_sign, String shop_inside
			);
	/**
	 * 1统计店铺的赊销未还款情况
	 * @param shop_unique
	 * @return
	 */
	public Map<String,Object> queryShopLoanMsg(String shop_unique);
	public Map<String, Object> queryIsOpenLoan(String shop_unique);

	public ShopsResult addOpenLoan(Map<String, Object> map, HttpServletRequest request);

	public void saveReturnMoney(Map<String, Object> params);

	/**
	 * 提前还款处理还款订单详情
	 * @param out_trade_no 还款生成的还款单号
	 * @param order_no 需要处理的赊销订单编号
	 * @param total_fee 还款总金额
	 * @param shop_unique 店铺编号
	 */
	public void updateReturnMoney(String out_trade_no,String order_no,String total_fee,String shop_unique);

	public ShopsResult queryOrderStatus(Map<String, Object> map);

	public ShopsResult queryLoanReturnList(Map<String, Object> map);

	public ShopsResult queryLoanList(Map<String, Object> map);

	public Map<String, Object> queryAdvanceMoney(String shop_unique);

	/*
	 * 1、查询赊销的平台设置信息
	 */
	public Map<String,Object> querySxPolicySet();
	public Map<String, Object> queryLoanMoneyRate(String shop_unique);
	
}
