package org.haier.shopUpdate.service;

import org.haier.shopUpdate.params.GetAuditStatusParams;
import org.haier.shopUpdate.params.GetQualificationsParams;
import org.haier.shopUpdate.params.SaveAggregationCodeParams;
import org.haier.shopUpdate.util.ShopsResult;

public interface AggregationCodeService {

    /**
     * 查询聚合码审核状态
     * @param params
     * @return
     */
    public ShopsResult getAuditStatus(GetAuditStatusParams params);

    /**
     * 查询资质信息
     * @param params
     * @return
     */
    public ShopsResult getQualifications(GetQualificationsParams params);

    /**
     * 保存资质信息
     * @param params
     * @return
     */
    public ShopsResult saveAggregationCode(SaveAggregationCodeParams params);

}
