package org.haier.shopUpdate.service;

import org.haier.shopUpdate.params.restockPlan.*;
import org.haier.shopUpdate.util.ShopsResult;

public interface ShopsRestockPlanService {

	ShopsResult addRestockPlan(AddRestockPlanParams params);
	ShopsResult deleteRestockPlan(DeleteRestockPlanParams params);
	ShopsResult queryRestockPlanList(RestockPlanListParams params);
	ShopsResult queryGoodsListByPlanId(RestockPlanGoodsParams params);
	ShopsResult addRestockPlanGoods(RestockPlanGoodsAddParams params);
	ShopsResult updatePlanStatus(UpdateStatusParams params);
	ShopsResult getPresentListByPlanId(QueryGoodsListGroupBySupplierParams params);
	ShopsResult updateSupplier(RestockPlanGoodsUpdateSupplierParams params);
	ShopsResult getGoodsSupplierMsg(GetGoodsSupplierMsgParams params);
	ShopsResult modifyRestockPlanGoods(ModifyGoodsParams params);
	ShopsResult queryGoodsListBySupplierId(QueryGoodsBySupplierParams params);
	ShopsResult queryGoodsDetail(QueryGoodsDetailParams params);
	ShopsResult updateRestockPlanSupplier(RestockPlanSupplierUpdateParams params);
	ShopsResult restockAgain(DeleteRestockPlanParams params);

}
