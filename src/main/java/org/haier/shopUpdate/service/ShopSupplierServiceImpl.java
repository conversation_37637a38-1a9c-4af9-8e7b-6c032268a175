package org.haier.shopUpdate.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.haier.shopUpdate.dao.GoodsDao;
import org.haier.shopUpdate.dao.ShopSupBillDao;
import org.haier.shopUpdate.dao.ShopSupplierDao;
import org.haier.shopUpdate.dao.ShopsRestockPlanDao;
import org.haier.shopUpdate.entity.*;
import org.haier.shopUpdate.params.shopSupBill.AddSupRelParams;
import org.haier.shopUpdate.params.shopSupBill.BillIdParams;
import org.haier.shopUpdate.params.shopSupBill.UpdateBillStatusParams;
import org.haier.shopUpdate.params.shopSupBill.externalCall.*;
import org.haier.shopUpdate.params.shopSupBill.supExternalCall.CustomerGoodsBingParams;
import org.haier.shopUpdate.params.shopSupplier.*;
import org.haier.shopUpdate.params.shopSupplier.paramsDao.QuerySupKindInfoByKuParams;
import org.haier.shopUpdate.params.shopSupplier.paramsDao.SupKindSortParams;
import org.haier.shopUpdate.params.shopSupplier.paramsDao.SupKindUniqueParams;
import org.haier.shopUpdate.result.shopSupplier.*;
import org.haier.shopUpdate.util.OrderNoUtils;
import org.haier.shopUpdate.util.ShopsResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

@Service
@Transactional
public class ShopSupplierServiceImpl implements ShopSupplierService {
    private final static String URL = ResourceBundle.getBundle("config").getString("url");
    @Autowired
    private ShopSupBillService shopSupBillService;
    @Autowired
    private ShopSupplierDao shopSupplierDao;
    @Autowired
    private ShopSupBillDao shopSupBillDao;
    @Autowired
    private ShopsRestockPlanDao shopsRestockPlanDao;
    @Autowired
    private GoodsDao goodsDao;

    //subList手动分页，page为第几页，rows为每页个数
    private static <T> List<T> subList(List<T> list, int page, int rows) {
        List<T> listSort = new ArrayList<>();
        int size = list.size();
        int pageStart = page == 1 ? 0 : (page - 1) * rows;//截取的开始位置
        int pageEnd = size < page * rows ? size : page * rows;//截取的结束位置
        if (size > pageStart) {
            listSort = list.subList(pageStart, pageEnd);
        }
        //总页数
        int totalPage = list.size() / rows;
        return listSort;
    }

    @Override
    public ShopsResult addSupKind(SupKindAddParams params) {
        ShopsResult result = new ShopsResult();
        ShopSupSupplierKindEntity entity = new ShopSupSupplierKindEntity();
        entity.setShopUnique(params.getShopUnique());
        entity.setSupplierKindName(params.getSupKindName());
        if (params.getSupKindParunique() != null && !"0".equals(params.getSupKindParunique())) {
            ShopSupSupplierKindEntity shopSupSupplierKindEntity = shopSupplierDao.querySupKindInfoByKindName(params);
            if (shopSupSupplierKindEntity != null) {
                result.setStatus(0);
                result.setMsg("该分类已存在");
                return result;
            }
            QuerySupKindInfoByKuParams querySupKindInfoByKuParams = new QuerySupKindInfoByKuParams();
            querySupKindInfoByKuParams.setShopUnique(params.getShopUnique());
            querySupKindInfoByKuParams.setSupKindUnique((params.getSupKindParunique()));
            ShopSupSupplierKindEntity kindParuniqueEntity = shopSupplierDao.querySupKindInfo(querySupKindInfoByKuParams);
            if (kindParuniqueEntity == null) {
                result.setStatus(0);
                result.setMsg("父级分类编号不存在");
                return result;
            }
            if (kindParuniqueEntity.getSupplierKindLevel() == 2) {
                result.setStatus(0);
                result.setMsg("供货商分类目前只有两级，请在上级分类下添加");
                return result;
            }
            //查询父类下分类排序最大值
            entity.setSupplierKindParunique(params.getSupKindParunique());
            entity.setSupplierKindLevel(2);
            Map<String, Object> orderSort = shopSupplierDao.querySupKindSort(querySupKindInfoByKuParams);
            if (orderSort == null) {
                entity.setOrderSort(0);
            } else {
                entity.setOrderSort(Integer.parseInt(String.valueOf(orderSort.get("orderSort"))) + 1);
            }
        } else {
            params.setSupKindParunique("0");
            ShopSupSupplierKindEntity shopSupSupplierKindEntity = shopSupplierDao.querySupKindInfoByKindName(params);
            if (shopSupSupplierKindEntity != null) {
                result.setStatus(0);
                result.setMsg("该分类已存在");
                return result;
            }

            entity.setSupplierKindParunique("0");
            entity.setSupplierKindLevel(1);
            QuerySupKindInfoByKuParams querySupKindInfoByKuParams = new QuerySupKindInfoByKuParams();
            querySupKindInfoByKuParams.setShopUnique(params.getShopUnique());
            querySupKindInfoByKuParams.setSupKindUnique(("0"));
            Map<String, Object> orderSort = shopSupplierDao.querySupKindSort(querySupKindInfoByKuParams);
            if (orderSort == null) {
                entity.setOrderSort(0);
            } else {
                entity.setOrderSort(Integer.parseInt(String.valueOf(orderSort.get("orderSort"))) + 1);
            }
        }
        String kindUnique = OrderNoUtils.createOrderNo("SSK");

        entity.setSupplierKindUnique(kindUnique);
        entity.setCreateId(params.getCreateId());
        entity.setCreateBy(params.getCreateBy());
        int i = shopSupplierDao.addShopSupSupplierKindEntity(entity);
        result.setStatus(1);
        result.setMsg("添加成功");
        result.setData(kindUnique);
        return result;
    }

    @Override
    public ShopsResult modifySupKind(SupKindModifyParams params) {
        ShopsResult result = new ShopsResult();
        if (params.getModifyType() == 1) {//修改
            ShopSupSupplierKindEntity entity = new ShopSupSupplierKindEntity();
            entity.setShopUnique(params.getShopUnique());
            entity.setSupplierKindUnique(params.getSupKindUnique());
            entity.setModifyBy(params.getCreateBy());
            entity.setModifyId(params.getCreateId());
            entity.setSupplierKindName(params.getSupKindName());
            int i = shopSupplierDao.updateShopSupSupplierKindEntity(entity);
            if (i == 1) {
                result.setStatus(1);
                result.setMsg("修改成功");
            } else {
                result.setStatus(0);
                result.setMsg("修改失败");
            }
        } else if (params.getModifyType() == 2) {//删除
            int m = 1;
            SupKindUniqueParams supKindUniqueParams = new SupKindUniqueParams();
            BeanUtil.copyProperties(params, supKindUniqueParams);
            List<ShopSupSupplierKindEntity> shopSupSupplierKindEntity = shopSupplierDao.querySupKindInfoFromSupplier(supKindUniqueParams);
            if (shopSupSupplierKindEntity != null && !shopSupSupplierKindEntity.isEmpty()) {
                result.setStatus(0);
                result.setMsg("有供货商在此分类中不允许删除");
                return result;
            }
            int n = shopSupplierDao.deleteSupKindBySupKindUnique(supKindUniqueParams);
            List<ShopSupSupplierKindEntity> subSupKindEntities = shopSupplierDao.querySupKindListByKindPreUnique(supKindUniqueParams);
            if (null != subSupKindEntities && !subSupKindEntities.isEmpty()) {
                m += subSupKindEntities.size();
            }
            for (int i = 0; null != subSupKindEntities && !subSupKindEntities.isEmpty() && i < subSupKindEntities.size(); i++) {
                SupKindUniqueParams subSupKindUniqueParams = new SupKindUniqueParams();
                ShopSupSupplierKindEntity subSupKindEntity = subSupKindEntities.get(i);
                subSupKindUniqueParams.setShopUnique(subSupKindEntity.getShopUnique());
                subSupKindUniqueParams.setSupKindUnique(subSupKindEntity.getSupplierKindUnique());
                n += shopSupplierDao.deleteSupKindBySupKindUnique(subSupKindUniqueParams);
            }
            if (m == n) {
                result.setStatus(1);
                result.setMsg("删除成功");
            } else {
                result.setStatus(0);
                result.setMsg("删除失败");
            }

        }
        return result;
    }

    @Override
    public ShopsResult updateSupKindSort(SupKindSortUpdateParams params) {
        ShopsResult result = new ShopsResult();
        String supKindSortList = params.getSupKindSortList();
        if (supKindSortList != null && !supKindSortList.isEmpty()) {
            List<SupKindSortParams> list = new ArrayList<>();
            JSONArray supKindSortArray = JSONUtil.parseArray(supKindSortList);
            int j = supKindSortArray.size();
            for (int i = 0; !supKindSortArray.isEmpty() && i < supKindSortArray.size(); i++) {
                JSONObject supKindSortObject = supKindSortArray.getJSONObject(i);
                SupKindSortParams supKindSortParams = new SupKindSortParams();
                supKindSortParams.setSupplierKindUnique(supKindSortObject.getStr("supplierKindUnique"));
                supKindSortParams.setOrderSort(j - i - 1);
                list.add(supKindSortParams);
            }
            shopSupplierDao.updateSupKindSortList(list);
            result.setStatus(1);
            result.setMsg("更新成功");
        }
        return result;
    }

    @Override
    public ShopsResult querySupKindList(ShopUniqueParams params) {
        ShopsResult result = new ShopsResult();
        List<ShopSupplierKindResult> shopSupplierListResult = shopSupplierDao.querySupKindList(params);
        result.setStatus(1);
        result.setMsg("查询成功！");
        result.setData(shopSupplierListResult);
        return result;
    }

    @Override
    public ShopsResult addShopSupRel(AddSupRelParams params) {
        ShopsResult result = new ShopsResult();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", params.getShopUnique());
        map.put("supplierUnique", params.getSupplierUnique());
        ShopSupSupplierExamineEntity shopSupSupplierExamineEntity = shopSupplierDao.queryShopSupplierByUnique(map);
        if (shopSupSupplierExamineEntity != null) {
            ShopSupSupplierExamineEntity entity = new ShopSupSupplierExamineEntity();
            BeanUtil.copyProperties(params, entity);
            entity.setId(shopSupSupplierExamineEntity.getId());
            if (shopSupSupplierExamineEntity.getBindFlag() == 1) {
                ShopSupSupplierEntity supplierEntity = new ShopSupSupplierEntity();
                BeanUtil.copyProperties(params, supplierEntity);
                int j = shopSupplierDao.updateShopSupSupplierEntity(supplierEntity);
                entity.setBindFlag(0);
            }
            int i = shopSupplierDao.updateShopSupSupplierExamineEntity(entity);
            result.setStatus(1);
            result.setMsg("修改成功");
        } else {
            ShopSupSupplierEntity entity = new ShopSupSupplierEntity();
            BeanUtil.copyProperties(params, entity);
            int id = shopSupplierDao.addShopSupRel(entity);
            result.setStatus(1);
            result.setMsg("新增成功");
        }
        return result;
    }

    @Override
    public ShopsResult addSupInfo(SupInfoAddParams params) {
        ShopsResult result = new ShopsResult();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", params.getShopUnique());
        map.put("contactMobile", params.getContactMobile());
        ShopSupSupplierEntity shopSupSupplierEntity = shopSupplierDao.queryShopSupplierByMobile(map);
        if (shopSupSupplierEntity != null) {
            result.setStatus(0);
            result.setMsg("该供货商已添加");
            return result;
        }
        ShopSupSupplierEntity entity = new ShopSupSupplierEntity();
        BeanUtil.copyProperties(params, entity);
        entity.setPurchaseType(2);
        shopSupplierDao.addShopSupSupplierEntity(entity);
        entity.setSupplierUnique(String.valueOf(entity.getId()));
        shopSupplierDao.updateShopSupSupplierEntity(entity);
        result.setStatus(1);
        result.setMsg("添加成功");
        result.setData(entity.getId());
        return result;
    }

    @Override
    public ShopsResult updateSupInfo(SupInfoUpdateParams params) {
        ShopsResult result = new ShopsResult();
        if (2 == params.getEnableStatus()) {
            Map<String, Object> map = new HashMap<>();
            map.put("shopUnique", params.getShopUnique());
            map.put("id", params.getId());
            ShopSupSupplierEntity shopSupSupplierEntity = shopSupplierDao.querySupplierById(map);
            if (shopSupSupplierEntity != null) {
                String supplierUnique = shopSupSupplierEntity.getSupplierUnique();
                SupplierUniqueParams supplierUniqueParams = new SupplierUniqueParams();
                supplierUniqueParams.setShopUnique(params.getShopUnique());
                supplierUniqueParams.setSupplierUnique(supplierUnique);
                List<ShopSupBillEntity> shopSupBillEntity = shopSupBillDao.queryShopSupBillEntityBySupplierUnique(supplierUniqueParams);
                if (shopSupBillEntity != null && !shopSupBillEntity.isEmpty()) {
                    result.setStatus(0);
                    result.setMsg("该供应商商品存在购销单中，无法停用");
                    return result;
                }

                List<RestockPlanEntity> restockPlanEntity = shopsRestockPlanDao.queryRestockPlanBySupplierUnique(supplierUniqueParams);
                if (restockPlanEntity != null && !restockPlanEntity.isEmpty()) {
                    result.setStatus(0);
                    result.setMsg("该供应商商品存在已生成补货任务中，无法停用");
                    return result;
                }
            }
        }
        ShopSupSupplierEntity entity = new ShopSupSupplierEntity();
        BeanUtil.copyProperties(params, entity);
        int i = shopSupplierDao.updateShopSupSupplierEntity(entity);
        if (i == 1) {
            result.setStatus(1);
            result.setMsg("修改成功");
        } else {
            result.setStatus(0);
            result.setMsg("修改失败");
        }
        result.setData(entity.getId());
        return result;
    }

    @Override
    public ShopsResult deleteSupInfo(SupplierIdParams params) {
        ShopsResult result = new ShopsResult();

        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", params.getShopUnique());
        map.put("id", params.getId());
        ShopSupSupplierEntity shopSupSupplierEntity = shopSupplierDao.querySupplierById(map);
        String supplierUnique = "";
        if (shopSupSupplierEntity != null) {
            supplierUnique = shopSupSupplierEntity.getSupplierUnique();
            SupplierUniqueParams supplierUniqueParams = new SupplierUniqueParams();
            supplierUniqueParams.setShopUnique(params.getShopUnique());
            supplierUniqueParams.setSupplierUnique(supplierUnique);
            List<ShopSupBillEntity> shopSupBillEntity = shopSupBillDao.queryShopSupBillEntityBySupplierUnique(supplierUniqueParams);
            if (shopSupBillEntity != null && !shopSupBillEntity.isEmpty()) {
                result.setStatus(0);
                result.setMsg("该供应商商品存在购销单中，无法删除");
                return result;
            }
            List<RestockPlanEntity> restockPlanEntity = shopsRestockPlanDao.queryRestockPlanBySupplierUnique(supplierUniqueParams);
            if (restockPlanEntity != null && !restockPlanEntity.isEmpty()) {
                result.setStatus(0);
                result.setMsg("该供应商商品存在已生成补货任务中，无法删除");
                return result;
            }
            List<ShopSupplierBillResult> shopSupplierBillResults = shopSupplierDao.queryUnpaidBillList(supplierUniqueParams);
            if (shopSupplierBillResults != null && !shopSupplierBillResults.isEmpty()) {
                result.setStatus(0);
                result.setMsg("您存在未付款金额，无法删除");
                return result;
            }
        }
        int i = shopSupplierDao.deleteShopSupSupplierEntityLogic(params);

        SupplierUniqueParams supplierUniqueParams = new SupplierUniqueParams();
        supplierUniqueParams.setShopUnique(params.getShopUnique());
        supplierUniqueParams.setSupplierUnique(supplierUnique);
//        shopSupplierDao.deleteSupplier(supplierUniqueParams);
        shopSupplierDao.deleteShopSupGoodsEntityRecordFlag(supplierUniqueParams);

        if (i == 1) {
            if (!"".equals(supplierUnique) && shopSupSupplierEntity.getPurchaseType() != 2) {
                CustomerUniqueParams customerUniqueParams = new CustomerUniqueParams();
                customerUniqueParams.setCustomerUnique(String.valueOf(params.getShopUnique()));
                customerUniqueParams.setSupplierUnique(supplierUnique);
                String data = JSONUtil.toJsonStr(customerUniqueParams);
                ShopsResult result1 = httpPost(URL + "/external/supplierCustomer/deleteCustomer", data);
                if (result1.getStatus() == 0) {
                    result.setStatus(0);
                    result.setMsg(result1.getMsg());
                    throw new RuntimeException(result1.getMsg());
                }
            }
            result.setStatus(1);
            result.setMsg("删除成功");
        } else {
            result.setStatus(0);
            result.setMsg("删除失败");
        }
        return result;
    }

    @Override
    public ShopsResult querySupList(QuerySupListParams params) {
        ShopsResult result = new ShopsResult();
        /*if (params.getPageIndex() != null && params.getPageSize() != null) {
            //请求参数处理
            params.setPageIndex((params.getPageIndex() - 1) * params.getPageSize());
        }*/
        if (params.getQueryMeg() != null) {
            params.setQueryMeg("%" + params.getQueryMeg() + "%");
        }
        List<ShopSupplierListResult> list = shopSupplierDao.querySupList(params);
        String totalDebts = shopSupplierDao.querySupplierDebts(params);
        if (params.getPageIndex() != null && params.getPageSize() != null) {
            list = subList(list, params.getPageIndex(), params.getPageSize());
        }
        /*BigDecimal totalDebts = BigDecimal.ZERO;
        for (int i = 0; list != null && !list.isEmpty() && i < list.size(); i++) {
            ShopSupplierListResult result1 = list.get(i);
            if (result1.getDebts() != null) {
                totalDebts = totalDebts.add(result1.getDebts());
            }
        }*/
        Map<String, Object> map = new HashMap<>();
        map.put("list", list);
        map.put("totalDebts", totalDebts);
        result.setStatus(1);
        result.setMsg("查询成功！");
        result.setData(map);
        return result;
    }

    @Override
    public ShopsResult querySupInfo(SupplierUniqueParams params) {
        ShopsResult result = new ShopsResult();
        ShopSupplierListResult shopSupplierListResult = shopSupplierDao.querySupInfo(params);
        result.setStatus(1);
        result.setMsg("查询成功！");
        result.setData(shopSupplierListResult);
        return result;
    }

    @Override
    public ShopsResult querySupBusinessInfo(SupplierUniqueParams params) {
        ShopsResult result = new ShopsResult();
        ShopSupplierBussResult bussResult = shopSupplierDao.querySupBusinessInfo(params);
        if (ObjectUtil.isNotEmpty(bussResult)) {
            Long billCount = shopSupBillDao.selectBillCount(params);
            bussResult.setBillCount(billCount);
        }
        result.setStatus(1);
        result.setMsg("查询成功！");
        result.setData(bussResult);
        return result;
    }

    @Override
    public ShopsResult querySupBillInfo(QueryBillListParams params) {
        ShopsResult result = new ShopsResult();
        /*if (params.getPageIndex() != null && params.getPageSize() != null) {
            //请求参数处理
            params.setPageIndex((params.getPageIndex() - 1) * params.getPageSize());
        }*/
        List<ShopSupplierBillResult> billResult = shopSupplierDao.querySupBillInfo(params);
        if (params.getPageIndex() != null && params.getPageSize() != null) {
            billResult = subList(billResult, params.getPageIndex(), params.getPageSize());
        }
        result.setStatus(1);
        result.setMsg("查询成功！");
        result.setData(billResult);
        return result;
    }

    @Override
    public ShopsResult querySupPaymentInfo(SupplierUniqueWithPageParams params) {
        ShopsResult result = new ShopsResult();
        /*if (params.getPageIndex() != null && params.getPageSize() != null) {
            //请求参数处理
            params.setPageIndex((params.getPageIndex() - 1) * params.getPageSize());
        }*/
        List<ShopSupplierPayResult> payResult = shopSupplierDao.querySupPaymentInfo(params);
        if (params.getPageIndex() != null && params.getPageSize() != null) {
            payResult = subList(payResult, params.getPageIndex(), params.getPageSize());
        }
        result.setStatus(1);
        result.setMsg("查询成功！");
        result.setData(payResult);
        return result;
    }

    @Override
    public ShopsResult addGoods(GoodsInfoParams params) {
        ShopsResult result = new ShopsResult();
        List<GoodsDetailInfoParams> goodsList = params.getGoodsList();
        for (int i = 0; goodsList != null && !goodsList.isEmpty() && i < goodsList.size(); i++) {
            GoodsDetailInfoParams detailInfoParams = goodsList.get(i);
            Map<String, Object> map = new HashMap<>();
            map.put("shopUnique", params.getCustomerUnique());
            map.put("supplierUnique", params.getSupplierUnique());
            map.put("goodsBarcode", detailInfoParams.getGoodsBarcode());
            ShopSupGoodsEntity shopSupGoodsEntityNow = shopSupplierDao.queryShopSupGoodsByGoodsBarcode(map);
            if (shopSupGoodsEntityNow != null && shopSupGoodsEntityNow.getSupplierUnique() != null && !shopSupGoodsEntityNow.getSupplierUnique().isEmpty()) {
                result.setStatus(0);
                result.setMsg("商品条码为【" + detailInfoParams.getGoodsBarcode() + "】的商品已添加，请勿重复添加");
                return result;
            } else if (shopSupGoodsEntityNow == null) {
                ShopSupGoodsEntity shopSupGoodsEntity = new ShopSupGoodsEntity();
                BeanUtil.copyProperties(detailInfoParams, shopSupGoodsEntity);
                shopSupGoodsEntity.setShopUnique(Long.valueOf(params.getCustomerUnique()));
                shopSupGoodsEntity.setSupplierUnique(params.getSupplierUnique());
                shopSupplierDao.addShopSupGoodsEntity(shopSupGoodsEntity);
            } else if (shopSupGoodsEntityNow.getSupplierUnique() == null || shopSupGoodsEntityNow.getSupplierUnique().isEmpty()) {
                shopSupplierDao.updateSupplier(map);
            }
        }
        result.setStatus(1);
        result.setMsg("添加未建档商品成功");
        return result;
    }

    @Override
    public ShopsResult addShopsGood(ShopGoodsInfoParams params) {
        ShopsResult result = new ShopsResult();
        List<String> shopList = params.getShopList();
        if (shopList == null || shopList.isEmpty()) {
            result.setStatus(0);
            result.setMsg("店铺编号不存在");
            return result;
        }
        List<GoodsDetailInfoParams> goodsList = params.getGoodsList();
        if (goodsList == null || goodsList.isEmpty()) {
            result.setStatus(0);
            result.setMsg("商品信息不存在");
            return result;
        }
        for (String shopUnique : shopList) {
            for (GoodsDetailInfoParams detailInfoParams : goodsList) {
                Map<String, Object> map = new HashMap<>();
                map.put("shopUnique", shopUnique);
                map.put("supplierUnique", params.getSupplierUnique());
                map.put("goodsBarcode", detailInfoParams.getGoodsBarcode());
                ShopSupGoodsEntity shopSupGoodsEntityNow = shopSupplierDao.queryShopSupGoodsByGoodsBarcode(map);
                if (shopSupGoodsEntityNow != null && shopSupGoodsEntityNow.getSupplierUnique() != null && !shopSupGoodsEntityNow.getSupplierUnique().isEmpty()) {
                    BeanUtil.copyProperties(detailInfoParams, shopSupGoodsEntityNow);
                    shopSupplierDao.updateShopSupGoodsEntity(shopSupGoodsEntityNow);
                } else if (shopSupGoodsEntityNow == null) {
                    ShopSupGoodsEntity shopSupGoodsEntity = new ShopSupGoodsEntity();
                    BeanUtil.copyProperties(detailInfoParams, shopSupGoodsEntity);
                    shopSupGoodsEntity.setShopUnique(Long.valueOf(shopUnique));
                    shopSupGoodsEntity.setSupplierUnique(params.getSupplierUnique());
                    shopSupplierDao.addShopSupGoodsEntity(shopSupGoodsEntity);
                } else if (shopSupGoodsEntityNow.getSupplierUnique() == null || shopSupGoodsEntityNow.getSupplierUnique().isEmpty()) {
                    shopSupplierDao.updateSupplier(map);
                }
            }
        }
        result.setStatus(1);
        result.setMsg("添加未建档商品成功");
        return result;
    }

    @Override
    public ShopsResult querySupRecordGoodList(QueryRecordGoodsListParams params) {
        ShopsResult result = new ShopsResult();
        List<ShopSupplierRecordGoodsResult> goodsList = shopSupplierDao.querySupRecordGoodList(params);
        for (int i = 0; goodsList != null && !goodsList.isEmpty() && i < goodsList.size(); i++) {
            ShopSupplierRecordGoodsResult recordGoods = goodsList.get(i);
            String goodsBarcode = recordGoods.getGoodsBarcode();
            if (goodsBarcode != null) {
                Map<String, Object> map = new HashMap<>();
                map.put("goodsBarcode", goodsBarcode);
                map.put("shopUnique", params.getShopUnique());
                BigDecimal monthlySales = shopSupplierDao.queryMonthlySales(map);
                recordGoods.setMonthlySales(monthlySales);
            }
        }
        if (params.getPageIndex() != null && params.getPageSize() != null && goodsList != null) {
            goodsList = subList(goodsList, params.getPageIndex(), params.getPageSize());
        }
        result.setStatus(1);
        result.setMsg("查询成功！");
        result.setData(goodsList);
        return result;
    }

    @Override
    public ShopsResult queryQepaymentInfo(QueryRepayHisInfoParams params) {
        ShopsResult result = new ShopsResult();
        ShopSupplierPayDetailResult payDetailResult = shopSupplierDao.queryQepaymentInfo(params);
        result.setStatus(1);
        result.setMsg("查询成功！");
        result.setData(payDetailResult);
        return result;
    }

    @Override
    public ShopsResult queryUnpaidBillList(SupplierUniqueParams params) {
        ShopsResult result = new ShopsResult();
        Map<String, Object> map = new HashMap<>();
        List<ShopSupplierBillResult> billResult = shopSupplierDao.queryUnpaidBillList(params);
        if (billResult != null) {
            map.put("outstandingCount", billResult.size());
            BigDecimal outstandingMoney = BigDecimal.ZERO;
            for (ShopSupplierBillResult shopSupplierBillResult : billResult) {
                outstandingMoney = outstandingMoney.add(shopSupplierBillResult.getTotalPrice());
            }
            map.put("purchaseAmounts", outstandingMoney);
            map.put("billList", billResult);
        }
        result.setStatus(1);
        result.setMsg("查询成功！");
        result.setData(map);
        return result;
    }

    @Override
    public ShopsResult repaymentBills(RepaymentBillsParams params) {
        ShopsResult result = new ShopsResult();
        if (params.getBillIdList().isEmpty()) {
            result.setStatus(0);
            result.setMsg("请选择购销单");
            return result;
        }
        BillIdParams billIdParams = new BillIdParams();
        BeanUtil.copyProperties(params, billIdParams);
        billIdParams.setBillId(params.getBillIdList().get(0));
        ShopSupSupplierExamineEntity shopSupSupplierExamineEntity = shopSupplierDao.queryShopSupplierExamineByUnique(billIdParams);
        if (shopSupSupplierExamineEntity != null) {
            if (params.getVoucherPicturepath() == null || params.getVoucherPicturepath().isEmpty()) {
                result.setStatus(0);
                result.setMsg("请上传付款凭证");
                return result;
            }
        }
        //将购销单ID列表转变为逗号分隔的字符串
        StringBuilder strBuilder = new StringBuilder();
        for (String billId : params.getBillIdList()) {
            if (strBuilder.length() > 0) {
                strBuilder.append(",").append(billId);
            } else {
                strBuilder.append(billId);
            }
        }
        String billIds = strBuilder.toString();
        ShopSupPaymentOrderEntity paymentOrderEntity = new ShopSupPaymentOrderEntity();
        paymentOrderEntity.setShopUnique(params.getShopUnique());
        paymentOrderEntity.setSupplierUnique(params.getSupplierUnique());
        paymentOrderEntity.setBillId(billIds);
        paymentOrderEntity.setPaymentMoney(params.getPaymentMoney());
        paymentOrderEntity.setRemark(params.getRemark());
        paymentOrderEntity.setCreateId(params.getCreateId());
        paymentOrderEntity.setCreateBy(params.getCreateBy());
        //保存付款信息
        int i = shopSupBillDao.addShopSupPaymentOrderEntity(paymentOrderEntity);
        List<String> voucherPicturepathList = params.getVoucherPicturepath();
        for (String s : voucherPicturepathList) {
            ShopSupPaymentEvidenceEntity paymentEvidenceEntity = new ShopSupPaymentEvidenceEntity();
            paymentEvidenceEntity.setPaymentId(paymentOrderEntity.getPaymentId());
            paymentEvidenceEntity.setVoucherPicturepath(s);
            paymentEvidenceEntity.setCreateId(params.getCreateId());
            paymentEvidenceEntity.setCreateBy(params.getCreateBy());
            //保存付款凭证信息
            int k = shopSupBillDao.addShopSupPaymentEvidenceEntity(paymentEvidenceEntity);
        }
        for (String billId : params.getBillIdList()) {
            UpdateBillStatusParams updateBillStatusParams = new UpdateBillStatusParams();
            updateBillStatusParams.setId(Long.valueOf(billId));
            updateBillStatusParams.setShopUnique(params.getShopUnique());
            updateBillStatusParams.setCreateId(params.getCreateId());
            updateBillStatusParams.setCreateBy(params.getCreateBy());
            if (shopSupSupplierExamineEntity != null) {
                updateBillStatusParams.setStatus(3);
            } else {
                updateBillStatusParams.setStatus(4);
            }
            shopSupBillService.updateBillStatus(updateBillStatusParams);
        }
        result.setStatus(1);
        result.setMsg("付款成功");
        return result;
    }

    @Override
    public ShopsResult updateSupRecordGood(SupRecordGoodsUpdateParams params) {
        ShopsResult result = new ShopsResult();
        ShopSupGoodsEntity entity = new ShopSupGoodsEntity();
        BeanUtil.copyProperties(params, entity);
        entity.setRecordFlag(1);
        int i = shopSupplierDao.updateShopSupGoodsEntity(entity);

        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", params.getShopUnique());
        map.put("id", params.getId());
        ShopSupGoodsEntity goodsEntity = shopSupplierDao.queryShopSupGoodsById(map);
        if (goodsEntity != null && null != goodsEntity.getSupplierUnique() && !goodsEntity.getSupplierUnique().isEmpty()) {
            CustomerGoodsBingParams customerGoodsBingParams = new CustomerGoodsBingParams();
            customerGoodsBingParams.setSupplierUnique(goodsEntity.getSupplierUnique());
            customerGoodsBingParams.setCustomerUnique(String.valueOf(params.getShopUnique()));
            customerGoodsBingParams.setGoodsBarcode(goodsEntity.getGoodsBarcode());
            customerGoodsBingParams.setGoodsName(goodsEntity.getGoodsName());
            customerGoodsBingParams.setGoodsUnit(goodsEntity.getGoodsUnit());
            customerGoodsBingParams.setExpirationDate(goodsEntity.getExpirationDate());
            customerGoodsBingParams.setGoodsImageUrl(goodsEntity.getGoodsImageUrl());
            customerGoodsBingParams.setGoodsSalePrice(goodsEntity.getGoodsSalePrice());
            String data = JSONUtil.toJsonStr(customerGoodsBingParams);
            ShopsResult result1 = httpPost(URL + "/external/goods/bingGoods", data);
            if (result1.getStatus() == 0) {
                result.setStatus(0);
                result.setMsg(result1.getMsg());
                throw new RuntimeException(result1.getMsg());
            }
        }
        result.setStatus(1);
        result.setMsg("更新成功");
        return result;
    }

    @Override
    public ShopsResult bindSupplier(BindSupParams params) {
        ShopsResult result = new ShopsResult();
        ShopSupSupplierExamineEntity shopSupSupplierExamineEntity = shopSupplierDao.queryShopSupplierExamineById(params);
        if (shopSupSupplierExamineEntity != null) {
            shopSupSupplierExamineEntity.setBindFlag(1);
            shopSupplierDao.updateShopSupSupplierExamineEntity(shopSupSupplierExamineEntity);

            Map<String, Object> map = new HashMap<>();
            map.put("shopUnique", params.getShopUnique());
            map.put("supplierUnique", shopSupSupplierExamineEntity.getSupplierUnique());
            ShopSupSupplierEntity shopSupSupplierEntity1 = shopSupplierDao.querySupplierByUnique(map);
            if (shopSupSupplierEntity1 != null) {
                shopSupSupplierEntity1.setDelFlag(0);
                shopSupSupplierEntity1.setSupplierName(shopSupSupplierExamineEntity.getSupplierName());
                shopSupSupplierEntity1.setContacts(shopSupSupplierExamineEntity.getContacts());
                shopSupSupplierEntity1.setAddress(shopSupSupplierExamineEntity.getAddress());
                shopSupSupplierEntity1.setContactMobile(shopSupSupplierExamineEntity.getContactMobile());
                shopSupplierDao.updateShopSupSupplierEntity(shopSupSupplierEntity1);
            } else {
                ShopSupSupplierEntity shopSupSupplierEntity = new ShopSupSupplierEntity();
                BeanUtil.copyProperties(shopSupSupplierExamineEntity, shopSupSupplierEntity);
                shopSupSupplierEntity.setEnableStatus(1);
                shopSupSupplierEntity.setPurchaseType(1);
                shopSupplierDao.addShopSupSupplierEntity(shopSupSupplierEntity);
            }

            SupplierCustomerAuditStatusParams supplierCustomerAuditStatusParams = new SupplierCustomerAuditStatusParams();
            supplierCustomerAuditStatusParams.setSupplierUnique(shopSupSupplierExamineEntity.getSupplierUnique());
            supplierCustomerAuditStatusParams.setCustomerUnique(String.valueOf(params.getShopUnique()));
            supplierCustomerAuditStatusParams.setAuditStatus(1);
            String data = JSONUtil.toJsonStr(supplierCustomerAuditStatusParams);
            ShopsResult result1 = httpPost(URL + "/external/supplierCustomer/syncAuditStatus", data);
            if (result1.getStatus() == 0) {
                result.setStatus(0);
                result.setMsg(result1.getMsg());
                throw new RuntimeException(result1.getMsg());
            }

            result.setStatus(1);
            result.setMsg("绑定成功");
        } else {
            result.setStatus(0);
            result.setMsg("未查询到供货商申请信息");
        }
        return result;
    }

    @Override
    public ShopsResult querySupExamineList(QuerySupListParams params) {
        ShopsResult result = new ShopsResult();
        if (params.getQueryMeg() != null) {
            params.setQueryMeg("%" + params.getQueryMeg() + "%");
        }
        List<ShopSupSupplierExamineEntity> list = shopSupplierDao.queryShopSupplierExamineList(params);
        result.setStatus(1);
        result.setData(list);
        result.setMsg("查询成功！");
        return result;
    }

    @Override
    public ShopsResult deleteShopSupGoodsEntity(ShopSupGoodsDeleteParams params) {
        ShopsResult result = new ShopsResult();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", params.getShopUnique());
        map.put("id", params.getId());
        ShopSupGoodsEntity entity = shopSupplierDao.queryShopSupGoodsById(map);
        if (entity == null) {
            result.setStatus(0);
            result.setMsg("商品信息不存在");
            return result;
        }
        int i = shopSupplierDao.deleteShopSupGoodsEntity(params);
        if (i == 1) {
            CustomerGoodsDeleteParams customerGoodsDeleteParams = new CustomerGoodsDeleteParams();
            customerGoodsDeleteParams.setSupplierUnique(entity.getSupplierUnique());
            customerGoodsDeleteParams.setCustomerUnique(String.valueOf(params.getShopUnique()));
            customerGoodsDeleteParams.setGoodsBarcode(entity.getGoodsBarcode());
            String data = JSONUtil.toJsonStr(Collections.singletonList(customerGoodsDeleteParams));
            ShopsResult result1 = httpPost(URL + "/external/goods/deleteBingGoods", data);
            if (result1.getStatus() == 0) {
                result.setStatus(0);
                result.setMsg(result1.getMsg());
                throw new RuntimeException(result1.getMsg());
            }

            result.setStatus(1);
            result.setMsg("删除成功");
        } else {
            result.setStatus(0);
            result.setMsg("删除失败");
        }
        return result;
    }

    @Override
    public ShopsResult addSupGood(Map<String, Object> params) {
        ShopsResult result = new ShopsResult();
        try {
            String checkMsg = checkAddSupGoodsParams(params);
            if (checkMsg != null) {
                result.setStatus(0);
                result.setMsg(checkMsg);
                return result;
            }
            //查询建档商品信息
            Map<String, Object> goodsMap = new HashMap<>();
            goodsMap.put("shopUnique", params.get("shopUnique"));
            goodsMap.put("goodsBarcode", params.get("goodsBarcode"));
            ShopSupGoodsEntity goodsEntity = shopSupplierDao.queryShopSupGoodsByGoodsBarcode(goodsMap);
            if (goodsEntity != null && !params.get("supplierUnique").equals(goodsEntity.getSupplierUnique())) {
                if (!Objects.equals(String.valueOf(goodsEntity.getRecordFlag()), "1")) {
                    result.setStatus(0);
                    result.setMsg("商品未建档");
                    return result;
                } else {
                    int i = shopSupplierDao.updateSupplier(params);//更换供货商
                    Map<String, Object> map = new HashMap<>();
                    map.put("shopUnique", params.get("shopUnique"));
                    map.put("supplierUnique", params.get("supplierUnique"));
                    ShopSupSupplierExamineEntity shopSupSupplierExamineEntity = shopSupplierDao.queryShopSupplierByUnique(map);
                    Map<String, Object> oldMap = new HashMap<>();
                    oldMap.put("shopUnique", params.get("shopUnique"));
                    oldMap.put("supplierUnique", goodsEntity.getSupplierUnique());
                    ShopSupSupplierExamineEntity oldShopSupSupplierExamineEntity = shopSupplierDao.queryShopSupplierByUnique(oldMap);
                    if (shopSupSupplierExamineEntity != null) {
                        CustomerGoodsBingParams customerGoodsBingParams = new CustomerGoodsBingParams();
                        customerGoodsBingParams.setSupplierUnique(String.valueOf(params.get("supplierUnique")));
                        if (oldShopSupSupplierExamineEntity != null) {
                            customerGoodsBingParams.setOldSupplierUnique(String.valueOf(goodsEntity.getSupplierUnique()));
                        }
                        customerGoodsBingParams.setCustomerUnique(String.valueOf(params.get("shopUnique")));
                        customerGoodsBingParams.setGoodsBarcode(String.valueOf(goodsEntity.getGoodsBarcode()));
                        customerGoodsBingParams.setGoodsName(String.valueOf(goodsEntity.getGoodsName()));
                        customerGoodsBingParams.setGoodsUnit(String.valueOf(goodsEntity.getGoodsUnit()));
                        customerGoodsBingParams.setExpirationDate(Integer.valueOf(String.valueOf(goodsEntity.getExpirationDate())));
                        customerGoodsBingParams.setGoodsImageUrl(String.valueOf(goodsEntity.getGoodsImageUrl()));
                        customerGoodsBingParams.setGoodsSalePrice(new BigDecimal(String.valueOf(goodsEntity.getGoodsSalePrice())));
                        String data = JSONUtil.toJsonStr(customerGoodsBingParams);
                        ShopsResult result1 = httpPost(URL + "/external/goods/bingGoods", data);
                        if (result1.getStatus() != 1) {
                            result.setStatus(0);
                            result.setMsg(result1.getMsg());
                            throw new RuntimeException(result1.getMsg());
                        }
                    } else if(oldShopSupSupplierExamineEntity != null){
                        CustomerGoodsDeleteParams customerGoodsDeleteParams = new CustomerGoodsDeleteParams();
                        customerGoodsDeleteParams.setSupplierUnique(String.valueOf(goodsEntity.getSupplierUnique()));
                        customerGoodsDeleteParams.setCustomerUnique(String.valueOf(params.get("shopUnique")));
                        customerGoodsDeleteParams.setGoodsBarcode(String.valueOf(goodsEntity.getGoodsBarcode()));
                        String data = JSONUtil.toJsonStr(Collections.singletonList(customerGoodsDeleteParams));
                        ShopsResult result1 = httpPost(URL + "/external/goods/deleteBingGoods", data);
                        if (result1.getStatus() != 1) {
                            result.setStatus(0);
                            result.setMsg(result1.getMsg());
                            throw new RuntimeException(result1.getMsg());
                        }
                    }
                    if (i == 1) {
                        result.setStatus(1);
                        result.setMsg("绑定成功");
                        return result;
                    } else {
                        result.setStatus(0);
                        result.setMsg("绑定失败");
                        return result;
                    }
                }
            } else if (goodsEntity == null) {
                GoodsEntity goodsEntity1 = goodsDao.queryGoodByBarcode(params);
                if (goodsEntity1 != null) {
                    Map<String, Object> supGoodParams = new HashMap<>();
                    BeanUtil.copyProperties(goodsEntity1, supGoodParams);
                    supGoodParams.put("supplierUnique", params.get("supplierUnique"));
                    supGoodParams.put("expirationDate", goodsEntity1.getGoodsLife());
                    supGoodParams.put("goodsImageUrl", goodsEntity1.getGoodsPicturepath());
                    supGoodParams.put("createId", params.get("createId"));
                    supGoodParams.put("createBy", params.get("createBy"));
                    int i = shopSupplierDao.addSupGood(supGoodParams);
                    Map<String, Object> map = new HashMap<>();
                    map.put("shopUnique", params.get("shopUnique"));
                    map.put("supplierUnique", params.get("supplierUnique"));
                    ShopSupSupplierExamineEntity shopSupSupplierExamineEntity = shopSupplierDao.queryShopSupplierByUnique(map);
                    if (shopSupSupplierExamineEntity != null) {
                        CustomerGoodsBingParams customerGoodsBingParams = new CustomerGoodsBingParams();
                        customerGoodsBingParams.setSupplierUnique(String.valueOf(params.get("supplierUnique")));
                        customerGoodsBingParams.setCustomerUnique(String.valueOf(params.get("shopUnique")));
                        customerGoodsBingParams.setGoodsBarcode(String.valueOf(goodsEntity1.getGoodsBarcode()));
                        customerGoodsBingParams.setGoodsName(String.valueOf(goodsEntity1.getGoodsName()));
                        customerGoodsBingParams.setGoodsUnit(String.valueOf(goodsEntity1.getGoodsUnit()));
                        customerGoodsBingParams.setExpirationDate(Integer.valueOf(String.valueOf(goodsEntity1.getGoodsLife())));
                        customerGoodsBingParams.setGoodsImageUrl(String.valueOf(goodsEntity1.getGoodsPicturepath()));
                        customerGoodsBingParams.setGoodsSalePrice(new BigDecimal(String.valueOf(goodsEntity1.getGoodsSalePrice())));
                        String data = JSONUtil.toJsonStr(customerGoodsBingParams);
                        ShopsResult result1 = httpPost(URL + "/external/goods/bingGoods", data);
                        if (result1.getStatus() != 1) {
                            result.setStatus(0);
                            result.setMsg(result1.getMsg());
                            throw new RuntimeException(result1.getMsg());
                        }
                    }
                    if (i == 1) {
                        result.setStatus(1);
                        result.setMsg("绑定成功");
                    } else {
                        result.setStatus(0);
                        result.setMsg("绑定失败");
                    }
                } else {
                    result.setStatus(0);
                    result.setMsg("该商品信息不存在");
                }
            } else if (params.get("supplierUnique").equals(goodsEntity.getSupplierUnique())) {
                if (!Objects.equals(String.valueOf(goodsEntity.getRecordFlag()), "1")) {
                    goodsEntity.setRecordFlag(1);
                    int i = shopSupplierDao.updateShopSupGoodsEntity(goodsEntity);
                    Map<String, Object> map = new HashMap<>();
                    map.put("shopUnique", params.get("shopUnique"));
                    map.put("supplierUnique", params.get("supplierUnique"));
                    ShopSupSupplierExamineEntity shopSupSupplierExamineEntity = shopSupplierDao.queryShopSupplierByUnique(map);
                    if (shopSupSupplierExamineEntity != null) {
                        CustomerGoodsBingParams customerGoodsBingParams = new CustomerGoodsBingParams();
                        customerGoodsBingParams.setSupplierUnique(goodsEntity.getSupplierUnique());
                        customerGoodsBingParams.setCustomerUnique(String.valueOf(params.get("shopUnique")));
                        customerGoodsBingParams.setGoodsBarcode(goodsEntity.getGoodsBarcode());
                        customerGoodsBingParams.setGoodsName(goodsEntity.getGoodsName());
                        customerGoodsBingParams.setGoodsUnit(goodsEntity.getGoodsUnit());
                        customerGoodsBingParams.setExpirationDate(goodsEntity.getExpirationDate());
                        customerGoodsBingParams.setGoodsImageUrl(goodsEntity.getGoodsImageUrl());
                        customerGoodsBingParams.setGoodsSalePrice(goodsEntity.getGoodsSalePrice());
                        String data = JSONUtil.toJsonStr(customerGoodsBingParams);
                        ShopsResult result1 = httpPost(URL + "/external/goods/bingGoods", data);
                        if (result1.getStatus() == 0) {
                            result.setStatus(0);
                            result.setMsg(result1.getMsg());
                            throw new RuntimeException(result1.getMsg());
                        }
                    }
                }
            }
        } catch (Exception e) {
            result.setStatus(0);
            result.setMsg("异常");
            throw new RuntimeException("绑定失败");
        }
        return result;
    }

    public ShopsResult httpPost(String url, String data) {
        ShopsResult result = new ShopsResult();
        System.out.println("-------------------------同步, 地址: {" + url + "},参数: {" + data + "}---------------");
        String result1 = HttpUtil.post(url, data);
        System.out.println("-------------------------同步结果: {" + result1 + "}---------------");
        JSONObject jo1 = JSONUtil.parseObj(result1);
        if (jo1.get("status") == null) {
            result.setStatus(0);
            result.setMsg("访问同步信息接口超时");
        } else if ("1".equals(String.valueOf(jo1.get("status")))) {
            result.setStatus(1);
            result.setMsg(String.valueOf(jo1.get("message")));
        } else {
            result.setStatus(0);
            result.setMsg(String.valueOf(jo1.get("message")));
        }
        return result;
    }

    private String checkAddSupGoodsParams(Map<String, Object> params) {
        StringBuilder str = new StringBuilder();
        if (params.get("shopUnique") == null || params.get("shopUnique") == "") {
            str.append("店铺编号不能为空；");
        }
        if (params.get("goodsBarcode") == null || params.get("goodsBarcode") == "") {
            str.append("商品条码不能为空；");
        }
        if (params.get("supplierUnique") == null || params.get("supplierUnique") == "") {
            str.append("供货商编号不能为空；");
        }
        if (params.get("createId") == null || params.get("createId") == "") {
            str.append("操作人ID不能为空；");
        }
        if (params.get("createBy") == null || params.get("createBy") == "") {
            str.append("操作人姓名不能为空；");
        }
        if (str.length() > 0) {
            return str.toString();
        } else {
            return null;
        }
    }
}
