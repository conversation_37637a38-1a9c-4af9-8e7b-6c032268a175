package org.haier.shopUpdate.service;

import org.haier.shopUpdate.entity.Inventory;
import org.haier.shopUpdate.util.ShopsResult;
import org.haier.shopUpdate.util.common.CommonResult;

import java.util.Map;

public interface InventoryService {
	/**
	 * 各状态的订单数量查询
	 * @param map
	 * @return
	 */
	public ShopsResult queryInvertoryCountByType(Map<String,Object> map);
	
	/**
	 * 盘点列表的信息查询
	 * @param map
	 * @return
	 */
	public ShopsResult queryInventoryList(Map<String,Object> map);
	
	
	/**
	 * 盘点详情
	 * @param map
	 * @return
	 */
	public ShopsResult queryInventoryDetail(Map<String,Object> map);
	
	/**
	 * 创建新的盘点草稿
	 * @param inven
	 * @return
	 */
	public ShopsResult createNewInvenDraft(Inventory inven);
	
	/**
	 * 扫码查询商品库存信息
	 * @param map
	 * @return
	 */
	public CommonResult queryGoodsByBarcode(Map<String,Object> map);
	
	/**
	 * 扫码查询商品库存信息
	 * @param map
	 * @return
	 */
	public ShopsResult queryGoodsByGoodsMessage(Map<String,Object> map);
	
	/**
	 * 添加或更新盘点记录
	 * @param map
	 * @return
	 */
	public CommonResult addNewGoodsStockRecord(Map<String,Object> map,String goodsBarcode);
	
	/**
	 * 提交盘点订单
	 * @param map
	 * @param stockOrigin 1、操作源为手机；2、操作源头为PC端；3、操作源头为网页；
	 * @return
	 */
	public CommonResult subInventory(Map<String,Object> map,Integer staffId , Integer stockOrigin);
	
	/**
	 * 某一商品的盘点详情查询
	 * @param map
	 * @return
	 */
	public ShopsResult queryGoodsInvenRecord(Map<String,Object> map);
	/**
	 * 编辑界面，删除勾选商品
	 * @param list
	 * @return
	 */
	public ShopsResult deleteInvenGoods(Map<String,Object> map);
	
	/**
	 * 删除未提交的盘点订单及详情
	 * @param map
	 * @return
	 */
	public ShopsResult deleteDraftLists(Map<String,Object> map);

	CommonResult queryGoodsByBarcodeCurrent(Long shopUnique, String goodsBarcode);
}
