package org.haier.shopUpdate.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import cn.hutool.core.util.ObjectUtil;
import org.haier.shopUpdate.dao.GoodsUnitDao;
import org.haier.shopUpdate.util.ShopsResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


@Service
@Transactional
public class GoodsUnitServiceImpl implements GoodsUnitService{
	
	@Resource
	private  GoodsUnitDao goodsUnitDao;

	public ShopsResult findGoodsUnitList(String shop_unique, String goods_unit) {
		ShopsResult result=new ShopsResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		//查询此商品是否有单位没有从大库里拿来保存
		List<Map<String, Object>> saleListWaitList=goodsUnitDao.findGoodsUnitListByShopUnique(map);
		if(saleListWaitList==null||saleListWaitList.size()<=0){
			map.put("shop_unique", 0);
			List<Map<String, Object>> goodsUnitList=goodsUnitDao.findGoodsUnitListByShopUnique(map);
			for (Map<String, Object> map2 : goodsUnitList) {
				map2.put("shop_unique", shop_unique);
				goodsUnitDao.addGoodsUnit(map2);
			}
		}
		map.put("shop_unique", shop_unique);
		if(goods_unit!=null&&!"".equals(goods_unit)){
			map.put("goods_unit", "%"+goods_unit+"%");
		}
		List<Map<String, Object>> goodsUnitByList=goodsUnitDao.findGoodsUnitList(map);
		result.setStatus(1);
		if(goodsUnitByList==null){
			result.setData(new ArrayList<Map<String, Object>>()); 
		}else{
			result.setData(goodsUnitByList); 
		}
		return result;
	}

	public ShopsResult addGoodsUnit(String shop_unique, String goods_unit) {
		ShopsResult result=new ShopsResult();
		if(ObjectUtil.isNull(goods_unit)){
			result.setStatus(0);
			result.setMsg("商品单位不能为空");
		}
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("shop_unique", shop_unique);
		map.put("goods_unit", goods_unit);

		List<Map<String,Object>> units = goodsUnitDao.findGoodsUnitList(map);

		if(ObjectUtil.isNotEmpty(units)){
			result.setStatus(0);
			result.setMsg("该单位已存在");
			return result;
		}

		goodsUnitDao.addGoodsUnit(map);
		result.setStatus(1);
		return result;
	}

	public ShopsResult editGoodsUnit(Integer goods_unit_id, String goods_unit) {
		ShopsResult result=new ShopsResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("goods_unit_id", goods_unit_id);
		map.put("goods_unit", goods_unit);
		goodsUnitDao.editGoodsUnit(map);
		result.setStatus(1);
		return result;
	}

	public ShopsResult deleteGoodsUnit(Integer goods_unit_id) {
		ShopsResult result=new ShopsResult();
		Map<String,Object> map=new HashMap<String, Object>();
		map.put("goods_unit_id", goods_unit_id);
		goodsUnitDao.deleteGoodsUnit(map);
		result.setStatus(1);
		return result;
	}
	
}
