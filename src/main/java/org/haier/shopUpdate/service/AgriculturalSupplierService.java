package org.haier.shopUpdate.service;

import org.haier.shopUpdate.params.agriculturalSupplier.*;
import org.haier.shopUpdate.util.ShopsResult;

public interface AgriculturalSupplierService {

    /**
     * 新增供货商、货主
     * @param req
     * @return
     */
    public ShopsResult addSupplier(AddSupplierParams req);

    /**
     * 修改供货商、货主
     * @param req
     * @return
     */
    public ShopsResult updateSupplier(UpdateSupplierParams req);

    /**
     * 删除数据
     * @param req
     * @return
     */
    public ShopsResult deleteSupplier(DeleteSupplierParams req);

    /**
     * 查询单条数据
     * @param req
     * @return
     */
    public ShopsResult querySupplierDetail(QuerySupplierDetailParams req);

    /**
     * 列表分页查询
     * @param req
     * @return
     */
    public ShopsResult querySupplierList(QuerySupplierListParams req);

}
