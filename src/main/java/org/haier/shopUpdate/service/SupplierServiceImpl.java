package org.haier.shopUpdate.service;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.shopUpdate.dao.SupplierDao;
import org.haier.shopUpdate.util.ShopsResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public class SupplierServiceImpl implements SupplierService{
	@Resource
	private SupplierDao supDao;
	
	/**
	 * 查询商品供货商信息
	 * @param map
	 * @return
	 */
	public ShopsResult queryShopSuppliers(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> data=supDao.queryShopSuppliers(map);
		if(null==data||data.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("没有满足条件的供货商信息！");
			return sr;
		}
		sr.setData(data);
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		return sr;
	}
}
