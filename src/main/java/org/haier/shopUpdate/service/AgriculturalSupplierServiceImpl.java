package org.haier.shopUpdate.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import org.haier.shopUpdate.dao.AgriculturalSupplierDao;
import org.haier.shopUpdate.dao.ShopStaffDao;
import org.haier.shopUpdate.entity.AgriculturalSupplierEntity;
import org.haier.shopUpdate.entity.ShopStaffEntity;
import org.haier.shopUpdate.params.agriculturalSupplier.*;
import org.haier.shopUpdate.result.agriculturalSupplier.QuerySupplierDetailResult;
import org.haier.shopUpdate.result.agriculturalSupplier.QuerySupplierListResult;
import org.haier.shopUpdate.util.ShopsResult;
import org.haier.shopUpdate.util.UtilForJAVA;
import org.haier.shopUpdate.util.common.utils.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class AgriculturalSupplierServiceImpl implements AgriculturalSupplierService{

    @Autowired
    private AgriculturalSupplierDao agriculturalSupplierDao;

    @Autowired
    private ShopStaffDao shopStaffDao;

    /**
     * 新增供货商、货主
     * @param req
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShopsResult addSupplier(AddSupplierParams req) {

        //入参校验
        ShopsResult checkResult = checkAddParam(req);
        if (checkResult.getStatus() != 1) return checkResult;

        AgriculturalSupplierEntity entity = new AgriculturalSupplierEntity();
        BeanUtils.copyProperties(req,entity);

        entity.setDelFlag(1);
        agriculturalSupplierDao.insert(entity);

        return ShopsResult.ok("操作成功",null);
    }

    /**
     * 修改供货商、货主
     * @param req
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShopsResult updateSupplier(UpdateSupplierParams req) {
        //数据校验
        ShopsResult checkResult = checkUpdateParam(req);
        if (checkResult.getStatus() != 1) return checkResult;

        AgriculturalSupplierEntity entity = new AgriculturalSupplierEntity();
        BeanUtils.copyProperties(req,entity);
        agriculturalSupplierDao.updateById(entity);

        return ShopsResult.ok("操作成功",null);
    }

    /**
     * 删除一条数据
     * @param req
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShopsResult deleteSupplier(DeleteSupplierParams req) {
        //校验数据
        AgriculturalSupplierEntity detail = findById(req.getId(), req.getShopUnique());
        if (ObjectUtil.isEmpty(detail)){
            return ShopsResult.fail("数据异常");
        }

        //校验修改人
        AddSupplierParams p = new AddSupplierParams();
        p.setModifyUser(req.getModifyUser());
        p.setShopUnique(req.getShopUnique());
        boolean checkUser = checkModifyUser(p);
        if (!checkUser){
            return ShopsResult.fail("请输入正确的操作人");
        }

        //逻辑删除
        AgriculturalSupplierEntity updSupplier = new AgriculturalSupplierEntity();
        updSupplier.setId(req.getId());
        updSupplier.setDelFlag(2);
        updSupplier.setModifyUser(req.getModifyUser());
        agriculturalSupplierDao.updateById(updSupplier);

        return ShopsResult.ok("操作成功",null);
    }

    /**
     * 详情查询
     * @param req
     * @return
     */
    @Override
    public ShopsResult querySupplierDetail(QuerySupplierDetailParams req) {
        AgriculturalSupplierEntity detail = findById(req.getId(), req.getShopUnique());
        if (ObjectUtil.isEmpty(detail)){
            return ShopsResult.fail("数据异常");
        }

        QuerySupplierDetailResult detailResult = new QuerySupplierDetailResult();
        BeanUtils.copyProperties(detail,detailResult);
        return ShopsResult.ok("操作成功",detailResult);
    }

    /**
     * 列表查询
     * @param req
     * @return
     */
    @Override
    public ShopsResult querySupplierList(QuerySupplierListParams req) {
        //数据查询
        if (ObjectUtil.isEmpty(req.getPageSize())){
            req.setPageSize(10);
        }
        if (ObjectUtil.isEmpty(req.getPageIndex()) || req.getPageIndex() == 0){
            req.setPageIndex(1);
        }
        req.setPageIndex((req.getPageIndex()-1)*req.getPageSize());

        List<AgriculturalSupplierEntity> pageList = agriculturalSupplierDao.findPageList(req);
        //数据转换
        List<QuerySupplierDetailResult> detailList = BeanUtil.copyToList(pageList, QuerySupplierDetailResult.class);
        //返回参数拼凑
        QuerySupplierListResult listResult = new QuerySupplierListResult();
        listResult.setList(detailList);

        return ShopsResult.ok("操作成功",listResult);
    }

    /**
     * 校验新增参数
     * @param req
     * @return
     */
    private ShopsResult checkAddParam(AddSupplierParams req){

        //手机号格式校验
        if(StringUtils.isNotBlank(req.getPhone())){
            boolean phone = UtilForJAVA.checkPhone(req.getPhone());
            if (!phone){
                return ShopsResult.fail("手机号格式错误");
            }
        }

        //供货商类型校验
        if (req.getType() != 1 && req.getType() != 2){
            return ShopsResult.fail("请输入正确的供货商类型");
        }

        //查询当前用户是否存在
        boolean checkUser = checkModifyUser(req);
        if (!checkUser){
            return ShopsResult.fail("请输入正确的操作人");
        }

        //查询姓名是否重复
        boolean checkName = checkName(req);
        if (!checkName){
            return ShopsResult.fail("姓名重复");
        }

        return ShopsResult.ok(null);
    }

    /**
     * 校验修改参数
     * @param req
     * @return
     */
    private ShopsResult checkUpdateParam(UpdateSupplierParams req){
        //手机号格式校验
        if(StringUtils.isNotBlank(req.getPhone())){
            boolean phone = UtilForJAVA.checkPhone(req.getPhone());
            if (!phone){
                return ShopsResult.fail("手机号格式错误");
            }
        }

        //供货商类型校验
        if (req.getType() != 1 && req.getType() != 2){
            return ShopsResult.fail("请输入正确的供货商类型");
        }

        //查询旧数据
        AgriculturalSupplierEntity oldData = findById(req.getId(), req.getShopUnique());

        if (ObjectUtil.isEmpty(oldData)){
            return ShopsResult.fail("数据异常");
        }

        if (!oldData.getType().equals(req.getType())){
            return ShopsResult.fail("供货商类型不允许修改");
        }

        if (!oldData.getModifyUser().equals(req.getModifyUser())){
            //查询当前用户是否存在
            boolean checkUser = checkModifyUser(req);
            if (!checkUser){
                return ShopsResult.fail("请输入正确的操作人");
            }
        }

        if (!oldData.getName().equals(req.getName())){
            //查询姓名是否重复
            boolean checkName = checkName(req);
            if (!checkName){
                return ShopsResult.fail("姓名重复");
            }
        }

        return ShopsResult.ok(null);
    }

    /**
     * 校验操作人
     * @return
     */
    private boolean checkModifyUser(AddSupplierParams req){
        //查询当前用户是否存在
        ShopStaffEntity staffEntity = new ShopStaffEntity();
        staffEntity.setStaffId(req.getModifyUser());
        staffEntity.setShopUnique(req.getShopUnique());
        ShopStaffEntity staff = shopStaffDao.findByStaffId(staffEntity);
        if (staff == null){
            return false;
        }

        return true;
    }

    /**
     * 校验姓名是否重复
     * @param req
     * @return
     */
    private boolean checkName(AddSupplierParams req){
        //查询姓名是否重复
        AgriculturalSupplierEntity supplierEntity = new AgriculturalSupplierEntity();
        supplierEntity.setShopUnique(req.getShopUnique());
        supplierEntity.setName(req.getName());
        supplierEntity.setType(req.getType());
        AgriculturalSupplierEntity entity = agriculturalSupplierDao.findByName(supplierEntity);
        if (entity != null){
            return false;
        }

        return true;
    }

    /**
     * 根据id查询
     * @param id
     * @param shopUnique
     * @return
     */
    private AgriculturalSupplierEntity findById(Long id,Long shopUnique){
        AgriculturalSupplierEntity query = new AgriculturalSupplierEntity();
        query.setId(id);
        query.setShopUnique(shopUnique);
        return agriculturalSupplierDao.findById(query);
    }
}
