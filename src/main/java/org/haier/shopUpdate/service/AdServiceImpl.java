package org.haier.shopUpdate.service;

import java.util.Map;

import javax.annotation.Resource;

import org.haier.shopUpdate.dao.AdDao;
import org.haier.shopUpdate.util.ShopsResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public class AdServiceImpl {
	@Resource
	public AdDao adDao;
	
	public ShopsResult adSearch(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		return sr;
	}
}
