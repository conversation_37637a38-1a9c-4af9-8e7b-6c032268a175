package org.haier.shopUpdate.service;

import java.io.File;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import org.haier.shopUpdate.dao.ShopFunctionDao;
import org.haier.shopUpdate.oss.UploadResult;
import org.haier.shopUpdate.upload.FileUploadService;
import org.haier.shopUpdate.util.ShopsResult;
import org.haier.shopUpdate.util.ShopsUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

@Service
@Transactional
public class ShopFunctionServiceImpl implements ShopFunctionService{
	@Resource
	private ShopFunctionDao funDao;
	@Resource
	private FileUploadService fileUploadService;
	
	/**
	 * 查询店铺功能列表
	 * @param map
	 * @return
	 */
	public ShopsResult queryShopFunction(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		Map<String,Object> data=new HashMap<String, Object>();
		data=funDao.queryShopFunction(map);
		if(null==data){
			data=ShopsUtil.map;
		}
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}
	/**
	 * 更新店铺功能
	 * @param map
	 * @return
	 */
	public ShopsResult modifyShopFunction(Map<String,Object> map){
	ShopsResult sr=new ShopsResult();
		int k= funDao.modifyShopFunction(map);
		if(k==0){
			sr.setStatus(1);
			sr.setMsg("修改失败！店铺信息错误");
			return sr;
		}
		sr.setStatus(1);
		sr.setMsg("更新成功！");
		return sr;
	}
	
	/**
	 * 查询店铺付款码信息
	 * @param map
	 * @return
	 */
	public ShopsResult queryShopPayCode(Map<String,Object> map){
		ShopsResult sr=new ShopsResult(1,"查询成功！");
		Map<String,Object> data=funDao.queryShopPayCode(map);
		if(null==data){
			sr.setStatus(2);
			sr.setMsg("尚未上传收款码信息！");
			return sr;
		}
		sr.setData(funDao.queryShopPayCode(map));
		return sr;
	}
	
	/**
	 * 修改店铺付款码信息
	 * @param request
	 * @param map
	 * @return
	 * @throws Exception 
	 */
	public ShopsResult modifyPayPic(HttpServletRequest request,Map<String,Object> map) throws Exception{
		ShopsResult sr=new ShopsResult(1,"修改成功！");
		MultipartFile file=ShopsUtil.testMulRequest(request, "payPic");
		if(null==file){
			sr.setStatus(2);
			sr.setMsg("未检测到图片信息！");
			return sr;
		}
		String shopUnique=map.get("shopUnique").toString();
		String filePath="image"+File.separator+shopUnique;
		InputStream is = file.getInputStream();
		if(map.get("picType").toString().equals("1")){
			UploadResult fileResult = fileUploadService.uploadFileByPath(is, filePath + "/weChat.jpg", file.getContentType());
			map.put("weChatPic", ObjectUtil.isNotNull(fileResult) ? fileResult.getUrl() : StrUtil.EMPTY);
		}else if(map.get("picType").toString().equals("2")) {
			UploadResult fileResult = fileUploadService.uploadFileByPath(is, filePath + "/aliPay.jpg", file.getContentType());
			map.put("aliPayPic", ObjectUtil.isNotNull(fileResult) ? fileResult.getUrl() : StrUtil.EMPTY);
		}
		funDao.modifyPayPic(map);
		return sr;
	}
}
