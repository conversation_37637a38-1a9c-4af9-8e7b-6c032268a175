package org.haier.shopUpdate.service;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.shopUpdate.dao.EvaluateDao;
import org.haier.shopUpdate.dao.ShopsStaffDao;
import org.haier.shopUpdate.entity.Evaluate;
import org.haier.shopUpdate.util.ShopsResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public class EvaluateServiceImpl implements EvaluateService{
	@Resource
	private EvaluateDao evalDao;
	@Resource
	private ShopsStaffDao  staffDao;
	/**
	 * 查询店铺近期的商品评价
	 * @param map
	 * @return
	 */
	public ShopsResult evaluateListPagesQuery(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		int k=evalDao.evaluateListPagesQuery(map);
		if(k==0){
			sr.setStatus(2);
			sr.setMsg("没有满足条件的评价信息！");
			return sr;
		}
		sr.setStatus(1);
		sr.setMsg("查询成功！"+k);
		sr.setData(k);
		return sr;
	}
	
	
	/**
	 * @param map
	 * @return
	 */
	public ShopsResult evaluateListQuery(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		List<Map<String,Object>> data=evalDao.evaluateListQuery(map);
		if(null==data||data.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("没有满足条件的订单信息！");
			return sr;
		}
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		sr.setData(data);
		return sr;
	}
	
	/**
	 * 查询评论详情
	 * @param map
	 * @return
	 */
	public ShopsResult queryEvaluateDetail(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		List<Evaluate> data=evalDao.queryEvaluateDetail(map);
		if(null==data||data.isEmpty()){
			sr.setStatus(2);
			sr.setMsg("没有满足条件的评论详情");
			return sr;
		}
		sr.setData(data);
		sr.setStatus(1);
		sr.setMsg("查询成功！");
		return sr;
	}
	
	/**
	 * 删除管理员评论内容
	 * @param map
	 * @return
	 */
	public ShopsResult deleteManagerEvaluate(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		int k=evalDao.deleteManagerEvaluate(map);
		if(k==0){
			sr.setStatus(2);
			sr.setMsg("删除失败！");
			return sr;
		}
		sr.setStatus(1);
		sr.setMsg("删除成功！");
		return sr;
	}
	
	/**
	 * 添加订单评论回复
	 * @param map
	 * @return
	 */
	public ShopsResult responeEvaluate(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		int k=evalDao.responeEvaluate(map);
		if(k==0){
			sr.setStatus(2);
			sr.setMsg("回复失败！");
			return sr;
		}
		k=evalDao.responeEvaluateStatus(map);//修改主评论回复状态
		Map<String,Object> data=staffDao.queryManagerMessage(map);
		sr.setStatus(1);
		sr.setMsg("回复成功！");
		sr.setData(data);
		return sr;
	}
}
