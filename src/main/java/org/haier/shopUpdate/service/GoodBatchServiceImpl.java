package org.haier.shopUpdate.service;

import cn.hutool.core.util.ObjectUtil;
import org.haier.shopUpdate.dao.GoodsBatchDao;
import org.haier.shopUpdate.dao.ShopsConfigDao;
import org.haier.shopUpdate.entity.ShopsConfig;
import org.haier.shopUpdate.enums.GoodsInPriceTypeEnums;
import org.haier.shopUpdate.params.goodBatch.GoodBatchListQueryParams;
import org.haier.shopUpdate.result.goodBatch.GoodBatchListQueryDto;
import org.haier.shopUpdate.util.ShopsResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * @ClassName GoodBatchServiceImpl
 * <AUTHOR>
 * @Date 2024/4/28 14:10
 */
@Service
@Transactional
public class GoodBatchServiceImpl implements GoodBatchService {
    @Resource
    private GoodsBatchDao goodsBatchDao;
    @Resource
    private ShopsConfigDao shopsConfigDao;

    @Override
    public ShopsResult queryGoodBatchList(GoodBatchListQueryParams params) {
        ShopsResult result = new ShopsResult();
        if (params.getPageIndex() != null && params.getPageSize() != null) {
            params.setPageIndex((params.getPageIndex() - 1) * params.getPageSize());
        }
        if(ObjectUtil.equal(2,params.getStockType())){
            List<GoodBatchListQueryDto> list = goodsBatchDao.queryGoodsBatchOutList(params);
            int count = goodsBatchDao.queryGoodsBatchOutListCount(params);
            if (ObjectUtil.isNotEmpty(list)) {
                result.setData(list);
                result.setCount(count);
            } else {
                result.setData(Collections.EMPTY_LIST);
                result.setCount(0);
            }
        } else {
            ShopsConfig shopsConfig = shopsConfigDao.selectByShopUnique(params.getShopUnique());
            if ((ObjectUtil.isNotEmpty(shopsConfig)
                    && ObjectUtil.isNotEmpty(shopsConfig.getGoodsInPriceType())
                    && ObjectUtil.equals(GoodsInPriceTypeEnums.BATCH.getCode(), shopsConfig.getGoodsInPriceType()))
                    || ObjectUtil.isNotEmpty(params.getStockId())) {
                List<GoodBatchListQueryDto> list = goodsBatchDao.queryGoodsBatchInList(params);
                int count = goodsBatchDao.queryGoodsBatchInListCount(params);
                if (ObjectUtil.isNotEmpty(list)) {
                    result.setData(list);
                    result.setCount(count);
                } else {
                    result.setData(Collections.EMPTY_LIST);
                    result.setCount(0);
                }
            } else {
                result.setData(Collections.EMPTY_LIST);
                result.setCount(0);
            }
        }
        result.setStatus(1);
        result.setMsg("成功");
        return result;
    }
}
