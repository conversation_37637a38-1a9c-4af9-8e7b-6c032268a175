package org.haier.shopUpdate.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.github.wxpay.sdk.WXPayUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.haier.shopUpdate.dao.GoodsDao;
import org.haier.shopUpdate.dao.GoodsPositionDao;
import org.haier.shopUpdate.entity.GoodsPositionEntity;
import org.haier.shopUpdate.entity.GoodsPositionResponse;
import org.haier.shopUpdate.util.ShopsResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 通知公告Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-18
 */
@Service
public class GoodsPositionServiceImpl implements GoodsPositionService {

    @Resource
    private GoodsPositionDao goodsPositionDao;
    @Resource
    private GoodsDao goodsDao;

    @Override
    public ShopsResult selectList(String positionName,String shopUnique) {
        ShopsResult sr = new ShopsResult(1,"查询成功！");
        List<GoodsPositionEntity> goodsPositionEntities = goodsPositionDao.selectList(positionName,shopUnique);
        if (CollectionUtil.isNotEmpty(goodsPositionEntities)) {
            List<GoodsPositionResponse> parentsResp = new ArrayList<>();
            List<GoodsPositionEntity> list = new ArrayList<>();
            for (GoodsPositionEntity item : goodsPositionEntities) {
                if (ObjectUtil.equal(item.getParentId(), NumberUtils.LONG_ZERO)){
                    GoodsPositionResponse response = new GoodsPositionResponse();
                    org.springframework.beans.BeanUtils.copyProperties(item, response);
                    response.addCompletePositionName(null,null);
                    response.addCompletePositionId(null, null);
                    response.setLevel(NumberUtils.INTEGER_ONE);
                    parentsResp.add(response);
                    list.add(item);
                }
            }
            if (CollectionUtil.isNotEmpty(parentsResp)) {
                goodsPositionEntities.removeAll(list);
                addChildren(parentsResp,goodsPositionEntities);
                sr.setData(parentsResp);
            }else {
                sr.setData(new ArrayList<>());
            }
        }else {
            sr.setData(new ArrayList<>());
        }
        return sr;
    }


    @Override
    public ShopsResult add(GoodsPositionEntity vo) {
        ShopsResult sr = new ShopsResult(1,"新增成功!");
        //顶级父类的父类不需要校验产品
        if(ObjectUtil.equal(vo.getParentId(),NumberUtils.LONG_ZERO)){
            vo.setLevel(NumberUtils.INTEGER_ONE);
        }else {
            List<String> check = goodsDao.check(vo.getParentId());
            if (CollectionUtil.isNotEmpty(check)) {
                sr.setMsg("该货位上仍有商品["+ StringUtils.join(check,"、") +"]，不允许新增子节点！");
                sr.setStatus(2);
                return sr;
            }
            GoodsPositionEntity goodsPositionEntity = goodsPositionDao.queryDetail(vo.getParentId());
            if (ObjectUtil.isEmpty(goodsPositionEntity)) {
                sr.setStatus(2);
                sr.setMsg("没有父级货位");
                return sr;
            }
            if (goodsPositionEntity.getLevel()>=4) {
                sr.setStatus(2);
                sr.setMsg("超出货位层数");
                return sr;
            }
            vo.setLevel(goodsPositionEntity.getLevel()+NumberUtils.INTEGER_ONE);
        }
        if (goodsPositionDao.cheakName(vo.getParentId(),vo.getPositionName(),vo.getShopUnique())>0) {
            sr.setStatus(2);
            sr.setMsg("同一上级下的货位名称不可重复");
            return sr;
        }
        int result = goodsPositionDao.add(vo);
        if(result>0){
            sr.setData(vo);
            return sr;
        }
        sr.setStatus(2);
        sr.setMsg("添加失败");
        return sr;
    }

    @Override
    public ShopsResult queryDetail(Long id) {
        ShopsResult sr = new ShopsResult(1,"查询成功！");
        GoodsPositionEntity goodsPositionEntity = goodsPositionDao.queryDetail(id);
        GoodsPositionResponse response = new GoodsPositionResponse();
        org.springframework.beans.BeanUtils.copyProperties(goodsPositionEntity,response);
        sr.setData(response);
        return sr;
    }

    @Override
    public ShopsResult update(GoodsPositionEntity vo) {
        ShopsResult sr = new ShopsResult(1,"修改成功");
        try {

            if (goodsPositionDao.cheakName(vo.getParentId(),vo.getPositionName(),vo.getShopUnique())>0) {
                sr.setStatus(2);
                sr.setMsg("同一上级下的货位名称不可重复");
                return sr;
            }
            if(goodsPositionDao.update(vo)>0){
                return sr;
            }
            sr.setMsg("新增失败");
            sr.setStatus(2);
            return sr;
        }catch (Exception e){
            WXPayUtil.getLogger().error(e.getMessage());
        }
        sr.setMsg("新增失败");
        sr.setStatus(2);
        return sr;
    }

    @Override
    public ShopsResult delete(Long id) {
        ShopsResult sr = new ShopsResult(1,"删除成功!");
        if (goodsDao.checkgoodsPosition(id)>0) {
            sr.setStatus(2);
            sr.setMsg("此货位已挂在商品不允许添加子货位");
            return sr;
        }
        Integer i = goodsPositionDao.queryCountByParent(id);
        if (i>0) {
            sr.setStatus(2);
            sr.setMsg("只能删除末端货位");
            return sr;
        }
        if(goodsPositionDao.delete(id)>0){
            return sr;
        }
        return sr;
    }

    @Override
    public  Map<String, String> queryDetailByPositionId(List<String> ids) {
        ids.removeIf(Objects::isNull);
        Map<String, String> result = new HashMap<>();
        if (CollectionUtil.isNotEmpty(ids)) {
            List<GoodsPositionEntity> goodsPositionEntities = goodsPositionDao.selectList(null,null);
            Map<Long, String> goodsPositionIdAndName = new HashMap<>();
            if(CollectionUtil.isNotEmpty(goodsPositionEntities)){
                for (GoodsPositionEntity goodsPositionEntity : goodsPositionEntities) {
                    goodsPositionIdAndName.put(goodsPositionEntity.getId(),goodsPositionEntity.getPositionName());
                }
            }else {
                return new HashMap<>();
            }
            for (String id : ids) {
                String[] splits = id.split(",");
                String name ="";
                for (String split : splits) {
                    if (StringUtils.isBlank(split)){
                        continue;
                    }
                    String s = goodsPositionIdAndName.get(Long.valueOf(split));
                    name =name.trim();
                    if(StrUtil.isNotEmpty(name)) {
                        name = name+ "-"+(StrUtil.isNotEmpty(s)?s:"");
                    }else {
                        name = s;
                    }
                }
                result.put(id,name);
            }
            return result;
        }
        return new HashMap<>();
    }

    @Override
    public ShopsResult check(Long id) {
        ShopsResult sr = new ShopsResult(1, "上传成功!");
        List<String> check = goodsDao.check(id);
        if (CollectionUtil.isEmpty(check)) {
            return sr;
        }
        sr.setMsg("该货位上仍有商品["+ StringUtils.join(check,"、") +"]，不允许删除！");
        sr.setStatus(2);
        return sr;
    }


    private void addChildren(List<GoodsPositionResponse> parents, List<GoodsPositionEntity> goodsPositionEntities){
        if (CollectionUtil.isEmpty(parents)) {
            return;
        }
        for (GoodsPositionResponse parent : parents) {
            for (GoodsPositionEntity goodsPositionEntity : goodsPositionEntities) {
                if (ObjectUtil.equal(parent.getId(),goodsPositionEntity.getParentId())) {
                    GoodsPositionResponse response = new GoodsPositionResponse();
                    org.springframework.beans.BeanUtils.copyProperties(goodsPositionEntity,response);
                    response.addCompletePositionName(parent.getPositionName(),parent.getCompletePositionName());
                    response.addCompletePositionId(parent.getId(),parent.getCompletePositionId());
                    parent.addChildren(response);
                    List<GoodsPositionResponse> p = new ArrayList<>();
                    p.add(response);
                    addChildren(p,goodsPositionEntities);
                }
            }
        }
    }
}
