package org.haier.shopUpdate.service;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.haier.shopUpdate.dao.UtilDao;
import org.haier.shopUpdate.util.ShopsResult;
import org.springframework.stereotype.Service;

@Service
public class UtilServiceImpl implements UtilService{
	@Resource
	private UtilDao utilDao;
	
	/**
	 * 将店铺中重复的商品删除并将云库中没有的商品添加到云库中
	 * @param map
	 * @return
	 */
	public ShopsResult deleteSameGoods(Map<String,Object> map){
		ShopsResult sr=new ShopsResult();
		List<String> list=utilDao.getSameGoodsInShops(map);
		int k=0;
		if(null==list||list.isEmpty()){
			sr.setStatus(1);
			sr.setMsg("没有重复商品!");
//			System.out.println("没有重复商品");
			return sr;
		}else{
			k =utilDao.deleteSameGoods(list);
		}
		//将店铺中有而云库中没有的商品信息添加到云库中
		k=utilDao.addShopsGoodsToCloud(map);
		System.out.println("新增的商品：：："+k);
		sr.setStatus(1);
		sr.setMsg("删除成功！");
		return sr;
	}
}
