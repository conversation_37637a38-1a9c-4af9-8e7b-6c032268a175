package org.haier.shopUpdate.service;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.haier.shopUpdate.entity.PalmResult;
import org.haier.shopUpdate.util.NoteResult;
import org.haier.shopUpdate.util.ShopsResult;

public interface PurService {
	/**
	 * 修改购物车商品数量
	 * @param map
	 * @return
	 */
	public ShopsResult modifyCartGoods(Map<String,Object> map);
	
	/**
	 * 修改购物车商品数量
	 * @param map
	 * @return
	 */
	public ShopsResult modifyCartGoodsNew(Map<String,Object> map,String giftMessage,String goodsGiftMessage);
	/**
	 * 购物车商品数量查询
	 * @param map
	 * @return
	 */
	public ShopsResult cartGoodsCount(Map<String,Object> map);
	/**
	 * 供货商全部商品全选或反选
	 * @param map
	 * @return
	 */
	public ShopsResult modifyCartCheckedAll(Map<String,Object> map);
	
	/**
	 * 直接修改商品购物车数量
	 * @param map
	 * @return
	 */
	public ShopsResult directlyModifyCartGoods(Map<String,Object> map);
	
	/**
	 * 直接修改商品购物车数量（新）
	 * @param map
	 * @return
	 */
	public ShopsResult directlyModifyCartGoodsNew(Map<String,Object> map,String giftMessage,String goodsGiftMessage );
	 
	public ShopsResult countSurplusGoods(Map<String, Object> map);

	public ShopsResult batchModifyCartGoods(Map<String, Object> map);

	public ShopsResult unsalableGoods(Map<String, Object> map);

	public ShopsResult bigKindCountCost(Map<String, Object> map);

	public ShopsResult smallKindCountCost(Map<String, Object> map);

	public ShopsResult smallKindCountProportion(Map<String, Object> map);

	public ShopsResult bigKindCountProportion(Map<String, Object> map);
	
	/**
	 * 购物车商品信息查询
	 * @param map
	 * @return
	 */
	public ShopsResult purCartGoodsSearch(Map<String,Object> map);
	/**
	 * 购物车商品信息查询（新版，根据供货商满赠信息，自动添加满赠商品，并返回信息；
	 * @param map
	 * @return
	 */
	public ShopsResult purCartGoodsSearchNew(Map<String,Object> map);
	public PalmResult paySupOrder(Map<String, Object> map);

	public PalmResult queryPurList(String shop_unique, Integer receipt_status, Integer pageNum, Integer pageSize,Long supplierUnique,String purMessage);

	public NoteResult subCartGoods(HttpServletRequest request, String shop_unique, String[] goodsBarcodes,String remarks);
	/**
	 * 购物车商品提交（新）
	 * @param request
	 * @param shop_unique
	 * @param goodsBarcodes
	 * @param remarks
	 * @return
	 */
	public NoteResult subCartGoodsNew(HttpServletRequest request, String shop_unique, String[] goodsBarcodes,String remarks);

	public PalmResult payOrders(String purchaseListUniques);
	
	/**
	 * 采购订单详情查询
	 * @param map
	 * @return
	 */
	public ShopsResult queryPurListDetail(Map<String,Object> map);
	/**
	 * 采购订单详情查询（新）
	 * @param map
	 * @return
	 */
	public ShopsResult queryPurListDetailNew(Map<String,Object> map);
	
	/**
	 * 更新订单处理状态，确认收货
	 * @param shopUnique
	 * @param purListUnique
	 * @param sameType
	 * @return
	 */
	public ShopsResult updateReceiptStatus(String shopUnique,String purListUnique,Integer sameType,Integer receiptStatus);
	
	/**
	 * 采购订单个状态数量查询
	 * @param map
	 * @return
	 */
	public ShopsResult shopPurListCount(Map<String,Object> map);
	/**
	 * 编辑界面：删除购物车选中商品
	 * @param map
	 * @return
	 */
	public ShopsResult purGoodsDelete(Map<String,Object> map);
	
	/**
	 * 结算
	 * @param shop_unique
	 * @param goodsBarcodes
	 * @param purchase_list_parunique
	 * @return
	 */
	public ShopsResult toSubmitCart(Long purListUnique,String[] goodsBarcodes);
	
	/**
	 * 结算
	 * @param shop_unique
	 * @param goodsBarcodes
	 * @param purchase_list_parunique
	 * @return
	 */
	public ShopsResult toSubmitCartNew(Long purListUnique,String[] goodsBarcodes);
	/**
	 * 取消订单
	 * @param purListUnique
	 * @param receiptStatus
	 * @return
	 */
	public ShopsResult cancelPurList(Long purListUnique,Integer  receiptStatus);
	
	/**
	 * 商品购物车详情查询测试
	 * @param map
	 * @return
	 */
	public ShopsResult queryBottomGoodsMessage(Map<String,Object> map);
	
	/**
	 * 更新购物车商品选中状态
	 * @param map
	 * @return
	 */
	public ShopsResult modifyCartChecked(Map<String,Object> map,String giftMessage);
	
	/**
	 * 购物车某供货商商品全选
	 * @param map
	 * @param giftMessage
	 * @param checked
	 * @return
	 */
	public  ShopsResult modifyCartCheckedSup(Map<String,Object> map,String giftMessage,Integer checked);

	public void sellListTask();

	public ShopsResult querySellList(Map<String, Object> map);

	public void closeEndTimeOrder();
	
}
