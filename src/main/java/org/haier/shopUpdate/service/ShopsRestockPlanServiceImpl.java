package org.haier.shopUpdate.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.haier.shopUpdate.dao.GoodsDao;
import org.haier.shopUpdate.dao.ShopSupplierDao;
import org.haier.shopUpdate.dao.ShopsRestockPlanDao;
import org.haier.shopUpdate.entity.*;
import org.haier.shopUpdate.params.restockPlan.*;
import org.haier.shopUpdate.params.shopSupBill.externalCall.BillDetailInfoParams;
import org.haier.shopUpdate.params.shopSupBill.externalCall.BillInfoParams;
import org.haier.shopUpdate.params.shopSupBill.externalCall.CustomerGoodsDeleteParams;
import org.haier.shopUpdate.params.shopSupBill.supExternalCall.CustomerGoodsBingParams;
import org.haier.shopUpdate.params.shopSupBill.supExternalCall.OrderAddParams;
import org.haier.shopUpdate.params.shopSupBill.supExternalCall.OrderDetailAddParams;
import org.haier.shopUpdate.params.shopSupplier.SupplierUniqueParams;
import org.haier.shopUpdate.result.restockPlan.*;
import org.haier.shopUpdate.util.OrderNoUtils;
import org.haier.shopUpdate.util.ShopsResult;
import org.haier.shopUpdate.util.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

@Service
@Transactional
public class ShopsRestockPlanServiceImpl implements ShopsRestockPlanService {
    private final static String URL = ResourceBundle.getBundle("config").getString("url");
    @Autowired
    private ShopSupBillService shopSupBillService;
    @Autowired
    private ShopSupplierService shopSupplierService;
    @Autowired
    private ShopsRestockPlanDao shopsRestockPlanDao;
    @Autowired
    private ShopSupplierDao shopSupplierDao;
    @Autowired
    private GoodsDao goodsDao;

    @Override
    public ShopsResult addRestockPlan(AddRestockPlanParams params) {
        ShopsResult sr = new ShopsResult();
        RestockPlanEntity entity = new RestockPlanEntity();
        entity.setShopUnique(params.getShopUnique());
        entity.setCreateUser(params.getLoginUser());
        if (null == params.getRestockPlanName() || params.getRestockPlanName().isEmpty()) {
            entity.setShopRestockplanName(DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        } else {
            entity.setShopRestockplanName(params.getRestockPlanName());
        }
        entity.setRestockNo(OrderNoUtils.createOrderNo("R"));
        shopsRestockPlanDao.addRestockPlan(entity);
        sr.setStatus(1);
        sr.setMsg("添加成功！");
        sr.setData(entity.getShopRestockplanId());
        return sr;
    }

    @Override
    public ShopsResult deleteRestockPlan(DeleteRestockPlanParams params) {
        ShopsResult sr = new ShopsResult();
        Map<String, Object> map = BeanUtil.beanToMap(params);
        RestockPlanEntity entity = shopsRestockPlanDao.queryRestockPlanById(map);
        if (null == entity || entity.getShopRestockplanId() == null) {
            sr.setStatus(0);
            sr.setMsg("该编号无对应计划信息！");
        } else {
            if (entity.getStatus() == 2) {
                sr.setStatus(0);
                sr.setMsg("该补货计划已提交无法删除！");
                return sr;
            }
            RestockPlanGoodsParams restockPlanGoodsParams = new RestockPlanGoodsParams();
            restockPlanGoodsParams.setPageSize(100000);
            restockPlanGoodsParams.setPageIndex(0);
            restockPlanGoodsParams.setShopUnique(params.getShopUnique());
            restockPlanGoodsParams.setRestockPlanId(params.getShopRestockplanId());
            Map<String, Object> goodsMap = BeanUtil.beanToMap(restockPlanGoodsParams);
            List<RestockPlanGoodsResult> list = shopsRestockPlanDao.queryGoodsListByPlanId(goodsMap);
            for (int i = 0; list != null && !list.isEmpty() && i < list.size(); i++) {
                RestockPlanGoodsResult restockPlanGoodsResult = list.get(i);
                Map<String, Object> deleteRestockPlanGoodsMap = new HashMap<>();
                deleteRestockPlanGoodsMap.put("shopRestockplanGoodsId", restockPlanGoodsResult.getShopRestockplanGoodsId());
                deleteRestockPlanGoodsMap.put("shopRestockplanId", params.getShopRestockplanId());
                deleteRestockPlanGoodsMap.put("shopUnique", params.getShopUnique());
                shopsRestockPlanDao.deleteRestockPlanGoods(deleteRestockPlanGoodsMap);
            }
            shopsRestockPlanDao.deleteRestockPlanPresent(map);
            shopsRestockPlanDao.deleteRestockPlan(map);
            sr.setStatus(1);
            sr.setMsg("删除成功！");
        }
        return sr;
    }

    @Override
    public ShopsResult queryRestockPlanList(RestockPlanListParams params) {
        ShopsResult sr = new ShopsResult();
        //请求参数处理
        params.setPageIndex((params.getPageIndex() - 1) * params.getPageSize());
        if (ObjectUtil.isNotEmpty(params.getStartDate())) {
            params.setStartDate(StringUtils.join(params.getStartDate(), " 00:00:00"));
        }
        if (ObjectUtil.isNotEmpty(params.getEndDate())) {
            params.setEndDate(StringUtils.join(params.getEndDate(), " 23:59:59"));
        }

        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("shopUnique", params.getShopUnique());
        queryMap.put("startDate", params.getStartDate());
        queryMap.put("endDate", params.getEndDate());
        queryMap.put("planStatus", params.getPlanStatus());
        queryMap.put("pageIndex", params.getPageIndex());
        queryMap.put("pageSize", params.getPageSize());
        queryMap.put("planName", params.getPlanName());
        List<RestockPlanListResult> list = shopsRestockPlanDao.queryRestockPlanList(queryMap);
        sr.setStatus(1);
        sr.setMsg("查询成功！");
        sr.setData(list);
        return sr;
    }

    @Override
    public ShopsResult queryGoodsListByPlanId(RestockPlanGoodsParams params) {
        ShopsResult sr = new ShopsResult();
        params.setPageIndex((params.getPageIndex() - 1) * params.getPageSize());
        Map<String, Object> map = BeanUtil.beanToMap(params);
        List<RestockPlanGoodsResult> list = shopsRestockPlanDao.queryGoodsListByPlanId(map);
        sr.setStatus(1);
        sr.setMsg("查询成功！");
        sr.setData(list);
        return sr;
    }

    @Override
    public ShopsResult addRestockPlanGoods(RestockPlanGoodsAddParams params) {
        ShopsResult sr = new ShopsResult();
        Map<String, Object> supMap = new HashMap<>();
        supMap.put("restockPlanId", params.getRestockPlanId());
        supMap.put("shopUnique", params.getShopUnique());
        supMap.put("supplierUnique", params.getSupplierUnique());
        Map<String, Object> supResult = shopsRestockPlanDao.querySupplierByPlanId(supMap);
        Long shopRestockplanPresentId;
        if (null == supResult || supResult.isEmpty()) {
            RestockPlanSupplierEntity supplierEntity = new RestockPlanSupplierEntity();
            supplierEntity.setShopRestockplanId(params.getRestockPlanId());
            supplierEntity.setShopUnique(params.getShopUnique());
            supplierEntity.setSupplierUnique(params.getSupplierUnique());
            supplierEntity.setCreateUser(params.getCreateUser());
            int i = shopsRestockPlanDao.addRestockPlanSupplier(supplierEntity);
            shopRestockplanPresentId = supplierEntity.getShopRestockplanPresentId();
        } else {
            shopRestockplanPresentId = Long.valueOf(String.valueOf(supResult.get("shop_restockplan_present_id")));
        }
        QueryGoodsByIdParams goodsByIdParams = new QueryGoodsByIdParams();
        goodsByIdParams.setRestockPlanId(params.getRestockPlanId());
        goodsByIdParams.setShopUnique(params.getShopUnique());
        goodsByIdParams.setGoodsBarcode(params.getGoodsBarcode());
        RestockPlanGoodsEntity goodsResult = shopsRestockPlanDao.queryGoodsById(goodsByIdParams);
        if (null != goodsResult && null != goodsResult.getShopRestockplanGoodsId()) {
            Long shopRestockPlanGoodsId = goodsResult.getShopRestockplanGoodsId();
            ModifyGoodsParams modifyGoodsParams = new ModifyGoodsParams();
            modifyGoodsParams.setShopRestockplanGoodsId(shopRestockPlanGoodsId);
            modifyGoodsParams.setGoodsBarcode(goodsResult.getGoodsBarcode());
            modifyGoodsParams.setDelFlag(1);
            modifyGoodsParams.setGoodsCount(goodsResult.getGoodsCount().add(params.getGoodsCount()));
            modifyGoodsParams.setShopRestockplanPresentId(shopRestockplanPresentId);
            modifyGoodsParams.setShopRestockplanId(params.getRestockPlanId());
            modifyGoodsParams.setShopUnique(params.getShopUnique());
            modifyGoodsParams.setGoodsInPrice(params.getGoodsInPrice());
            this.modifyRestockPlanGoods(modifyGoodsParams);
        } else {
            RestockPlanGoodsEntity goodsEntity = new RestockPlanGoodsEntity();
            goodsEntity.setShopRestockplanId(params.getRestockPlanId());
            goodsEntity.setShopRestockplanPresentId(shopRestockplanPresentId);
            goodsEntity.setGoodsBarcode(params.getGoodsBarcode());
            goodsEntity.setShopUnique(params.getShopUnique());
            goodsEntity.setGoodsCount(params.getGoodsCount());
            goodsEntity.setGoodsInPrice(params.getGoodsInPrice());
            int j = shopsRestockPlanDao.addRestockPlanGoods(goodsEntity);
        }
        sr.setStatus(1);
        sr.setMsg("添加成功！");
        sr.setData(null);
        return sr;
    }

    @Override
    public ShopsResult updatePlanStatus(UpdateStatusParams params) {
        ShopsResult sr = new ShopsResult();
        RestockPlanGoodsParams goodsParams = new RestockPlanGoodsParams();
        goodsParams.setShopUnique(params.getShopUnique());
        goodsParams.setRestockPlanId(params.getRestockPlanId());
        goodsParams.setPageIndex(0);
        goodsParams.setPageSize(1);
        Map<String, Object> goodsMap = BeanUtil.beanToMap(goodsParams);
        List<RestockPlanGoodsResult> list = shopsRestockPlanDao.queryGoodsListByPlanId(goodsMap);
        if ((list == null || list.isEmpty()) && params.getPlanStatus() == 2) {
            sr.setStatus(0);
            sr.setMsg("当前计划下未添加商品，不能提交！");
        } else if (params.getPlanStatus() == 2) {
            //向供应商APP推送新增补货单
            Map<String, Object> planMap = new HashMap<>();
            planMap.put("shopUnique", params.getShopUnique());
            planMap.put("restockPlanId", params.getRestockPlanId());
            List<RestockPlanSuppliersResult> planList = shopsRestockPlanDao.getPresentListByPlanId(planMap);
            for (int j = 0; null != planList && !planList.isEmpty() && j < planList.size(); j++) {
                RestockPlanSuppliersResult restockPlanSuppliersResult = planList.get(j);
                if (restockPlanSuppliersResult.getEnableStatus() != 1) {
                    sr.setStatus(0);
                    sr.setMsg("供应商【" + restockPlanSuppliersResult.getSupplierName() + "】已解除绑定，请先更换商品供货商后提交补货任务！");
                    return sr;
                }
            }
            Map<String, Object> map = BeanUtil.beanToMap(params);
            int i = shopsRestockPlanDao.updatePlanStatus(map);
            List<OrderAddParams> orderList = new ArrayList<>();
            for (int j = 0; null != planList && !planList.isEmpty() && j < planList.size(); j++) {
                OrderAddParams orderAddParams = new OrderAddParams();
                RestockPlanSuppliersResult restockPlanSuppliersResult = planList.get(j);
                if (restockPlanSuppliersResult.getSupplierExamineId() != null) {
                    orderAddParams.setCustomerUnique(String.valueOf(restockPlanSuppliersResult.getShopUnique()));
                    orderAddParams.setSupplierUnique(restockPlanSuppliersResult.getSupplierUnique());
                    orderAddParams.setOrderNo(restockPlanSuppliersResult.getOrderNo());
                    orderAddParams.setRemark(restockPlanSuppliersResult.getRemark());
                    List<OrderDetailAddParams> detailList = new ArrayList<>();
                    List<RestockPlanGoodsResult> goodsList = restockPlanSuppliersResult.getGoodsList();
                    orderAddParams.setCategoryCount(goodsList.size());
                    BigDecimal totalCount = BigDecimal.ZERO;
                    for (int k = 0; !goodsList.isEmpty() && k < goodsList.size(); k++) {
                        OrderDetailAddParams orderDetailAddParams = new OrderDetailAddParams();
                        RestockPlanGoodsResult restockPlanGoodsResult = goodsList.get(k);
                        orderDetailAddParams.setOrderNo(restockPlanSuppliersResult.getOrderNo());
                        orderDetailAddParams.setGoodsBarcode(restockPlanGoodsResult.getGoodsBarcode());
                        orderDetailAddParams.setGoodsCount(restockPlanGoodsResult.getGoodsCount());
                        totalCount = totalCount.add(restockPlanGoodsResult.getGoodsCount());
                        detailList.add(orderDetailAddParams);
                    }
                    orderAddParams.setTotalCount(totalCount);
                    orderAddParams.setDetailList(detailList);
                    orderList.add(orderAddParams);
                } else {
                    BillInfoParams billInfoParams = new BillInfoParams();
                    billInfoParams.setBillNo(OrderNoUtils.createOrderNo("B"));
                    billInfoParams.setSupplierUnique(restockPlanSuppliersResult.getSupplierUnique());
                    billInfoParams.setCustomerUnique(String.valueOf(restockPlanSuppliersResult.getShopUnique()));
                    billInfoParams.setCategoryCount(restockPlanSuppliersResult.getGoodsCounts());
                    billInfoParams.setTotalMoney(restockPlanSuppliersResult.getGoodsTotal());
                    billInfoParams.setRemark(restockPlanSuppliersResult.getRemark());
                    billInfoParams.setStatus(1);
                    List<BillDetailInfoParams> billDetailInfoParamsList = new ArrayList<>();
                    List<RestockPlanGoodsResult> goodsList = restockPlanSuppliersResult.getGoodsList();
                    BigDecimal totalCount = BigDecimal.ZERO;
                    for (int k = 0; !goodsList.isEmpty() && k < goodsList.size(); k++) {
                        BillDetailInfoParams billDetailInfoParams = new BillDetailInfoParams();
                        RestockPlanGoodsResult restockPlanGoodsResult = goodsList.get(k);
                        billDetailInfoParams.setOrderNo(restockPlanSuppliersResult.getOrderNo());
                        billDetailInfoParams.setGoodsBarcode(restockPlanGoodsResult.getGoodsBarcode());
                        billDetailInfoParams.setGoodsName(restockPlanGoodsResult.getGoodsName());
                        billDetailInfoParams.setGoodsUnit(restockPlanGoodsResult.getGoodsUnit());
                        billDetailInfoParams.setGoodsCount(restockPlanGoodsResult.getGoodsCount());
                        billDetailInfoParams.setGoodsPurchasePrice(String.valueOf(restockPlanGoodsResult.getGoodsInPrice()));
                        billDetailInfoParams.setGoodsPurchaseUnit(restockPlanGoodsResult.getGoodsUnit());
                        billDetailInfoParams.setGoodsPurchaseCount(restockPlanGoodsResult.getGoodsCount());
                        billDetailInfoParams.setGoodsSalePrice(restockPlanGoodsResult.getGoodsInPrice());
                        billDetailInfoParams.setGoodsImageUrl(restockPlanGoodsResult.getGoodsPicturepath());
                        billDetailInfoParams.setGoodsProduceDate(DateUtil.format(new Date(), "yyyy/MM/dd"));
                        billDetailInfoParams.setSubtotalMoney(restockPlanGoodsResult.getGoodsTotal());
                        billDetailInfoParamsList.add(billDetailInfoParams);
                        totalCount = totalCount.add(restockPlanGoodsResult.getGoodsCount());
                    }
                    billInfoParams.setTotalCount(totalCount);
                    billInfoParams.setDetail(billDetailInfoParamsList);
                    shopSupBillService.addShopSupBill(billInfoParams);
                }
            }
            if (!orderList.isEmpty()) {
                String data = JSONUtil.toJsonStr(orderList);
                ShopsResult result1 = httpPost(URL + "/external/customerOrder/add", data);
                if (result1.getStatus() == 0) {
                    sr.setStatus(0);
                    sr.setMsg(result1.getMsg());
                    throw new RuntimeException(result1.getMsg());
                }
            }
            sr.setStatus(1);
            sr.setMsg("提交成功！");
        } else if (params.getPlanStatus() == 3) {
            Map<String, Object> map = BeanUtil.beanToMap(params);
            int i = shopsRestockPlanDao.updatePlanStatus(map);
            sr.setStatus(1);
            sr.setMsg("取消成功！");
        }
        return sr;
    }

    @Override
    public ShopsResult getPresentListByPlanId(QueryGoodsListGroupBySupplierParams params) {
        ShopsResult sr = new ShopsResult();
        Map<String, Object> map = BeanUtil.beanToMap(params);
        List<RestockPlanSuppliersResult> list = shopsRestockPlanDao.getPresentListByPlanId(map);
        sr.setStatus(1);
        sr.setMsg("查询成功！");
        sr.setData(list);
        return sr;
    }

    @Override
    public ShopsResult updateSupplier(RestockPlanGoodsUpdateSupplierParams params) {
        ShopsResult sr = new ShopsResult();
        Map<String, Object> goodsMap = new HashMap<>();
        goodsMap.put("shopUnique", params.getShopUnique());
        goodsMap.put("goodsBarcode", params.getGoodsBarcode());
        ShopSupGoodsEntity goodsEntity = shopSupplierDao.queryShopSupGoodsByGoodsBarcode(goodsMap);
        if (goodsEntity != null) {
            if (goodsEntity.getRecordFlag() != 1) {
                sr.setStatus(0);
                sr.setMsg("该商品未建档，不能更换供货商");
                return sr;
            }

            //将不同规格的商品供货商同步修改
            Map<String,Object> queryOneByParamMap = new HashMap<>();
            queryOneByParamMap.put("shopUnique",params.getShopUnique());
            queryOneByParamMap.put("goodsBarcode",params.getGoodsBarcode());
            GoodsEntity goodsEntity1 = goodsDao.queryOneByParam(queryOneByParamMap);
            GoodsEntity goods = new GoodsEntity();
            goods.setShopUnique(params.getShopUnique());
            goods.setForeignKey(goodsEntity1.getForeignKey());
            List<GoodsEntity> goodsEntities = goodsDao.selectGoodsByParam(goods);
            if(goodsEntities != null){
                for (GoodsEntity goods1:
                        goodsEntities) {
                    if(!Objects.equals(goods1.getGoodsBarcode(), params.getGoodsBarcode())){
                        Map<String, Object> goodsForeignMap = new HashMap<>();
                        goodsForeignMap.put("shopUnique", params.getShopUnique());
                        goodsForeignMap.put("goodsBarcode", params.getGoodsBarcode());
                        ShopSupGoodsEntity goodsForeignEntity = shopSupplierDao.queryShopSupGoodsByGoodsBarcode(goodsForeignMap);
                        if (goodsForeignEntity != null) {
                            if (goodsForeignEntity.getRecordFlag() != 1) {
                                sr.setStatus(0);
                                sr.setMsg("不同规格商品【"+goods1.getGoodsName()+"】未建档，不能更换供货商");
                                return sr;
                            }
                        }
                    }
                }
            }
            Map<String, Object> map = BeanUtil.beanToMap(params);
            int i = shopSupplierDao.updateSupplier(map);
            bingGoods(params,goodsEntity,sr);

            if(goodsEntities != null){
                for (GoodsEntity goods1:
                        goodsEntities) {
                    if(!Objects.equals(goods1.getGoodsBarcode(), params.getGoodsBarcode())){
                        Map<String, Object> goodsForeignMap = new HashMap<>();
                        goodsForeignMap.put("shopUnique", params.getShopUnique());
                        goodsForeignMap.put("goodsBarcode", goods1.getGoodsBarcode());
                        ShopSupGoodsEntity goodsForeignEntity = shopSupplierDao.queryShopSupGoodsByGoodsBarcode(goodsForeignMap);
                        if (goodsForeignEntity != null) {
                            RestockPlanGoodsUpdateSupplierParams foreignParams = new RestockPlanGoodsUpdateSupplierParams();
                            BeanUtil.copyProperties(params,foreignParams);
                            foreignParams.setGoodsBarcode(goods1.getGoodsBarcode());
                            Map<String, Object> foreignMap = BeanUtil.beanToMap(foreignParams);
                            shopSupplierDao.updateSupplier(foreignMap);
                            bingGoods(foreignParams,goodsForeignEntity,sr);
                        } else {
                            Map<String, Object> foreignParams = new HashMap<>();
                            foreignParams.put("shopUnique", params.getShopUnique());
                            foreignParams.put("goodsBarcode", goods1.getGoodsBarcode());
                            foreignParams.put("supplierUnique", params.getSupplierUnique());
                            foreignParams.put("createId", "1");
                            foreignParams.put("createBy", "SYSTEM");
                            shopSupplierService.addSupGood(foreignParams);
                        }
                    }
                }
            }

        } else {
            sr.setStatus(0);
            sr.setMsg("建档商品中无此商品，不能更换供货商");
        }
        return sr;
    }

    private void bingGoods(RestockPlanGoodsUpdateSupplierParams params, ShopSupGoodsEntity goodsEntity,ShopsResult sr){
        if (!params.getSupplierUnique().equals(goodsEntity.getSupplierUnique())) {
            SupplierUniqueParams supplierUniqueParams = new SupplierUniqueParams();
            supplierUniqueParams.setShopUnique(params.getShopUnique());
            supplierUniqueParams.setSupplierUnique(params.getSupplierUnique());
            ShopSupSupplierExamineEntity shopSupSupplierExamineEntity = shopSupplierDao.queryShopSupplierExamineBySupplierUnique(supplierUniqueParams);
            if (shopSupSupplierExamineEntity != null) {
                CustomerGoodsBingParams customerGoodsBingParams = new CustomerGoodsBingParams();
                customerGoodsBingParams.setSupplierUnique(params.getSupplierUnique());
                customerGoodsBingParams.setCustomerUnique(String.valueOf(params.getShopUnique()));
                if (goodsEntity.getSupplierUnique() != null) {
                    Map<String, Object> oldMap = new HashMap<>();
                    oldMap.put("shopUnique", params.getShopUnique());
                    oldMap.put("supplierUnique", goodsEntity.getSupplierUnique());
                    ShopSupSupplierExamineEntity oldShopSupSupplierExamineEntity = shopSupplierDao.queryShopSupplierByUnique(oldMap);
                    if (oldShopSupSupplierExamineEntity != null) {
                        customerGoodsBingParams.setOldSupplierUnique(goodsEntity.getSupplierUnique());
                    }
                }
                customerGoodsBingParams.setGoodsBarcode(goodsEntity.getGoodsBarcode());
                customerGoodsBingParams.setGoodsName(goodsEntity.getGoodsName());
                customerGoodsBingParams.setGoodsUnit(goodsEntity.getGoodsUnit());
                customerGoodsBingParams.setExpirationDate(goodsEntity.getExpirationDate());
                customerGoodsBingParams.setGoodsImageUrl(goodsEntity.getGoodsImageUrl());
                customerGoodsBingParams.setGoodsSalePrice(goodsEntity.getGoodsSalePrice());
                String data = JSONUtil.toJsonStr(customerGoodsBingParams);
                ShopsResult result1 = httpPost(URL + "/external/goods/bingGoods", data);
                if (result1.getStatus() == 0) {
                    sr.setStatus(0);
                    sr.setMsg(result1.getMsg());
                    throw new RuntimeException(result1.getMsg());
                }


            } else {
                if (goodsEntity.getSupplierUnique() != null && !goodsEntity.getSupplierUnique().isEmpty()) {
                    CustomerGoodsDeleteParams customerGoodsDeleteParams = new CustomerGoodsDeleteParams();
                    customerGoodsDeleteParams.setSupplierUnique(goodsEntity.getSupplierUnique());
                    customerGoodsDeleteParams.setCustomerUnique(String.valueOf(params.getShopUnique()));
                    customerGoodsDeleteParams.setGoodsBarcode(goodsEntity.getGoodsBarcode());
                    String data = JSONUtil.toJsonStr(Collections.singletonList(customerGoodsDeleteParams));
                    ShopsResult result1 = httpPost(URL + "/external/goods/deleteBingGoods", data);
                    if (result1.getStatus() == 0) {
                        sr.setStatus(0);
                        sr.setMsg(result1.getMsg());
                        throw new RuntimeException(result1.getMsg());
                    }
                }
            }
            sr.setStatus(1);
            sr.setMsg("更改供应商成功！");
        }
    }
    @Override
    public ShopsResult getGoodsSupplierMsg(GetGoodsSupplierMsgParams params) {
        ShopsResult result = new ShopsResult();
        String shopUnique = params.getShopUnique();
        List<Map<String, Object>> supList = shopSupplierDao.getGoodsSupplierMsg(shopUnique);
        result.setStatus(1);
        result.setMsg("查询成功！");
        result.setData(supList);
        return result;
    }

    @Override
    public ShopsResult modifyRestockPlanGoods(ModifyGoodsParams params) {
        ShopsResult sr = new ShopsResult();
        if (null != params.getDelFlag()) {
            if (!(1 == params.getDelFlag() || 2 == params.getDelFlag())) {
                sr.setStatus(0);
                sr.setMsg("商品更新标志为1,2，请修改后重新调用");
                return sr;
            }
        } else {
            sr.setStatus(0);
            sr.setMsg("商品更新标志不能为空");
            return sr;
        }
        Map<String, Object> map = BeanUtil.beanToMap(params);
        map.remove("delFlag");
        if (1 == params.getDelFlag()) {
            if (ObjectUtil.isNotEmpty(checkUpdateGoodsParams(params))) {
                sr.setStatus(0);
                sr.setMsg(checkUpdateGoodsParams(params));
                return sr;
            }
            int i = shopsRestockPlanDao.updateRestockPlanGoods(map);
            if (i == 1) {
                sr.setStatus(1);
                sr.setMsg("修改成功！");
            } else {
                sr.setStatus(1);
                sr.setMsg("修改失败！");
            }
        } else if (2 == params.getDelFlag()) {
            Map<String, Object> goodsListMap = BeanUtil.beanToMap(params);
            Map<String, Object> goodsListResult = shopsRestockPlanDao.queryGoodsListByGoodsId(goodsListMap);
            if (goodsListResult != null && Integer.parseInt(String.valueOf(goodsListResult.get("count"))) == 1) {
                String presentId = String.valueOf(goodsListResult.get("presentId"));
                Map<String, Object> deletePresentMap = BeanUtil.beanToMap(params);
                deletePresentMap.put("presentId", presentId);
                shopsRestockPlanDao.deleteRestockPlanPresent(deletePresentMap);
            }
            int i = shopsRestockPlanDao.deleteRestockPlanGoods(map);
            if (i == 1) {
                sr.setStatus(1);
                sr.setMsg("删除成功！");
            } else {
                sr.setStatus(0);
                sr.setMsg("删除失败！");
            }
        }
        return sr;
    }

    @Override
    public ShopsResult queryGoodsListBySupplierId(QueryGoodsBySupplierParams params) {
        ShopsResult result = new ShopsResult();
        Map<String, Object> map = BeanUtil.beanToMap(params);
        List<RestockPlanSupplierResult> supResult = shopsRestockPlanDao.queryGoodsListBySupplierId(map);
        result.setStatus(1);
        result.setMsg("查询成功！");
        result.setData(ObjectUtil.isNotEmpty(supResult) ? supResult.get(0) : null);
        return result;
    }

    @Override
    public ShopsResult queryGoodsDetail(QueryGoodsDetailParams params) {
        ShopsResult result = new ShopsResult();
        Map<String, Object> map = BeanUtil.beanToMap(params);
        RestockPlanGoodsDetailResult goods = shopsRestockPlanDao.queryGoodsSaleCount(map);
        if (null != goods) {
            if (goods.getGoodsChengType() != null) {
                if (goods.getGoodsChengType() == 0) {
                    BigDecimal count = new BigDecimal(String.valueOf(goods.getCount7())).add(new BigDecimal(String.valueOf(goods.getOutStockCount()))).subtract(new BigDecimal(String.valueOf(goods.getGoodsCount())));
                    if (count.compareTo(BigDecimal.ZERO) > 0) {
                        goods.setBestCount(count);
                    } else {
                        goods.setBestCount(BigDecimal.ZERO);
                    }
                } else if (goods.getGoodsChengType() == 1) {
                    BigDecimal count = new BigDecimal(String.valueOf(goods.getCount3())).add(new BigDecimal(String.valueOf(goods.getOutStockCount()))).subtract(new BigDecimal(String.valueOf(goods.getGoodsCount())));
                    if (count.compareTo(BigDecimal.ZERO) > 0) {
                        goods.setBestCount(count);
                    } else {
                        goods.setBestCount(BigDecimal.ZERO);
                    }
                }
            }
        }
        result.setStatus(1);
        result.setMsg("查询成功！");
        result.setData(goods);
        return result;
    }

    @Override
    public ShopsResult updateRestockPlanSupplier(RestockPlanSupplierUpdateParams params) {
        ShopsResult sr = new ShopsResult();
        Map<String, Object> map = BeanUtil.beanToMap(params);
        int i = shopsRestockPlanDao.updateRestockPlanSupplier(map);
        sr.setStatus(1);
        sr.setMsg("添加备注成功！");
        sr.setData(i);
        return sr;
    }

    @Override
    public ShopsResult restockAgain(DeleteRestockPlanParams params) {
        ShopsResult sr = new ShopsResult();
        Map<String, Object> map = BeanUtil.beanToMap(params);
        RestockPlanEntity entity = shopsRestockPlanDao.queryRestockPlanById(map);
        if (null == entity || entity.getShopRestockplanId() == null) {
            sr.setStatus(0);
            sr.setMsg("该编号无对应计划信息！");
        } else {
            Long oldPlanId = entity.getShopRestockplanId();
            entity.setShopRestockplanId(null);
            entity.setCreateTime(null);
            entity.setRestockNo(OrderNoUtils.createOrderNo("R"));
            shopsRestockPlanDao.addRestockPlan(entity);
            map.put("restockPlanId", oldPlanId);
            List<RestockPlanSupplierEntity> supList = shopsRestockPlanDao.querySupplier(map);
            if (null != supList && !supList.isEmpty()) {
                for (int i = 0; i < supList.size(); i++) {
                    RestockPlanSupplierEntity supplierEntity = supList.get(i);
                    map.put("shopRestockplanPresentId", supplierEntity.getShopRestockplanPresentId());
                    supplierEntity.setShopRestockplanId(entity.getShopRestockplanId());
                    supplierEntity.setShopRestockplanPresentId(null);
                    supplierEntity.setCreateTime(null);
                    shopsRestockPlanDao.addRestockPlanSupplier(supplierEntity);
                    List<RestockPlanGoodsEntity> goodsList = shopsRestockPlanDao.queryGoods(map);
                    if (null != goodsList && !goodsList.isEmpty()) {
                        for (int j = 0; j < goodsList.size(); j++) {
                            RestockPlanGoodsEntity goodsEntity = goodsList.get(j);
                            goodsEntity.setShopRestockplanGoodsId(null);
                            goodsEntity.setShopRestockplanId(entity.getShopRestockplanId());
                            goodsEntity.setShopRestockplanPresentId(supplierEntity.getShopRestockplanPresentId());
                            shopsRestockPlanDao.addRestockPlanGoods(goodsEntity);
                        }
                    }
                }
            }
            sr.setStatus(1);
            sr.setMsg("再次补货成功！");
            sr.setData(entity.getShopRestockplanId());
        }
        return sr;
    }

    private String checkUpdateGoodsParams(ModifyGoodsParams params) {
        StringBuilder str = new StringBuilder();

        if (null == params.getGoodsBarcode() || "".equals(String.valueOf(params.getGoodsBarcode()))) {
            str.append("商品编码不能为空;");
        }
        if (null == params.getGoodsCount() || "".equals(String.valueOf(params.getGoodsCount()))) {
            str.append("采购数量不能为空;");
        }
        return str.toString();
    }

    public ShopsResult httpPost(String url, String data) {
        ShopsResult result = new ShopsResult();
        System.out.println("-------------------------同步, 地址: {" + url + "},参数: {" + data + "}---------------");
        String result1 = HttpUtil.post(url, data);
        System.out.println("-------------------------同步结果: {" + result1 + "}---------------");
        JSONObject jo1 = JSONUtil.parseObj(result1);
        if (jo1.get("status") == null) {
            result.setStatus(0);
            result.setMsg("访问同步信息接口超时");
        } else if ("1".equals(String.valueOf(jo1.get("status")))) {
            result.setStatus(1);
            result.setMsg(String.valueOf(jo1.get("message")));
        } else {
            result.setStatus(0);
            result.setMsg(String.valueOf(jo1.get("message")));
        }
        return result;
    }

    /*private String checkDeleteGoodsParams(ModifyGoodsParams params) {
        StringBuilder str = new StringBuilder();
        if (null == params.getShopRestockplanGoodsId() || "".equals(String.valueOf(params.getShopRestockplanGoodsId()))) {
            str.append("商品ID不能为空;");
        }
        return str.toString();
    }*/
}
