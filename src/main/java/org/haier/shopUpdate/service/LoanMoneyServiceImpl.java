package org.haier.shopUpdate.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import lombok.extern.slf4j.Slf4j;
import cn.hutool.core.util.ObjectUtil;
import org.haier.shopUpdate.dao.LoanMoneyDao;
import org.haier.shopUpdate.oss.UploadResult;
import org.haier.shopUpdate.upload.FileUploadService;
import org.haier.shopUpdate.util.JPushClientUtil;
import org.haier.shopUpdate.util.ShopsResult;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import com.google.gson.JsonObject;
@Slf4j
@Service
public class LoanMoneyServiceImpl implements LoanMoneyService{

	@Resource
	private LoanMoneyDao loanMoneyDao;
	@Resource
	private FileUploadService fileUploadService;


	/**
	 * 查询订单还款列表详情
	 * @param shop_unique 店铺编号
	 * @param order_no 订单编号
	 * @param page 页码
	 * @param page_size 单页查询数量
	 * @return
	 */
	public ShopsResult queryOrderFenqiList(String shop_unique,String order_no,Integer page,Integer page_size) {
		ShopsResult sr = new ShopsResult(1,"查询成功！");

		Map<String,Object> map = new HashMap<String,Object>();
		map.put("order_no", order_no);
		map.put("startNum", (page - 1) * page_size);
		map.put("pageSize", page_size);
		System.out.println("查询信息!!!" + map);
		List<Map<String,Object>> list = loanMoneyDao.queryOrderFenqiList(map);
		Map<String,Object> resMap = loanMoneyDao.queryOrderFenqiCount(map);

		sr.setRows(list);
		sr.setData(resMap);

		return sr;
	}

	/**
	 * 提交店铺赊销申请信息
	 * @param shop_unique 店铺编号
	 * @param business_image 营业执照照片路径
	 * @param business_image_hand 手持营业执照照片路径
	 * @param ID_front_image 身份证正面照片
	 * @param ID_back_image 身份证反面照片（人脸面）
	 * @param ID_back_image_hand 手持身份证人脸面照片
	 * @param bank_front_image 银行卡正面照
	 * @param bank_back_image 银行卡反面照
	 * @param shop_doorhead 门头照
	 * @param shop_cashier 店内收银台照片
	 * @param loan_sign 签名信息
	 * @param shop_inside 店内照
	 * @return
	 */
	@Override
	public ShopsResult addOpenLoanNew(String shop_unique,String business_image,String business_image_hand,
			String ID_front_image,String ID_back_image,	String ID_back_image_hand,String bank_front_image,String bank_back_image,
			String shop_doorhead,String shop_cashier,String loan_sign, String shop_inside
			) {
		ShopsResult sr = new ShopsResult(1,"保存成功");
		//更新或者上传新的文件信息，更新前需要确认审核状态，防止已审核通过的店铺被更新
		Map<String,Object> map = new HashMap<>();
		map.put("shop_unique", shop_unique);

		//未审核通过，可以修改
		map.put("business_image", business_image);
		map.put("business_image_hand", business_image_hand);
		map.put("ID_front_image", ID_front_image);
		map.put("ID_back_image", ID_back_image);
		map.put("ID_back_image_hand", ID_back_image_hand);
		map.put("bank_front_image", bank_front_image);
		map.put("bank_back_image", bank_back_image);
		map.put("shop_doorhead", shop_doorhead);
		map.put("shop_cashier", shop_cashier);
		map.put("loan_sign", loan_sign);
		map.put("shop_inside", shop_inside);
		Map<String,Object> shopMap = loanMoneyDao.queryIsOpenLoan(map);
		if(null != shopMap && !shopMap.isEmpty()) {
			Integer audit_status = Integer.parseInt(shopMap.get("audit_status").toString());
			if(audit_status == 2) {
				//已经审核通过了，不允许再次修改
				sr.setStatus(0);
				sr.setMsg("已经审核通过了，不允许修改证件信息");
				return sr;
			}

			//更新现有数据
			loanMoneyDao.editOpenLoan(map);

		}else {
			loanMoneyDao.addOpenLoan(map);
		}

		return sr;
	}
	/**
	 * 1统计店铺的赊销未还款情况
	 * @param shop_unique
	 * @return
	 */
	public Map<String,Object> queryShopLoanMsg(String shop_unique){
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("shop_unique", shop_unique);
		Map<String,Object> resMap = loanMoneyDao.queryShopLoanMsg(map);
		return resMap;
	}

	@Override
	public Map<String, Object> queryIsOpenLoan(String shop_unique) {
		Map<String,Object> params=new HashMap<>();
		params.put("shop_unique", shop_unique);
		return loanMoneyDao.queryIsOpenLoan(params);
	}


	@Override
	public ShopsResult addOpenLoan(Map<String, Object> map, HttpServletRequest request) {
		ShopsResult sr=new ShopsResult();
		MultipartFile file2=null;
		if(request instanceof MultipartHttpServletRequest){
			MultipartHttpServletRequest multipartHttpServletRequest =(MultipartHttpServletRequest)request;
			Map<String, MultipartFile> mp = multipartHttpServletRequest.getFileMap();
			file2 = mp.get("business_image");
		}
		try {
			if(file2!=null){
				UploadResult ur = fileUploadService.upload(file2);
				if (ObjectUtil.isNotNull(ur)) {
					map.put("business_image", ur.getUrl());
				}
			}
			MultipartFile file3=null;
			if(request instanceof MultipartHttpServletRequest){
				MultipartHttpServletRequest multipartHttpServletRequest =(MultipartHttpServletRequest)request;
				Map<String, MultipartFile> mp = multipartHttpServletRequest.getFileMap();
				file3 = mp.get("bank_image");
			}
			if(file3!=null){
				UploadResult ur = fileUploadService.upload(file3);
				if (ObjectUtil.isNotNull(ur)) {
					map.put("bank_image", ur.getUrl());
				}
			}
			MultipartFile file4=null;
			if(request instanceof MultipartHttpServletRequest){
				MultipartHttpServletRequest multipartHttpServletRequest =(MultipartHttpServletRequest)request;
				Map<String, MultipartFile> mp = multipartHttpServletRequest.getFileMap();
				file4 = mp.get("ID_front_image");
			}
			if(file4!=null){
				UploadResult ur = fileUploadService.upload(file4);
				if (ObjectUtil.isNotNull(ur)) {
					map.put("ID_front_image", ur.getUrl());
				}
			}
			MultipartFile file5=null;
			if(request instanceof MultipartHttpServletRequest){
				MultipartHttpServletRequest multipartHttpServletRequest =(MultipartHttpServletRequest)request;
				Map<String, MultipartFile> mp = multipartHttpServletRequest.getFileMap();
				file5 = mp.get("ID_back_image");
			}
			if(file5!=null){
				UploadResult ur = fileUploadService.upload(file5);
				if (ObjectUtil.isNotNull(ur)) {
					map.put("ID_back_image", ur.getUrl());
				}

			}
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		if(map.get("id")!=null&&!"".equals(map.get("id").toString())){
			loanMoneyDao.editOpenLoan(map);
		}else{
			loanMoneyDao.addOpenLoan(map);
		}

		sr.setStatus(1);
		return sr;
	}


	@Override
	public void saveReturnMoney(Map<String, Object> params) {
		loanMoneyDao.saveReturnMoney(params);
	}


	/**
	 * 还款后，添加还款记录，修改对应的还款日志信息
	 */
	public void updateReturnMoney(String out_trade_no,String order_no,String total_fee,String shop_unique) {

		/*
		 * 0、查询提前还款的费率；
		 * 1、查询店铺未还款的赊销信息 sx_order_loan,sx_fenqi_log
		 * 2、依次根据赊销信息，计算本次赊销还款的本金金额和赊销金额；
		 * 3、更新sx_fenqi_long
		 * 4、添加赊销还款信息
		 * 5、更新店铺赊销额度
		 *
		 * 0特殊说明，如果店铺还款金额过大，则增加店铺临时额度，需要找到合理的解决方案，
		 */
		try {

			Map<String,Object> sxSetMap = loanMoneyDao.querySxPolicySet();
			BigDecimal breach_rate = new BigDecimal(sxSetMap.get("breach_rate").toString());
			BigDecimal canUseMoney = new BigDecimal(total_fee).divide(new BigDecimal(100)).setScale(2,BigDecimal.ROUND_HALF_UP);
			BigDecimal tempBreach = new BigDecimal("1").add(breach_rate).setScale(8,BigDecimal.ROUND_HALF_UP);
			BigDecimal canUsePrincipal = canUseMoney.divide(tempBreach,2,BigDecimal.ROUND_HALF_UP);
			BigDecimal totalBackPrincipal = canUsePrincipal;
			BigDecimal returnMoney = new BigDecimal(0);

			//本次还款需要修改的sx_fenqi_log
			List<Map<String,Object>> backList = new ArrayList<Map<String,Object>>();

			Map<String,Object> map = new HashMap<String,Object>();
			map.put("shop_unique", shop_unique);
			map.put("status", 1);
			List<Map<String,Object>> notReturnMoneyList = loanMoneyDao.queryWaitReturnLoan(map);
			if(null == notReturnMoneyList || notReturnMoneyList.isEmpty()) {
				//提前还款出问题了，需要人工干预，
			}else {
				//正常还款流程
				for(Map<String,Object> tm : notReturnMoneyList) {
					BigDecimal fenqi_money = new BigDecimal(tm.get("fenqi_money").toString());
					BigDecimal already_money = new BigDecimal(tm.get("already_money").toString());
					BigDecimal principal_money = new BigDecimal(tm.get("principal_money").toString());
					BigDecimal already_principal_money = new BigDecimal(tm.get("already_principal_money").toString());

					Integer status = Integer.parseInt(tm.get("status").toString());

					BigDecimal needReturnPrincipal = principal_money.subtract(already_principal_money).setScale(2, BigDecimal.ROUND_HALF_UP);

					Map<String,Object> backMap = new HashMap<String,Object>();

					if(status == 1) {
						if(canUsePrincipal.compareTo(needReturnPrincipal) > 0) {
							canUsePrincipal = canUsePrincipal.subtract(needReturnPrincipal).setScale(2,BigDecimal.ROUND_HALF_UP);

							backMap.put("id", tm.get("id"));
							backMap.put("status", 2);
							backMap.put("backMoney", fenqi_money.subtract(already_money).setScale(2,BigDecimal.ROUND_HALF_UP));
							backMap.put("backPrincipal", needReturnPrincipal);
							returnMoney = returnMoney.add(fenqi_money).subtract(already_money);

							backList.add(backMap);
						}else if(canUsePrincipal.compareTo(needReturnPrincipal) == 0) {
							canUsePrincipal = canUsePrincipal.subtract(needReturnPrincipal).setScale(2,BigDecimal.ROUND_HALF_UP);

							backMap.put("id", tm.get("id"));
							backMap.put("status", 2);
							backMap.put("backMoney", fenqi_money.subtract(already_money).setScale(2,BigDecimal.ROUND_HALF_UP));
							backMap.put("backPrincipal", needReturnPrincipal);
							returnMoney = returnMoney.add(fenqi_money).subtract(already_money).setScale(2,BigDecimal.ROUND_HALF_UP);
							backList.add(backMap);

							break;
						}else {

							//未能还完全部的赊销款，需要计算分期还款额

							backMap.put("id", tm.get("id"));
							backMap.put("backPrincipal", canUsePrincipal);
							BigDecimal backMoney = (fenqi_money.multiply(canUsePrincipal)).divide(principal_money,2,BigDecimal.ROUND_HALF_UP);
							backMap.put("backMoney", backMoney);
							returnMoney = returnMoney.add(fenqi_money).subtract(already_money);
							backList.add(backMap);
							canUsePrincipal = new BigDecimal(0);
							break;
						}
					}
				}//计算赊销还款信息结束，循环结束

				//批量更新sx_fenqi_log
				loanMoneyDao.updateSxFenQiLog(backList);

				//恢复店铺赊销额度
				//特殊说明：如果店铺还款的金额超过了应还的金额，多还的部分，不扣除额度，直接增加可用额度，方便日后查询原因
				Map<String,Object> sxShopMsg = new HashMap<String,Object>();
				if(canUsePrincipal.compareTo(new BigDecimal(0)) > 0) {
					//还款金额超过了应还金额，
					totalBackPrincipal = canUseMoney.subtract((totalBackPrincipal.subtract(canUsePrincipal)).multiply(new BigDecimal(1).add(breach_rate))).setScale(2, BigDecimal.ROUND_HALF_UP);
				}
				sxShopMsg.put("backPrincipal", totalBackPrincipal);
				sxShopMsg.put("shop_unique", shop_unique);
				sxShopMsg.put("returnMoney", returnMoney);
				loanMoneyDao.updateSxShopMsg(sxShopMsg);
				//添加赊销还款订单
				sxShopMsg.clear();
				sxShopMsg.put("sale_list_unique", out_trade_no);
				sxShopMsg.put("return_money", canUseMoney.doubleValue());
				sxShopMsg.put("type", "2");
				sxShopMsg.put("pay_status", "2");
				sxShopMsg.put("shop_unique", shop_unique);
				sxShopMsg.put("pay_type", "1");
				sxShopMsg.put("principal_money", totalBackPrincipal);

				loanMoneyDao.saveReturnMoney(sxShopMsg);
				//推送安卓收银机
				Map<String,Object> shop= loanMoneyDao.queryShopJGId(sxShopMsg);
				if(shop!=null){
					JsonObject js=new JsonObject();
					js.addProperty("type", 2);
					js.addProperty("out_trade_no", out_trade_no);
					JPushClientUtil.notifyAndoidPos(shop.get("pc_registration_id").toString(), "消息通知", 2, js.toString());
					System.out.println("推送成功");
				}
			}


		}catch (Exception e) {
			//如果出现异常，记录到日志中,或者发消息，方便及时处理
			log.error("还款异常",e);
		}


	}


	@Override
	public ShopsResult queryOrderStatus(Map<String, Object> map) {
		ShopsResult rs=new ShopsResult();
		Map<String,Object> order= loanMoneyDao.queryReturnMoneyOrder(map);
		rs.setData(order);
		return rs;
	}


	@Override
	public ShopsResult queryLoanReturnList(Map<String, Object> map) {
		ShopsResult result = new ShopsResult();
		try {
			List<Map<String,Object>> list = loanMoneyDao.queryLoanReturnList(map);
	    	Integer count = loanMoneyDao.queryLoanReturnListCount(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setPageCount(count);
			result.setData(list);
		} catch (Exception e) {
			log.error("异常",e);
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}


	@Override
	public ShopsResult queryLoanList(Map<String, Object> map) {
		ShopsResult result = new ShopsResult();
		try {
			List<Map<String,Object>> list = loanMoneyDao.queryLoanList(map);
	    	Integer count = loanMoneyDao.queryLoanListCount(map);
	    	result.setStatus(1);
			result.setMsg("成功");
			result.setPageCount(count);
			result.setData(list);
		} catch (Exception e) {
			log.error("异常",e);
			result.setStatus(0);
			result.setMsg("异常");
		}
		return result;
	}


	@Override
	public Map<String, Object> queryAdvanceMoney(String shop_unique) {
		Map<String,Object> params=new HashMap<>();
		params.put("shop_unique", shop_unique);
		Map<String,Object> order= loanMoneyDao.queryAdvanceMoney(params);
		return order;
	}

	public Map<String,Object> querySxPolicySet(){
		return loanMoneyDao.querySxPolicySet();
	}

	@Override
	public Map<String, Object> queryLoanMoneyRate(String shop_unique) {
		Map<String,Object> params=new HashMap<>();
		params.put("shop_unique", shop_unique);
		Map<String,Object> order= loanMoneyDao.queryLoanMoneyRate(params);
		return order;
	}
}
