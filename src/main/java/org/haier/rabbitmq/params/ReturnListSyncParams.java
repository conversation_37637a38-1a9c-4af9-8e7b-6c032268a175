package org.haier.rabbitmq.params;


import java.io.Serializable;
import java.util.List;

/**
 * @Description MQ退款订单
 * @ClassName RetSaleListSubscribeParams
 * <AUTHOR>
 * @Date 2024/9/7 9:43
 **/
public class ReturnListSyncParams implements Serializable {
    /**
     * 退款订单
     */
    private ReturnListParams returnListParams;
    /**
     * 退款商品明细
     */
    private List<ReturnListDetailParams> detailParamsList;
    /**
     * 退款支付明细
     */
    private List<ReturnListPayDetailParams> payDetailParamsList;

    public ReturnListParams getReturnListParams() {
        return returnListParams;
    }

    public void setReturnListParams(ReturnListParams returnListParams) {
        this.returnListParams = returnListParams;
    }

    public List<ReturnListDetailParams> getDetailParamsList() {
        return detailParamsList;
    }

    public void setDetailParamsList(List<ReturnListDetailParams> detailParamsList) {
        this.detailParamsList = detailParamsList;
    }

    public List<ReturnListPayDetailParams> getPayDetailParamsList() {
        return payDetailParamsList;
    }

    public void setPayDetailParamsList(List<ReturnListPayDetailParams> payDetailParamsList) {
        this.payDetailParamsList = payDetailParamsList;
    }
}
