package org.haier.rabbitmq.params;


import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description 退款订单
 * @ClassName RetSaleListParams
 * <AUTHOR>
 * @Date 2024/9/7 9:39
 **/
public class ReturnListParams implements Serializable {
    private static final long serialVersionUID = 4111320786281232033L;

    /**
     * 退货单号
     */
    private Long id;

    /**
     * 销售订单编号
     */
    private String saleListUnique;

    /**
     * 退款的店铺编号
     */
    private String shopUnique;

    /**
     * 退货日期
     */
    private Date retListDatetime;

    /**
     * 退货总金额
     */
    private BigDecimal retListTotal;

    /**
     * 退货总数量
     */
    private BigDecimal retListCount;

    /**
     * 退款状态:1-未退款，2-已退款
     */
    private String retListState;

    /**
     * 退货申请受理状态：1未处理，2-已受理，3受理完毕，4、驳回
     */
    private String retListHandlestate;

    /**
     * 备注信息
     */
    private String retListRemarks;

    /**
     * 操作员工编号
     */
    private Long staffId;

    /**
     * 退货机器macid
     */
    private String macId;

    /**
     * 退货来源：1、pc收银；2、网页；3、小程序；4、app
     */
    private Integer retOrigin;

    /**
     * 退款方式：1、现金；2、支付宝；3、微信；4、银行卡；5、储值卡 6 易通（原路退回）;7、赊账（原订单赊账）
     */
    private Integer retMoneyType;

    /**
     * 实际退款金额
     */
    private BigDecimal retListTotalMoney;

    /**
     * 退款商品销售时总原价
     */
    private BigDecimal retListOriginTotal;

    /**
     * 退款单号
     */
    private String outRefundNo;

    /**
     * 退款到帐时间
     */
    private Date retBackDatetime;

    /**
     * 退款申请单号
     */
    private String retListUnique;

    /**
     * 扣除的百货豆数量（因消费而赠送，需要扣回的）
     */
    private Integer retListBean;

    /**
     * 申请退款原因
     */
    private String retListReason;

    /**
     * 退还的配送费
     */
    private BigDecimal retListDelfee;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSaleListUnique() {
        return saleListUnique;
    }

    public void setSaleListUnique(String saleListUnique) {
        this.saleListUnique = saleListUnique;
    }

    public String getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(String shopUnique) {
        this.shopUnique = shopUnique;
    }

    public Date getRetListDatetime() {
        return retListDatetime;
    }

    public void setRetListDatetime(Date retListDatetime) {
        this.retListDatetime = retListDatetime;
    }

    public BigDecimal getRetListTotal() {
        return retListTotal;
    }

    public void setRetListTotal(BigDecimal retListTotal) {
        this.retListTotal = retListTotal;
    }

    public BigDecimal getRetListCount() {
        return retListCount;
    }

    public void setRetListCount(BigDecimal retListCount) {
        this.retListCount = retListCount;
    }

    public String getRetListState() {
        return retListState;
    }

    public void setRetListState(String retListState) {
        this.retListState = retListState;
    }

    public String getRetListHandlestate() {
        return retListHandlestate;
    }

    public void setRetListHandlestate(String retListHandlestate) {
        this.retListHandlestate = retListHandlestate;
    }

    public String getRetListRemarks() {
        return retListRemarks;
    }

    public void setRetListRemarks(String retListRemarks) {
        this.retListRemarks = retListRemarks;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public String getMacId() {
        return macId;
    }

    public void setMacId(String macId) {
        this.macId = macId;
    }

    public Integer getRetOrigin() {
        return retOrigin;
    }

    public void setRetOrigin(Integer retOrigin) {
        this.retOrigin = retOrigin;
    }

    public Integer getRetMoneyType() {
        return retMoneyType;
    }

    public void setRetMoneyType(Integer retMoneyType) {
        this.retMoneyType = retMoneyType;
    }

    public BigDecimal getRetListTotalMoney() {
        return retListTotalMoney;
    }

    public void setRetListTotalMoney(BigDecimal retListTotalMoney) {
        this.retListTotalMoney = retListTotalMoney;
    }

    public BigDecimal getRetListOriginTotal() {
        return retListOriginTotal;
    }

    public void setRetListOriginTotal(BigDecimal retListOriginTotal) {
        this.retListOriginTotal = retListOriginTotal;
    }

    public String getOutRefundNo() {
        return outRefundNo;
    }

    public void setOutRefundNo(String outRefundNo) {
        this.outRefundNo = outRefundNo;
    }

    public Date getRetBackDatetime() {
        return retBackDatetime;
    }

    public void setRetBackDatetime(Date retBackDatetime) {
        this.retBackDatetime = retBackDatetime;
    }

    public String getRetListUnique() {
        return retListUnique;
    }

    public void setRetListUnique(String retListUnique) {
        this.retListUnique = retListUnique;
    }

    public Integer getRetListBean() {
        return retListBean;
    }

    public void setRetListBean(Integer retListBean) {
        this.retListBean = retListBean;
    }

    public String getRetListReason() {
        return retListReason;
    }

    public void setRetListReason(String retListReason) {
        this.retListReason = retListReason;
    }

    public BigDecimal getRetListDelfee() {
        return retListDelfee;
    }

    public void setRetListDelfee(BigDecimal retListDelfee) {
        this.retListDelfee = retListDelfee;
    }
}
