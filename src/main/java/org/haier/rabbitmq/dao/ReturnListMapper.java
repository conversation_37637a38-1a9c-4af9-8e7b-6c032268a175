package org.haier.rabbitmq.dao;

import org.apache.ibatis.annotations.Param;
import org.haier.rabbitmq.entity.ReturnListEntity;

/**
* @Description 退货表，管理客户退货信息
* @ClassName ReturnList
* <AUTHOR> 
* @Date 2024-09-07
**/
public interface ReturnListMapper {
    ReturnListEntity selectBySaleListUniqueAndRetListUnique(@Param("saleListUnique") String saleListUnique, @Param("retListUnique") String retListUnique);
}