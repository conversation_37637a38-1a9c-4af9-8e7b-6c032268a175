package org.haier.rabbitmq.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import org.haier.rabbitmq.config.RabbitMqConfig;
import org.haier.rabbitmq.dao.ReturnListDetailMapper;
import org.haier.rabbitmq.dao.ReturnListMapper;
import org.haier.rabbitmq.dao.ReturnListPaydetailMapper;
import org.haier.rabbitmq.entity.ReturnListDetailEntity;
import org.haier.rabbitmq.entity.ReturnListEntity;
import org.haier.rabbitmq.entity.ReturnListPaydetailEntity;
import org.haier.rabbitmq.entity.ShopEntity;
import org.haier.rabbitmq.params.ReturnListDetailParams;
import org.haier.rabbitmq.params.ReturnListParams;
import org.haier.rabbitmq.params.ReturnListPayDetailParams;
import org.haier.rabbitmq.params.ReturnListSyncParams;
import org.haier.rabbitmq.service.ReturnListSyncService;
import org.haier.shopUpdate.dao.GoodsDao;
import org.haier.shopUpdate.dao.ShopsDao;
import org.haier.shopUpdate.util.NoteResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * @ClassName RetSaleListSyncServiceImpl
 * <AUTHOR>
 * @Date 2024/9/7 11:11
 **/
@Service
public class ReturnListSyncServiceImpl implements ReturnListSyncService {

    private final Logger logger = LoggerFactory.getLogger(ReturnListSyncServiceImpl.class);
    @Resource
    private RabbitTemplate rabbitReturnListTemplate;
    @Resource
    private RabbitMqConfig rabbitMqConfig;
    @Resource
    private ReturnListMapper returnListDao;
    @Resource
    private ReturnListDetailMapper returnListDetailMapper;
    @Resource
    private ReturnListPaydetailMapper returnListPaydetailMapper;
    @Resource
    private GoodsDao goodsDao;
    @Resource
    private ShopsDao shopDao;

    @Override
    public NoteResult syncRetSaleList(String saleListUnique, String retListUnique) {
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            logger.error("-------[发送MQ退款订单]线程休眠三秒异常------------订单编码：{}--------", saleListUnique);
        }
        logger.info("-------[发送MQ退款订单]------------订单编码：{}---退款订单编码：{}-----", saleListUnique, retListUnique);
        NoteResult noteResult = new NoteResult();
        try {
            if (StrUtil.isBlank(saleListUnique)) {
                noteResult.setMsg("订单编码为空");
                noteResult.setStatus(1);
                return noteResult;
            }
            ReturnListEntity returnList = returnListDao.selectBySaleListUniqueAndRetListUnique(saleListUnique, retListUnique);
            if (null == returnList) {
                logger.error("-------[发送MQ退款订单]------------订单编码：{}---退款订单编码：{}------退款订单为空--------", saleListUnique, retListUnique);
                noteResult.setMsg("退款订单不存在");
                noteResult.setStatus(1);
                return noteResult;
            }
            ShopEntity shopEntity = shopDao.selectByShopUnique(Long.parseLong(returnList.getShopUnique()));
            if (null == shopEntity) {
                logger.error("-------[发送MQ退款订单]------------订单编码：{}---退款订单编码：{}------非纳统订单--------", saleListUnique, retListUnique);
                noteResult.setMsg("非企业退款订单无需同步");
                noteResult.setStatus(1);
                return noteResult;
            }
            ReturnListDetailEntity queryDetail = new ReturnListDetailEntity();
            queryDetail.setSaleListUnique(returnList.getSaleListUnique());
            queryDetail.setRetListUnique(returnList.getRetListUnique());
            List<ReturnListDetailEntity> detailEntityList = returnListDetailMapper.findList(queryDetail);
            ReturnListPaydetailEntity queryPayDetail = new ReturnListPaydetailEntity();
            queryPayDetail.setSaleListUnique(returnList.getSaleListUnique());
            queryPayDetail.setRetListUnique(returnList.getRetListUnique());
            List<ReturnListPaydetailEntity> paydetailEntityList = returnListPaydetailMapper.findList(queryPayDetail);
            ReturnListSyncParams syncParams = new ReturnListSyncParams();
            ReturnListParams returnListParams = new ReturnListParams();
            BeanUtil.copyProperties(returnList, returnListParams);
            syncParams.setReturnListParams(returnListParams);
            Set<String> goodsBarcodeList = new HashSet<>();
            for (ReturnListDetailEntity detailEntity : detailEntityList) {
                if(StrUtil.isNotBlank(detailEntity.getGoodsBarcode())){
                    goodsBarcodeList.add(detailEntity.getGoodsBarcode());
                }
            }
            Map<String, Integer> goodsMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(goodsBarcodeList) && goodsBarcodeList.size() > 0) {
                List<Map<String, Object>> goodsList = goodsDao.queryShopGoodsIds(Long.parseLong(returnList.getShopUnique()), goodsBarcodeList);
                for (Map<String, Object> m : goodsList) {
                    String goodsBarcode = null;
                    if(ObjectUtil.isNotNull(m.get("goods_barcode"))){
                        goodsBarcode = String.valueOf(m.get("goods_barcode"));
                    }
                    Integer goodsId = null;
                    if(ObjectUtil.isNotNull(m.get("goods_id"))){
                        goodsId = (Integer) m.get("goods_id");
                    }
                    if (null != goodsBarcode && null != goodsId) {
                        goodsMap.put(goodsBarcode, goodsId);
                    }
                }
            }
            List<ReturnListDetailParams> detailParamsList = BeanUtil.copyToList(detailEntityList, ReturnListDetailParams.class);
            for (ReturnListDetailParams detailParams : detailParamsList) {
                if (StrUtil.isNotBlank(detailParams.getGoodsBarcode())) {
                    detailParams.setGoodsId(MapUtil.getInt(goodsMap, detailParams.getGoodsBarcode()));
                }
            }
            syncParams.setDetailParamsList(detailParamsList);
            List<ReturnListPayDetailParams> payDetailParamsList = BeanUtil.copyToList(paydetailEntityList, ReturnListPayDetailParams.class);
            syncParams.setPayDetailParamsList(payDetailParamsList);
            String body = JSONUtil.toJsonStr(syncParams);
            logger.info("-------[发送MQ退款订单]------------订单编详情--{}---", body);
            rabbitReturnListTemplate.convertAndSend(rabbitMqConfig.getReturnListSyncExchange(), "", body);
            noteResult.setStatus(0);
            noteResult.setMsg("操作成功");
        } catch (Exception e) {
            logger.error("-------[发送MQ退款订单]------------订单编码：{}---退款订单编码：{}------异常：{}--------", saleListUnique, retListUnique, e.getMessage());
        }
        return noteResult;
    }
}
