package org.haier.rabbitmq.service;

import org.haier.shopUpdate.util.NoteResult;

/**
 * @Description 退款订单同步
 * @ClassName RetSaleListSyncService
 * <AUTHOR>
 * @Date 2024/9/7 11:11
 **/
public interface ReturnListSyncService {

    /**
     * 推送退款订单到MQ
     * @param saleListUnique 订单编码
     * @param retListUnique 退款订单编码
     * @return
     */
    NoteResult syncRetSaleList(String saleListUnique, String retListUnique);
}
