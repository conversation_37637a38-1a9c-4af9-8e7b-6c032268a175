package org.haier.rabbitmq.entity;


import java.math.BigDecimal;

/**
 * <AUTHOR> 
 * @Description 退货单明细
 * @ClassName ReturnListDetail
 * @Date 2024-09-07
 **/

public class ReturnListDetailEntity {
	/**
	* id
	*/
	private Long retListDetailId;

	/**
	* 退货单唯一标识符
	*/
	private String saleListUnique;

	/**
	* 商品条形码
	*/
	private String goodsBarcode;

	/**
	* 商品名称
	*/
	private String goodsName;

	/**
	* 退货数量
	*/
	private BigDecimal retListDetailCount;

	/**
	* 退货价格
	*/
	private BigDecimal retListDetailPrice;

	/**
	* 退回货物处理方式：1、入库；2、报损（过期产品）；3、其他
	*/
	private Integer handleWay;

	/**
	* 退货商品的销售原价
	*/
	private BigDecimal retListOriginPrice;

	/**
	* 订单详情ID
	*/
	private Long rsaleListDetailId;

	/**
	* 退款单申请号
	*/
	private String retListUnique;

	public Long getRetListDetailId() {
		return retListDetailId;
	}

	public void setRetListDetailId(Long retListDetailId) {
		this.retListDetailId = retListDetailId;
	}

	public String getSaleListUnique() {
		return saleListUnique;
	}

	public void setSaleListUnique(String saleListUnique) {
		this.saleListUnique = saleListUnique;
	}

	public String getGoodsBarcode() {
		return goodsBarcode;
	}

	public void setGoodsBarcode(String goodsBarcode) {
		this.goodsBarcode = goodsBarcode;
	}

	public String getGoodsName() {
		return goodsName;
	}

	public void setGoodsName(String goodsName) {
		this.goodsName = goodsName;
	}

	public BigDecimal getRetListDetailCount() {
		return retListDetailCount;
	}

	public void setRetListDetailCount(BigDecimal retListDetailCount) {
		this.retListDetailCount = retListDetailCount;
	}

	public BigDecimal getRetListDetailPrice() {
		return retListDetailPrice;
	}

	public void setRetListDetailPrice(BigDecimal retListDetailPrice) {
		this.retListDetailPrice = retListDetailPrice;
	}

	public Integer getHandleWay() {
		return handleWay;
	}

	public void setHandleWay(Integer handleWay) {
		this.handleWay = handleWay;
	}

	public BigDecimal getRetListOriginPrice() {
		return retListOriginPrice;
	}

	public void setRetListOriginPrice(BigDecimal retListOriginPrice) {
		this.retListOriginPrice = retListOriginPrice;
	}

	public Long getRsaleListDetailId() {
		return rsaleListDetailId;
	}

	public void setRsaleListDetailId(Long rsaleListDetailId) {
		this.rsaleListDetailId = rsaleListDetailId;
	}

	public String getRetListUnique() {
		return retListUnique;
	}

	public void setRetListUnique(String retListUnique) {
		this.retListUnique = retListUnique;
	}
}