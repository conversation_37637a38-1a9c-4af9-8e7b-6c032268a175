package org.haier.rabbitmq.entity;

import java.io.Serializable;

/**
 * 店铺 表
 * @ClassName ShopEntity
 * <AUTHOR>
 * @Date 2023/9/4 16:16
 **/

public class ShopEntity implements Serializable {
    private static final long serialVersionUID = -5254651383227644965L;
    private String shopName;
    private Long shopUnique;
    /**
     * 所属企业编码
     */
    private String invitationCode;

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public Long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getInvitationCode() {
        return invitationCode;
    }

    public void setInvitationCode(String invitationCode) {
        this.invitationCode = invitationCode;
    }
}
