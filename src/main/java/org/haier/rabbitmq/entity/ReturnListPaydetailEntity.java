package org.haier.rabbitmq.entity;

import java.math.BigDecimal;

/**
 * <AUTHOR> 
 * @Description 退款支付明细
 * @ClassName ReturnListPaydetail
 * @Date 2024-09-07
 **/

public class ReturnListPaydetailEntity {
	private Long id;

	/**
	* 订单单号
	*/
	private String saleListUnique;

	/**
	* 退货单号，一个订单可退多次，也可以有几种不同的退款方式
	*/
	private String retListUnique;

	/**
	* 退款的收款方式：1、现金；2、支付宝；3、微信；4、银行卡；5、储值卡；6、其他；7、优惠券；8、百货豆
查询时，先判断service_type，在判断pay_type
	*/
	private Integer payType;

	/**
	* 退款金额
	*/
	private BigDecimal payMoney;

	/**
	* 现金支付服务端：1、线下操作；2、拉卡拉平台； 3 易通 4、微信平台；5、其他平台 ,6、合利宝，7、云平台
	*/
	private Integer serviceType;

	/**
	* 退款的账号
	*/
	private String mchId;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getSaleListUnique() {
		return saleListUnique;
	}

	public void setSaleListUnique(String saleListUnique) {
		this.saleListUnique = saleListUnique;
	}

	public String getRetListUnique() {
		return retListUnique;
	}

	public void setRetListUnique(String retListUnique) {
		this.retListUnique = retListUnique;
	}

	public Integer getPayType() {
		return payType;
	}

	public void setPayType(Integer payType) {
		this.payType = payType;
	}

	public BigDecimal getPayMoney() {
		return payMoney;
	}

	public void setPayMoney(BigDecimal payMoney) {
		this.payMoney = payMoney;
	}

	public Integer getServiceType() {
		return serviceType;
	}

	public void setServiceType(Integer serviceType) {
		this.serviceType = serviceType;
	}

	public String getMchId() {
		return mchId;
	}

	public void setMchId(String mchId) {
		this.mchId = mchId;
	}
}