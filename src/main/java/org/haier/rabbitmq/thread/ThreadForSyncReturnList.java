package org.haier.rabbitmq.thread;


import lombok.extern.slf4j.Slf4j;
import org.haier.rabbitmq.service.ReturnListSyncService;

/**
 *
* @author: 作者:王恩龙
* @version: 2022年12月8日 上午11:35:20
*/
@Slf4j
public class ThreadForSyncReturnList implements Runnable{
	private ReturnListSyncService returnListSyncService;
	private String saleListUnique;

	private String retListUnique;



	public ThreadForSyncReturnList(ReturnListSyncService returnListSyncService, String saleListUnique,String retListUnique) {
		super();
		this.returnListSyncService = returnListSyncService;
		this.saleListUnique = saleListUnique;
		this.retListUnique = retListUnique;
	}

	/**
	 */
	public void run() {
		try {
			returnListSyncService.syncRetSaleList(saleListUnique,retListUnique);
		} catch (Exception e) {
			System.out.println("订单同步到纳统报错");
			log.error("订单同步到纳统报错",e);
		}
	}
}
