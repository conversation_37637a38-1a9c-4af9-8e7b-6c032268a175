package org.haier.rabbitmq.config;


import java.io.Serializable;

/**
 * @Description
 * @ClassName RabitMqConfig
 * <AUTHOR>
 * @Date 2024/9/5 17:11
 **/

public class RabbitMqConfig implements Serializable {
    private static final long serialVersionUID = -4627880686728290581L;

    /**
     * 退款订单同步队列
     */
    private String returnListSyncQueue;

    /**
     * 退款同步交换机
     */
    private String returnListSyncExchange;

    public String getReturnListSyncQueue() {
        return returnListSyncQueue;
    }

    public void setReturnListSyncQueue(String returnListSyncQueue) {
        this.returnListSyncQueue = returnListSyncQueue;
    }

    public String getReturnListSyncExchange() {
        return returnListSyncExchange;
    }

    public void setReturnListSyncExchange(String returnListSyncExchange) {
        this.returnListSyncExchange = returnListSyncExchange;
    }

}
