-- auto-generated definition
create table goods_price_adjustment
(
    id                   bigint auto_increment
        primary key,
    shop_unique          bigint                                      null,
    goods_barcode        varchar(64)    default ''                   not null,
    goods_sale_price     decimal(11, 2) default 0.00                 not null comment '销售价',
    goods_web_sale_price decimal(11, 2) default 0.00                 null comment '网单价',
    goods_cus_price      decimal(11, 2) default 0.00                 null comment '会员价',
    create_time          datetime(3)    default CURRENT_TIMESTAMP(3) null
)
    comment '商品调价记录';

create index goods_price_adjustment_create_time_index
    on goods_price_adjustment (create_time);

create index goods_price_adjustment_goods_barcode_index
    on goods_price_adjustment (goods_barcode);

create index goods_price_adjustment_shop_unique_index
    on goods_price_adjustment (shop_unique);

