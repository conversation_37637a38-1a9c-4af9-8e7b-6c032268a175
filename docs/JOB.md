# 定时任务业务文档

## 概述

本文档描述了ShopUpdate系统中的定时任务相关业务逻辑，包括定时任务的配置、执行流程和业务功能。系统采用Spring Task框架实现定时任务调度，主要用于微信Token刷新、销售数据统计等核心业务。

## 定时任务架构

### 1. 配置架构

系统通过Spring XML配置启用定时任务功能：

**配置文件**: [`src/main/resources/conf/spring-mvc.xml`](../src/main/resources/conf/spring-mvc.xml)

```xml
<!-- 定时器开关 -->
<task:annotation-driven />

<!-- 定时任务Bean配置 -->
<bean id="task" class="org.haier.shopUpdate.util.task.Task"></bean>

<!-- 定时任务调度配置 -->
<task:scheduled-tasks>
    <task:scheduled ref="task" method="refreshAccessToken" cron="1 1 * * * ?"/>
    <!-- 注释的测试任务 -->
    <!-- <task:scheduled ref="task" method="addNewTestGoods" cron="* * * * * ?"/> -->
</task:scheduled-tasks>
```

### 2. 任务调度常量

**常量定义**: [`src/main/java/org/haier/shopUpdate/util/common/constant/ScheduleConstants.java`](../src/main/java/org/haier/shopUpdate/util/common/constant/ScheduleConstants.java)

定义了任务调度相关的通用常量，包括：
- 任务类名标识
- 执行目标属性
- 失火策略常量
- 任务状态枚举

## 任务执行时间表

| 任务名称 | Cron表达式 | 执行时间 | 状态 | 说明 |
|---------|-----------|---------|------|------|
| 微信AccessToken刷新 | `1 1 * * * ?` | 每小时1分1秒 | 已配置 | 当前逻辑被注释 |
| 销售排行统计 | `0 30 0 * * ?` | 每天凌晨0:30 | 运行中 | 生成热销排行榜 |
| 测试商品添加 | `* * * * * ?` | 每秒执行 | 已注释 | 仅用于测试 |

## 核心定时任务

### 1. 微信AccessToken刷新任务

**业务目标**: 定期刷新微信公众号的AccessToken，确保微信推送服务正常运行

**执行频率**: 每小时的第1分第1秒执行 (`cron="1 1 * * * ?"`)

**实现类**: [`src/main/java/org/haier/shopUpdate/util/task/Task.java`](../src/main/java/org/haier/shopUpdate/util/task/Task.java)

**业务流程**:
1. 定时器触发 `refreshAccessToken()` 方法
2. 调用 [`WXPush.getToken()`](../src/main/java/org/haier/shopUpdate/util/wechat/wxpush/WXPush.java) 获取新Token
3. 更新全局AccessToken缓存

**注意事项**:
- 当前实现中Token刷新逻辑被注释，实际未执行刷新操作
- 需要根据实际业务需求启用Token刷新功能
- Token有效期为2小时，建议每1.5小时刷新一次

**关键代码**:
```java
/**
 * 定时刷新微信ACCESSTOKEN
 */
public void refreshAccessToken() {
    WXPush.getToken();
}
```

**相关业务模块**:
- 微信推送服务 ([`WXPush`](../src/main/java/org/haier/shopUpdate/util/wechat/wxpush/WXPush.java))
- 会员充值通知
- 消费通知推送
- 退卡通知

### 2. 销售排行统计任务

**业务目标**: 每日统计商品销售排行数据，生成热销商品排行榜并推送给店铺

**执行频率**: 每天凌晨0点30分执行 (`cron="0 30 0 * * ?"`)

**实现类**: [`src/main/java/org/haier/shopUpdate/service/QuerySaleListTask.java`](../src/main/java/org/haier/shopUpdate/service/QuerySaleListTask.java)

**业务流程**:
1. 定时器触发，检查当日是否已有销售排行数据
2. 如果没有数据，调用 `purService.sellListTask()` 执行统计
3. 统计昨日商品销售数据
4. 计算销售增长比例
5. 生成热销排行Top20
6. 向所有审核通过的店铺推送排行消息

**核心业务逻辑**: [`src/main/java/org/haier/shopUpdate/service/PurServiceImpl.java`](../src/main/java/org/haier/shopUpdate/service/PurServiceImpl.java) 的 `sellListTask()` 方法

**详细流程**:

1. **数据查询阶段**:
   - 查询昨天的商品销售记录
   - 查询前天的商品销售记录用于对比

2. **数据计算阶段**:
   - 计算销售增长比例: `((昨日销量-前日销量)/前日销量)*100`
   - 新商品标记为100%增长

3. **数据存储阶段**:
   - 将统计结果保存到 `sale_list_task` 表

4. **消息推送阶段**:
   - 查询所有审核状态为4的店铺
   - 生成推送消息标题: "M.dd 日全网产品热销排行top20!"
   - 插入到 `shops_msg` 表

**关键代码片段**:
```java
@Scheduled(cron="0 30 0 * * ?")
public void print(){
    System.out.println("定时查询销售排行");
    try {
        //定时查询销售排行
        Map<String, Object> map=new HashMap<String, Object>();
        map.put("startDate", new SimpleDateFormat("yyyy-MM-dd").format(new Date()));
        ShopsResult result=purService.querySellList(map);
        if(result.getStatus()<=0){
            System.out.println("开始");
            purService.sellListTask();
        }
    } catch (Exception e) {
        log.error("定时任务异常：",e);
    }
}
```

**数据表结构**:
- `sale_list_task` - 销售排行任务结果表
- `shops_msg` - 店铺消息推送表
- `shops` - 店铺信息表 (examinestatus=4为审核通过状态)

## 异步任务处理

### 1. 异步日志记录

**实现类**: [`src/main/java/org/haier/shopUpdate/log/event/RemoteLogEventListener.java`](../src/main/java/org/haier/shopUpdate/log/event/RemoteLogEventListener.java)

**业务功能**: 异步处理系统操作日志，发送到远程日志服务

**关键特性**:
- 使用 `@Async` 注解实现异步处理
- 监听 `OperLogEvent` 事件
- 通过HTTP接口发送日志到远程服务

### 2. MQTT消息推送线程

**实现类**: [`src/main/java/org/haier/shopUpdate/util/thread/SendMqttMsg.java`](../src/main/java/org/haier/shopUpdate/util/thread/SendMqttMsg.java)

**业务功能**: 异步发送MQTT消息通知收银设备商品信息更新

**使用场景**:
- 商品信息修改后通知收银机
- 盘点任务完成后批量通知
- 进货单处理后商品更新通知

**业务流程**:
1. 业务操作完成后创建 `SendMqttMsg` 线程
2. 异步调用MQTT服务接口
3. 通知收银设备更新商品信息

### 3. 订单同步线程

**实现类**: [`src/main/java/org/haier/rabbitmq/thread/ThreadForSyncReturnList.java`](../src/main/java/org/haier/rabbitmq/thread/ThreadForSyncReturnList.java)

**业务功能**: 异步同步退货订单到纳统系统

**执行时机**: 退货订单处理完成后

**业务流程**:
1. 接收销售单号和退货单号参数
2. 调用 `returnListSyncService.syncRetSaleList()` 同步数据
3. 异常处理和日志记录

### 4. 商品信息同步线程

**使用场景**: 在多个业务场景中使用 `ThreadUtil.execute()` 执行异步任务
- 商品批量添加后的供应商关系处理
- 商品信息更新后的MQTT通知
- 其他耗时的后台处理任务

## 业务任务管理

### 1. 盘点任务管理

**服务类**: [`src/main/java/org/haier/shopUpdate/service/InventoryTaskServiceImpl.java`](../src/main/java/org/haier/shopUpdate/service/InventoryTaskServiceImpl.java)

**核心功能**:
- 盘点任务创建和管理
- 盘点进度跟踪
- 盘点结果统计
- 库存更新处理

**任务状态流转**:
1. 待盘点 (WAITING)
2. 盘点中 (PROCESSING) 
3. 已完成 (FINISHED)

### 2. 补货计划任务

**服务类**: [`src/main/java/org/haier/shopUpdate/service/ShopsRestockPlanServiceImpl.java`](../src/main/java/org/haier/shopUpdate/service/ShopsRestockPlanServiceImpl.java)

**业务功能**: 管理店铺补货计划，自动生成补货任务

## 任务监控与异常处理

### 1. 异常处理策略

所有定时任务都包含完善的异常处理机制：
- 使用 `try-catch` 包装业务逻辑
- 记录详细的错误日志
- 确保单次任务失败不影响后续执行

### 2. 日志记录

- 任务开始和结束都有日志记录
- 异常情况详细记录到日志文件
- 支持远程日志收集和分析

### 3. 服务器限制

部分任务通过IP地址限制执行服务器，避免集群环境下重复执行：
```java
// 检测服务器地址，如果副服务器地址不匹配，不执行定时任务
if(IPGet.ExtranetIP.equals(AuthUtil.IP5)) {
    // 执行任务逻辑
}
```

## 配置管理

### 1. 环境配置

不同环境的配置文件：
- [`application.properties`](../src/main/resources/application.properties) - 默认配置
- [`application_dev.properties`](../src/main/resources/application_dev.properties) - 开发环境
- [`application_test.properties`](../src/main/resources/application_test.properties) - 测试环境

### 2. 任务开关控制

通过配置文件可以控制定时任务的启用状态：
- XML配置中注释任务配置可禁用任务
- 支持动态调整任务执行频率

## 性能优化

### 1. 异步处理

- 耗时操作使用异步线程处理
- 避免阻塞主业务流程
- 提高系统响应性能

### 2. 批量处理

- 销售统计任务批量处理数据
- MQTT消息支持批量发送
- 减少数据库访问次数

### 3. 缓存机制

- 微信Token使用Redis缓存
- 减少外部API调用频率
- 提高任务执行效率

## 扩展性设计

### 1. 任务扩展

新增定时任务的步骤：
1. 在 `Task` 类中添加任务方法
2. 在 `spring-mvc.xml` 中配置调度规则
3. 实现具体的业务逻辑
4. 添加异常处理和日志记录

### 2. 监控扩展

- 支持添加任务执行监控
- 可集成第三方监控系统
- 支持任务执行状态通知

## 待优化项

### 1. 微信Token刷新
- 当前Token刷新逻辑被注释，需要根据业务需求启用
- 建议添加Token有效性检查机制
- 考虑添加Token刷新失败的重试机制

### 2. 任务监控
- 建议添加任务执行状态监控
- 可考虑集成钉钉通知或其他告警机制
- 添加任务执行时长统计

### 3. 配置优化
- 考虑将Cron表达式配置化，支持动态调整
- 添加任务开关配置，便于运维管理

## 总结

ShopUpdate系统的定时任务架构设计合理，覆盖了微信服务、数据统计、消息推送等核心业务场景。通过Spring Task框架实现了可靠的任务调度，配合异步处理和缓存机制，确保了系统的高性能和稳定性。

**核心特点**:
- 基于Spring Task的轻量级调度框架
- 完善的异常处理和日志记录机制
- 异步处理提升系统性能
- 支持集群环境下的任务执行控制
- 良好的扩展性设计

**主要业务价值**:
- 自动化的微信服务维护
- 及时的销售数据统计和推送
- 高效的设备消息通知机制
- 可靠的后台任务处理能力
