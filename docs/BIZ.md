# shopUpdate 商家端业务流程文档

## 项目概述

shopUpdate 是海尔集团开发的新版商家端APP接口汇总系统，主要为各类商家（便利店、水果店、母婴店、益农中心站等）提供完整的店铺管理、商品管理、销售管理、库存管理、支付管理等核心业务功能。

### 技术架构
- **框架**: Spring MVC 4.0.0 + MyBatis 3.3.0
- **数据库**: MySQL 8.0.33
- **缓存**: Redis
- **消息队列**: RabbitMQ
- **支付**: 支付宝、微信支付、合利宝支付
- **其他**: 百度人脸识别、极光推送、阿里云短信等

## 核心业务模块

### 1. 店铺管理模块

#### 1.1 店铺基础信息管理
**业务流程**: 店铺注册 → 信息完善 → 审核 → 开通营业

**核心实体**: 
- [`ShopsEntity`](../src/main/java/org/haier/shopUpdate/entity/ShopsEntity.java) - 店铺基础信息
- [`ShopsConfig`](../src/main/java/org/haier/shopUpdate/entity/ShopsConfig.java) - 店铺配置信息

**主要功能**:
- 店铺注册和审核管理
- 店铺类型配置（便利店、水果店、母婴店等13种类型）
- 营业时间和状态管理
- 地理位置和配送范围设置
- 支付方式配置（微信、支付宝收款码）

**相关代码**:
- 控制器: [`ShopTitleController`](../src/main/java/org/haier/shopUpdate/controller/ShopTitleController.java)
- 服务层: [`ShopTitleService`](../src/main/java/org/haier/shopUpdate/service/ShopTitleService.java)

#### 1.2 员工管理
**业务流程**: 员工添加 → 权限分配 → 登录管理

**核心实体**:
- [`ShopStaffEntity`](../src/main/java/org/haier/shopUpdate/entity/ShopStaffEntity.java) - 员工信息
- [`StaffPower`](../src/main/java/org/haier/shopUpdate/entity/StaffPower.java) - 员工权限

**相关代码**:
- 控制器: [`ShopsStaffController`](../src/main/java/org/haier/shopUpdate/controller/ShopsStaffController.java)
- 服务层: [`ShopsStaffService`](../src/main/java/org/haier/shopUpdate/service/ShopsStaffService.java)

### 2. 商品管理模块

#### 2.1 商品基础管理
**业务流程**: 商品建档 → 分类管理 → 价格设置 → 上下架管理

**核心实体**:
- [`GoodsEntity`](../src/main/java/org/haier/shopUpdate/entity/GoodsEntity.java) - 商品基础信息
- [`GoodsKind`](../src/main/java/org/haier/shopUpdate/entity/GoodsKind.java) - 商品分类
- [`GoodsPositionEntity`](../src/main/java/org/haier/shopUpdate/entity/GoodsPositionEntity.java) - 商品货位

**主要功能**:
- 商品条码管理和扫码建档
- 商品分类体系管理（公共分类/自定义分类）
- 商品价格管理（进价、售价、会员价、网购价）
- 商品规格、单位、保质期管理
- 商品图片和详情管理

**相关代码**:
- 控制器: [`GoodsController`](../src/main/java/org/haier/shopUpdate/controller/GoodsController.java)
- 服务层: [`GoodsService`](../src/main/java/org/haier/shopUpdate/service/GoodsService.java)
- 分类管理: [`GoodsKindsController`](../src/main/java/org/haier/shopUpdate/controller/GoodsKindsController.java)

#### 2.2 商品批次管理
**业务流程**: 批次创建 → 生产日期设置 → 保质期监控 → 过期预警

**相关代码**:
- 服务层: [`GoodBatchService`](../src/main/java/org/haier/shopUpdate/service/GoodBatchService.java)

### 3. 库存管理模块

#### 3.1 库存基础管理
**业务流程**: 入库 → 出库 → 调拨 → 盘点 → 库存预警

**核心实体**:
- [`ShopStock`](../src/main/java/org/haier/shopUpdate/entity/ShopStock.java) - 库存记录
- [`Inventory`](../src/main/java/org/haier/shopUpdate/entity/Inventory.java) - 盘点单
- [`InventoryDetail`](../src/main/java/org/haier/shopUpdate/entity/InventoryDetail.java) - 盘点详情

**出入库类型**:
1. 手动出入库
2. 销售订单出库
3. 进货订单入库
4. 盘库调整
5. 网上订单出库
6. 寄存
7. 云商采购
8. 调拨
9. 退货

**相关代码**:
- 控制器: [`StockController`](../src/main/java/org/haier/shopUpdate/controller/StockController.java)
- 服务层: [`StockService`](../src/main/java/org/haier/shopUpdate/service/StockService.java)
- 库存服务: [`InventoryService`](../src/main/java/org/haier/shopUpdate/service/InventoryService.java)

#### 3.2 盘点管理
**业务流程**: 创建盘点任务 → 扫码盘点 → 差异分析 → 库存调整

**核心实体**:
- [`InventoryTaskEntity`](../src/main/java/org/haier/shopUpdate/entity/InventoryTaskEntity.java) - 盘点任务
- [`InventoryTaskDetailEntity`](../src/main/java/org/haier/shopUpdate/entity/InventoryTaskDetailEntity.java) - 盘点明细

**相关代码**:
- 控制器: [`InventoryTaskController`](../src/main/java/org/haier/shopUpdate/controller/validate/InventoryTaskController.java)
- 服务层: [`InventoryTaskService`](../src/main/java/org/haier/shopUpdate/service/InventoryTaskService.java)

#### 3.3 商品调拨
**业务流程**: 创建调拨单 → 确认调拨 → 发货 → 收货 → 入库

**核心实体**:
- [`AllocationShopEntity`](../src/main/java/org/haier/shopUpdate/entity/goods/AllocationShopEntity.java) - 调拨单

**相关代码**:
- 控制器: [`AllocateController`](../src/main/java/org/haier/shopUpdate/controller/goods/AllocateController.java)
- 服务层: [`AllocateService`](../src/main/java/org/haier/shopUpdate/service/goods/AllocateService.java)

### 4. 销售管理模块

#### 4.1 收银管理
**业务流程**: 商品扫码 → 购物车管理 → 会员识别 → 优惠计算 → 支付结算

**核心实体**:
- [`SaleList`](../src/main/java/org/haier/shopUpdate/entity/SaleList.java) - 销售订单
- [`SaleListDetail`](../src/main/java/org/haier/shopUpdate/entity/SaleListDetail.java) - 销售明细

**订单类型**:
1. 实体店订单
2. 网单
3. 微信订单
4. APP订单

**相关代码**:
- 控制器: [`AppPayController`](../src/main/java/org/haier/shopUpdate/controller/AppPayController.java)
- 服务层: [`AppPayService`](../src/main/java/org/haier/shopUpdate/service/AppPayService.java)
- 销售服务: [`SaleListService`](../src/main/java/org/haier/shopUpdate/service/SaleListService.java)

#### 4.2 订单管理
**业务流程**: 订单生成 → 支付确认 → 订单处理 → 发货配送 → 订单完成

**订单状态管理**:
- 支付状态：未支付、已支付、已退款
- 处理状态：待处理、已确认、已发货、已完成、已取消

**相关代码**:
- 控制器: [`SaleListController`](../src/main/java/org/haier/shopUpdate/controller/SaleListController.java)

### 5. 支付管理模块

#### 5.1 多支付方式支持
**支持的支付方式**:
1. 现金支付
2. 支付宝支付（扫码支付、刷脸支付）
3. 微信支付（扫码支付）
4. 银行卡支付
5. 合利宝支付
6. 人脸支付

**业务流程**: 选择支付方式 → 支付验证 → 支付确认 → 支付回调处理

**相关代码**:
- 支付宝: [`AliPayController`](../src/main/java/org/haier/shopUpdate/controller/AliPayController.java)
- 微信支付: [`WechatPayController`](../src/main/java/org/haier/shopUpdate/controller/WechatPayController.java)
- 支付工具: [`AlipayPayUtil`](../src/main/java/org/haier/shopUpdate/util/AlipayPayUtil.java)
- 微信工具: [`WechatPayUtil`](../src/main/java/org/haier/shopUpdate/util/WechatPayUtil.java)

#### 5.2 聚合支付码管理
**业务流程**: 申请聚合码 → 配置支付方式 → 生成二维码 → 收款管理

**相关代码**:
- 控制器: [`AggregationCodeController`](../src/main/java/org/haier/shopUpdate/controller/AggregationCodeController.java)
- 服务层: [`AggregationCodeService`](../src/main/java/org/haier/shopUpdate/service/AggregationCodeService.java)

### 6. 会员管理模块

#### 6.1 会员基础管理
**业务流程**: 会员注册 → 信息完善 → 积分管理 → 等级管理

**核心实体**:
- [`CusCheckout`](../src/main/java/org/haier/shopUpdate/entity/CusCheckout.java) - 会员信息
- [`CusConsumption`](../src/main/java/org/haier/shopUpdate/entity/CusConsumption.java) - 消费记录
- [`CusRecharge`](../src/main/java/org/haier/shopUpdate/entity/CusRecharge.java) - 充值记录

**相关代码**:
- 控制器: [`CusCheckoutContorller`](../src/main/java/org/haier/shopUpdate/controller/CusCheckoutContorller.java)
- 服务层: [`CusCheckoutService`](../src/main/java/org/haier/shopUpdate/service/CusCheckoutService.java)

#### 6.2 积分和优惠管理
**业务流程**: 积分获取 → 积分使用 → 优惠券发放 → 优惠券使用

**相关代码**:
- 现金管理: [`CashController`](../src/main/java/org/haier/shopUpdate/controller/CashController.java)
- 金圈币: [`GoldController`](../src/main/java/org/haier/shopUpdate/controller/GoldController.java)

### 7. 供应商管理模块

#### 7.1 供应商基础管理
**业务流程**: 供应商注册 → 资质审核 → 商品供应 → 结算管理

**核心实体**:
- [`ShopSupSupplierEntity`](../src/main/java/org/haier/shopUpdate/entity/ShopSupSupplierEntity.java) - 供应商信息
- [`ShopSupBillEntity`](../src/main/java/org/haier/shopUpdate/entity/ShopSupBillEntity.java) - 购销单

**相关代码**:
- 控制器: [`ShopSupplierController`](../src/main/java/org/haier/shopUpdate/controller/validate/ShopSupplierController.java)
- 服务层: [`ShopSupplierService`](../src/main/java/org/haier/shopUpdate/service/ShopSupplierService.java)

#### 7.2 购销单管理
**业务流程**: 创建购销单 → 商品入库 → 核对验收 → 付款结算

**相关代码**:
- 控制器: [`ShopSupBillController`](../src/main/java/org/haier/shopUpdate/controller/validate/ShopSupBillController.java)
- 服务层: [`ShopSupBillService`](../src/main/java/org/haier/shopUpdate/service/ShopSupBillService.java)

### 8. 统计分析模块

#### 8.1 经营数据统计
**业务流程**: 数据采集 → 统计分析 → 报表生成 → 决策支持

**统计维度**:
- 销售统计（日、周、月、年）
- 商品销量排行
- 库存预警统计
- 会员消费分析
- 员工业绩统计

**相关代码**:
- 控制器: [`StatisticsController`](../src/main/java/org/haier/shopUpdate/controller/StatisticsController.java)
- 服务层: [`StatisticsService`](../src/main/java/org/haier/shopUpdate/service/StatisticsService.java)

### 9. 语音助手模块

#### 9.1 语音指令管理
**业务流程**: 语音识别 → 指令解析 → 功能执行 → 语音反馈

**核心实体**:
- [`SpeechCmdEntity`](../src/main/java/org/haier/shopUpdate/entity/SpeechCmdEntity.java) - 语音指令
- [`SpeechSetEntity`](../src/main/java/org/haier/shopUpdate/entity/SpeechSetEntity.java) - 语音设置

**相关代码**:
- 控制器: [`SpeechController`](../src/main/java/org/haier/shopUpdate/controller/SpeechController.java)
- 服务层: [`SpeechService`](../src/main/java/org/haier/shopUpdate/service/SpeechService.java)

## 关键业务流程

### 1. 完整销售流程

```mermaid
graph TD
    A[商品扫码] --> B[添加到购物车]
    B --> C[会员识别]
    C --> D[优惠计算]
    D --> E[选择支付方式]
    E --> F[支付处理]
    F --> G[库存扣减]
    G --> H[订单生成]
    H --> I[小票打印]
    I --> J[交易完成]
```

**涉及的主要接口**:
- 商品搜索: [`AppPayController.searchGoods()`](../src/main/java/org/haier/shopUpdate/controller/AppPayController.java#L24)
- 添加商品: [`AppPayController.addGoodsToCar()`](../src/main/java/org/haier/shopUpdate/controller/AppPayController.java#L8)
- 支付收银: [`AppPayController.shop_pay()`](../src/main/java/org/haier/shopUpdate/controller/AppPayController.java#L175)

### 2. 库存管理流程

```mermaid
graph TD
    A[商品入库] --> B[库存增加]
    B --> C[销售出库]
    C --> D[库存减少]
    D --> E[库存预警]
    E --> F[补货提醒]
    F --> G[盘点核实]
    G --> H[库存调整]
```

**涉及的主要接口**:
- 库存查询: [`StockController`](../src/main/java/org/haier/shopUpdate/controller/StockController.java)
- 盘点管理: [`InventoryTaskController`](../src/main/java/org/haier/shopUpdate/controller/validate/InventoryTaskController.java)

### 3. 供应商采购流程

```mermaid
graph TD
    A[创建购销单] --> B[商品入库]
    B --> C[核对验收]
    C --> D[确认入库]
    D --> E[生成应付款]
    E --> F[付款结算]
    F --> G[完成采购]
```

**涉及的主要接口**:
- 购销单管理: [`ShopSupBillController`](../src/main/java/org/haier/shopUpdate/controller/validate/ShopSupBillController.java)
- 商品入库: [`ShopSupBillController.storageGoods()`](../src/main/java/org/haier/shopUpdate/controller/validate/ShopSupBillController.java#L62)

### 4. 会员消费流程

```mermaid
graph TD
    A[会员识别] --> B[查询会员信息]
    B --> C[计算会员优惠]
    C --> D[积分计算]
    D --> E[消费记录]
    E --> F[积分更新]
    F --> G[等级评估]
```

**涉及的主要接口**:
- 会员管理: [`CusCheckoutContorller`](../src/main/java/org/haier/shopUpdate/controller/CusCheckoutContorller.java)
- 会员查询: [`CusCheckoutContorller.findCusById()`](../src/main/java/org/haier/shopUpdate/controller/CusCheckoutContorller.java#L127)

### 5. 盘点业务流程

```mermaid
graph TD
    A[创建盘点任务] --> B[选择盘点商品]
    B --> C[扫码盘点]
    C --> D[录入实际库存]
    D --> E[计算差异]
    E --> F[差异审核]
    F --> G[库存调整]
    G --> H[完成盘点]
```

**涉及的主要接口**:
- 盘点任务: [`InventoryTaskController.addTask()`](../src/main/java/org/haier/shopUpdate/controller/validate/InventoryTaskController.java#L32)
- 盘点详情: [`InventoryTaskController.addTaskDetail()`](../src/main/java/org/haier/shopUpdate/controller/validate/InventoryTaskController.java#L87)

## 系统集成

### 1. 外部系统集成
- **支付中心**: 统一支付接口管理
  - 配置: [`PayCenterConfig`](../src/main/java/org/haier/shopUpdate/config/PayCenterConfig.java)
  - 常量: [`PayCenterConstant`](../src/main/java/org/haier/shopUpdate/constant/PayCenterConstant.java)
- **电子价签**: 商品价格同步
  - 配置: [`ElectronicConfig`](../src/main/java/org/haier/shopUpdate/config/ElectronicConfig.java)
- **Nova系统**: 海外业务支持
- **消息推送**: 极光推送、百度推送
  - 工具: [`JPushClientUtil`](../src/main/java/org/haier/shopUpdate/util/JPushClientUtil.java)
- **人脸识别**: 百度AI人脸识别
  - 工具: [`BaiDuFace`](../src/main/java/org/haier/shopUpdate/util/BaiDuFace.java)

### 2. 数据同步
- **商品数据**: 与电子价签系统同步
- **订单数据**: 与支付中心同步
- **会员数据**: 跨店铺数据同步

### 3. 消息队列集成
- **RabbitMQ配置**:
  - 退款订单同步: `return_list_sync_queue`
  - 交换机: `return_list_sync_exchange`

## 技术特性

### 1. 多语言支持
- 国际化配置: [`I18nLanguageMsgUtil`](../src/main/java/org/haier/shopUpdate/util/I18nLanguageMsgUtil.java)
- 多语言切面: [`I18nAspect`](../src/main/java/org/haier/shopUpdate/aop/I18nAspect.java)
- 语言初始化: [`I18nLanguageMsgInit`](../src/main/java/org/haier/shopUpdate/init/I18nLanguageMsgInit.java)

### 2. 缓存机制
- Redis缓存: [`RedisCache`](../src/main/java/org/haier/shopUpdate/redism/RedisCache.java)
- 缓存策略: 商品信息、会员信息、配置信息缓存
- 缓存传输: [`RedisCacheTransfer`](../src/main/java/org/haier/shopUpdate/redism/RedisCacheTransfer.java)

### 3. 安全机制
- 请求验证: [`ValidateReqAspect`](../src/main/java/org/haier/shopUpdate/aop/ValidateReqAspect.java)
- 参数校验: [`ValidateUtils`](../src/main/java/org/haier/shopUpdate/util/ValidateUtils.java)
- 拦截器: [`MyInterceptor`](../src/main/java/org/haier/shopUpdate/aop/MyInterceptor.java)

### 4. 日志管理
- 操作日志: [`LogAspect`](../src/main/java/org/haier/shopUpdate/log/aspect)
- 业务日志: 记录关键业务操作
- 日志注解: [`LogAnnotation`](../src/main/java/org/haier/shopUpdate/log/annotation)

### 5. 文件存储
- OSS对象存储: [`OssClient`](../src/main/java/org/haier/shopUpdate/oss/OssClient.java)
- 文件上传: [`FileUploadService`](../src/main/java/org/haier/shopUpdate/upload/FileUploadService.java)
- 存储工厂: [`OssFactory`](../src/main/java/org/haier/shopUpdate/oss/OssFactory.java)

## 数据模型关系

### 1. 核心实体关系

```mermaid
erDiagram
    SHOPS ||--o{ SHOP_STAFF : has
    SHOPS ||--o{ GOODS : contains
    SHOPS ||--o{ SALE_LIST : generates
    SHOPS ||--o{ CUSTOMER : serves
    SHOPS ||--o{ SUPPLIER : cooperates

    GOODS ||--o{ GOODS_KIND : belongs_to
    GOODS ||--o{ SHOP_STOCK : has_stock
    GOODS ||--o{ SALE_LIST_DETAIL : sold_in

    SALE_LIST ||--o{ SALE_LIST_DETAIL : contains
    SALE_LIST }o--|| CUSTOMER : belongs_to
    SALE_LIST }o--|| SHOP_STAFF : handled_by

    SUPPLIER ||--o{ SHOP_SUP_BILL : creates
    SHOP_SUP_BILL ||--o{ SHOP_SUP_BILL_DETAIL : contains

    INVENTORY_TASK ||--o{ INVENTORY_TASK_DETAIL : contains
    INVENTORY_TASK_DETAIL }o--|| GOODS : inventories
```

### 2. 业务状态流转

#### 2.1 订单状态流转
```mermaid
stateDiagram-v2
    [*] --> 待支付
    待支付 --> 已支付 : 支付成功
    待支付 --> 已取消 : 取消订单
    已支付 --> 已发货 : 商家发货
    已支付 --> 已退款 : 申请退款
    已发货 --> 已完成 : 确认收货
    已发货 --> 已退款 : 退货退款
    已完成 --> [*]
    已取消 --> [*]
    已退款 --> [*]
```

#### 2.2 库存状态流转
```mermaid
stateDiagram-v2
    [*] --> 正常库存
    正常库存 --> 库存预警 : 库存不足
    正常库存 --> 盘点中 : 开始盘点
    库存预警 --> 正常库存 : 补货入库
    盘点中 --> 正常库存 : 盘点完成
    盘点中 --> 库存调整 : 发现差异
    库存调整 --> 正常库存 : 调整完成
```

## 部署配置

### 1. 环境配置
- 开发环境: [`application_dev.properties`](../src/main/resources/application_dev.properties)
- 测试环境: [`application_test.properties`](../src/main/resources/application_test.properties)
- 生产环境: [`application_prod.properties`](../src/main/resources/application_prod.properties)

### 2. 数据库配置
- **主数据库**: 商家业务数据 (MySQL)
- **缓存数据库**: Redis缓存
- **消息队列**: RabbitMQ

### 3. 第三方服务配置
- **支付服务**: 支付宝、微信支付、合利宝
- **推送服务**: 极光推送、百度推送
- **AI服务**: 百度人脸识别
- **短信服务**: 阿里云短信
- **存储服务**: Minio对象存储

## 接口规范

### 1. 统一返回格式
所有接口统一使用 [`ShopsResult`](../src/main/java/org/haier/shopUpdate/util/ShopsResult.java) 作为返回格式：

```json
{
    "status": 1,           // 状态码：1-成功，0-失败
    "msg": "操作成功",      // 返回消息
    "data": {},           // 返回数据
    "count": 0            // 数据总数（分页时使用）
}
```

### 2. 分页参数规范
使用 [`PageQuery`](../src/main/java/org/haier/shopUpdate/entity/PageQuery.java) 进行分页查询：
- `pageIndex`: 页码（从1开始）
- `pageSize`: 每页大小
- `total`: 总记录数

### 3. 参数验证
使用 [`ValidateCommonReq`](../src/main/java/org/haier/shopUpdate/params/ValidateCommonReq.java) 进行通用参数验证：
- 必填参数验证
- 参数格式验证
- 业务规则验证

## 性能优化

### 1. 缓存策略
- **商品信息缓存**: 热点商品信息缓存30分钟
- **会员信息缓存**: 活跃会员信息缓存1小时
- **配置信息缓存**: 系统配置信息缓存24小时

### 2. 数据库优化
- **索引优化**: 关键查询字段建立索引
- **分页查询**: 大数据量查询使用分页
- **读写分离**: 读操作使用从库，写操作使用主库

### 3. 接口优化
- **批量操作**: 支持批量商品操作
- **异步处理**: 耗时操作使用异步处理
- **数据压缩**: 大数据量传输使用压缩

## 监控告警

### 1. 业务监控
- **销售监控**: 实时销售数据监控
- **库存监控**: 库存预警和异常监控
- **支付监控**: 支付成功率和异常监控

### 2. 系统监控
- **性能监控**: 接口响应时间监控
- **错误监控**: 系统异常和错误监控
- **资源监控**: CPU、内存、磁盘使用监控

### 3. 日志管理
- **业务日志**: 关键业务操作日志
- **错误日志**: 系统错误和异常日志
- **访问日志**: 接口访问和性能日志

## 安全机制

### 1. 数据安全
- **数据加密**: 敏感数据加密存储
- **传输加密**: HTTPS传输加密
- **访问控制**: 基于角色的访问控制

### 2. 接口安全
- **参数验证**: 严格的参数验证
- **防重放攻击**: 请求签名和时间戳验证
- **限流控制**: 接口访问频率限制

### 3. 业务安全
- **权限控制**: 细粒度的功能权限控制
- **操作审计**: 关键操作审计日志
- **异常检测**: 异常操作检测和告警

## 总结

shopUpdate系统是一个功能完整的商家端管理系统，涵盖了店铺管理、商品管理、库存管理、销售管理、支付管理、会员管理、供应商管理等核心业务模块。系统采用Spring MVC架构，支持多种支付方式，具备完善的库存管理和统计分析功能，能够满足各类商家的日常经营需求。

### 系统优势
1. **功能完整**: 覆盖商家经营全流程
2. **架构清晰**: 模块化设计，易于维护
3. **扩展性强**: 支持多种业务场景扩展
4. **性能优化**: 缓存、分页、异步等优化策略
5. **安全可靠**: 完善的安全机制和监控告警

### 技术特色
1. **多支付集成**: 支持主流支付方式
2. **智能库存**: 自动预警和盘点功能
3. **数据分析**: 丰富的统计分析功能
4. **语音助手**: 创新的语音操作体验
5. **国际化**: 支持多语言和多币种

系统的设计充分考虑了商家的实际业务场景，提供了灵活的配置选项和丰富的功能模块，同时具备良好的扩展性和可维护性。通过标准化的接口设计和模块化的架构，系统能够快速适应不同类型商家的个性化需求。
