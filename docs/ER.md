# 商店更新系统数据库ER图

## 目录
- [概述](#概述)
- [核心业务模块](#核心业务模块)
- [详细ER图](#详细er图)
- [主要实体关系说明](#主要实体关系说明)
- [核心业务流程](#核心业务流程)
- [数据完整性约束](#数据完整性约束)
- [索引建议](#索引建议)
- [性能优化建议](#性能优化建议)
- [扩展性考虑](#扩展性考虑)
- [表结构优化建议](#表结构优化建议)

## 概述

本文档描述了商店更新系统(shopUpdate)的数据库实体关系图(ER图)。该系统是一个综合性的商店管理平台，支持商品管理、库存管理、销售管理、供应商管理、促销活动等核心业务功能。

### 系统特点
- **多租户架构**: 通过shop_unique实现多店铺数据隔离
- **完整业务闭环**: 涵盖从商品管理到销售结算的完整流程
- **灵活扩展性**: 支持连锁店铺、供应商管理等复杂业务场景
- **国际化支持**: 内置多语言支持机制

## 核心业务模块

### 1. 店铺管理模块
- **shops**: 店铺基础信息表
- **shops_config**: 店铺配置信息表
- **shop_staff**: 店铺员工信息表

### 2. 商品管理模块
- **goods**: 商品基础信息表
- **goods_kind**: 商品分类表
- **goods_dict**: 商品字典表
- **goods_price_adjustment**: 商品调价记录表
- **goods_wholesale**: 商品批发价格表

### 3. 库存管理模块
- **inventory_task**: 盘点任务表
- **inventory_task_detail**: 盘点任务详情表
- **shop_stock**: 店铺库存表
- **allocation_shop**: 店铺调拨单表
- **allocation_shop_detail**: 调拨单详情表

### 4. 销售管理模块
- **sale_list**: 销售订单表
- **sale_list_detail**: 销售订单详情表
- **sale_list_pay_detail**: 销售支付详情表

### 5. 供应商管理模块
- **shop_sup_supplier**: 店铺供应商表
- **shop_sup_goods**: 供应商商品表
- **shop_sup_bill**: 供应商账单表

### 6. 促销管理模块
- **promotion_activity**: 促销活动表
- **promotion_goods_gift**: 商品满赠表
- **promotion_goods_single**: 商品单件促销表

## 详细ER图

```mermaid
erDiagram
    %% 店铺相关实体
    shops {
        int shop_id PK
        bigint shop_unique UK "店铺唯一标识"
        varchar shop_name "店铺名称"
        int shop_type "店铺类型"
        varchar manager_unique "管理员唯一标识"
        varchar manager_account "管理员账号"
        varchar manager_pwd "管理员密码"
        varchar shop_address_detail "详细地址"
        varchar shop_phone "联系电话"
        decimal shop_latitude "纬度"
        decimal shop_longitude "经度"
        int examine_status "审核状态"
        datetime registration_date "注册时间"
        int shop_status "营业状态"
        int shop_class "店铺分类"
    }

    shops_config {
        int id PK
        bigint shop_unique FK "店铺唯一标识"
        varchar wechat_pic "微信收款码"
        varchar alipay_pic "支付宝收款码"
        int mianmi_status "免密支付状态"
        varchar teamviewer "远程协助ID"
        int same_goods "商品同步状态"
        int same_cus "会员同步状态"
    }

    shop_staff {
        int id PK
        bigint shop_unique FK "店铺唯一标识"
        varchar staff_name "员工姓名"
        varchar staff_account "员工账号"
        varchar staff_pwd "员工密码"
        int staff_power "员工权限"
        datetime create_time "创建时间"
    }

    %% 商品相关实体
    goods {
        int goods_id PK
        bigint shop_unique FK "店铺唯一标识"
        varchar goods_barcode UK "商品条码"
        bigint goods_kind_unique FK "商品分类ID"
        varchar goods_name "商品名称"
        varchar goods_brand "商品品牌"
        decimal goods_in_price "进价"
        decimal goods_sale_price "售价"
        decimal goods_web_sale_price "网购价"
        decimal goods_cus_price "会员价"
        decimal goods_count "库存数量"
        varchar goods_unit "计价单位"
        varchar goods_standard "商品规格"
        varchar goods_address "产地"
        varchar goods_picturepath "图片路径"
        bigint foreign_key "包装分类外键"
        varchar default_supplier_unique "默认供应商"
        int shelf_state "上架状态"
        datetime update_time "更新时间"
    }

    goods_kind {
        int goods_kind_id PK
        bigint goods_kind_unique UK "分类唯一标识"
        bigint shop_unique FK "店铺唯一标识"
        varchar goods_kind_name "分类名称"
        bigint goods_kind_parunique "父分类ID"
        int goods_kind_icon_id "分类图标ID"
        varchar goods_kind_alias "分类别名"
        int edit_type "编辑类型"
    }

    goods_dict {
        int id PK
        varchar barcode UK "商品条码"
        varchar name "商品名称"
        varchar brand "品牌"
        bigint kind "分类ID"
        varchar unit "单位"
        varchar standard "规格"
        varchar address "产地"
        varchar picture_name "图片名称"
        decimal inprice "进价"
        decimal saleprice "售价"
        varchar remarks "备注"
        int base "是否标准库商品"
    }

    goods_price_adjustment {
        bigint id PK
        bigint shop_unique FK "店铺唯一标识"
        varchar goods_barcode "商品条码"
        decimal goods_sale_price "销售价"
        decimal goods_web_sale_price "网单价"
        decimal goods_cus_price "会员价"
        datetime create_time "创建时间"
    }

    goods_wholesale {
        bigint id PK
        int goods_id FK "商品ID"
        bigint shop_unique FK "店铺唯一标识"
        varchar goods_barcode "商品条码"
        decimal wholesale_price "批发价"
        decimal wholesale_count "起批数量"
    }

    %% 库存相关实体
    inventory_task {
        int task_id PK
        bigint shop_unique FK "店铺唯一标识"
        varchar task_name "任务名称"
        int task_status "任务状态"
        datetime create_time "创建时间"
        datetime start_time "开始时间"
        datetime end_time "结束时间"
        int create_user "创建人"
        varchar task_remark "任务备注"
    }

    inventory_task_detail {
        int id PK
        int task_id FK "任务ID"
        varchar goods_barcode "商品条码"
        decimal system_count "系统库存"
        decimal inventory_count "盘点数量"
        decimal diff_count "差异数量"
        varchar remark "备注"
        datetime create_time "创建时间"
    }

    allocation_shop {
        int purchase_list_id PK
        bigint purchase_list_unique UK "调拨单唯一标识"
        bigint storehouse_in_id "调入仓库ID"
        bigint storehouse_out_id "调出仓库ID"
        varchar purchase_list_remark "订单备注"
        datetime purchase_list_date "申请时间"
        int user_id "操作人"
        int allocation_status "调拨状态"
        int recipients_user_id "接收人ID"
        varchar recipients_user_id_name "接收人姓名"
        datetime recipients_time "接收时间"
        varchar user_name "申请人姓名"
    }

    allocation_shop_detail {
        int id PK
        bigint purchase_list_unique FK "调拨单唯一标识"
        varchar goods_barcode "商品条码"
        decimal allocation_count "调拨数量"
        decimal received_count "已收数量"
        varchar remark "备注"
    }

    %% 销售相关实体
    sale_list {
        int sale_list_id PK
        bigint sale_list_unique UK "销售单唯一标识"
        bigint shop_unique FK "店铺唯一标识"
        datetime sale_list_datetime "销售时间"
        decimal sale_list_total "订单总额"
        decimal sale_list_pur "实收金额"
        int sale_list_totalCount "商品总数"
        varchar sale_list_name "客户姓名"
        varchar sale_list_phone "客户电话"
        varchar sale_list_address "配送地址"
        int sale_type "销售类型"
        int sale_list_state "订单状态"
        int sale_list_payment "支付方式"
        varchar sale_list_remarks "订单备注"
        varchar sale_list_cashier "收银员"
    }

    sale_list_detail {
        int sale_list_detail_id PK
        bigint sale_list_unique FK "销售单唯一标识"
        varchar goods_barcode "商品条码"
        decimal sale_list_detail_count "销售数量"
        decimal sale_list_detail_price "销售单价"
        varchar goods_picturepath "商品图片"
    }

    sale_list_pay_detail {
        int id PK
        bigint sale_list_unique FK "销售单唯一标识"
        int pay_method "支付方式"
        decimal pay_money "支付金额"
        varchar pay_remark "支付备注"
        datetime create_time "创建时间"
    }

    %% 供应商相关实体
    shop_sup_supplier {
        int id PK
        bigint shop_unique FK "店铺唯一标识"
        varchar supplier_name "供应商名称"
        bigint supplier_unique UK "供应商唯一标识"
        varchar contacts "联系人"
        varchar contact_mobile "联系电话"
        varchar address "地址"
        int enable_status "启用状态"
        int purchase_type "采购类型"
        bigint supplier_kind_unique "供应商分类"
        datetime create_time "创建时间"
    }

    shop_sup_goods {
        int id PK
        bigint shop_unique FK "店铺唯一标识"
        bigint supplier_unique FK "供应商唯一标识"
        varchar goods_barcode "商品条码"
        varchar supplier_goods_barcode "供应商商品编码"
        decimal goods_in_price "进价"
        decimal goods_sale_price "建议售价"
        int record_flag "记录标识"
    }

    shop_sup_bill {
        int id PK
        bigint shop_unique FK "店铺唯一标识"
        bigint supplier_unique FK "供应商唯一标识"
        varchar bill_no "账单号"
        decimal bill_amount "账单金额"
        int bill_status "账单状态"
        datetime bill_date "账单日期"
        varchar bill_remark "账单备注"
    }

    %% 促销相关实体
    promotion_activity {
        int promotion_activity_id PK
        bigint shop_unique FK "店铺唯一标识"
        varchar promotion_activity_name "活动名称"
        int type "活动类型"
        datetime start_time "开始时间"
        datetime end_time "结束时间"
        int status "活动状态"
        int activity_range "投放范围"
        int order_activity "是否参与订单促销"
        datetime create_time "创建时间"
    }

    promotion_goods_gift {
        int id PK
        int promotion_activity_id FK "促销活动ID"
        varchar goods_barcode "商品条码"
        decimal full_money "满足金额"
        varchar gift_goods_barcode "赠品条码"
        decimal gift_count "赠品数量"
    }

    promotion_goods_single {
        int id PK
        int promotion_activity_id FK "促销活动ID"
        varchar goods_barcode "商品条码"
        int piece_num "第几件"
        decimal discount_rate "折扣率"
    }

    %% 其他辅助实体
    speech_set {
        bigint id PK
        varchar file_identify "文件标识"
        varchar text_identify "文本标识"
        datetime create_time "创建时间"
        datetime update_time "更新时间"
    }

    i18n_language {
        bigint id PK
        varchar key_name "键名"
        varchar language_code "语言代码"
        text content "内容"
        datetime create_time "创建时间"
        datetime update_time "更新时间"
    }

    %% 关系定义
    shops ||--o{ shops_config : "配置"
    shops ||--o{ shop_staff : "员工"
    shops ||--o{ goods : "商品"
    shops ||--o{ goods_kind : "分类"
    shops ||--o{ inventory_task : "盘点任务"
    shops ||--o{ sale_list : "销售订单"
    shops ||--o{ shop_sup_supplier : "供应商"
    shops ||--o{ promotion_activity : "促销活动"

    goods_kind ||--o{ goods : "分类商品"
    goods_kind ||--o{ goods_kind : "父子分类"

    goods ||--o{ goods_price_adjustment : "调价记录"
    goods ||--o{ goods_wholesale : "批发价格"
    goods ||--o{ sale_list_detail : "销售详情"
    goods ||--o{ inventory_task_detail : "盘点详情"
    goods ||--o{ allocation_shop_detail : "调拨详情"
    goods ||--o{ shop_sup_goods : "供应商商品"

    inventory_task ||--o{ inventory_task_detail : "盘点详情"

    allocation_shop ||--o{ allocation_shop_detail : "调拨详情"

    sale_list ||--o{ sale_list_detail : "订单详情"
    sale_list ||--o{ sale_list_pay_detail : "支付详情"

    shop_sup_supplier ||--o{ shop_sup_goods : "供应商商品"
    shop_sup_supplier ||--o{ shop_sup_bill : "供应商账单"

    promotion_activity ||--o{ promotion_goods_gift : "满赠商品"
    promotion_activity ||--o{ promotion_goods_single : "单件促销"
```

## 主要实体关系说明

### 1. 店铺与商品关系
- 一个店铺可以有多个商品 (1:N)
- 一个店铺可以有多个商品分类 (1:N)
- 商品分类之间存在父子关系 (1:N)

### 2. 商品与库存关系
- 一个商品可以有多个调价记录 (1:N)
- 一个商品可以有多个批发价格设置 (1:N)
- 商品参与盘点任务详情 (1:N)

### 3. 销售订单关系
- 一个销售订单包含多个商品详情 (1:N)
- 一个销售订单可以有多种支付方式 (1:N)
- 销售订单详情关联具体商品 (N:1)

### 4. 供应商关系
- 一个店铺可以有多个供应商 (1:N)
- 一个供应商可以提供多个商品 (1:N)
- 供应商与店铺之间有账单关系 (1:N)

### 5. 促销活动关系
- 一个促销活动可以包含多个满赠规则 (1:N)
- 一个促销活动可以包含多个单件促销规则 (1:N)

## 重要字段说明

### 店铺相关字段
- **shop_unique**: 店铺唯一标识，系统核心外键，用于数据隔离
- **shop_type**: 店铺类型(1-便利店, 2-水果店, 3-母婴店等)
- **shop_class**: 店铺分类(0-普通商家, 1-连锁, 2-加盟等)
- **examine_status**: 审核状态(1-未提交, 2-已提交, 3-未通过, 4-通过)

### 商品相关字段
- **goods_barcode**: 商品条码，商品唯一标识
- **goods_kind_unique**: 商品分类唯一标识，支持层级分类
- **shelf_state**: 上架状态(1-已上架, 2-已下架)
- **goods_cheng_type**: 称重类型(0-按件, 1-按重量)

### 订单相关字段
- **sale_list_unique**: 销售订单唯一标识
- **sale_type**: 销售类型(区分线上线下等不同渠道)
- **sale_list_state**: 订单状态(待付款、已付款、已发货等)
- **sale_list_payment**: 支付方式(现金、微信、支付宝等)

### 库存相关字段
- **allocation_status**: 调拨状态(1-待调拨, 2-待发货, 3-待收货, 4-已完成, 5-已撤销)
- **task_status**: 盘点任务状态
- **diff_count**: 盘点差异数量(正数为盘盈，负数为盘亏)

## 核心业务流程

### 1. 商品管理流程
1. 创建商品分类 (goods_kind)
2. 添加商品基础信息 (goods)
3. 设置商品价格 (goods_price_adjustment)
4. 配置批发价格 (goods_wholesale)

### 2. 库存管理流程
1. 创建盘点任务 (inventory_task)
2. 录入盘点详情 (inventory_task_detail)
3. 处理库存差异
4. 店铺间调拨 (allocation_shop, allocation_shop_detail)

### 3. 销售流程
1. 创建销售订单 (sale_list)
2. 添加商品详情 (sale_list_detail)
3. 处理支付信息 (sale_list_pay_detail)
4. 更新库存

### 4. 供应商管理流程
1. 注册供应商信息 (shop_sup_supplier)
2. 维护供应商商品 (shop_sup_goods)
3. 管理供应商账单 (shop_sup_bill)

## 数据完整性约束

### 主键约束
- 每个表都有明确的主键定义
- 使用自增ID或业务唯一标识作为主键

### 外键约束
- shop_unique: 关联店铺的核心外键
- goods_barcode: 商品条码作为商品关联的核心字段
- 各种unique字段保证业务唯一性

### 业务约束
- 商品库存不能为负数
- 销售价格必须大于0
- 订单状态必须在预定义范围内
- 促销活动时间必须合理

## 索引建议

### 核心索引
```sql
-- 商品表核心索引
CREATE INDEX idx_goods_shop_barcode ON goods(shop_unique, goods_barcode);
CREATE INDEX idx_goods_kind ON goods(goods_kind_unique);
CREATE INDEX idx_goods_update_time ON goods(update_time);

-- 销售订单索引
CREATE INDEX idx_sale_list_shop_time ON sale_list(shop_unique, sale_list_datetime);
CREATE INDEX idx_sale_list_unique ON sale_list(sale_list_unique);

-- 库存相关索引
CREATE INDEX idx_inventory_task_shop ON inventory_task(shop_unique, task_status);
CREATE INDEX idx_allocation_shop_status ON allocation_shop(allocation_status);

-- 供应商索引
CREATE INDEX idx_shop_sup_supplier_shop ON shop_sup_supplier(shop_unique, enable_status);
```

## 性能优化建议

### 1. 分区策略
- 对于大表如sale_list可按时间分区
- goods表可按shop_unique分区

### 2. 缓存策略
- 商品基础信息适合缓存
- 商品分类信息适合缓存
- 店铺配置信息适合缓存

### 3. 查询优化
- 避免全表扫描
- 合理使用复合索引
- 定期分析表统计信息

## 扩展性考虑

### 1. 多租户支持
- 通过shop_unique实现数据隔离
- 支持连锁店铺管理

### 2. 国际化支持
- i18n_language表支持多语言
- 商品名称、分类名称支持国际化

### 3. 审计日志
- 关键操作记录操作日志
- 支持数据变更追踪

## 表结构优化建议

### 1. 数据类型优化
- 使用合适的数据类型减少存储空间
- 价格字段统一使用DECIMAL类型
- 状态字段使用TINYINT类型

### 2. 字段设计优化
- 添加必要的默认值
- 合理设置字段长度
- 重要字段添加NOT NULL约束

### 3. 表设计优化
- 考虑垂直分表策略
- 历史数据归档策略
- 读写分离支持

## 常见查询模式

### 1. 商品查询
```sql
-- 查询店铺商品及分类信息
SELECT g.*, gk.goods_kind_name
FROM goods g
LEFT JOIN goods_kind gk ON g.goods_kind_unique = gk.goods_kind_unique
WHERE g.shop_unique = ? AND g.shelf_state = 1;
```

### 2. 销售统计
```sql
-- 查询店铺日销售统计
SELECT DATE(sale_list_datetime) as sale_date,
       SUM(sale_list_total) as total_amount,
       COUNT(*) as order_count
FROM sale_list
WHERE shop_unique = ? AND DATE(sale_list_datetime) = CURDATE()
GROUP BY DATE(sale_list_datetime);
```

### 3. 库存预警
```sql
-- 查询低库存商品
SELECT g.goods_name, g.goods_count, g.out_stock_count
FROM goods g
WHERE g.shop_unique = ?
  AND g.goods_count <= g.out_stock_count
  AND g.shelf_state = 1;
```

## 数据迁移注意事项

### 1. 历史数据处理
- 销售数据建议按年度归档
- 库存变更记录定期清理
- 商品调价记录保留近3年数据

### 2. 数据一致性检查
- 定期检查商品库存与销售记录的一致性
- 验证订单金额与支付记录的匹配性
- 确保分类层级关系的完整性

## 安全考虑

### 1. 数据访问控制
- 基于shop_unique的行级安全策略
- 敏感字段(如密码)需要加密存储
- 支付相关数据需要特殊保护

### 2. 审计要求
- 关键业务操作需要记录操作日志
- 价格变更、库存调整等需要审计追踪
- 用户登录和权限变更需要监控

---

*本文档基于shopUpdate项目代码分析生成，反映了系统的核心数据模型和业务关系。*

**文档信息:**
- 版本: v1.0
- 创建时间: 2024年
- 分析范围: 基于项目源码和数据库映射文件
- 维护状态: 活跃维护中

**贡献者:**
- 数据库设计分析: AI Assistant
- 业务流程梳理: 基于代码逻辑推导
- 性能优化建议: 基于最佳实践
