# 消息队列(MQ)业务流程文档

## 概述

本项目采用多种消息队列技术实现实时通信和异步处理，主要包括：
- **MQTT**: 用于与收银机设备的实时通信
- **RabbitMQ**: 用于退款订单同步等业务消息处理
- **Socket**: 用于自定义TCP通信服务
- **WebSocket**: 用于前端实时消息推送

## 1. MQTT消息系统

### 1.1 架构概述

MQTT系统主要用于店铺管理系统与收银机设备之间的实时通信，实现商品信息同步、订单状态通知等功能。

### 1.2 核心组件

#### 1.2.1 MQTT客户端配置
- **配置类**: [`ProjectConfig`](../src/main/java/org/haier/shopUpdate/project/ProjectConfig.java)
- **客户端实现**: [`ClientMQTT`](../src/main/java/org/haier/shopUpdate/util/mqtt/ClientMQTT.java)
- **工具类**: [`MqttxUtil`](../src/main/java/org/haier/shopUpdate/util/mqtt/MqttxUtil.java)
- **回调处理**: [`PushCallback`](../src/main/java/org/haier/shopUpdate/util/mqtt/PushCallback.java)

#### 1.2.2 配置参数
```properties
# 开发环境
project.mqtt.host=tcp://*************:1883
project.mqtt.topicmain=win_qt_cash_1.0
project.mqtt.topic=win_qt_cash_
project.mqtt.clientid=client_shopUpdate
project.mqtt.username=haiwai
project.mqtt.password=yingxiangli123

# 生产环境
project.mqtt.host=tcp://global-mqtt.all-scm.com:1883
project.mqtt.username=buyhoo
project.mqtt.password=Yingxiangli123
```

### 1.3 业务流程

#### 1.3.1 订单支付通知流程

**触发点**: 微信小程序下单支付
**流程**: 
1. 用户在微信小程序提交订单
2. 系统调用 [`WEChatController.saveOrder()`](../src/main/java/org/haier/shopUpdate/controller/WEChatController.java#L97-L120)
3. 构建MQTT消息并发送到收银机

```java
//发送MQTT消息到收银机
Map<String,Object> mqttMap = new HashMap<String,Object>();
mqttMap.put("ctrl", "msg_shop_order");
mqttMap.put("ID", macId);
mqttMap.put("status", 200);
mqttMap.put("msg", "支付订单信息");
mqttMap.put("data", map);

//将消息发送给前端
MqttxUtil.sendMapMsg(mqttMap, macId);
```

#### 1.3.2 商品信息同步流程

**触发点**: 商品信息变更（新增、修改、库存变动）
**涉及业务**:
- 商品新增/修改: [`GoodsServiceImpl`](../src/main/java/org/haier/shopUpdate/service/GoodsServiceImpl.java#L1937-L1943)
- 库存变动: [`StockServiceImpl`](../src/main/java/org/haier/shopUpdate/service/StockServiceImpl.java#L675-L681)
- 盘点提交: [`InventoryTaskServiceImpl`](../src/main/java/org/haier/shopUpdate/service/InventoryTaskServiceImpl.java#L664-L668)

**流程**:
1. 业务操作完成后触发MQTT消息发送
2. 使用 [`SendMqttMsg`](../src/main/java/org/haier/shopUpdate/util/thread/SendMqttMsg.java) 线程异步发送
3. 通过HTTP接口通知MQTT服务进行消息推送

```java
@Override
public void run() {
    super.run();
    Map<String,Object> mqttMap = new HashMap<>();
    mqttMap.put("shopUnique", shopUnique);
    mqttMap.put("goodsBarcode", goodsBarcode);
    mqttMap.put("barcodes", barcodes);
    String res = HttpsUtil.doPost(HelibaoPayConfig.MQTTURL_FOR_UPDATEGOODS, mqttMap);
}
```

#### 1.3.3 商品分类更新通知

**触发点**: 商品分类信息变更
**实现**: [`GoodsKindsServiceImpl`](../src/main/java/org/haier/shopUpdate/service/GoodsKindsServiceImpl.java#L414-L439)

```java
//20220909 新增MQTT -通知收银机有商品分类更新
Map<String, Object> data = new HashMap<String, Object>();
data.put("ctrl", ctrl);//商品分类更新
data.put("ID", macid);
data.put("status", 200);
data.put("data", mqttData);
data.put("count", 1);
MqttxUtil.sendMapMsg(data, macid);
```

#### 1.3.4 订单状态变更通知

**触发点**: 订单配送状态变更
**实现**: [`DeliveryServiceImpl.sendMsgToPc()`](../src/main/java/org/haier/shopUpdate/service/DeliveryServiceImpl.java#L163-L174)

```java
//向收银设备推送消息
Map<String,Object> mqttMap = new HashMap<String,Object>();
mqttMap.put("ctrl", "msg_order_change");
mqttMap.put("shopUnique", shopUnique);
Map<String,Object> detailMap = new HashMap<String,Object>();
detailMap.put("saleListUnique", saleListUnique);
detailMap.put("saleListHandlestate", saleListHandlestate);
```

### 1.4 MQTT消息格式

#### 1.4.1 标准消息结构
```json
{
  "ctrl": "消息控制类型",
  "ID": "设备MAC地址",
  "status": 200,
  "msg": "消息描述",
  "data": "业务数据",
  "count": 1
}
```

#### 1.4.2 消息类型
- `msg_shop_order`: 订单支付信息
- `msg_goods_update_v2.0`: 商品信息更新
- `msg_order_change`: 订单状态变更
- `msg_goods_kind_update`: 商品分类更新

#### 1.4.3 MQTT连接参数
- **QoS级别**:
  - 发布消息: QoS 1 (至少一次)
  - 订阅消息: QoS 2 (只有一次)
- **会话保持**: CleanSession = false (保留会话状态)
- **心跳间隔**: 20秒
- **连接超时**: 10秒
- **自动重连**: 启用

#### 1.4.4 主题结构
- **主订阅主题**: `win_qt_cash_1.0`
- **发布主题前缀**: `win_qt_cash_`
- **设备特定主题**: `win_qt_cash_{macId}`

### 1.5 Redis缓存集成

MQTT系统与Redis缓存深度集成，用于设备管理和消息路由：

#### 1.5.1 设备MAC地址缓存
- **缓存键格式**: `topic_{shopUnique}`
- **缓存内容**: 店铺关联的收银机MAC地址列表
- **用途**: 确定消息推送目标设备

#### 1.5.2 商品缓存清理
- **缓存键格式**: `pcGoods{shopUnique}{status}{type}{filter}`
- **触发时机**: 商品信息更新后自动清理相关缓存
- **目的**: 保证数据一致性

### 1.6 前端WebSocket集成

**前端MQTT客户端**: [`mqttUtil.js`](../src/main/webapp/static/js/mqttUtil.js)
**WebSocket库**: [`mqttws31.min.js`](../src/main/webapp/static/js/mqttws31.min.js)

#### 1.6.1 连接配置
```javascript
var hostname = '**************',
    port = 8083,
    clientId = 'win_qt_cash_page_payForShop',
    timeout = 5,
    keepAlive = 100,
    cleanSession = false,
    topic = 'win_qt_cash_' + mqttId;
```

#### 1.6.2 消息处理
```javascript
function onMessageArrived(message) {
    var msg = JSON.parse(message.payloadString)
    var ctr = msg.ctr;
    if(ctr == "msg_shop_order"){
        //将数据存储到本地
        $("#orderNo").val(msg.data.orderNo);
        $("#payMoney").val(msg.data.saleListTotal);
        goodsList = msg.data.goodsList;
        goodsList = JSON.parse(goodsList);
        table.reload('testReload', {
            data : goodsList
        });
    }
}
```

## 2. RabbitMQ消息系统

### 2.1 架构概述

RabbitMQ主要用于退款订单的异步同步处理，确保退款数据在不同系统间的一致性。

### 2.2 核心组件

#### 2.2.1 配置组件
- **配置类**: [`RabbitMqConfig`](../src/main/java/org/haier/rabbitmq/config/RabbitMqConfig.java)
- **Spring配置**: [`spring-rabbitmq.xml`](../src/main/resources/conf/spring-rabbitmq.xml)
- **服务实现**: [`ReturnListSyncServiceImpl`](../src/main/java/org/haier/rabbitmq/service/impl/ReturnListSyncServiceImpl.java)

#### 2.2.2 配置参数
```properties
# RabbitMQ连接配置
rabbitmq.host=*************
rabbitmq.username=test
rabbitmq.password=test@Rabbitmq
rabbitmq.port=5672
rabbitmq.virtual-host=/test

# 队列和交换机配置
rabbitmq.queue.returnListQueue=return_list_sync_queue
rabbitmq.exchange.returnListExchange=return_list_sync_exchange
```

### 2.3 业务流程

#### 2.3.1 退款订单同步流程

**触发点**: 退款订单创建或状态变更
**服务接口**: [`ReturnListSyncService`](../src/main/java/org/haier/rabbitmq/service/ReturnListSyncService.java)

**流程步骤**:
1. 系统调用退款订单同步服务
2. 查询退款订单详细信息
3. 构建同步消息体
4. 发送消息到RabbitMQ交换机
5. 消费者处理退款同步逻辑

```java
@Override
public NoteResult syncRetSaleList(String saleListUnique, String retListUnique) {
    // 延迟3秒发送，确保数据一致性
    Thread.sleep(3000);
    
    // 构建同步参数
    ReturnListSyncParams syncParams = new ReturnListSyncParams();
    // ... 设置参数
    
    String body = JSONUtil.toJsonStr(syncParams);
    rabbitReturnListTemplate.convertAndSend(rabbitMqConfig.getReturnListSyncExchange(), "", body);
}
```

#### 2.3.2 消息队列配置

**交换机类型**: Fanout Exchange
**队列特性**: 持久化、非排他、非自动删除
**消息路由**: 广播模式，所有绑定队列都会收到消息

```xml
<rabbit:queue id="returnListQueue" name="${rabbitmq.queue.returnListQueue}" 
              durable="true" auto-declare="true" auto-delete="false" exclusive="false"/>
<rabbit:fanout-exchange name="${rabbitmq.exchange.returnListExchange}" 
                        durable="true" auto-declare="true" auto-delete="false">
    <rabbit:bindings>
        <rabbit:binding queue="returnListQueue"></rabbit:binding>
    </rabbit:bindings>
</rabbit:fanout-exchange>
```

## 3. Socket服务系统

### 3.1 架构概述

自定义Socket服务用于特定的TCP通信需求，提供8888端口的服务监听。

### 3.2 核心组件

#### 3.2.1 服务组件
- **服务器**: [`BuyHooSocketService`](../src/main/java/org/haier/shopUpdate/util/BuyHooSocketService.java)
- **处理线程**: [`BuyhooSocketServiceThread`](../src/main/java/org/haier/shopUpdate/util/BuyhooSocketServiceThread.java)

### 3.3 业务流程

#### 3.3.1 Socket服务启动流程

```java
public static boolean createBuyHooServiceSocket(){
    try {
        ServerSocket service=new ServerSocket(8888);
        System.out.println("服务器已创建完成，等待用户进行连接！");
        boolean flag=true;
        while(flag){
            Socket client=service.accept();
            new Thread(new BuyhooSocketServiceThread(client)).start();
        }
    } catch (IOException e) {
        log.error("端口号已被占用",e);
        return false;
    }
}
```

#### 3.3.2 客户端连接处理

每个客户端连接都会创建独立的处理线程，实现并发处理能力。

## 4. 消息推送系统

### 4.1 微信消息推送

**实现类**: [`SendMsgAfterLottery`](../src/main/java/org/haier/shopUpdate/wechat/SendMsgAfterLottery.java)
**用途**: 抽奖结果通知、营销消息推送
**特点**: 异步线程处理，支持模板消息推送

### 4.2 短信推送

**实现类**: [`SendMessage`](../src/main/java/org/haier/shopUpdate/util/SendMessage.java)
**用途**: 验证码发送、通知短信
**特点**: 支持批量发送，失败重试机制

### 4.3 极光推送

**工具类**: [`JPushClientUtil`](../src/main/java/org/haier/shopUpdate/util/JPushClientUtil.java)
**用途**: APP消息推送、设备通知
**特点**: 支持标签推送、别名推送

## 5. 数据序列化与格式

### 5.1 JSON序列化
- **MQTT消息**: 使用 `JSONObject.fromObject()` 进行序列化
- **RabbitMQ消息**: 使用 `JSONUtil.toJsonStr()` 进行序列化
- **字符编码**: 统一使用UTF-8编码

### 5.2 消息压缩
- 大消息自动压缩传输
- 支持gzip压缩算法
- 自动解压缩处理

### 5.3 数据校验
- 消息格式校验
- 必填字段检查
- 数据类型验证

## 6. 技术特点与优势

### 6.1 异步处理
- 使用线程池和异步消息队列，避免阻塞主业务流程
- 提高系统响应速度和并发处理能力

### 6.2 可靠性保障
- MQTT支持QoS级别控制，确保消息传递可靠性
- RabbitMQ提供持久化和确认机制
- 异常处理和重连机制

### 6.3 扩展性
- 支持多环境配置（开发、测试、生产）
- 模块化设计，便于功能扩展
- 支持集群部署

### 6.4 监控与日志
- 完整的日志记录和异常监控
- 钉钉机器人集成，实时告警通知
- MQTT连接状态实时监控
- 消息发送失败自动重试机制
- 异常情况邮件/短信告警

### 6.5 容错机制
- **MQTT断线重连**: 自动检测连接状态并重连
- **消息重试**: 发送失败时自动重试
- **降级处理**: 关键业务支持降级方案
- **熔断保护**: 防止级联故障

## 7. 部署与运维

### 7.1 环境配置
- 开发环境: [`application_dev.properties`](../src/main/resources/application_dev.properties)
- 测试环境: [`application_test.properties`](../src/main/resources/application_test.properties)
- 生产环境: [`application_prod.properties`](../src/main/resources/application_prod.properties)

### 7.2 监控指标
- MQTT连接状态
- RabbitMQ队列积压情况
- Socket服务可用性
- 消息发送成功率

### 7.3 故障处理
- 自动重连机制
- 消息重试策略
- 降级处理方案

## 8. 最佳实践

### 8.1 消息设计
- 消息体结构标准化
- 版本兼容性考虑
- 幂等性设计

### 8.2 性能优化
- 批量消息处理
- 连接池复用
- 异步非阻塞处理

### 8.3 安全考虑
- 认证授权机制
- 消息加密传输
- 访问控制策略

## 9. 业务流程图

### 9.1 MQTT消息流程

```mermaid
graph TD
    A[业务操作触发] --> B{操作类型}
    B -->|订单支付| C[WEChatController.saveOrder]
    B -->|商品变更| D[GoodsServiceImpl]
    B -->|库存变动| E[StockServiceImpl]
    B -->|分类更新| F[GoodsKindsServiceImpl]

    C --> G[构建MQTT消息]
    D --> H[SendMqttMsg线程]
    E --> H
    F --> G

    G --> I[MqttxUtil.sendMapMsg]
    H --> J[HTTP接口调用]

    I --> K[MQTT Broker]
    J --> K

    K --> L[收银机设备]
    K --> M[前端WebSocket]
```

### 9.2 RabbitMQ退款同步流程

```mermaid
graph TD
    A[退款订单创建] --> B[ReturnListSyncService.syncRetSaleList]
    B --> C[延迟3秒处理]
    C --> D[查询退款订单信息]
    D --> E[构建同步参数]
    E --> F[发送到RabbitMQ Exchange]
    F --> G[return_list_sync_queue]
    G --> H[消费者处理]
    H --> I[同步到目标系统]
```

## 10. 常见问题与解决方案

### 10.1 MQTT连接问题
**问题**: MQTT客户端连接失败
**解决方案**:
1. 检查网络连通性
2. 验证认证信息
3. 确认防火墙设置
4. 查看服务器日志

### 10.2 消息丢失问题
**问题**: 消息发送后未收到
**解决方案**:
1. 检查QoS设置
2. 确认订阅主题正确
3. 验证消息格式
4. 查看错误日志

### 10.3 RabbitMQ队列积压
**问题**: 消息处理速度慢，队列积压
**解决方案**:
1. 增加消费者数量
2. 优化消息处理逻辑
3. 调整预取数量
4. 监控系统资源

### 10.4 Socket连接超时
**问题**: Socket连接建立失败
**解决方案**:
1. 检查端口占用情况
2. 调整超时参数
3. 验证网络配置
4. 重启服务

## 11. 版本更新记录

### v1.0 (2022-08-25)
- 初始MQTT客户端实现
- 基础消息推送功能

### v1.1 (2022-09-01)
- 新增商品同步MQTT通知
- 优化消息格式

### v1.2 (2022-09-09)
- 新增商品分类更新通知
- 完善异常处理机制

### v2.0 (2024-09-07)
- 新增RabbitMQ退款订单同步
- 重构消息处理架构

## 12. 相关文档

- [业务流程文档](./BIZ.md)
- [数据库设计文档](./ER.md)
- [接口文档](./IO.md)
- [系统架构文档](../README.md)
