# API 接口文档

## 概述

本文档描述了商家管理系统的所有REST API接口。系统基于Spring MVC框架构建，提供商品管理、订单处理、会员管理、支付集成等核心功能。

## 基础信息

- **框架**: Spring MVC 4.0.0
- **数据格式**: JSON
- **字符编码**: UTF-8
- **基础路径**: `/`

## 统一返回格式

所有接口统一使用 [`ShopsResult`](../src/main/java/org/haier/shopUpdate/util/ShopsResult.java) 作为返回格式：

```json
{
    "status": 1,           // 状态码：1-成功，0-失败
    "msg": "操作成功",      // 返回消息
    "data": {},           // 返回数据
    "count": 0,           // 数据总数（分页时使用）
    "pageIndex": 1,       // 当前页码
    "pageSize": 20,       // 每页大小
    "pageCount": 5,       // 总页数
    "total": 100          // 总记录数
}
```

部分接口使用 [`PurResult`](../src/main/java/org/haier/shopUpdate/util/PurResult.java) 格式：

```json
{
    "status": 1,          // 状态码：1-成功，0-失败
    "msg": "成功",         // 返回消息
    "data": {},          // 返回数据
    "count": 0,          // 数据总数
    "total": 100         // 总记录数
}
```

## 通用参数

### 分页参数
- `pageIndex`: 页码（从1开始）
- `pageSize`: 每页大小（默认20）
- `page`: 页码（部分接口使用）
- `limit`: 每页大小（部分接口使用）

### 通用业务参数
- `shopUnique`: 店铺唯一标识（必填）
- `staffId`: 员工ID
- `userId`: 用户ID

## 目录

- [1. 商品管理 (Goods Management)](#1-商品管理-goods-management)
  - [1.1 商品基础操作](#11-商品基础操作)
  - [1.2 商品分类管理](#12-商品分类管理)
  - [1.3 货位管理](#13-货位管理)
- [2. 库存管理 (Inventory Management)](#2-库存管理-inventory-management)
  - [2.1 盘点管理](#21-盘点管理)
  - [2.2 补货管理](#22-补货管理)
- [3. 供应商管理 (Supplier Management)](#3-供应商管理-supplier-management)
  - [3.1 供应商基础管理](#31-供应商基础管理)
  - [3.2 购销单管理](#32-购销单管理)
  - [3.3 农批供应商](#33-农批供应商)
- [4. 订单管理 (Order Management)](#4-订单管理-order-management)
  - [4.1 订单创建与处理](#41-订单创建与处理)
  - [4.2 支付管理](#42-支付管理)
- [5. 支付集成 (Payment Integration)](#5-支付集成-payment-integration)
  - [5.1 微信支付](#51-微信支付)
  - [5.2 支付宝支付](#52-支付宝支付)
- [6. 会员管理 (Customer Management)](#6-会员管理-customer-management)
  - [6.1 会员基础操作](#61-会员基础操作)
  - [6.2 会员积分管理](#62-会员积分管理)
- [7. 活动管理 (Activity Management)](#7-活动管理-activity-management)
  - [7.1 促销活动](#71-促销活动)
- [8. 聚合码管理 (Aggregation Code Management)](#8-聚合码管理-aggregation-code-management)
  - [8.1 聚合码审核](#81-聚合码审核)
- [9. 系统管理 (System Management)](#9-系统管理-system-management)
  - [9.1 店铺管理](#91-店铺管理)
  - [9.2 工具接口](#92-工具接口)
- [10. 测试接口 (Test Interfaces)](#10-测试接口-test-interfaces)
- [补充接口](#补充接口)
  - [文件上传接口](#文件上传接口)
  - [商品搜索接口](#商品搜索接口)
  - [统计分析接口](#统计分析接口)
  - [会员消费记录接口](#会员消费记录接口)
- [接口使用示例](#接口使用示例)
- [数据模型说明](#数据模型说明)

## 接口总览

| 功能模块 | 接口数量 | 主要功能 |
|---------|---------|---------|
| 商品管理 | 6+ | 商品增删改查、分类管理、货位管理 |
| 库存管理 | 8+ | 盘点任务、补货计划、库存调整 |
| 供应商管理 | 12+ | 供应商信息、购销单、农批管理 |
| 订单管理 | 4+ | 订单创建、支付处理、配送管理 |
| 支付集成 | 4+ | 微信支付、支付宝、合利宝等 |
| 会员管理 | 8+ | 会员信息、积分管理、消费记录 |
| 活动管理 | 4+ | 促销活动、秒杀、优惠券 |
| 聚合码管理 | 2+ | 聚合码申请、审核状态查询 |
| 系统管理 | 3+ | 店铺配置、工具接口 |
| 统计分析 | 6+ | 销售统计、业务总览、报表 |
| 文件上传 | 2+ | 图片上传、文件管理 |
| 测试接口 | 4+ | 参数校验、功能测试 |

## 接口分类

### 1. 商品管理 (Goods Management)

#### 1.1 商品基础操作

##### 新增商品 (V2.0)
- **接口**: `POST /goods/v2/addGoods.do`
- **控制器**: [`GoodsV2Controller.addGoods()`](../src/main/java/org/haier/shopUpdate/controller/validate/GoodsV2Controller.java#L37)
- **参数**: [`AddGoodsBaseParam`](../src/main/java/org/haier/shopUpdate/params/goods/AddGoodsBaseParam.java)
- **返回**: `ShopsResult`
- **说明**: 新增商品信息，支持商品基础信息、价格、库存等设置

##### 查询商品基础信息
- **接口**: `GET /goods/searchBaseGoods.do`
- **控制器**: [`GoodsController.searchBaseGoods()`](../src/main/java/org/haier/shopUpdate/controller/GoodsController.java#L576)
- **参数**: 
  - `shopUnique`: 店铺编号
  - `goodsBarcode`: 商品条码
  - `type`: 查询类型
- **返回**: `ShopsResult`
- **说明**: 根据条码查询商品基础信息

##### 查询促销商品列表
- **接口**: `POST /goods/queryPromotionActivityList.do`
- **控制器**: [`GoodsController.queryPromotionActivityList()`](../src/main/java/org/haier/shopUpdate/controller/GoodsController.java#L49)
- **参数**: [`QueryPromotionActivityListParams`](../src/main/java/org/haier/shopUpdate/params/goods/QueryPromotionActivityListParams.java)
- **返回**: `ShopsResult`
- **说明**: 查询店铺的促销商品列表

#### 1.2 商品分类管理

##### 查询商品大类
- **接口**: `GET /goodsKinds/queryMoreGoodsBigKinds.do`
- **控制器**: [`GoodsKindsController.queryMoreGoodsBigKinds()`](../src/main/java/org/haier/shopUpdate/controller/GoodsKindsController.java#L119)
- **参数**:
  - `shop_unique`: 店铺编号
  - `kindType`: 分类类型（默认1）
  - `validType`: 有效类型（默认1）
- **返回**: `ShopsResult`
- **说明**: 查询更多商品大类信息

#### 1.3 货位管理

##### 查询货位列表
- **接口**: `POST /shops/goods/position/list.do`
- **控制器**: [`GoodsPositionController.query()`](../src/main/java/org/haier/shopUpdate/controller/GoodsPositionController.java#L41)
- **参数**: [`GoodsPositionEntity`](../src/main/java/org/haier/shopUpdate/entity/GoodsPositionEntity.java)
- **返回**: `ShopsResult`
- **说明**: 查询货位集合

##### 新增货位
- **接口**: `POST /shops/goods/position/add.do`
- **控制器**: [`GoodsPositionController.add()`](../src/main/java/org/haier/shopUpdate/controller/GoodsPositionController.java#L50)
- **参数**: [`GoodsPositionEntity`](../src/main/java/org/haier/shopUpdate/entity/GoodsPositionEntity.java)
- **返回**: `ShopsResult`
- **说明**: 新增货位信息

### 2. 库存管理 (Inventory Management)

#### 2.1 盘点管理

##### 新增盘点任务
- **接口**: `POST /inventoryTask/addTask.do`
- **控制器**: [`InventoryTaskController.addTask()`](../src/main/java/org/haier/shopUpdate/controller/validate/InventoryTaskController.java#L32)
- **参数**: [`AddTaskParams`](../src/main/java/org/haier/shopUpdate/params/inventoryTask/AddTaskParams.java)
- **返回**: `ShopsResult`
- **说明**: 新增盘点任务

##### 盘点任务列表
- **接口**: `POST /inventoryTask/taskList.do`
- **控制器**: [`InventoryTaskController.taskList()`](../src/main/java/org/haier/shopUpdate/controller/validate/InventoryTaskController.java#L43)
- **参数**: [`TaskListParams`](../src/main/java/org/haier/shopUpdate/params/inventoryTask/TaskListParams.java)
- **返回**: `ShopsResult`
- **说明**: 查询盘点任务列表

#### 2.2 补货管理

##### 添加补货计划
- **接口**: `POST /restockPlan/addRestockPlan.do`
- **控制器**: [`ShopsRestockPlanController.addRestockPlan()`](../src/main/java/org/haier/shopUpdate/controller/validate/ShopsRestockPlanController.java#L31)
- **参数**: [`AddRestockPlanParams`](../src/main/java/org/haier/shopUpdate/params/restockPlan/AddRestockPlanParams.java)
- **返回**: `ShopsResult`
- **说明**: 添加补货计划

##### 查询补货计划列表
- **接口**: `POST /restockPlan/queryRestockPlanList.do`
- **控制器**: [`ShopsRestockPlanController.queryRestockPlanList()`](../src/main/java/org/haier/shopUpdate/controller/validate/ShopsRestockPlanController.java#L53)
- **参数**: [`RestockPlanListParams`](../src/main/java/org/haier/shopUpdate/params/restockPlan/RestockPlanListParams.java)
- **返回**: `ShopsResult`
- **说明**: 查询补货计划列表信息

##### 补货计划中添加商品
- **接口**: `POST /restockPlan/addRestockPlanGoods.do`
- **控制器**: [`ShopsRestockPlanController.addRestockPlanGoods()`](../src/main/java/org/haier/shopUpdate/controller/validate/ShopsRestockPlanController.java#L75)
- **参数**: [`RestockPlanGoodsAddParams`](../src/main/java/org/haier/shopUpdate/params/restockPlan/RestockPlanGoodsAddParams.java)
- **返回**: `ShopsResult`
- **说明**: 在补货计划中添加商品

### 3. 供应商管理 (Supplier Management)

#### 3.1 供应商基础管理

##### 添加供应商分类
- **接口**: `POST /shopSupplier/addSupKind.do`
- **控制器**: [`ShopSupplierController.addSupKind()`](../src/main/java/org/haier/shopUpdate/controller/validate/ShopSupplierController.java#L33)
- **参数**: [`SupKindAddParams`](../src/main/java/org/haier/shopUpdate/params/shopSupplier/SupKindAddParams.java)
- **返回**: `ShopsResult`
- **说明**: 添加供应商分类信息

##### 添加供应商信息
- **接口**: `POST /shopSupplier/addsupInfo.do`
- **控制器**: [`ShopSupplierController.addSupInfo()`](../src/main/java/org/haier/shopUpdate/controller/validate/ShopSupplierController.java#L80)
- **参数**: [`SupInfoAddParams`](../src/main/java/org/haier/shopUpdate/params/shopSupplier/SupInfoAddParams.java)
- **返回**: `ShopsResult`
- **说明**: 添加供应商信息

##### 查询供应商列表
- **接口**: `GET /supplier/queryShopSuppliers.do`
- **控制器**: [`SupplierController.queryShopSuppliers()`](../src/main/java/org/haier/shopUpdate/controller/SupplierController.java#L29)
- **参数**: `shopUnique`: 店铺编号
- **返回**: `ShopsResult`
- **说明**: 查询商品供货商信息

#### 3.2 购销单管理

##### 添加购销单
- **接口**: `POST /stockSuppGoods/addShopSupBill.do`
- **控制器**: [`StockSuppGoodsController.addShopSupBill()`](../src/main/java/org/haier/shopUpdate/controller/validate/StockSuppGoodsController.java#L35)
- **参数**: [`BillInfoParams`](../src/main/java/org/haier/shopUpdate/params/shopSupBill/BillInfoParams.java)
- **返回**: `ShopsResult`
- **说明**: 添加购销单

##### 添加单个店铺商品
- **接口**: `POST /stockSuppGoods/addGoods.do`
- **控制器**: [`StockSuppGoodsController.addGoods()`](../src/main/java/org/haier/shopUpdate/controller/validate/StockSuppGoodsController.java#L47)
- **参数**: [`GoodsInfoParams`](../src/main/java/org/haier/shopUpdate/params/shopSupBill/GoodsInfoParams.java)
- **返回**: `ShopsResult`
- **说明**: 添加单个店铺商品

#### 3.3 农批供应商

##### 新增农批供货商
- **接口**: `POST /agriculturalSupplier/addSupplier.do`
- **控制器**: [`AgriculturalSupplierController.addSupplier()`](../src/main/java/org/haier/shopUpdate/controller/validate/AgriculturalSupplierController.java#L27)
- **参数**: [`AddSupplierParams`](../src/main/java/org/haier/shopUpdate/params/agriculturalSupplier/AddSupplierParams.java)
- **返回**: `ShopsResult`
- **说明**: 新增供货商、货主

### 4. 订单管理 (Order Management)

#### 4.1 订单创建与处理

##### 配送订单创建
- **接口**: `POST /cash/createOrder.do`
- **控制器**: [`CashController.doCreateOrder()`](../src/main/java/org/haier/shopUpdate/controller/CashController.java#L1109)
- **参数**: [`CreateOrderParams`](../src/main/java/org/haier/shopUpdate/params/CreateOrderParams.java)
- **返回**: `PurResult`
- **说明**: 创建配送订单，支持自配送、美团配送、一刻钟配送

##### 保存订单
- **接口**: `POST /wechat/saveOrder.do`
- **控制器**: [`WEChatController.saveOrder()`](../src/main/java/org/haier/shopUpdate/controller/WEChatController.java#L97)
- **参数**:
  - `shopUnique`: 店铺编号
  - `saleListUnique`: 订单编号
  - `goodsMsg`: 商品信息
  - `saleListTotal`: 订单总额
  - `staffId`: 员工ID
  - `macId`: 设备ID
- **返回**: `ShopsResult`
- **说明**: 保存订单并发送MQTT消息到收银机

#### 4.2 支付管理

##### APP支付收银
- **接口**: `POST /appPay/shop_pay.do`
- **控制器**: [`AppPayController.shop_pay()`](../src/main/java/org/haier/shopUpdate/controller/AppPayController.java#L175)
- **参数**:
  - `shop_unique`: 店铺编号
  - `sale_list_unique`: 订单编号
  - `sale_list_payment`: 支付方式
  - `sale_list_discount`: 折扣金额
  - `sale_list_total`: 订单总额
  - `sale_list_actually_received`: 实收金额
  - `sale_list_remarks`: 备注
  - `spbill_create_ip`: 客户端IP
  - `auth_code`: 授权码
- **返回**: `ShopsResult`
- **说明**: 支付收银处理

### 5. 支付集成 (Payment Integration)

#### 5.1 微信支付

##### 生成微信预支付订单
- **接口**: `POST /wechatPay/generateWXOrder.do`
- **控制器**: [`WechatPayController.generateWXOrder()`](../src/main/java/org/haier/shopUpdate/controller/WechatPayController.java#L56)
- **参数**:
  - `total_fee`: 支付金额（分）
  - `out_trade_no`: 商户订单号
- **返回**: `ShopsResult`
- **说明**: 生成预支付交易单-微信

#### 5.2 支付宝支付

##### APP支付接口
- **接口**: `POST /aliPay/aliAppPay.do`
- **控制器**: [`AliPayController.aliAppPay()`](../src/main/java/org/haier/shopUpdate/controller/AliPayController.java#L221)
- **参数**:
  - `total_amount`: 支付金额
  - `out_trade_no`: 商户订单号
- **返回**: `String`
- **说明**: 支付宝APP支付接口

### 6. 会员管理 (Customer Management)

#### 6.1 会员基础操作

##### 添加会员信息
- **接口**: `POST /cuscheckout/addCusNP.do`
- **控制器**: [`CusCheckoutContorller.addCusNP()`](../src/main/java/org/haier/shopUpdate/controller/CusCheckoutContorller.java#L47)
- **参数**:
  - `cusUnique`: 会员编号
  - `cusName`: 会员姓名
  - `cusPhone`: 会员电话
  - `shopUnique`: 店铺编号
- **返回**: `PalmResult`
- **说明**: 添加或更新会员信息

##### 查询会员列表
- **接口**: `GET /cuscheckout/getCustList.do`
- **控制器**: [`CusCheckoutContorller.getCustList()`](../src/main/java/org/haier/shopUpdate/controller/CusCheckoutContorller.java#L117)
- **参数**:
  - `searchKey`: 搜索关键字
  - `shopUnique`: 店铺编号
  - `pages`: 页码
  - `perpage`: 每页数量
- **返回**: `PalmResult`
- **说明**: 查询会员列表

##### 修改会员信息
- **接口**: `POST /cuscheckout/editCus.do`
- **控制器**: [`CusCheckoutContorller.editCus()`](../src/main/java/org/haier/shopUpdate/controller/CusCheckoutContorller.java#L178)
- **参数**: 包含会员详细信息的多个参数
- **返回**: `PalmResult`
- **说明**: 修改会员信息

##### 查询会员详情
- **接口**: `GET /cuscheckout/findCusById.do`
- **控制器**: [`CusCheckoutContorller.findCusById()`](../src/main/java/org/haier/shopUpdate/controller/CusCheckoutContorller.java#L127)
- **参数**:
  - `cus_unique`: 会员编号
  - `shopUnique`: 店铺编号
  - `searchType`: 搜索类型（默认2）
- **返回**: `PalmResult`
- **说明**: 查询会员详情

##### 添加新会员信息（微信端）
- **接口**: `POST /wechat/addNewCusMsg.do`
- **控制器**: [`WEChatController.addNewCusMsg()`](../src/main/java/org/haier/shopUpdate/controller/WEChatController.java#L799)
- **参数**:
  - `cus_phone`: 会员电话
  - `cus_name`: 会员姓名
  - `cus_sex`: 会员性别
  - `cus_birthday`: 会员生日
  - `cus_weixin`: 微信号
- **返回**: `ShopsResult`
- **说明**: 添加会员信息（微信端）

#### 6.2 会员积分管理

##### 查询百货豆列表
- **接口**: `GET /bean/queryBeanList.do`
- **控制器**: [`BeanController.queryBeanList()`](../src/main/java/org/haier/shopUpdate/controller/BeanController.java#L43)
- **参数**:
  - `shopUnique`: 店铺编号
  - `page`: 页码（默认1）
  - `pageSize`: 每页大小（默认20）
  - `beanType`: 豆类型（默认1）
- **返回**: `BeanResult`
- **说明**: 百货豆首页（查询豆的数量、支出情况）

##### 查询店铺豆促销活动
- **接口**: `GET /cash/queryShopBeansPromation.do`
- **控制器**: [`CashController.queryShopBeansPromation()`](../src/main/java/org/haier/shopUpdate/controller/CashController.java#L289)
- **参数**:
  - `page`: 页码（默认1）
  - `limit`: 每页大小（默认8）
- **返回**: `ShopsResult`
- **说明**: 查询店铺豆促销活动

##### 查询PC活动菜单列表
- **接口**: `GET /cash/queryPcActivityMenuList.do`
- **控制器**: [`CashController.queryPcActivityMenuList()`](../src/main/java/org/haier/shopUpdate/controller/CashController.java#L299)
- **返回**: `ShopsResult`
- **说明**: 查询PC活动菜单列表

### 7. 活动管理 (Activity Management)

#### 7.1 促销活动

##### 查询促销列表
- **接口**: `POST /activity/queryPromotionList.do`
- **控制器**: [`ActivityController.queryPromotionList()`](../src/main/java/org/haier/shopUpdate/controller/activity/ActivityController.java#L25)
- **参数**: [`QueryActivityListParams`](../src/main/java/org/haier/shopUpdate/params/QueryActivityListParams.java)
- **返回**: `ShopsResult`
- **说明**: 查询促销列表

##### 删除促销活动
- **接口**: `POST /activity/deleteActivity.do`
- **控制器**: [`ActivityController.deleteActivity()`](../src/main/java/org/haier/shopUpdate/controller/activity/ActivityController.java#L38)
- **参数**: [`ActivityInfoParams`](../src/main/java/org/haier/shopUpdate/params/ActivityInfoParams.java)
- **返回**: `ShopsResult`
- **说明**: 删除促销活动

##### 添加秒杀促销
- **接口**: `POST /activity/submitFlashSale.do`
- **控制器**: [`ActivityController.submitFlashSale()`](../src/main/java/org/haier/shopUpdate/controller/activity/ActivityController.java#L171)
- **参数**: [`AddActivityParams`](../src/main/java/org/haier/shopUpdate/params/AddActivityParams.java)
- **返回**: `ShopsResult`
- **说明**: 添加秒杀促销

##### 单品促销查看详情
- **接口**: `POST /activity/querySingleGoodsPromotionDetail.do`
- **控制器**: [`ActivityController.querySingleGoodsPromotionDetail()`](../src/main/java/org/haier/shopUpdate/controller/activity/ActivityController.java#L158)
- **参数**: [`ActivityInfoParams`](../src/main/java/org/haier/shopUpdate/params/ActivityInfoParams.java)
- **返回**: `ShopsResult`
- **说明**: 单品促销查看详情

### 8. 聚合码管理 (Aggregation Code Management)

#### 8.1 聚合码审核

##### 查询聚合码审核状态
- **接口**: `POST /shopQualificationInfo/aggregationCode/getAuditStatus.do`
- **控制器**: [`AggregationCodeController.getAuditStatus()`](../src/main/java/org/haier/shopUpdate/controller/AggregationCodeController.java#L26)
- **参数**: [`GetAuditStatusParams`](../src/main/java/org/haier/shopUpdate/params/GetAuditStatusParams.java)
- **返回**: `ShopsResult`
- **说明**: 查询聚合码审核状态

##### 查询资质信息
- **接口**: `POST /shopQualificationInfo/aggregationCode/getQualifications.do`
- **控制器**: [`AggregationCodeController.getQualifications()`](../src/main/java/org/haier/shopUpdate/controller/AggregationCodeController.java#L37)
- **参数**: [`GetQualificationsParams`](../src/main/java/org/haier/shopUpdate/params/GetQualificationsParams.java)
- **返回**: `ShopsResult`
- **说明**: 查询资质信息

### 9. 系统管理 (System Management)

#### 9.1 店铺管理

##### 查询主界面模块信息
- **接口**: `GET /shopTitle/queryMainPageTitle.do`
- **控制器**: [`ShopTitleController.queryMainPageTitle()`](../src/main/java/org/haier/shopUpdate/controller/ShopTitleController.java#L35)
- **参数**: [`ShopTitle`](../src/main/java/org/haier/shopUpdate/entity/ShopTitle.java)
- **返回**: `ShopsResult`
- **说明**: 查询商家端APP主界面的模块信息

#### 9.2 工具接口

##### 删除重复商品
- **接口**: `POST /deleteSameGoods.do`
- **控制器**: [`UTILController.deleteSameGoods()`](../src/main/java/org/haier/shopUpdate/controller/UTILController.java#L34)
- **参数**: `shopUnique`: 店铺编号
- **返回**: `ShopsResult`
- **说明**: 将店铺中重复的商品删除并将云库中没有的商品添加到云库中

### 10. 测试接口 (Test Interfaces)

##### 参数格式校验测试
- **接口**: `POST /test/testParam.do`
- **控制器**: [`TestController.testParam()`](../src/main/java/org/haier/shopUpdate/controller/TestController.java#L37)
- **参数**: [`ParamTest`](../src/main/java/org/haier/shopUpdate/util/ParamTest.java)
- **返回**: `ShopsResult`
- **说明**: 参数格式校验测试

##### 测试接口
- **接口**: `GET /test/test.do`
- **控制器**: [`TestController.test()`](../src/main/java/org/haier/shopUpdate/controller/TestController.java#L52)
- **返回**: `ShopsResult`
- **说明**: 通用测试接口

##### 添加商品测试
- **接口**: `GET /test/addGoods.do`
- **控制器**: [`TestController.addGoods()`](../src/main/java/org/haier/shopUpdate/controller/TestController.java#L111)
- **参数**:
  - `pageNum`: 页码
  - `pageSize`: 每页大小（默认20）
- **返回**: `ShopsResult`
- **说明**: 添加商品测试接口

##### 查询销售单号
- **接口**: `GET /test/querySaleListUnique.do`
- **控制器**: [`TestController.querySaleListUnique()`](../src/main/java/org/haier/shopUpdate/controller/TestController.java#L125)
- **返回**: `ShopsResult`
- **说明**: 查询销售单号测试接口

## 详细接口列表

### 补货计划管理接口

##### 删除补货计划
- **接口**: `POST /restockPlan/deleteRestockPlan.do`
- **控制器**: [`ShopsRestockPlanController.deleteRestockPlan()`](../src/main/java/org/haier/shopUpdate/controller/validate/ShopsRestockPlanController.java#L42)
- **参数**: [`DeleteRestockPlanParams`](../src/main/java/org/haier/shopUpdate/params/restockPlan/DeleteRestockPlanParams.java)
- **返回**: `ShopsResult`
- **说明**: 删除补货计划

##### 根据补货计划ID查询商品详细信息
- **接口**: `POST /restockPlan/queryGoodsListByPlanId.do`
- **控制器**: [`ShopsRestockPlanController.queryGoodsListByPlanId()`](../src/main/java/org/haier/shopUpdate/controller/validate/ShopsRestockPlanController.java#L64)
- **参数**: [`RestockPlanGoodsParams`](../src/main/java/org/haier/shopUpdate/params/restockPlan/RestockPlanGoodsParams.java)
- **返回**: `ShopsResult`
- **说明**: 根据补货计划ID查询商品详细信息

##### 查询店铺绑定的供货商信息
- **接口**: `POST /restockPlan/getGoodsSupplierMsg.do`
- **控制器**: [`ShopsRestockPlanController.getGoodsSupplierMsg()`](../src/main/java/org/haier/shopUpdate/controller/validate/ShopsRestockPlanController.java#L119)
- **参数**: [`GetGoodsSupplierMsgParams`](../src/main/java/org/haier/shopUpdate/params/restockPlan/GetGoodsSupplierMsgParams.java)
- **返回**: `ShopsResult`
- **说明**: 查询店铺绑定的供货商信息

##### 修改或删除补货计划下商品信息
- **接口**: `POST /restockPlan/modifyRestockPlanGoods.do`
- **控制器**: [`ShopsRestockPlanController.modifyRestockPlanGoods()`](../src/main/java/org/haier/shopUpdate/controller/validate/ShopsRestockPlanController.java#L130)
- **参数**: [`ModifyGoodsParams`](../src/main/java/org/haier/shopUpdate/params/restockPlan/ModifyGoodsParams.java)
- **返回**: `ShopsResult`
- **说明**: 修改或删除补货计划下商品信息

##### 根据供货商ID获取商品分组信息
- **接口**: `POST /restockPlan/getGoodsListBySupplierId.do`
- **控制器**: [`ShopsRestockPlanController.queryGoodsListBySupplierId()`](../src/main/java/org/haier/shopUpdate/controller/validate/ShopsRestockPlanController.java#L141)
- **参数**: [`QueryGoodsBySupplierParams`](../src/main/java/org/haier/shopUpdate/params/restockPlan/QueryGoodsBySupplierParams.java)
- **返回**: `ShopsResult`
- **说明**: 根据供货商ID获取商品分组信息

##### 查询商品详细信息
- **接口**: `POST /restockPlan/queryGoodsDetail.do`
- **控制器**: [`ShopsRestockPlanController.queryGoodsDetail()`](../src/main/java/org/haier/shopUpdate/controller/validate/ShopsRestockPlanController.java#L152)
- **参数**: [`QueryGoodsDetailParams`](../src/main/java/org/haier/shopUpdate/params/restockPlan/QueryGoodsDetailParams.java)
- **返回**: `ShopsResult`
- **说明**: 查询商品详细信息

##### 修改补货计划下供货商备注信息
- **接口**: `POST /restockPlan/updateRestockPlanSupplier.do`
- **控制器**: [`ShopsRestockPlanController.updateRestockPlanSupplier()`](../src/main/java/org/haier/shopUpdate/controller/validate/ShopsRestockPlanController.java#L163)
- **参数**: [`RestockPlanSupplierUpdateParams`](../src/main/java/org/haier/shopUpdate/params/restockPlan/RestockPlanSupplierUpdateParams.java)
- **返回**: `ShopsResult`
- **说明**: 修改补货计划下供货商备注信息

##### 再次补货
- **接口**: `POST /restockPlan/restockAgain.do`
- **控制器**: [`ShopsRestockPlanController.restockAgain()`](../src/main/java/org/haier/shopUpdate/controller/validate/ShopsRestockPlanController.java#L174)
- **参数**: [`DeleteRestockPlanParams`](../src/main/java/org/haier/shopUpdate/params/restockPlan/DeleteRestockPlanParams.java)
- **返回**: `ShopsResult`
- **说明**: 再次补货

### 供应商管理扩展接口

##### 修改或删除供应商分类信息
- **接口**: `POST /shopSupplier/modifySupKind.do`
- **控制器**: [`ShopSupplierController.modifySupKind()`](../src/main/java/org/haier/shopUpdate/controller/validate/ShopSupplierController.java#L45)
- **参数**: [`SupKindModifyParams`](../src/main/java/org/haier/shopUpdate/params/shopSupplier/SupKindModifyParams.java)
- **返回**: `ShopsResult`
- **说明**: 修改或删除供应商分类信息

##### 修改供应商信息
- **接口**: `POST /shopSupplier/updateSupInfo.do`
- **控制器**: [`ShopSupplierController.updateSupInfo()`](../src/main/java/org/haier/shopUpdate/controller/validate/ShopSupplierController.java#L93)
- **参数**: [`SupInfoUpdateParams`](../src/main/java/org/haier/shopUpdate/params/shopSupplier/SupInfoUpdateParams.java)
- **返回**: `ShopsResult`
- **说明**: 修改供应商信息

##### 查询供应商给店铺所供商品列表
- **接口**: `POST /shopSupplier/querySupRecordGoodList.do`
- **控制器**: [`ShopSupplierController.querySupRecordGoodList()`](../src/main/java/org/haier/shopUpdate/controller/validate/ShopSupplierController.java#L179)
- **参数**: [`QueryRecordGoodsListParams`](../src/main/java/org/haier/shopUpdate/params/shopSupplier/QueryRecordGoodsListParams.java)
- **返回**: `ShopsResult`
- **说明**: 查询供应商给店铺所供商品列表（包括未建档和已建档）

##### 查询店铺与供应商还款信息
- **接口**: `POST /shopSupplier/queryQepaymentInfo.do`
- **控制器**: [`ShopSupplierController.queryQepaymentInfo()`](../src/main/java/org/haier/shopUpdate/controller/validate/ShopSupplierController.java#L191)
- **参数**: [`QueryRepayHisInfoParams`](../src/main/java/org/haier/shopUpdate/params/shopSupplier/QueryRepayHisInfoParams.java)
- **返回**: `ShopsResult`
- **说明**: 查询店铺与供应商还款信息

### 农批供应商扩展接口

##### 删除农批供货商
- **接口**: `POST /agriculturalSupplier/deleteSupplier.do`
- **参数**: [`DeleteSupplierParams`](../src/main/java/org/haier/shopUpdate/params/agriculturalSupplier/DeleteSupplierParams.java)
- **返回**: `ShopsResult`
- **说明**: 删除供货商、货主

##### 修改农批供货商
- **接口**: `POST /agriculturalSupplier/updateSupplier.do`
- **参数**: [`UpdateSupplierParams`](../src/main/java/org/haier/shopUpdate/params/agriculturalSupplier/UpdateSupplierParams.java)
- **返回**: `ShopsResult`
- **说明**: 修改供货商、货主信息

##### 查询农批供货商列表
- **接口**: `POST /agriculturalSupplier/querySupplierList.do`
- **参数**: [`QuerySupplierListParams`](../src/main/java/org/haier/shopUpdate/params/agriculturalSupplier/QuerySupplierListParams.java)
- **返回**: `ShopsResult`
- **说明**: 查询供货商、货主列表

##### 查询农批供货商详情
- **接口**: `POST /agriculturalSupplier/querySupplierDetail.do`
- **参数**: [`QuerySupplierDetailParams`](../src/main/java/org/haier/shopUpdate/params/agriculturalSupplier/QuerySupplierDetailParams.java)
- **返回**: `ShopsResult`
- **说明**: 查询供货商、货主详情

## 补充接口

### 文件上传接口

##### 通用文件上传
- **接口**: `POST /loanMoney/uploadFile.do`
- **控制器**: [`LoanMoneyController.uploadFile()`](../src/main/java/org/haier/shopUpdate/controller/LoanMoneyController.java#L77)
- **参数**:
  - `file`: 上传文件（必填）
  - `yasuo`: 是否压缩（1-不压缩，2-压缩）
  - `file_width`: 压缩后宽度（默认400）
  - `file_height`: 压缩后高度（默认400）
- **返回**: `ShopsResult`
- **说明**: 通用文件上传接口，支持图片压缩

##### 收银机文件上传
- **接口**: `POST /cash/uploadFile.do`
- **控制器**: [`CashController.uploadFile()`](../src/main/java/org/haier/shopUpdate/controller/CashController.java#L45)
- **参数**:
  - `shop_unique`: 店铺编号
  - `file`: 上传文件
- **返回**: `ShopsResult`
- **说明**: 收银机专用文件上传接口

### 商品搜索接口

##### 搜索商品（APP端）
- **接口**: `POST /appPay/searchGoods.do`
- **控制器**: [`AppPayController.searchGoods()`](../src/main/java/org/haier/shopUpdate/controller/AppPayController.java#L70)
- **参数**:
  - `shop_unique`: 店铺编号
  - `goods_message`: 商品信息（名称或条码）
- **返回**: `ShopsResult`
- **说明**: APP端商品搜索功能

##### 添加商品到购物车
- **接口**: `POST /appPay/addGoodsToCar.do`
- **控制器**: [`AppPayController.addGoodsToCar()`](../src/main/java/org/haier/shopUpdate/controller/AppPayController.java#L51)
- **参数**:
  - `shop_unique`: 店铺编号
  - `sale_list_unique`: 订单编号
  - `goods_barcode`: 商品条码
  - `sale_list_detail_count`: 商品数量
  - `sale_list_detail_price`: 商品价格
  - `goods_type`: 商品类型
  - `sale_list_cashier`: 收银员ID
  - `table_id`: 桌台ID
- **返回**: `ShopsResult`
- **说明**: 扫码添加商品到购物车

##### 查询商品图片
- **接口**: `GET /goods/searchGoodsImg.do`
- **控制器**: [`GoodsController.searchGoodsImg()`](../src/main/java/org/haier/shopUpdate/controller/GoodsController.java#L91)
- **参数**:
  - `goodsMsg`: 商品信息
  - `shopUnique`: 店铺编号
- **返回**: `ShopsResult`
- **说明**: 根据条码查询对应店铺的商品图片信息

### 统计分析接口

##### 查询支付方式列表
- **接口**: `GET /statistics/getPayMethodList.do`
- **控制器**: [`StatisticsController.getPayMethodList()`](../src/main/java/org/haier/shopUpdate/controller/StatisticsController.java#L46)
- **返回**: `ShopsResult`
- **说明**: 获取所有支付方式列表

##### 查询订单支付详情
- **接口**: `POST /statistics/querySaleListPayMethodBySaleListUnique.do`
- **控制器**: [`StatisticsController.querySaleListPayMethodBySaleListUnique()`](../src/main/java/org/haier/shopUpdate/controller/StatisticsController.java#L40)
- **参数**: [`QuerySaleListPayMethodBySaleListUniqueParams`](../src/main/java/org/haier/shopUpdate/params/QuerySaleListPayMethodBySaleListUniqueParams.java)
- **返回**: `ShopsResult`
- **说明**: 查询订单或退款订单的支付详情

##### 按支付方式查询订单列表
- **接口**: `POST /statistics/querySaleListByPaymethod.do`
- **控制器**: [`StatisticsController.querySaleListByPaymethod()`](../src/main/java/org/haier/shopUpdate/controller/StatisticsController.java#L58)
- **参数**: [`QuerySaleListByPayMethodParams`](../src/main/java/org/haier/shopUpdate/params/QuerySaleListByPayMethodParams.java)
- **返回**: `ShopsResult`
- **说明**: 查询各支付方式下的订单列表

##### 店铺统计信息
- **接口**: `POST /statistics/queryStatisticsByShop.do`
- **控制器**: [`StatisticsController.queryStatisticsByShop()`](../src/main/java/org/haier/shopUpdate/controller/StatisticsController.java#L68)
- **参数**: [`QueryStatisticsByShopParams`](../src/main/java/org/haier/shopUpdate/params/QueryStatisticsByShopParams.java)
- **返回**: `ShopsResult`
- **说明**: 查询店铺统计信息

##### 主界面信息统计
- **接口**: `GET /statistics/queryStatisticsMessageInMain.do`
- **控制器**: [`StatisticsController.queryStatisticsMessageInMain()`](../src/main/java/org/haier/shopUpdate/controller/StatisticsController.java#L156)
- **参数**:
  - `shopUnique`: 店铺编号
  - `datetime`: 查询日期
- **返回**: `ShopsResult`
- **说明**: 新版主界面信息统计

##### 业务总览
- **接口**: `GET /statistics/businessOverview.do`
- **控制器**: [`StatisticsController.businessOverview()`](../src/main/java/org/haier/shopUpdate/controller/StatisticsController.java#L177)
- **参数**:
  - `shopUnique`: 店铺编号
  - `checkType`: 查询类型（1-日，2-周，3-月，默认1）
- **返回**: `ShopsResult`
- **说明**: 统计界面店铺总览

### 会员消费记录接口

##### 查询会员充值记录
- **接口**: `GET /wechat/searchRechargeRecord.do`
- **控制器**: [`WEChatController.searchRechargeRecord()`](../src/main/java/org/haier/shopUpdate/controller/WEChatController.java#L222)
- **参数**:
  - `cusId`: 会员ID
  - `page`: 页码
  - `rows`: 每页数量
- **返回**: `ShopsResult`
- **说明**: 查询宁宇会员充值记录

##### 查询会员消费记录
- **接口**: `GET /wechat/queryCusConsumptionRecord.do`
- **控制器**: [`WEChatController.queryCusConsumptionRecord()`](../src/main/java/org/haier/shopUpdate/controller/WEChatController.java#L235)
- **参数**:
  - `cusId`: 会员ID
  - `page`: 页码
  - `rows`: 每页数量
- **返回**: `ShopsResult`
- **说明**: 查询宁宇会员消费记录

##### 查询支付方式
- **接口**: `GET /cash/queryPayMethod.do`
- **控制器**: [`CashController.queryPayMethod()`](../src/main/java/org/haier/shopUpdate/controller/CashController.java#L38)
- **返回**: `ShopsResult`
- **说明**: 查询所有支付方式

## 接口使用示例

### 1. 新增商品示例

```bash
curl -X POST "http://localhost:8080/goods/v2/addGoods.do" \
  -H "Content-Type: application/json" \
  -d '{
    "shopUnique": 123456,
    "goodsBarcode": "6901234567890",
    "goodsName": "测试商品",
    "goodsInPrice": 10.00,
    "goodsSalePrice": 15.00,
    "goodsCount": 100,
    "goodsUnit": "个",
    "staffId": 1001
  }'
```

**响应示例**:
```json
{
  "status": 1,
  "msg": "操作成功",
  "data": {
    "goodsId": 789,
    "goodsBarcode": "6901234567890"
  }
}
```

### 2. 查询商品信息示例

```bash
curl -X GET "http://localhost:8080/goods/searchBaseGoods.do?shopUnique=123456&goodsBarcode=6901234567890&type=1"
```

**响应示例**:
```json
{
  "status": 1,
  "msg": "操作成功",
  "data": {
    "goodsBarcode": "6901234567890",
    "goodsName": "测试商品",
    "goodsSalePrice": 15.00,
    "goodsCount": 100,
    "goodsUnit": "个"
  }
}
```

### 3. 创建订单示例

```bash
curl -X POST "http://localhost:8080/cash/createOrder.do" \
  -H "Content-Type: application/json" \
  -d '{
    "sale_list_unique": "ORDER20231201001",
    "delivery_type": 0,
    "goods_weight": 1.5,
    "shop_courier_id": 1001,
    "courier_name": "张三",
    "courier_phone": "13800138000",
    "sale_list_cashier": 1001,
    "goodsList": "[{\"goods_barcode\":\"6901234567890\",\"goods_name\":\"测试商品\",\"goods_count\":2,\"goods_price\":15.00,\"goods_subtotal\":30.00}]"
  }'
```

### 4. 文件上传示例

```bash
curl -X POST "http://localhost:8080/loanMoney/uploadFile.do" \
  -F "file=@/path/to/image.jpg" \
  -F "yasuo=2" \
  -F "file_width=800" \
  -F "file_height=600"
```

**响应示例**:
```json
{
  "status": 1,
  "msg": "上传成功!",
  "data": {
    "url": "https://example.com/uploads/image_20231201_001.jpg"
  }
}
```

### 5. 查询统计信息示例

```bash
curl -X GET "http://localhost:8080/statistics/queryStatisticsMessageInMain.do?shopUnique=123456&datetime=2023-12-01"
```

**响应示例**:
```json
{
  "status": 1,
  "msg": "操作成功",
  "data": {
    "todaySales": 1500.00,
    "todayOrders": 25,
    "todayCustomers": 18,
    "monthSales": 45000.00
  }
}
```

## 数据模型说明

### 通用返回模型

#### ShopsResult
```java
public class ShopsResult {
    private Integer status;        // 状态码：1-成功，0-失败
    private String msg;           // 返回消息
    private Object data;          // 返回数据
    private Integer pageIndex;    // 当前页码
    private Integer pageSize;     // 每页大小
    private Integer pageCount;    // 总页数
    private Integer count;        // 数据总数
    private Integer total;        // 总记录数
    private List<?> rows;         // 数据行
    private Object Redundant;     // 辅助参数
    private Object object;        // 辅助对象
}
```

#### PurResult
```java
public class PurResult {
    private Integer status;       // 状态码：1-成功，0-失败
    private String msg;          // 返回消息
    private Object data;         // 返回数据
    private Object cord;         // 辅助信息
    private Integer count;       // 数据总数
    private Integer countNum;    // 计数
    private Integer total;       // 总记录数
    private Object rows;         // 数据行
}
```

### 通用请求参数基类

#### ValidateCommonReq
```java
public class ValidateCommonReq extends GoodsOperParam {
    @NotNull(message = "请输入店铺编号")
    private Long shopUnique;     // 店铺编号
    private Integer staffId;     // 员工ID
}
```

## 错误码说明

| 状态码 | 说明 |
|--------|------|
| 1 | 成功 |
| 0 | 失败 |

## 注意事项

1. **参数验证**: 所有接口都会进行参数验证，必填参数不能为空
2. **权限控制**: 部分接口需要登录验证，通过拦截器实现
3. **事务处理**: 涉及数据修改的接口都有事务保护
4. **日志记录**: 重要操作会记录操作日志
5. **异常处理**: 所有接口都有统一的异常处理机制
6. **拦截器**: 系统配置了统一拦截器，部分接口（如`/queryGoodsMessage.do`）不被拦截

## 开发规范

1. **命名规范**: 接口路径使用驼峰命名，以`.do`结尾
2. **参数传递**: POST请求使用`@RequestBody`接收JSON参数，GET请求使用查询参数
3. **返回格式**: 统一使用`ShopsResult`或`PurResult`格式
4. **代码注释**: 所有接口都有详细的中文注释说明
5. **参数校验**: 使用JSR-303注解进行参数校验
6. **日志注解**: 重要操作使用`@RemoteLog`注解记录日志

## 最佳实践

### 1. 接口调用建议
- **分页查询**: 大数据量查询时建议使用分页，避免一次性查询过多数据
- **参数校验**: 前端应进行基础参数校验，后端会进行严格的参数验证
- **错误处理**: 根据返回的`status`字段判断接口调用是否成功
- **超时设置**: 建议设置合理的接口超时时间（建议30秒）

### 2. 性能优化建议
- **缓存使用**: 频繁查询的数据（如商品信息）建议使用缓存
- **批量操作**: 支持批量操作的接口优先使用批量方式
- **图片压缩**: 上传图片时建议使用压缩功能减少存储空间
- **分页加载**: 列表数据使用分页加载，提升用户体验

### 3. 安全注意事项
- **参数过滤**: 所有用户输入都会进行安全过滤
- **权限验证**: 敏感操作需要相应的权限验证
- **数据加密**: 敏感数据传输建议使用HTTPS
- **防重复提交**: 重要操作建议添加防重复提交机制

## 常见问题

### 1. 接口返回状态码0的常见原因
- 必填参数缺失或格式错误
- 权限不足或未登录
- 业务逻辑验证失败
- 系统异常或网络问题

### 2. 文件上传失败的常见原因
- 文件大小超过限制（默认10MB）
- 文件格式不支持
- 网络连接不稳定
- 存储空间不足

### 3. 分页查询注意事项
- 页码从1开始计算
- 建议每页大小不超过100条
- 总页数和总记录数在返回结果中提供
- 支持按多种条件排序

### 4. 商品条码处理
- 系统会自动处理条码前导零
- 支持EAN-13、EAN-8等标准格式
- 条码长度建议不超过14位
- 重复条码会进行去重处理

## 版本更新记录

### v2.0 (当前版本)
- 新增商品管理V2.0接口
- 优化库存管理功能
- 增强支付集成能力
- 完善统计分析功能

### v1.0 (历史版本)
- 基础商品管理功能
- 简单订单处理
- 基础会员管理
- 基础支付功能

## 相关文档

- [业务流程文档](./BIZ.md)
- [数据库设计文档](./ER.md)
- [消息队列文档](./MQ.md)
- [定时任务文档](./JOB.md)
- [输入输出文档](./IO.md)
